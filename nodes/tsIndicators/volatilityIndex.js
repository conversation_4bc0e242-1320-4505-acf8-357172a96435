const technicalIndicators = require('technicalindicators');
technicalIndicators.setConfig('precision', 6);

const volatilityIdx = exports.volatilityIdx = props => {
    const {
        closePrices, comparisonClosePrices, 
        period = 30, 
        inputSymbol = 'SPCFD:SPX', 
        currentSymbol = 'currentSymbol'
    } = props;

    return new Promise((resolve, reject) => {
        try {
          // Utility function to calculate logarithm of returns
          function logReturns(prices) {
            let logReturns = [];
            for (let i = 1; i < prices.length; i++) {
              logReturns.push(Math.log(prices[i] / prices[i - 1]));
            }
            return logReturns;
          }
    
          // Function to calculate the standard deviation
          function standardDeviation(values, period) {
            let stdDev = [];
            for (let i = 0; i <= values.length - period; i++) {
              let slice = values.slice(i, i + period);
              let mean = slice.reduce((a, b) => a + b, 0) / period;
              let variance = slice.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / period;
              stdDev.push(Math.sqrt(variance));
            }
            return stdDev;
          }
    
          // Calculate volatility for the given prices and period
          function calculateVolatility(prices, period) {
            const logReturnsArray = logReturns(prices);
            const stdDev = standardDeviation(logReturnsArray, period);
            return stdDev.map(val => val * 100);
          }
    
          // Calculate volatility for the main symbol
          const calcVolatilityThis = calculateVolatility(closePrices, period);
    
          // Calculate volatility for the comparison symbol
          let calcVolatilityComp;
          if (inputSymbol !== currentSymbol) {
            calcVolatilityComp = calculateVolatility(comparisonClosePrices, period);
          } else {
            calcVolatilityComp = new Array(closePrices.length).fill(NaN); // Return NaN array if comparing with the same symbol
          }
    
          // Resolve with the calculated volatilities
          resolve({
            volatility: calcVolatilityThis,
            comparison: calcVolatilityComp
          });
        } catch (error) {
          reject(error);
        }
      });
    }