
const ATR = require('technicalindicators').ATR;
const HA = require('technicalindicators').<PERSON><PERSON>n<PERSON>hi;
// const moment = require('moment');
const _ = require('underscore-node');
// const Log = require('../util/logger');

const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const Log = exports.log = (...args) => console.log(timestamp(), ...args)

var calculateSTR = function(result, { multiplier = 3, period = 14, is_ha = true } = {}) {
    let open = [],
        close = [],
        high = [],
        low = [];

    _.each(result, function(item) {
        open.push(Number(item.open));
        close.push(Number(item.close));
        high.push(Number(item.high));
        low.push(Number(item.low));
    });

    let atr_result;
    if (is_ha) {
        let ha_results_arr = HA.calculate({ high: high, low: low, close: close, open: open }),
            ha_open = ha_results_arr.open,
            ha_high = ha_results_arr.high,
            ha_low = ha_results_arr.low,
            ha_close = ha_results_arr.close,
            ha_final_results = [];

        atr_result = ATR.calculate({ high: ha_high, low: ha_low, close: ha_close, period: period });

        _.each(result, function(item, i) {
            ha_final_results.push({
                open: ha_open[i],
                high: ha_high[i],
                low: ha_low[i],
                close: ha_close[i],
                date: item.date,
                timestamp: item.timestamp
            });
        });
        result = ha_final_results;
    } else {
        atr_result = ATR.calculate({ high: high, low: low, close: close, period: period });
    }
    let previous_final_upper = 0,
        previous_final_low = 0,
        previous_st, finalres;

    for (let i = 0; i < atr_result.length; i++) {
        let result_counter = i + period,
            tdata = result[result_counter],
            atr = atr_result[i],
            high = tdata.high,
            low = tdata.low,
            base_upper = (((high + low) / 2) + (multiplier * atr)),
            base_lower = (((high + low) / 2) - (multiplier * atr)),
            final_upper = 0,
            final_lower = 0,
            previous_close = 0,
            previous_data = result[result_counter - 1];

        previous_final_upper = 0;
        previous_final_low = 0;
        previous_final_upper = previous_data.fup || 0;
        previous_final_low = previous_data.flow || 0;
        previous_close = previous_data.close || 0;
        previous_st = previous_data.st || 0;

        let st,
            result_action;

        final_upper = (base_upper < previous_final_upper || previous_close > previous_final_upper) ? base_upper : previous_final_upper;
        final_lower = (base_lower > previous_final_low || previous_close < previous_final_low) ? base_lower : previous_final_low;


        if (previous_st == previous_final_upper && tdata.close <= final_upper) {
            st = final_upper;
        } else if (previous_st == previous_final_upper && tdata.close >= final_upper) {
            st = final_lower;
        } else if (previous_st == previous_final_low && tdata.close >= final_lower) {
            st = final_lower;
        } else if (previous_st == previous_final_low && tdata.close <= final_lower) {
            st = final_upper;
        } else {
            st = 0;
        }

        result[result_counter].fup = final_upper;
        result[result_counter].flow = final_lower;
        result[result_counter].st = st;

        if (tdata.close > st) {
            result_action = "Buy";

        } else if (tdata.close < st) {
            result_action = "Sell";

        }
        finalres = { timestamp: tdata.timestamp, open: tdata.open.toFixed(2), high: tdata.high.toFixed(2), low: tdata.low.toFixed(2), close: tdata.close.toFixed(2), pclose: previous_close.toFixed(2), atr: atr.toFixed(2), bu: base_upper.toFixed(2), bl: base_lower.toFixed(2), fu: final_upper.toFixed(2), fl: final_lower.toFixed(2), st: st.toFixed(2), action: result_action };
    }
    Log("HA ST Indicator", period, finalres);
    return finalres;
};


module.exports = calculateSTR;