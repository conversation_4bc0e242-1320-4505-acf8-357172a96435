//@version=5
// Copyright (c) 2019-present, <PERSON> (everget)
// Chandelier Exit script may be freely distributed under the terms of the GPL-3.0 license.
indicator('Chandelier Exit', shorttitle='CE', overlay=true)

var string calcGroup = 'Calculation'
length = input.int(title='ATR Period', defval=22, group=calcGroup)
mult = input.float(title='ATR Multiplier', step=0.1, defval=3.0, group=calcGroup)
useClose = input.bool(title='Use Close Price for Extremums', defval=true, group=calcGroup)

var string visualGroup = 'Visuals'
showLabels = input.bool(title='Show Buy/Sell Labels', defval=true, group=visualGroup)
highlightState = input.bool(title='Highlight State', defval=true, group=visualGroup)

var string alertGroup = 'Alerts'
awaitBarConfirmation = input.bool(title="Await Bar Confirmation", defval=true, group=alertGroup)

atr = mult * ta.atr(length)

longStop = (useClose ? ta.highest(close, length) : ta.highest(length)) - atr
longStopPrev = nz(longStop[1], longStop)
longStop := close[1] > longStopPrev ? math.max(longStop, longStopPrev) : longStop

shortStop = (useClose ? ta.lowest(close, length) : ta.lowest(length)) + atr
shortStopPrev = nz(shortStop[1], shortStop)
shortStop := close[1] < shortStopPrev ? math.min(shortStop, shortStopPrev) : shortStop

var int dir = 1
dir := close > shortStopPrev ? 1 : close < longStopPrev ? -1 : dir

var color longColor = color.green
var color shortColor = color.red
var color longFillColor = color.new(color.green, 90)
var color shortFillColor = color.new(color.red, 90)
var color textColor = color.new(color.white, 0)

longStopPlot = plot(dir == 1 ? longStop : na, title='Long Stop', style=plot.style_linebr, linewidth=2, color=color.new(longColor, 0))
buySignal = dir == 1 and dir[1] == -1
plotshape(buySignal ? longStop : na, title='Long Stop Start', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(longColor, 0))
plotshape(buySignal and showLabels ? longStop : na, title='Buy Label', text='Buy', location=location.absolute, style=shape.labelup, size=size.tiny, color=color.new(longColor, 0), textcolor=textColor)

shortStopPlot = plot(dir == 1 ? na : shortStop, title='Short Stop', style=plot.style_linebr, linewidth=2, color=color.new(shortColor, 0))
sellSignal = dir == -1 and dir[1] == 1
plotshape(sellSignal ? shortStop : na, title='Short Stop Start', location=location.absolute, style=shape.circle, size=size.tiny, color=color.new(shortColor, 0))
plotshape(sellSignal and showLabels ? shortStop : na, title='Sell Label', text='Sell', location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.new(shortColor, 0), textcolor=textColor)

midPricePlot = plot(ohlc4, title='', style=plot.style_circles, linewidth=0, display=display.none, editable=false)

longStateFillColor = highlightState ? dir == 1 ? longFillColor : na : na
shortStateFillColor = highlightState ? dir == -1 ? shortFillColor : na : na
fill(midPricePlot, longStopPlot, title='Long State Filling', color=longStateFillColor)
fill(midPricePlot, shortStopPlot, title='Short State Filling', color=shortStateFillColor)

await = awaitBarConfirmation ? barstate.isconfirmed : true
alertcondition(dir != dir[1] and await, title='Alert: CE Direction Change', message='Chandelier Exit has changed direction!')
alertcondition(buySignal and await, title='Alert: CE Buy', message='Chandelier Exit Buy!')
alertcondition(sellSignal and await, title='Alert: CE Sell', message='Chandelier Exit Sell!')
