const technicalIndicators = require('technicalindicators');
technicalIndicators.setConfig('precision', 6);

const { ATR, ChandelierExit, Highest, Lowest
} = technicalIndicators;


const chandelierExit = exports.chandelierExit = props => {
    const {
        closePrices, 
        highPrices, 
        lowPrices, 
        atrPeriod = 22, 
        atrMultiplier = 3.0, 
        useClose = true,
    } = props;

    return new Promise((resolve, reject) => {
        try {
          // Calculate ATR (Average True Range)
          function calculateATR(closePrices, highPrices, lowPrices, period) {
            let atr = [];
            for (let i = 1; i < closePrices.length; i++) {
              let tr = Math.max(highPrices[i] - lowPrices[i], Math.abs(highPrices[i] - closePrices[i - 1]), Math.abs(lowPrices[i] - closePrices[i - 1]));
              atr.push(tr);
            }
    
            let atrValues = [];
            for (let i = period - 1; i < atr.length; i++) {
              let slice = atr.slice(i - period + 1, i + 1);
              let avgTR = slice.reduce((a, b) => a + b, 0) / period;
              atrValues.push(avgTR);
            }
            return atrValues;
          }
    
          // Calculate ATR with the given period and multiplier
          // const atr = calculateATR(closePrices, highPrices, lowPrices, atrPeriod).map(value => value * atrMultiplier);
    
          const atr = ATR.calculate({ period: atrPeriod, close: closePrices, high: highPrices, low: lowPrices, }).map(value => value * atrMultiplier);

          // Calculate longStop and shortStop
          let longStop = [];
          let shortStop = [];
          for (let i = atrPeriod; i < closePrices.length; i++) {
            const high = useClose ? Math.max(...closePrices.slice(i - atrPeriod, i + 1)) : Math.max(...highPrices.slice(i - atrPeriod, i + 1));
            const low = useClose ? Math.min(...closePrices.slice(i - atrPeriod, i + 1)) : Math.min(...lowPrices.slice(i - atrPeriod, i + 1));
    
            longStop.push(high - atr[i - atrPeriod]);
            shortStop.push(low + atr[i - atrPeriod]);
          }
          // Calculate direction and signals
          let dir = 1; // Start with long direction
          let buySignal = [];
          let sellSignal = [];
          let Signal = [];
          let dirArr = [];
          for (let i = 1; i < longStop.length; i++) {
            dir = closePrices[i + atrPeriod] > shortStop[i - 1] ? 1 : closePrices[i + atrPeriod] < longStop[i - 1] ? -1 : dir;
            dirArr.push(dir);
          //   // Buy signal: direction switches to long
            let buyStg = dir === 1 && closePrices[i + atrPeriod - 1] < longStop[i - 1];
            buySignal.push(buyStg);
          //   // Sell signal: direction switches to short
            let sellStg = dir === -1 && closePrices[i + atrPeriod - 1] > shortStop[i - 1];
            sellSignal.push(sellStg);
            Signal.push(buyStg !== sellStg);
          }
    
          // Resolve the Promise with the calculated values
          resolve({
            // atr: atr, 
            longStop: longStop,
            shortStop: shortStop,
            buySignal: buySignal,
            sellSignal: sellSignal,
            dirArr: dirArr,
            dirSignal: Signal,
          });
        } catch (error) {
          reject(error); // Reject the Promise if any error occurs
        }
      });
    }