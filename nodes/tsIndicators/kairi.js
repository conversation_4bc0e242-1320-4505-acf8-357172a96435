const technicalIndicators = require('technicalindicators');
technicalIndicators.setConfig('precision', 6);
const { SMA, EMA} = technicalIndicators;
const kairi = exports.kairi = (props) => {
    return new Promise(async (resolve) => {
        var resp;
        const {src, period: length, movType: maInput, levelUpper: ust, levelLower: alt} = props;
        const getMA = (srcM, lengthM) => {
            var ma = 0.0;
            if(maInput == 'SMA') {
                ma = SMA.calculate({ period: parseInt(lengthM), values: [...src] })
            } else if(maInput == 'EMA') {
                ma = EMA.calculate({ period: parseInt(lengthM), values: [...src] })
            } else if(maInput == 'TSF') {
                var rSRC = [...srcM].reverse()
                var lrc = linreg(rSRC, lengthM, 0)
                var lrc1 = linreg(rSRC, lengthM, 1)
                var lrs = lrc - lrc1
                var TSF = linreg(rSRC, lengthM, 0) + lrs
                ma = TSF
            }
            return ma;
        }
        var calc = getMA(src, length)
        var calc0 = Array.isArray(calc) ? calc.slice(-1)[0] : 0;
        var src0 = Array.isArray(src) ? src.slice(-1)[0] : 0;
        var fCalc = (src0 - calc0) * 100 / calc0
        resp = {
            kairi: fCalc,
            // calc,
            calc0,
            src0,
        }
        resolve(resp);
    });
}



const linreg = (source, length, offset) => {
    let begin = 0, end = length - 1;
    let sumX = 0.0;
    let sumY = 0.0;
    let sumXSqr = 0.0;
    let sumXY = 0.0;

    for (let i = 0; i < length; ++i) {
        // must calculate across X-axis =>   x-3, x-2, x-1, x = 0
        // hence the quick and dirty reverse() above.

        let val = source[begin + i];
        let per = i + 1;
        sumX += per;
        sumY += val;
        sumXSqr += per * per;
        sumXY += val * per;
    }

    var m = (length * sumXY - sumX * sumY) / (length * sumXSqr - sumX * sumX);
    var b = (sumY / length) - (m * sumX) / length + m;
    return b + m * (length - 1 - offset);
}