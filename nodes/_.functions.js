const { exec } = require('child_process');
const shell = require('shelljs');
const path = require('path');
const cronParser = require('cron-parser');
const fs = require('fs').promises;
const fss = require('fs');
const { v4: uuidv4 } = require('uuid');
const Redis = require('ioredis');

const fnxIdx = require('./_.functions.indicators');
const fnxDex = require('./_.functions.dex');
const config = require('./_.config');
const redixPrefix = require('../src/lib/redis.prefix');
const XLSX = require('xlsx');


const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args)
const redisKeys = {
    battle_init: 'battle_init',
}

const battleNode = exports.battleNode = {
    updateNodeProcessIDandPort: async props => {
        const { redisClient, redisKey, pid, port, forceInsert = false, battle } = props;
        return new Promise(async (resolve, reject) => {
            try {
                const redisVal = await redisClient.get(redisKey);
                if (redisVal) {
                    let redisValStg = JSON.parse(redisVal);
                    let nodeSettings = redisValStg?.nodeSettings;
                        nodeSettings.port = port;
                        nodeSettings.process_id = pid;
                        nodeSettings.is_active = true;
                        nodeSettings.dtupdated = Date.now();
                        nodeSettings.dtupdatedEn = new Date(Date.now());
                    redisValStg.nodeSettings = nodeSettings;
                    await redisClient.set(redisKey, JSON.stringify(redisValStg));
                    resolve(true)
                } else {
                    reject('error, redisKey not found!')
                }
            }
            catch (e) {
                console.log('error: logServerStartStop err', e)
                reject('error, check logs1')
            }
        });
    },
    getBattleData: async ({ cronParser, redisClient, redisKey, pid, port }) => {
        return new Promise(async (resolve, reject) => {
            let battleData = {};
            battleData = { ...battleData, redisKey, };

            try {
                let nodeTask; //get node settings...
                try {
                    const redisVal = await redisClient.get(redisKey);
                    let redisValStg = JSON.parse(redisVal);
                    nodeTask = redisValStg.nodeSettings;
                }
                catch (e) {
                    // console.log('error: get node settings err')
                    await battleNode.addLog({
                        redisClient, redisKey, logField: 'errors',
                        logType: 'error',
                        logValue: {
                            dtcreatedEn: new Date(Date.now()),
                            errorDesc: 'get node settings err',
                            error: JSON.stringify(e),
                        }
                    });
                };

                let battle; //get battle settings...
                try {
                    const redisBattleVal = await redisClient.get(redisKeys.battle_init);
                    let redisBattleValStg = JSON.parse(redisBattleVal);
                    battle = redisBattleValStg;
                }
                catch (e) {
                    await battleNode.addLog({
                        redisClient, redisKey, logField: 'errors',
                        logType: 'error',
                        logValue: {
                            dtcreatedEn: new Date(Date.now()),
                            errorDesc: 'get battle settings err',
                            error: JSON.stringify(e),
                        }
                    })
                }

                if (nodeTask) {
                    const symi = nodeTask?.node_key_tag;
                    const { battleID, node_tasks } = nodeTask;
                    let nodeTasks;
                    try {
                        nodeTasks = typeof node_tasks !== 'object' ? JSON.parse(node_tasks) : node_tasks;
                    } catch (eN) {
                        await battleNode.addLog({
                            redisClient, redisKey, logField: 'errors',
                            logType: 'error',
                            logValue: {
                                dtcreatedEn: new Date(Date.now()),
                                errorDesc: 'get nodeTasks err',
                                error: JSON.stringify(eN),
                            }
                        });
                    }
                    let nodeWorkPeriod = nodeTasks?.tasks[0]['period'];
                    let nodeBatch = nodeTasks?.tasks[0]['batch'];
                    let nodeInterval = nodeTasks?.tasks[0]['nodeInterval'];
                    let battle_params = typeof battle?.battle_params !== 'object' ? JSON.parse(battle?.battle_params) : JSON.parse(JSON.stringify(battle?.battle_params));
                    const parameters = typeof battle_params?.parameters !== 'object' ? JSON.parse(battle_params?.parameters) : JSON.parse(JSON.stringify(battle_params?.parameters));
                    // const {intervals, battleInterval, indicatorsWParams, rulesets, trading, exit, wallet, } = parameters;
                    battleData = { ...battleData, symi, battleID, period: nodeWorkPeriod, batch: nodeBatch, nodeInterval, nodeTasks, ...parameters };
                    if (port) {
                        battleData.port = port;
                    };
                    if (pid) {
                        battleData.pid = pid;
                    }
                    let nextR = await battleNode.calcNextWorkTime({ cronPeriod: nodeWorkPeriod, promisee: true })
                    // log(symi, 'next run', nextR);
                    battleData = { ...battleData, nextRun: nextR };
                    resolve(battleData);
                } else {
                    log('node task not found: ', nodeTask);
                    reject('nodeTask not found')
                }
            } catch (e) {
                log('error 1957: ', e)
                await battleNode.addLog({
                    redisClient, redisKey, logField: 'errors',
                    logType: 'error',
                    logValue: {
                        dtcreatedEn: new Date(Date.now()),
                        errorDesc: 'get battledata err',
                        error: JSON.stringify(e),
                    }
                });
                reject('battledata failed')
            }
        });
    },
    addLog: async ({redisClient, redisKey, logField, logValue, logType = 'generic' }) => {
        const logLimit = logType == 'error' ? 99 : 5;
        return new Promise(async (resolve, reject) => {
            const redisVal = await redisClient.get(redisKey);
            if (redisVal) {
                let redisValStg = JSON.parse(redisVal);
                let aField = redisValStg[logField] ? redisValStg[logField] : [];
                aField.push(logValue);
                aField.slice(-1 * logLimit);
                redisValStg[logField] = aField;
                await redisClient.set(redisKey, JSON.stringify(redisValStg));
                resolve(aField);
            } else {
                //create new rediskey
                reject('01-redisKey not found');
            }
        });
    },
    calcNextWorkTime: ({cronPeriod, promisee = false}) => {
        if (promisee) {
            return new Promise(async (resolve, reject) => {
                let resp = {};
                try {
                    var nodeInterval = cronParser.parseExpression(cronPeriod);
                    let nedeNextWorkTimeStg = nodeInterval.next();
                    let nedeNextWorkTime = new Date(nedeNextWorkTimeStg.toString());
                    let nedeNextInSec = (nedeNextWorkTime - new Date(Date.now())) / 1000;
                    resp.dt = nedeNextWorkTime;
                    resp.sec = parseInt(nedeNextInSec);
                    resolve(resp)
                } catch (e) {
                    reject(e)
                }
            })
        } else {
            let resp = {};
            try {
                var nodeInterval = cronParser.parseExpression(cronPeriod);
                let nedeNextWorkTimeStg = nodeInterval.next();
                let nedeNextWorkTime = new Date(nedeNextWorkTimeStg.toString());
                let nedeNextInSec = (nedeNextWorkTime - new Date(Date.now())) / 1000;
                resp.dt = nedeNextWorkTime;
                resp.sec = parseInt(nedeNextInSec);
                return resp
            } catch (e) {
                return e
            }
        }
    },
    fetchKline: async ({ binanceApi, connTimeOut = 12000, symbol, interval = '1m', limit = 300, asavetoFile = false,  }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            try {
                var filename = symbol.toLowerCase() + '_' + interval + '_' + Date.now().toString() + '.json';
                var filepath = path.resolve(__dirname, './bin/klines/' + filename);
                rawData = await await promiseTimeout(binanceApi.futuresCandles(symbol, interval, {
                    limit
                }), connTimeOut);
                asavetoFile && await savetoFile(rawData, filepath);
                resolve(rawData);
            }
            catch (e) {
                reject(e)
            }
            // rawData = await fnx.promiseTimeout(
            //     binanceApi.futuresCandles(jTaskParams?.symbol, jTaskParams?.interval), connTimeOut)
        });
    },
    ws_setSubscription: async props => {
        return new Promise(async (resolve, reject) => {
            const { redisClient, dbMarket, binanceApi, battleData, symi, intervals, battleInterval, 
                battleID, period, batch, nodeInterval, nodeTasks, pairs, candleCounts,
                indicatorsWParams, rulesets, trading, port, pid, nextRun, modeDebug,
                tickerTimeMonitor, tickerProcessTimeMonitor, tickerProcessTimeEMonitor, tickerProcessTimer } = props;
            const { redisKey } = battleData;

            const strategyRulesetParams = await fnxIdx.strategies.getStrategyRulesetParams({
                db: dbMarket,
                qRef: 'wsocket',
            })
            let nodeTaskz = nodeTasks?.tasks
            // log('propz', props, strategyRulesetParams);
            if (Array.isArray(nodeTaskz)) {
                try {
                    let errs = [];
                    for (c of nodeTaskz) {
                        const dtBOP = Date.now();
                        let {
                            battleID, nodeKey, batch, nodeInterval,
                            taskDesc, taskID, taskTag, battleInterval,
                            script2Run, script2RunUri, taskType,
                            period, periodDesc,
                            taskParams, wsconn, wsconnuri, 
                        } = c;

                        const symbol = taskTag.split('_')[0];
                        const taskTagLw = taskTag.toLowerCase();
                        let taskParamz = typeof taskParams !== 'object' ? JSON.parse(taskParams) : taskParams;
                        const jTask = taskParamz;
                        jTask.battleID = battleID;
                        jTask.taskID = taskID;
                        function mngLiveTicker(xsmi, n, props) {
                            const { redisClient, binanceApi, symbol, interval, limit, strategies, modeDebug, socket, indicatorParams } = props;
                            return new Promise(async (resolve, reject) => {
                                try {
                                    var dataName = symbol.toLowerCase() + '@kline_' + interval;
                                    let isNodeTaskWIP = false // DONE: await nTasks.checkNodeTaskIsWIP({...c, redisClient});
                                    if (isNodeTaskWIP) {
                                        // log(dataName, 'isNodeTaskWIP ? do nothing', isNodeTaskWIP)
                                    } else {
                                        let redisTickerDataKey = redixPrefix.dataKlineTicker + taskTagLw;
                                        await redisClient.set(redisTickerDataKey, JSON.stringify(n));
                                        let sStatsValue = {
                                            dtupdatedEn: new Date(Date.now()).toISOString(),
                                            dataOpen: n.E,
                                            dataOpenEn: n.E && new Date(n.E).toISOString(),
                                        };
                                        await redisClient.set(redixPrefix.monitorKlineTicker + taskTagLw, JSON.stringify(sStatsValue));
                                    }
                                    resolve(xsmi)

                                }
                                catch (e) {
                                    reject(e)
                                }
                            });
                        };

                        function mngLiveTicker_daily(props) {
                            const { redisClient, nx, binanceApi, strategies, modeDebug, socket, indicatorParams, sqlDB } = props;
                            return new Promise(async (resolve, reject) => {
                                try {
                                    // let sq = dailyWsCreateUpdateSQL({
                                    //     payloadArr: nx, //.slice(-2)
                                    //     tableName: 'market_futuresDaily',
                                    //     batchID: 'ws:' + Date.now().toString(),
                                    // });
                                    log('mngLiveTicker_daily', nx.slice(-1))
                                    // await sqlDB.exec(sq);
                                    resolve(true);
                                }
                                catch (e) {
                                    log('e daily', e)
                                    resolve(false)
                                }

                            });
                        }

                        try {
                            async function xfn(n) {
                                var refID = Date.now();
                                try {
                                    var xsmi = n.k ? n.s.toLowerCase() + '@kline_' + n.k.i : null;
                                    var pairTag = n.k ? n.s.toLowerCase() + '_' + n.k.i : null;
                                    var klineKey = redixPrefix.dataKline + taskTagLw;
                                    var klineTickerKey = redixPrefix.dataKlineTicker + taskTagLw;
                                    var klineMonitorKey = redixPrefix.monitorKline + taskTagLw;
                                    var klineTickerMonitorKey = redixPrefix.monitorKlineTicker + taskTagLw;

                                    if (xsmi) {
                                        tickerTimeMonitor[xsmi] = Date.now();
                                        var timeMonitorStamp = tickerProcessTimeMonitor[xsmi];
                                        var deltaTimer = timeMonitorStamp ? (Date.now() - timeMonitorStamp) : (tickerProcessTimer + 1);
                                        if (deltaTimer > tickerProcessTimer) {
                                            tickerProcessTimeMonitor[xsmi] = Date.now();
                                            n.EEn = new Date(n.E);
                                            await redisClient.set(klineTickerKey, JSON.stringify(n));
                                            try {
                                                let sStatsValue = {
                                                    dtupdatedEn: new Date(Date.now()).toISOString(),
                                                    dataOpen: n.E,
                                                    dataOpenEn: new Date(n.E).toISOString(),
                                                };
                                                await redisClient.set(klineTickerMonitorKey, JSON.stringify(sStatsValue));
                                            }
                                            catch (eSt) {
                                                modeDebug && log('stat save error', symbol, eSt);
                                            };

                                            var { trading = {}, battleType = {} } = battleData;
                                            // Array.isArray(successRulesets) && successRulesets.length !== 0 && console.log('battleParamsz', symbol, battleData);
                                            var { entry, exit, wallet } = trading;
                                            var isExchange = battleType && battleType.dex == 'exchange';
                                            const lastCandle = nBinance.tick2json(n.k, true);

                                            var timex = Date.now();
                                            var { config: dexConfig = {} } = battleType;
                                            const { testDex, dexCode, dexTitle, apiKey, apiSecret } = dexConfig;
                                            let globalDexApi = await fnxDex.dex.getDexApi({redisClient})?.api;

                                            let trds = await fnxIdx.fnTrades.updateOpenTradesPnls({
                                                redisClient, symbol: symbol || jTask.symbol || n.s, xsmi: pairTag, 
                                                lastCandle, ref: 'wsocket', dexApi: globalDexApi
                                            });
                                            
                                            if (trds && Array.isArray(trds) && trds.length !== 0) {
                                                await fnxIdx.fnTrades.sLossTProfitCheck({
                                                    redisClient,
                                                    lastCandle, symbol: symbol || jTask.symbol || n.s,
                                                    xsmi: pairTag, wallet, exit, ref: 'wsocket',
                                                    battleType, isExchange, dexApi: globalDexApi,
                                                });
                                            }

                                            let indicatorCalculations;
                                            try {
                                                indicatorCalculations = await fnxIdx.indicators.execCalculations({
                                                    redisClient, battleID, taskID,taskTag, batch,nodeInterval,battleInterval,
                                                    battleData, rawData: false, tickerData: n, taskParams: jTask,
                                                    klineRef: 'wsocket', saveResults: true, 
                                                });
                                            } catch (eIdx) {
                                                // log('indx error', eIdx)
                                            }

                                            //calculate strategies....
                                            if (indicatorCalculations) {
                                                try {
                                                    let taskParams = jTask;
                                                    const strategyRulesetParams = await fnxIdx.strategies.getStrategyRulesetParams({})
                                                    let strategyRulesetCalculations;
                                                    if (battleInterval == nodeInterval) {
                                                        var xsmi = taskParams && taskParams.keyTag;
                                                        var symbol = taskParams.symbol || n.s;
                                                        try {
                                                            strategyRulesetCalculations = await fnxIdx.strategies.calculate({
                                                                redisClient,
                                                                params: {
                                                                    symbol, xsmi, battleInterval,
                                                                    battle_params: battleData,
                                                                    indicatorParams: battleData.indicatorsWParams,
                                                                    indicatorCalculations,
                                                                    strategyRulesets: battleData.rulesets, // strategyRulesets,
                                                                    strategyRulesetParams: strategyRulesetParams,
                                                                    lastCandle: lastCandle, klineRef: 'wsocket'
                                                                }
                                                            });
                                                            // log('strategyRulesetCalculations', JSON.stringify(strategyRulesetCalculations), battleInterval, nodeInterval)
                                                            if (strategyRulesetCalculations) {
                                                                let redisIndKey = redixPrefix.dataStrategies + taskTagLw;
                                                                await redisClient.set(redisIndKey, JSON.stringify(strategyRulesetCalculations));
                                                                // console.log('strategyRulesetCalculations', strategyRulesetCalculations.length)
                                                            } else {
                                                                log('1055 / xfn strategyRulesetCalculations failed',)
                                                            }
                                                        } catch (eIdx) {
                                                            log('1054 / xfn strategyRulesetCalculations failed',)
                                                        }

                                                        try {
                                                            let successRulesets = Array.isArray(strategyRulesetCalculations) && [...strategyRulesetCalculations].filter(rs => rs.result == true);
                                                            
                                                            // log('isExchange', symbol, 'wsocket', isExchange);
                                                            var entryCheck = await fnxIdx.fnTrades.entryCheck({
                                                                redisClient, symbol, xsmi, wallet, exit,
                                                                lastCandle, battleInterval, newBar: lastCandle.barClosed || false,
                                                                entryParams: entry, strategyRulesetSuccess: successRulesets,
                                                                debugMode: false, ref: 'wsocket', battleType, isExchange, 
                                                                dexApi: globalDexApi,
                                                                // prop,
                                                            });
                                                            // isExchange ? 
                                                            //     await fnxIdx.dex.entryCheck({
                                                            //         redisClient, symbol, xsmi, wallet, exit,
                                                            //         lastCandle, battleInterval,
                                                            //         newBar: lastCandle.barClosed || false,
                                                            //         entryParams: entry,
                                                            //         strategyRulesetSuccess: successRulesets,
                                                            //         debugMode: false,
                                                            //         ref: 'wsocket',
                                                            //         battleType,
                                                            //         // prop,
                                                            //     })
                                                            //     :
                                                            //     await fnxIdx.fnTrades.entryCheck({
                                                            //     redisClient, symbol, xsmi, wallet, exit,
                                                            //     lastCandle, battleInterval,
                                                            //     newBar: lastCandle.barClosed || false,
                                                            //     entryParams: entry,
                                                            //     strategyRulesetSuccess: successRulesets,
                                                            //     debugMode: false,
                                                            //     ref: 'wsocket',
                                                            //     battleType,
                                                            //     // prop,
                                                            // });
                                                            // successRulesets.length !== 0 && log('wsocket: act checktrader', successRulesets.length)

                                                            let battlestarted = await redisClient.get('appVars:battlestarted');
                                                            // fnx.log('entryCheck2 battlestarted', symbol, battlestarted);


                                                            if (entryCheck?.result && parseFloat(battlestarted) == 1) {
                                                                await fnxIdx.fnTrades.entry({
                                                                    ...props,
                                                                    redisClient, symbol, xsmi, wallet, exit,
                                                                    lastCandle, battleInterval,
                                                                    debugMode: false,
                                                                    newBar: lastCandle.barClosed || false,
                                                                    entryParams: entry,
                                                                    ref: 'wsocket',
                                                                    act: entryCheck.act,
                                                                    entryCheck: entryCheck.conditions,
                                                                    strategyRulesetSuccess: successRulesets,
                                                                    battleType, isExchange, dexApi: globalDexApi,
                                                                });
                                                                log('wsocket: enter position done!', xsmi) //, entryCheck
                                                            }

                                                        } catch (eT) {
                                                            log('wsocket: checktrader trader is not working...', eT)
                                                        };
                                                    } else {
                                                        //other time frame process . no need for strategy actions!
                                                    }
                                                }
                                                catch (eSt) {
                                                    log('error in strat calc', eSt);
                                                }
                                                // log('wsconn strate ok')
                                                return true
                                            } else {
                                                // resolve(false)
                                                // log('xfn: no indicatorCalculations - wsconn strate not ok', n, symbol)
                                                return false
                                            }

                                        }
                                    } else {
                                        log('no xsmi', n);
                                    }
                                }
                                catch (a2) {
                                    log(`xfn error bnode_ws_set ${xsmi}`, a2);
                                }
                            };
                            async function xfn_daily(n) {
                                // log('xfn_daily', n && n[0]);
                                let arrN = [];
                                Array.isArray(n) && n.map(p => { 
                                    let delta = (parseFloat(p.c) - parseFloat(p.l)) / (parseFloat(p.h) - parseFloat(p.l)) * 100;

                                    arrN.push({
                                        "symbol": p.s,
                                        "priceChange": p.p,
                                        "priceChangePercent": p.P,
                                        "delta": delta,
                                        "weightedAvgPrice": p.w,
                                        "lastPrice": p.c,
                                        "lastQty": p.Q,
                                        "openPrice": p.o,
                                        "highPrice": p.h,
                                        "lowPrice": p.l,
                                        "volume": p.v,
                                        "quoteVolume": p.q,
                                        "openTime": p.O,
                                        "closeTime": p.C,
                                        "firstId": p.F,
                                        "lastId": p.L,
                                        "count": p.n,
                                        "src": "xfnD",
                                        "openTimeHRF": new Date(p.O).toISOString(),
                                        "closeTimeHRF": new Date(p.C).toISOString(),
                                        "dteventISO": new Date(p.E).toISOString(),
                                        "dtcreatedISO": new Date(Date.now()).toISOString(),
                                    })
                                });
                                let redisKey = redixPrefix.dataMarket + 'futuresdaily'.toLowerCase();
                                let valuesStg = await redisClient.get(redisKey);
                                if (valuesStg) {
                                    let values = JSON.parse(valuesStg);
                                    arrN.map(a => {
                                        let p2Update = a.symbol;
                                        values[p2Update] = a;
                                    });
                                    await redisClient.set(redisKey, JSON.stringify(values));
                                    try {
                                        let sKey = redixPrefix.monitorMarketTicker + taskTag.split('_')[1].toLowerCase();
                                        let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                                        await redisClient.set(sKey, JSON.stringify(sStatsValue));
                                    }
                                    catch (eSt) {
                                        modeDebug && log('stat save error', taskTag.split('_')[1].toLowerCase());
                                    }
                                }
                            }

                            // async function xfn_X(n) {
                            // }

                            if (wsconn) {
                                //do act
                                const symi = taskTag.toLowerCase();
                                const symix = wsconnuri; 
                                if (taskDesc == 'klineLoader') {
                                    await binanceApi.futuresSubscribe(symix, xfn);
                                    // log('kline socket Subscription set ', symi, symix)
                                
                                } else if (taskDesc == 'marketTicker') {
                                    if (taskTag == 'mt_futuresDaily') {
                                        await binanceApi.futuresSubscribe('!ticker@arr', xfn_daily);
                                    } else {
                                        log('wsconn what taskTag ?', symix, taskTag)
                                    }
                                } else {
                                    log('wsconn what ?', symix, taskDesc)
                                }
                                // else if (taskDesc == 'futuresFundingRate') {
                                //     await binanceApi.futuresSubscribe(symix, xfn_X);

                                // } else if (taskDesc == 'futuresMarkPrice') {
                                //     await binanceApi.futuresSubscribe(symix, xfn_X);
                                // } 

                            } else {
                                // log(symi + 'no wsConn - ignore: ' + taskDesc + '/' + taskTag)
                                // errs.push({symi, error: 'no wsConn: ' + taskDesc + '/' + taskTag })
                            }
                        }
                        catch (eF) {
                            log(symi, 'setSubscription error: ', eF);
                            errs.push({symi, error: eF.toString()})
                        }
                    }
                    errs.length !== 0 && log(symi, 'err in ws subs', errs)
                    errs.every( ex => ex == true) ? resolve(true) : reject(errs);
                }
                catch (e) {
                    log(symi, 'setSubscription error: ', e);
                    reject(symi + ' ticker failed3')
                }
            } else {
                log(symi, 'setSubscription error no nodeTaskz: ');
                reject(symi + ' ticker failed3')
            }
        });
    },
    trades_getlist: async (req, res, prps ) => {
        const {redisClient,} = prps;
        return new Promise(async (resolve, reject) => {
            let trades = [];
            let params = req.query;
            let export2Excel = params?.excel ? true : false;
            try {
                // log('trades_getlist', export2Excel)
                trades = await fnxIdx.fnTrades.fnGetTrades({
                    redisClient, export2Excel: true
                });
                if (!export2Excel) {
                    res.render('trades', { data: trades });
                } else {
                    let fileName = "trades.xlsx"
                    const worksheet = XLSX.utils.json_to_sheet(trades);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
                    XLSX.writeFile(workbook, fileName);
                    let filePath = path.join(__dirname, '../', fileName)
                    res.sendFile(
                        filePath,
                        (err, data) => {
                            if (err) {
                                console.error('Error sending file:', err);
                            } else {
                                console.log('file sent:', filePath);
                            }
                        }
                    );
                }
            } catch (e) {
                log('trades_getlist erro', e)
                res.render('trades', { data: trades });
            }
        }
    )},
    listCronJobs: str => {
        const cronList = [];
        let crons = str.substring(1, str.length - 1).split("\n");
        let newLine = false;
        const resp = {}
        let key = '-';
        let newKey = '';
        for (let i = 0; i < crons.length; i++) {
            if (i !== 0 && i !== crons.length - 1 && crons[i].length > 0) {
                const statusIndex = crons[i].search("}:");
                const keyIndex = crons[i].search("':");
                if (keyIndex > - 1) {
                    newKey = crons[i].substring(0, keyIndex).trim().replace("'", "");
                    newLine = key !== newKey;
                    key = newKey;
                    resp.key = newKey
                } else {
                    key = resp.key;
                    newKey = '';
                    newLine = false;
                }
                if (statusIndex > - 1) {
                    resp.status = crons[i].substring(statusIndex + 3, crons[i].length)
                }
                if (!newLine && (resp.key !== undefined) && (resp.status !== undefined)) {
                    cronList.findIndex(c => c.key === resp.key) < 0 && cronList.push({ ...resp })
                }
            }
        }
        return cronList
    },
    ws_stopSubscriptions: (props = {}) => {
        const { binanceApi, Sentry = false, isLocal = false } = props
        return new Promise(async function (resolve, reject) {
            try {
              var currentSubs = binanceApi ? binanceApi.futuresSubscriptions() : [];
              var ixx = 0;
              if (Object.keys(currentSubs).length > 0) {
                for (var k in currentSubs) {
                  ixx++;
                //   log(k, 'stopping...', ixx, k);
                  binanceApi && await binanceApi.futuresTerminate(k);
                }
                binanceApi && await binanceApi.futuresTerminate('!miniTicker@arr');
              }
              resolve(true)
            } catch (e) {
                isLocal && log('socket_stopSubscriptions error', e)
              reject(false)
            }
        })
    },
}
const generateKey = exports.generateKey = ({inclTime = false}) => {
    let randStr = (+new Date * Math.random()).toString(36).substring(0,6);
    let resp = inclTime ? Date.now().toString() +  '-' + randStr : randStr;
    return resp
};
const klines = exports.klines = {
    getTasks: async ({ redisClient }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let tasksArr = []
                let nodesArr = [];
                let nodes = await redisClient.keys(redixPrefix.node + '*');
                if (Array.isArray(nodes)) {
                    for (n of nodes) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        nodesArr.push(nodeData.nodeSettings);
                    };
                    nodesArr.map(n => {
                        let taskstg = typeof n.node_tasks !== 'object' ? JSON.parse(n.node_tasks) : n.node_tasks;
                        Array.isArray(taskstg.tasks) && taskstg.tasks.map(async st => {
                            let nextRun = battleNode.calcNextWorkTime({ cronPeriod: st.period })
                            st.taskParams = (typeof st.taskParams !== 'object') ? JSON.parse(st.taskParams) : st.taskParams;
                            st.redisKey = n.redisKey;
                            st.is_active = n.is_active;
                            st.process_id = n.process_id;
                            st.battleID = n.battleID;
                            st.port = n.port;
                            st.pair = n.pair || st.taskTag;
                            st.dtcreatedEn = n.dtcreatedEn;
                            st.id = st.taskID;
                            st.nextRun = nextRun;
                            tasksArr.push(st);
                        })
                    });
                    resolve(tasksArr)
                } else {
                    log('no node !!, getTasks failed!')
                    reject('no node!')
                }

            } catch (e) {
                log('error getTasks', e);
                reject(e)
            }
        });
    },

    getTask: async ({ redisClient, taskID }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let tasksArr = await klines.getTasks({redisClient})
                let task = tasksArr.find(t => t.taskID == taskID);
                if (task) {
                    resolve(task)
                } else {
                    log('getTask failed: taskID', taskID, JSON.stringify(tasksArr))
                    reject('task not found')
                }
            }
            catch(e) {
                log('error getTask', e);
                reject(e)
            }

        });
    },
};
const savetoFile = exports.savetoFile = async (dt, filename, debg = false, props) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
        try {
            props = props || {};
            const basePath = path.resolve(process.cwd(), filename);
            var res = await fs.writeFile(filename, props?.noStr ? dt : JSON.stringify(dt), 'utf8');
            debg && console.log("data is saved.", filename, Date.now() - bop);
            resolve(res)
        } catch (err) {
            log('dosya kadedilemedi.', filename, err)
            reject(err);
        }
    });
}
const readFile = exports.readFile = (filename) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
        try {
            var resp = fss.readFileSync(filename, { encoding: 'utf8', flag: 'r' }); //
            resolve(resp.toString());
        } catch (err) {
            log('readFile error.', filename, err)
            reject(err);
        }
    });
}; 
const getlistoffiles = exports.getlistoffiles = (folder, debugMode = false) => {
    var resp = []
    const basedir = path.resolve(process.cwd());
    const basePath = basedir + '/' + folder 
    return new Promise(async (resolve, reject) => {
        fss.readdir(basePath, function (err, files) {
            if (err) {
                debugMode && console.log('fn: getlistoffiles --- Unable to scan directory: ' + err, basePath);
                reject(err)
                return;
            }
            files.forEach(function (file) {
                resp.push(file)
            });
            resolve(resp);
        });
    });
};
const cleanfiles = exports.cleanfiles = (folder, files, target = "kline_", debuglog = false) => {
    const basedir = path.resolve(process.cwd());
    const basePath = basedir + '/' + folder 
    return new Promise(async (resolve) => {
        if (Array.isArray(files)) {
            for (const f of files) {
                if (f.startsWith(target)) {
                    var file2d = basePath + '/' + f;
                    try {
                        debuglog && console.log('file2d', file2d)
                        fss.unlink(file2d, function (err) {
                            if (err) return console.log(f, err);
                        });
                    } catch (e) {
                        console.error(f, e);
                    } 
                }
            }
            resolve(true)
        } else {
            resolve(true)
        }
    });
  }
const sleep = exports.sleep = (ms = 300) => {
    return new Promise(resolve => setTimeout(resolve, ms));
}//dt, tabl
const promiseTimeout = exports.promiseTimeout = (promise, to = 3500) => {
    // Create a promise that rejects in <ms> milliseconds
    let timeout = new Promise((resolve, reject) => {
        let id = setTimeout(() => {
            clearTimeout(id);
            reject('Timed out in ' + to + 'ms.')
        }, to);
    });
    // Returns a race between our timeout and the passed in promise
    return Promise.race([
        promise,
        timeout,
    ]);
};
const savetoDBJSON = exports.savetoDBJSON = async ({
    db, rawData, tableName, time1Field = null,
}) => {
    return new Promise(async (resolve, reject) => {
        let arr = rawData;
        try {
            let batchid = Date.now();
            let sq_pre = `update ${tableName} set is_deleted = true where 1=1`;
            db.exec(sq_pre);
            !Array.isArray(arr) && log('arr', tableName, arr);
            Array.isArray(arr) && arr.map(a => {
                a.batchid = batchid;
                if (time1Field) {
                    a[time1Field + 'HRF'] = new Date(a[time1Field]).toISOString();
                }
            });
            let sql = Array.isArray(arr) && await generateSQL({ payload: arr, table: tableName })
            var res = Array.isArray(arr) && db.exec(sql);
            resolve(res);
        }
        catch (e) {
            reject(e)
        }
    });
}
const savetoDBJSONArr = exports.savetoDBJSONArr = async ({
    db, rawData, tableName, time1Field = null, time2Field = null, debug = false }) => {
    return new Promise(async (resolve, reject) => {
        try {
            let arr = await json2arr(rawData);
            let batchid = Date.now();
            let sq_pre = `update ${tableName} set is_deleted = true where batchid not in (${batchid})`;
            db.exec(sq_pre);
            Array.isArray(arr) && arr.map(a => {
                a.batchid = batchid;
                delete a.lastUpdateId;
                if (time1Field) {
                    a[time1Field + 'HRF'] = new Date(a[time1Field]).toISOString();
                }
                if (time2Field) {
                    a[time2Field + 'HRF'] = new Date(a[time2Field]).toISOString();
                }
            })
            let sql = await generateSQL({ payload: arr, table: tableName });
            // debug && console.log('sql', sql)
            var res = db.exec(sql);
            resolve(res);
        }
        catch (e) {
            reject(e)
        }
    });
};
const clearOldData = exports.clearOldData = async (props = {}) => {
    const { db, tableName, lastN } = props;
    return new Promise(async (resolve, reject) => {
        try {
            let sql = lastN !== 0 ? `
                delete from ${tableName}
                where batchid < (SELECT * FROM (SELECT DISTINCT batchid from ${tableName} ) asfd ORDER BY batchid DESC LIMIT 1 OFFSET ${lastN})
            ` : `
                delete from ${tableName}
                `;
            var res = db.exec(sql);
            resolve(res);
        }
        catch (e) {
            reject(e)
        }
    });
}
const logTransaction = exports.logTransaction = async props => {
    const { db } = props;
    return new Promise(async (resolve, reject) => {
        let table = 'log_binance_fetches';
        let fields = [
            'fnservice', 'is_success', 'status_desc',
            'dtstarted', 'dtcompleted', 'task_timer'
        ];
        let fieldValues = [
            "'" + props.service.toString() + "'", props.isSuccess, "'" + props.status_desc.toString() + "'",
            "'" + new Date(props.dtstarted).toISOString() + "'", "'" + new Date(props.dtcompleted).toISOString() + "'", props.task_timer
        ]

        try {
            let sql = `
                    INSERT INTO ${table} (${fields.toString()}) 
                    VALUES (${fieldValues.toString()})`;
            var res = db.exec(sql);
            resolve(res);
        }
        catch (e) {
            reject(e)
        }
    });
}
const generateSQL = async ({ payload, table }) => {
    // 
    let ssql = '';
    let arr = payload;
    return new Promise(async (resolve, reject) => {
        for (a of arr) {
            let pl = await getJSONFields(a);
            ssql = ssql + 'INSERT INTO ' + table + ' (' + pl.fields.toString() + ') VALUES (' + pl.fieldValues.toString() + ');\n\n'
        }
        resolve(ssql)
    });
};
const dailyWsData2MarketData = payload => {
    // 
    let resp = [];
    let arr = payload;
    arr.map(a => {
        let ix = {
            symbol: a.s, 
            priceChange: a.p, 
            priceChangePercent: a.P, 
            weightedAvgPrice: a.w, 
            lastPrice: a.c, 
            lastQty: a.Q, 
            openPrice: a.o, 
            highPrice: a.h, 
            lowPrice: a.l, 
            volume: a.v, 
            quoteVolume: a.q, 
            openTime: a.O, 
            closeTime: a.C, 
            firstId: a.F, 
            lastId: a.L, 
            count: a.n, 
            // batchid: 'ws', 
            // openTimeHRF: new Date(a.O), 
            // closeTimeHRF: new Date(a.C), 
            // is_deleted: 0, 
            // dtcreated, 
            dtupdated: a.E,
            dtcreatedISO: new Date(Date.now()).toISOString(),
            src: 'functions_dailyWsData2MarketData',
        };
        resp.push(ix);
    })
    return resp;
};
const dailyWsCreateUpdateSQL = ({payloadArr, tableName = 'market_futuresDaily', batchID}) => {
    // {
    //     symbol: 'TIAUSDT',
    //     priceChange: '0.7439000',
    //     priceChangePercent: '4.051',
    //     weightedAvgPrice: '18.8049429',
    //     lastPrice: '19.1068000',
    //     lastQty: '2',
    //     openPrice: '18.3629000',
    //     highPrice: '19.3212000',
    //     lowPrice: '18.2374000',
    //     volume: '10446408',
    //     quoteVolume: '196444105.6575000',
    //     openTime: 1708200060000,
    //     closeTime: 1708286469180,
    //     firstId: 195272976,
    //     lastId: 195907387,
    //     count: 634412,
    //     dtupdated: 2024-02-18T20:01:09.183Z
    //   }
    let sq = '';
    try {
        Array.isArray(payloadArr) && payloadArr.map(payload => {
            let ssql = '';
            payload.batchid = batchID;
            payload.openTimeHRF = new Date(payload.openTime).toISOString();
            payload.closeTimeHRF = new Date(payload.closeTime).toISOString();
            payload.dtupdated = new Date(payload.dtupdated).toISOString();
            // delete payload.dtupdated ; //= new Date(payload.dtupdated);
            for (const key in payload) {
                if (payload.hasOwnProperty(key)) {
                    ssql += key.toString() + '=' + (typeof (payload[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]) + ', '
                }
            }
            let ssql2 = `UPDATE  ${tableName} set ${ssql} dtcreated = CURRENT_TIMESTAMP
                where symbol = '${payload['symbol']}';\n `;
        sq += ssql2;
        })
        return(sq)
    }
    catch(s1) {
        log('wsupdate add new line db save error', s1)
        return(false)
    }
    
};
const findJSONElement = exports.findJSONElement = (obj, prop, defval) => {
    if (typeof defval == 'undefined') defval = null;
    prop = prop.split('.');
    for (var i = 0; i < prop.length; i++) {
        if (typeof obj[prop[i]] == 'undefined')
            return defval;
        obj = obj[prop[i]];
    }
    return obj;
};
const getJSONFields = payload => {
    return new Promise(async (resolve, reject) => {
        let resp = {}
        let fields = [];
        let fieldValues = [];
        let tmp = JSON.parse(JSON.stringify(payload))
        try {
            for (const key in tmp) {
                if (tmp.hasOwnProperty(key)) {
                    fields.push(key);
                    fieldValues.push(typeof (tmp[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]);
                }
            }
            resp = { fields, fieldValues };
            resolve(resp);
        }
        catch (e) {
            reject(e);
        }
    });
};
const json2arr = dt => {
    let arr = [];
    return new Promise(async (resolve, reject) => {
        if (dt) {
            try {
                for (const key in dt) {
                    if (dt.hasOwnProperty(key)) {
                        arr.push(dt[key]);
                        //   console.log(`${key} : ${JSON.stringify(dt[key])}`)
                    }
                }
                resolve(arr)
            }
            catch (e) {
                reject(e)
            }
        } else {
            reject('no value')
        }
    });
};
const getActiveNodePorts = exports.getActiveNodePorts = (ms = 300) => {
    return new Promise((resolve, reject) => {
        let arr = []
        try {
            //killall node
            exec('lsof -c node -a -i', (err, stdout, stderr) => { //ps aux | grep node
                if (err) {
                    // node couldn't execute the command
                    return;
                }
                var lines = stdout.toString().split('\n');
                Array.isArray(lines) && lines.map((l, i) => {
                    let liarr = l.split('    ');
                    let porttStg = Array.isArray(liarr) && liarr[1] && liarr[1].length !== 0 && liarr[1].split(' ');
                    porttStg && console.log('l', i, porttStg[0])
                    porttStg && arr.push(porttStg[0]);
                })
                let portList = [...new Set(arr)];
                // the *entire* stdout and stderr (buffered)
                console.log(`stdout: ${stdout}`);
                // console.log(`stderr: ${stderr}`);
                resolve(portList)
            });
        } catch (e) {
            reject(e)
        }
    });
};
const nodes = exports.nodes = {
    getBattle: async ({ db, battleID }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let qq = `  
                    SELECT *
                    FROM battle_init
                    Where is_deleted = 0 and is_active = 1 and battleID = '${battleID}';
                    `;
                let task = await db.prepare(qq).all();
                if (task && Array.isArray(task) && task.length !== 0) {
                    resolve(task[0]);
                } else {
                    reject('error: no record')
                }
            }
            catch (e) {
                console.log('error: nodes getBattle', e)
                reject(e)
            }
        });
    },
    sqlite_getNode: async ({ db, taskID }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let qq = `  
                    SELECT *
                    FROM battle_nodes
                    Where is_deleted = 0 and is_active = 1 and node_key = '${taskID}';
                    `;
                let task = await db.prepare(qq).all();
                if (task && Array.isArray(task) && task.length !== 0) {
                    resolve(task[0]);
                } else {
                    reject('error: no record')
                }
            }
            catch (e) {
                console.log('error: getNode', e)
                reject(e)
            }
        });
    },
    killNode: async ({ db, process_id, fail = false, node_key }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let qq = `  
                    UPDATE battle_nodes
                    SET is_active = false, is_deleted = true, dtupdated = CURRENT_TIMESTAMP
                    where process_id = '${process_id}'`;
                db.exec(qq)

                if (node_key) {
                    let qq = `  
                        UPDATE battle_node_tasks
                        SET is_active = false, is_deleted = true, is_wip = false, dtupdated = CURRENT_TIMESTAMP
                        where nodeKey = '${node_key}'`;
                    db.exec(qq)
                }
                process.exit(fail ? 5 : 0)
            }
            catch (e) {
                console.log('error: getNode', e)
                process.exit(5)
            }
        });
    },
    generateKey: ({inclTime = false}) => {
        let randStr = (+new Date * Math.random()).toString(36).substring(0,6);
        let resp = inclTime ? Date.now().toString() +  '-' + randStr : randStr;
        return resp
    },
};
const cud = (exports.cud = {
    insertOne: ({ db, table, payload, setid = false }) => {
        return new Promise(async (resolve, reject) => {
            let sqlq = '';
            try {
                let fields = [];
                let fieldValues = [];
                for (const key in payload) {
                    if (payload.hasOwnProperty(key)) {
                        fields.push(key);
                        fieldValues.push(typeof (payload[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]);
                    }
                }
                if (setid) {
                    id = uuidv4();
                    fields.push(setid);
                    fieldValues.push("'" + id + "'");
                }
                sqlq += ' INSERT INTO ' + table + ' (' + fields.toString() + ')';
                sqlq += ' VALUES( ' + fieldValues.toString() + ' );'
                var res = db.prepare(sqlq).run();
                resolve(res)
            } catch (e) {
                console.log('cud insertOne e', e, sqlq)
                reject(e)
            }
        });
    }
});
const nBinance = exports.nBinance = {
    futuresDaily: async ({ binanceApi, connTimeOut, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            const connTimeOutV = connTimeOut || 10000;
            try {
                rawData =  await promiseTimeout(binanceApi.futuresDaily(), connTimeOutV);
                resolve(rawData);
            }
            catch (e) {
                reject(e)
            }
        });
    },
    futuresFundingRate: async ({ binanceApi, connTimeOut, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            const connTimeOutV = connTimeOut || 10000;
            try {
                rawData =  await promiseTimeout(binanceApi.futuresFundingRate(), connTimeOutV);
                resolve(rawData);
            }
            catch (e) {
                reject(e)
            }
        });
    },
    futuresMarkPrice: async ({ binanceApi, connTimeOut, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            const connTimeOutV = connTimeOut || 10000;
            try {
                rawData =  await promiseTimeout(binanceApi.futuresMarkPrice(), connTimeOutV);
                resolve(rawData);
            }
            catch (e) {
                reject(e)
            }
        });
    },
    tick2json: (adata, live = false, cbType = 'json') => {
        if (!live) {
            if (Array.isArray(adata)) {
                var time = adata[0];
                var timeTR = new Date(adata[0]).toLocaleString();
                var timeEN = new Date(adata[0]);
                var open = Number(adata[1])
                var high = Number(adata[2])
                var low = Number(adata[3])
                var close = Number(adata[4])
                var volume = Number(adata[5])
                var closeTime = new Date(adata[6])
                var assetVolume = Number(adata[7])
                var trades = Number(adata[8])
                var color = close - open >= 0 ? 'blue' : 'red';
          
                return {
                    time, timeTR, timeEN,
                    open, high, low, close, volume,
                    closeTime, assetVolume, trades, color
                }
            }
        } else {
                //https://github.com/binance/binance-spot-api-docs/blob/master/web-socket-streams.md#klinecandlestick-streams-for-utc
                var time = adata.t;
                var timeTR = new Date(adata.t).toLocaleString();
                var timeEN = new Date(adata.t);
                var open = Number(adata.o)
                var high = Number(adata.h)
                var low = Number(adata.l)
                var close = Number(adata.c)
                var volume = Number(adata.v)
                var closeTime = new Date(adata.T)
                var closeTimeEn = new Date(adata.T)
                var closeTimeSys = adata.T
                var assetVolume = Number(adata.q)
                var trades = Number(adata.n)

                var takerVolume = Number(adata.V);
                var takerAssetVolume = Number(adata.Q);
                var barClosed = adata.x;


                var color = close - open >= 0 ? 'blue' : 'red';
                if (cbType == 'json') {
                    return {
                        time, timeTR, timeEN,
                        open, high, low, close, volume,
                        closeTime, assetVolume, trades, color, closeTimeSys, barClosed
                    }
                } else {
                    let arr = [time, open, high, low, close, volume, closeTimeSys, 
                        assetVolume, trades, takerVolume, takerAssetVolume, "1" ];
                    return arr;
                }
        }
  },
};
const chunkify = exports.chunkify = (a, n, balanced) => {
    //array splitter...
    if (n < 2)
      return [a];
  
    var len = a.length,
      out = [],
      i = 0,
      size;
  
    if (len % n === 0) {
      size = Math.floor(len / n);
      while (i < len) {
        out.push(a.slice(i, i += size));
      }
    }
  
    else if (balanced) {
      while (i < len) {
        size = Math.ceil((len - i) / n--);
        out.push(a.slice(i, i += size));
      }
    }
  
    else {
  
      n--;
      size = Math.floor(len / n);
      if (len % size === 0)
        size--;
      while (i < size * n) {
        out.push(a.slice(i, i += size));
      }
      out.push(a.slice(size * n));
  
    }
  
    return out;
};
const redisLog = exports.redisLog = props => {
    var { redisClient, param, value, action = 'get' } = props;
    return new Promise(async (resolve, reject) => {
        try {
            let rediskey = redixPrefix.dex.dataLogs + '_fnLogs';
            let currValStg = await redisClient.get(rediskey);
            let xcurrVal = currValStg ? JSON.parse(currValStg) : {};
            let currValS = xcurrVal ? xcurrVal[param] : {};
            currValS = currValS || {};

            if (action == 'get') {
                resolve(currValS)
            } else if (action == 'set') {
                currValS.dtUpdated = Date.now();
                currValS.dtupdatedISO = new Date(Date.now()).toISOString();
                if (value) {
                    currValS.value = value;
                }
                xcurrVal[param] = currValS;
                await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                resolve(true);
            } else {
                resolve(currValS)
            }
        } catch (e) {
            log('save log error', e);
            reject(false);
        }
    });
}
const logX = exports.logX = props => {
    var { redisClient, param = 'generic', folder = 'symbols:', 
        value, lastN = 1250 
    } = props;

    redisClient = redisClient || new Redis({
        host: '127.0.0.1',
        port: 6379,
    });

    return new Promise(async (resolve, reject) => {
        try {
            let rediskey = redixPrefix.dex.dataLogs + folder + param.toUpperCase();
            let currValStg = await redisClient.get(rediskey);
            let xcurrVal = currValStg && typeof currValStg !== "object" ? JSON.parse(currValStg) : [];
            xcurrVal.push({
                lID: generateKey({inclTime: false}),
                lTime: Date.now(),
                lTimeISO:new Date(Date.now()).toISOString(),
                value,
            });
            if (lastN) {
                xcurrVal = xcurrVal.slice((-1) * lastN);
            }
            await redisClient.set(rediskey, JSON.stringify(xcurrVal));
            resolve(true);
        } catch (e) {
            log('logX error', e);
            resolve(false);
        }
    });
}
const timerFunction = exports.timerFunction = (props, ...args) => {
    var { 
        redisClient, fn, fnName, fnNote, logMe = false, refID, ref,
        frequency = 60000 * 5, counter = 2000, saveResp2Return = false
    } = props;
    return new Promise(async (resolve, reject) => {
        try {
            redisClient = redisClient || new Redis({
                host: '127.0.0.1',
                port: 6379,
            });
            let rediskey = redixPrefix.dex.dataLogs + '_fnTimedFunctions';
            let nVal = {}
            let currValStg = await redisClient.get(rediskey);
            let xcurrVal = currValStg ? JSON.parse(currValStg) : {};
            let iData = xcurrVal ? xcurrVal[fnName] : false;
            let rule1 = iData && iData.dtUpdated && ((Date.now() - iData?.dtUpdated) > frequency);
            let rule2 = iData && iData.sayac && (iData.sayac > counter);
            let rule0 = !iData;

            let fResp;
            if ([rule0, rule1, rule2].some(r => r == true)) {
                //run fn...
                // log('running', fnNote);
                //..if in blacklist remove. -> sayac ı sıfırla ve 
                let resp = await fn(...args);
                nVal = {
                    sayac: 1,
                    dtUpdated: Date.now(),
                    dtUpdatedISO: new Date(Date.now()),
                    resp: saveResp2Return ? resp : false,
                };
                // log('running ', fnName,  refID, ref, 'resp', Array.isArray(resp) && resp[0]);
                xcurrVal[fnName] = nVal;
                await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                // resolve(resp)
                fResp = resp;
            } else {
                let ySayac = iData?.sayac ? iData?.sayac + 1 : 1;
                nVal = {
                    ...iData,
                    sayac: ySayac,
                };
                xcurrVal[fnName] = nVal;
                // log('running ', fnName, refID, 'failed', ref, iData, rule1, rule2, rule0);
                await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                // resolve(iData ? iData?.resp : false);
                fResp = iData ? iData?.resp : false;
            }
            resolve(fResp);
        } catch (e) {
            log('timerFunction error', e);
            reject(false);
        }
    });
};
const redisKeyGenerate = exports.redisKeyGenerate = values => {
    let base = '';
    // let {values} = props;
    let ix = 0;
    for (const v of values) {
        ix++;
        base += v.toString();
        base += ix < values.length ? '_' : '';
    } 
    return base;
}

const redisKeyGenerate2 = exports.redisKeyGenerate2 = props => {
    let base = '';
    let bRoot = props.bRoot;
    let bFunction = props.bFunction;
    let bHeader = props.bHeader;
    base += bRoot + bFunction + bHeader;
    return base;
}