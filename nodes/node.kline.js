const Redis = require('ioredis');
const Database = require('better-sqlite3');
const cronParser = require('cron-parser');
const path = require('path');
const fnx = require('./_.functions');
const fnxDb = require('./_.functions.db');
// const nBinance = require('./_.functions.binance')
const fnxIdx = require('./_.functions.indicators');
const fnxDex = require('./_.functions.dex.js');
const redixPrefix = require('../src/lib/redis.prefix.js');
// const process= require('process');
const process = require('node:process');
process.removeAllListeners('warning');
const Binance = require('node-binance-api');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const args = process.argv.slice(2);
const modeDebug = false;

var redisClient = new Redis({
    host: '127.0.0.1',
    port: 6379,
});

process.on('exit', (code) => {
//   console.log(`About to exit with code: ${code}`);
});

const getTaskDone = async props => {
    const refID = Date.now();
    var redisClient = props.redisClient || new Redis({
        host: '127.0.0.1',
        port: 6379,
    });
    const { taskDesc, battleID, nodeKey, taskTag, batch, nodeInterval, battleInterval = '',
        taskType, period, periodDesc, taskParams, redisKey, pair, nextRun, dtcreatedEn, battleData, ...rest } = props;

    return new Promise(async (resolve, reject) => {
        const taskTagLw = taskTag.toLowerCase();
        let symbolU = taskParams?.symbol.toUpperCase();
        let rawData;
        try {
            let fetchInitials = { symbol: pair, interval: battleData?.nodeInterval.toString(), limit: battleData?.candleCounts }
            rawData = await fnx.battleNode.fetchKline({ binanceApi, ...fetchInitials });
            let redisDataKey = redixPrefix.dataKline + taskTag.toLowerCase();
            await redisClient.set(redisDataKey, JSON.stringify(rawData));
            let lastKlineValue = [...rawData].slice(-1)[0][0];
            try {
                let sStatsValue = {
                    dtupdatedEn: new Date(Date.now()).toISOString(),
                    dataOpen: lastKlineValue,
                    dataOpenEn: new Date(lastKlineValue).toISOString(),
                };
                await redisClient.set(redixPrefix.monitorKline + taskTagLw, JSON.stringify(sStatsValue));
                // fnx.log(redisKey, 'data fetched and saved.', lastKlineValue);
            }
            catch (eSt) {
                modeDebug && fnx.log('stat save error', symbol);
            }

            const lastCandle = rawData && fnx.nBinance.tick2json([...rawData].slice(-1)[0], false)
            var { trading = {}, battleType = {}, pairs } = battleData;
            var { entry, exit, wallet } = trading;
            // Array.isArray(successRulesets) && successRulesets.length !== 0 && console.log('battleParamsz', symbol, battleData);
            var isExchange = battleType && battleType.dex == 'exchange';
            // fnx.log('isExchange', symbol, 'closedBar', isExchange);
            var { config: dexConfig = {} } = battleType;
            const { testDex, dexCode, dexTitle, apiKey, apiSecret } = dexConfig;
            let globalDexApi = await fnxDex.dex.getDexApi({redisClient});

            // let xsmi2 = taskParams && taskParams.keyTag.toLowerCase()
            let xsmi2 = taskTagLw // symbol.toLowerCase() + '_' + battleInterval.toString();

            let updateExchX = isExchange ? Array.isArray(pairs) && pairs[0] == pair.toUpperCase() : false;
            let trds = await fnxIdx.fnTrades.updateOpenTradesPnls({
                redisClient, symbol: symbol || taskParams.symbol, 
                xsmi: xsmi2, src:'node.kline',
                lastCandle, ref: 'closedBar', dexApi: globalDexApi,
                updateDexPostData: updateExchX, clearUnrlvReduceOrders: updateExchX,
            });

            // console.log('trds', trds, xsmi, xsmi2, symbol || taskParams.symbol)
            if (trds && Array.isArray(trds) && trds.length !== 0) {

                let actedSLTP = await fnxIdx.fnTrades.sLossTProfitCheck({
                    redisClient,
                    lastCandle, symbol: symbol || taskParams.symbol,
                    xsmi: xsmi2,
                    wallet, exit, ref: 'closedBar',
                    battleType, isExchange, dexApi: globalDexApi,
                });

                // console.log('actedSLTP', symbol || taskParams.symbol, lastCandle, actedSLTP);

            }

            //calculate indicators..
            let indicatorCalculations;
            try {
                indicatorCalculations = await fnxIdx.indicators.execCalculations({
                    ...props,
                    rawData,
                    klineRef: 'closedBar',
                });
                let redisIndKey = redixPrefix.dataIndicators + taskTagLw;
                await redisClient.set(redisIndKey, JSON.stringify(indicatorCalculations));
                // console.log('indicatorCalculations', indicatorCalculations.length)
            } catch (eIdx) {
                fnx.log('1108 / fnGetTaskDone indicatorCalculations fnxIdx.main.start',)
            }
            if (indicatorCalculations) {
                try {
                    let sKey = redixPrefix.monitorIndicators + taskTagLw;
                    let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                    await redisClient.set(sKey, JSON.stringify(sStatsValue));
                }
                catch (eSt) {
                    modeDebug && fnx.log('stat save error', taskTagLw);
                }
            }

            //calculate strategies....
            if (indicatorCalculations && rawData && Array.isArray(rawData)) {
                try {
                    const strategyRulesetParams = await fnxIdx.strategies.getStrategyRulesetParams({})
                    // fnx.log('lastCandle1', lastCandle, lastKlineValue)
                    let strategyRulesetCalculations;
                    if (battleInterval == nodeInterval) {
                        var xsmi = taskParams && taskParams.keyTag.toLowerCase();
                        var symbol = taskParams.symbol;
                        try {
                            strategyRulesetCalculations = await fnxIdx.strategies.calculate({
                                redisClient,
                                params: {
                                    symbol, xsmi, battleInterval,
                                    battle_params: battleData,
                                    indicatorParams: battleData.indicatorsWParams,
                                    indicatorCalculations,
                                    strategyRulesets: battleData.rulesets, // strategyRulesets,
                                    strategyRulesetParams: strategyRulesetParams,
                                    lastCandle: lastCandle,
                                }
                            });
                            // fnx.log('strategyRulesetCalculations', JSON.stringify(strategyRulesetCalculations), battleInterval, nodeInterval)
                            if (strategyRulesetCalculations) {
                                let redisIndKey = redixPrefix.dataStrategies + taskTagLw;
                                await redisClient.set(redisIndKey, JSON.stringify(strategyRulesetCalculations));
                                // console.log('strategyRulesetCalculations', strategyRulesetCalculations.length)
                            } else {
                                fnx.log('1055 / fnGetTaskDone strategyRulesetCalculations failed',)
                            }
                        } catch (eIdx) {
                            fnx.log('1054 / fnGetTaskDone strategyRulesetCalculations failed',)
                        }

                        try {
                            let successRulesets = Array.isArray(strategyRulesetCalculations) && [...strategyRulesetCalculations].filter(rs => rs.result == true);

                            var entryCheck = await fnxIdx.fnTrades.entryCheck({
                                redisClient, symbol, xsmi, wallet, exit,
                                lastCandle, battleInterval,
                                newBar: true, entryParams: entry, ref: 'closedBar',
                                strategyRulesetSuccess: successRulesets,
                                debugMode: false, battleType, isExchange, 
                                dexApi: globalDexApi,
                            }); 

                            let battlestarted = await redisClient.get('appVars:battlestarted');
                            // fnx.log('entryCheck battlestarted', symbolU, battlestarted);

                            if (entryCheck?.result && parseFloat(battlestarted) == 1) {
                                await fnx.logX({
                                    redisClient,
                                    param: symbolU,
                                    value: {
                                        section: 'nodeKline:getTaskDone:entryCheck:Success',
                                        note: 'entry Check - Conditions Succceed',
                                        symbol: symbolU,
                                        refValues: {
                                            ref: 'closedBar',
                                            entryCheck,
                                        }
                                    }
                                });
                                await fnxIdx.fnTrades.entry({
                                    ...props,
                                    redisClient, symbol, xsmi, wallet, exit,
                                    lastCandle, battleInterval, debugMode: false, newBar: true, 
                                    entryParams: entry, ref: 'closedBar',
                                    act: entryCheck.act, entryCheck: entryCheck.conditions,
                                    strategyRulesetSuccess: successRulesets, 
                                    battleType, isExchange, dexApi: globalDexApi,
                                });
                                // fnx.log('enter position done!', xsmi) //, entryCheck
                            } else {
                                // fnx.log('entryCheck failed!', xsmi, JSON.stringify(entryCheck))
                                entryCheck?.stateOfCreateOrder && await fnx.logX({
                                    redisClient,
                                    param: symbolU,
                                    value: {
                                        section: 'nodeKline:getTaskDone:entryCheck',
                                        note: 'entry Check - Conditions Failed - closedBar',
                                        symbol: symbolU,
                                        refValues: {
                                            ref: 'closedBar',
                                            entryCheck,
                                        }
                                    }
                                });
                            }

                        } catch (eT) {
                            fnx.log('checktrader trader is not working...', eT)
                        };
                    } else { 
                        //other time frame process . no need for strategy actions!
                    }
                }
                catch (eSt) {
                    fnx.log('error in strat calc', eSt);
                }
                resolve(true) //rawData
            } else {
                resolve(false)
            }
            resolve(true);
        } catch (e) {
            !e.toString().includes("ESOCKETTIMEDOUT") && fnx.log('getTaskDone Failed', e)
            reject(e.toString().includes("ESOCKETTIMEDOUT") ? taskTag + ' - task ESOCKETTIMEDOUT' : taskTag + ' - task failed')
        }

    });
};

(async () => {
    const dtBOP = Date.now();
    const killConns = async (signal) => {
        console.log(`'nodekline killConns Got ${signal} signal.'`);
        redisClient && redisClient.quit();
        process.exit(0)
    };
    //`exit`, 
    //https://stackoverflow.com/questions/14031763/doing-a-cleanup-action-just-before-node-js-exits
    [`SIGINT`, `SIGUSR1`, `SIGUSR2`, `uncaughtException`, `SIGTERM`].forEach((eventType) => {
        process.on(eventType, killConns.bind(eventType));
    });

    const taskID = Array.isArray(args) && args.length !== 0 ? args[0] : 'binance.x';
    let task = {}
    let battleData = {};
    task.taskID = taskID;

    try {
        task = await fnx.klines.getTask({redisClient, taskID});
        const {
            taskDesc, battleID, nodeKey, taskTag, batch, nodeInterval, battleInterval,
            taskType, period, periodDesc, taskParams, redisKey, pair, nextRun, dtcreatedEn,
        } = task;

        try {
            battleData = await fnx.battleNode.getBattleData({
                cronParser, redisClient, redisKey
            });
        } catch(e) {
            fnx.log('getBattleData error', e);
        }
        //  fnx.log(redisKey, 'task started!:' );
        //  fnx.log('battleData', JSON.stringify(battleData));
        //  fnx.log('task', JSON.stringify(task));

        try {
            await getTaskDone({
                redisClient,
                ...task,
                battleData,
            });
        } catch (e) {
            fnx.log('getTaskDone error', e);
        }
        // fnx.log('exit', taskID);
        process.exit(0);
    }
    catch (e) {
        let dtElapsed = Date.now() - dtBOP 
        fnx.log('1124 / node.kline.js get Battle Data.. ', taskID, e);
        process.exit(0);
    } 
})();

/*
 task {"taskDesc":"klineLoader","battleID":"1719128818820-brvhdc","nodeKey":"4sznuh","taskID":"4sznuh-1","taskTag":"LPTUSDT_1m","script2Run":"node.kline.js","script2RunUri":"node.kline.js 4sznuh-1","batch":"0","nodeInterval":"1m","battleInterval":"1m","taskType":"main","wsconn":true,"wsconnuri":"lptusdt@kline_1m","period":"1 * / 1 * * * *","periodDesc":"At 1 seconds past the minute","taskParams":{"battleID":"1719128818820-brvhdc","nodeID":"4sznuh","keyTag":"lptusdt_1m","symbol":"lptusdt","interval":"1m","battleInterval":"1m","batch":0,"batchAdd":0,"limit":300,"indicatorsWParams":[{"id":1718913757975,"indicator":"ema","refName":"emaRef_1m","battleParams":{"refName":"emaRef_1m","length":9,"source":"close","timeFrame":"1m"}},{"id":1718913758914,"indicator":"rsi","refName":"rsiRef","battleParams":{"refName":"rsiRef","length":9,"source":"close","timeFrame":"1m","upperBand":70,"middleBand":50,"lowerBand":10,"rsiSMAFastPeriod":8,"rsiSMASlowPeriod":14}},{"id":1718913759687,"indicator":"ichimoku","refName":"ichimokuRef","battleParams":{"refName":"ichimokuRef","length":9,"source":"close","timeFrame":"1m","conversionPeriod":9,"basePeriod":26,"spanPeriod":52,"displacement":26}},{"id":1718913761528,"indicator":"atr","refName":"atr14","battleParams":{"refName":"atr14","length":14,"timeFrame":"1m"}},{"id":1718916837263,"indicator":"supertrend","refName":"supertrend1","battleParams":{"refName":"supertrend1","length":14,"multiplier":3,"timeFrame":"1m"}},{"id":1718916841112,"indicator":"chandelierexit","refName":"chandelierexit","battleParams":{"refName":"chandelierexit","length":14,"multiplier":3,"timeFrame":"1m"}}]},"redisKey":"node:lptusdt_1m","is_active":true,"process_id":21327,"port":55424,"pair":"lptusdt","dtcreatedEn":"2024-06-23T07:46:58.822Z","id":"4sznuh-1","nextRun":{"dt":"2024-06-23T07:48:01.000Z","sec":59}}
*/

/*
battleData: {
  redisKey: 'node:dydxusdt_1m',
  symi: 'dydxusdt_1m',
  battleID: '1718889289458-2pjprz',
  period: '3 * /1 * * * *',
  batch: '2',
  nodeInterval: '1m',
  nodeTasks: { tasks: [ [Object] ] },
  pairs: [ 'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'LPTUSDT', 'DYDXUSDT' ],
  candleCounts: 300,
  intervals: [ '1m', '5m', '15m' ],
  battleInterval: [ '1m' ],
  indicatorsWParams: [],
  rulesets: [],
  trading: {
    entry: {
      enterPositon: true,
      actOnBarClose: true,
      direction: 'both',
      positionBudget: 100,
      positionMaxBudget: 400,
      addAdditionPosition: true,
      additionPositionPercentage: 0.3,
      additionPosRefPriceIsAvg: true,
      additionPositionCandleInterval: 3
    },
    exit: {
      takeProfitRatio: 0.7,
      stopLoss: 50,
      stopLossUsePercentage: true,
      stopLossPercentage: 2,
      useTrailingStop: true,
      actOnBarClose: true
    },
    wallet: {
      usePacalMode: true,
      walletBudget: 400,
      walletLeverage: 20,
      pacalTakeProfitUSD: 100,
      pacalStopLossUSD: 400,
      pacalMaxPairs: 55
    }
  },
  socketUse: true,
  socketRefreshFrequency: 10000,
  timeoutSource: 1500,
  limit: 300,
  nextRun: { dt: 2024-06-20T14:13:03.000Z, sec: 59 }
}
*/