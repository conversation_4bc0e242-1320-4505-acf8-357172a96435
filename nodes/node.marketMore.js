const Redis = require('ioredis');
const Database = require('better-sqlite3');
const cronParser = require('cron-parser');
const path = require('path');
const fnx = require('./_.functions');
const fnxDb = require('./_.functions.db');
// const nBinance = require('./_.functions.binance')
const fnxIdx = require('./_.functions.indicators');
const redixPrefix = require('../src/lib/redis.prefix.js');
// const process= require('process');
const process = require('node:process');
process.removeAllListeners('warning');
const Binance = require('node-binance-api');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const args = process.argv.slice(2);
const modeDebug = false;

const redisClient = new Redis({
    host: '127.0.0.1',
    port: 6379,
});

process.on('exit', (code) => {
//   console.log(`About to exit with code: ${code}`);
});

const fnGetTaskDone = {
    fetchPrices: async ({ connTimeOut = 10000, binanceApi, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            try {
                rawData = await fnx.nBinance.futuresDaily({ binanceApi, connTimeOut });
                // rawData = await await fnx.promiseTimeout(binanceApi.futuresDaily(), connTimeOut);
                resolve(rawData);
            }
            catch (e) {
                reject(e)
            }
        });
    },

    futuresFundingRate: async ({ connTimeOut = 10000, binanceApi, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            try {
                rawData = await fnx.nBinance.futuresFundingRate({ binanceApi, connTimeOut });
                resolve(rawData);
            }
            catch (e) {
                fnx.log('futuresFundingRate', e)
                reject(e)
            }
        });
    },
    futuresMarkPrice: async ({ connTimeOut = 10000, binanceApi, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            try {
                rawData = await fnx.nBinance.futuresMarkPrice({ binanceApi, connTimeOut });
                resolve(rawData);
            }
            catch (e) {
                fnx.log('futuresMarkPrice', e)
                reject(e)
            }
        });
    },
    
}
const getTaskDone = async props => {
    const refID = Date.now();
    const { redisClient, taskDesc, battleID, nodeKey, taskTag, batch, nodeInterval, battleInterval = '',
        taskType, period, periodDesc, taskParams, redisKey, pair, nextRun, dtcreatedEn, battleData, ...rest } = props;
    
        return new Promise(async (resolve, reject) => {
            const taskTagLw = taskTag.toLowerCase();
            let rawData; 
            let redisDataKey;
        try {  
            redisDataKey = redixPrefix.dataMarket + taskTag.split('_')[1].toLowerCase()
            if (taskTag == 'mt_fundingRate') {
                rawData = await fnGetTaskDone.futuresFundingRate({binanceApi,}) 
            } else if (taskTag == 'mt_markPrice') {
                rawData = await fnGetTaskDone.futuresMarkPrice({binanceApi,}) 
            }
            await redisClient.set(redisDataKey, JSON.stringify(rawData));
            try {
                let sKey = redixPrefix.monitorMarket + taskTag.split('_')[1].toLowerCase();
                let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                await redisClient.set(sKey, JSON.stringify(sStatsValue));
            }
            catch (eSt) {
                modeDebug && fnx.log('stat save error', taskTag.split('_')[1].toLowerCase());
            }

            resolve(rawData)
            
        }
        catch(ej) {
            fnx.log(`market ticker error: getTaskDone`, taskTag, ej)
            reject(ej);
        }
    });
}

(async () => {
    const dtBOP = Date.now();
    const killConns = async (signal) => {
        console.log(`'market more killConns Got ${signal} signal.'`);
        redisClient && redisClient.quit();
        process.exit(0)
    };
    //`exit`, 
    //https://stackoverflow.com/questions/14031763/doing-a-cleanup-action-just-before-node-js-exits
    [`SIGINT`, `SIGUSR1`, `SIGUSR2`, `uncaughtException`, `SIGTERM`].forEach((eventType) => {
        process.on(eventType, killConns.bind(eventType));
    });

    const taskID = Array.isArray(args) && args.length !== 0 ? args[0] : 'binance.x';
    let task = {}
    let battleData = {};
    task.taskID = taskID;

    try {
        task = await fnx.klines.getTask({redisClient, taskID});
        const {
            taskDesc, battleID, nodeKey, taskTag, batch, nodeInterval, battleInterval,
            taskType, period, periodDesc, taskParams, redisKey, pair, nextRun, dtcreatedEn,
        } = task;

        battleData = await fnx.battleNode.getBattleData({
            cronParser, redisClient, redisKey
        });
        // fnx.log(redisKey, 'task started!:' );
        await getTaskDone({ 
            redisClient,
            ...task,
            battleData,
        });
        // fnx.log('exit', taskID);
        process.exit(0);
    }
    catch (e) {
        let dtElapsed = Date.now() - dtBOP 
        fnx.log('1124 / node.marketmore.js get Battle Data.. ', e);
        process.exit(0);
    } 
})();

