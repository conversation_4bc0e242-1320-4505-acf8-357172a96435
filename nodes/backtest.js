const Redis = require('ioredis');
const { Worker, workerData, MessageChannel, receiveMessageOnPort } = require('worker_threads')
const fnx = require('./_.functions');
const fnxIdx = require('./_.functions.indicators');
const redixPrefix = require('../src/lib/redis.prefix.js');

const os = require('os');
// Create a new worker
const commissionRate = 0.0007;
(async () => {
    const redisClient = new Redis({
        host: '127.0.0.1',
        port: 6379,
    });
    let dtBop = Date.now();

    // Printing os.cpus()
    let no_of_logical_coreX;
    try {
        let cpu_s = os.cpus();
        let no_of_logical_core = 0;
        cpu_s.forEach(element => {
            no_of_logical_core++;
        });
        no_of_logical_coreX = no_of_logical_core
        // console.log("total number of logical core is "  + no_of_logical_core);
    } catch (eC) { }
    var coreCPUs = no_of_logical_coreX || 10;
    console.log("total number of logical core is " + coreCPUs);

    let backtestDataStg = await redisClient.get('backtestData');
    let backtestData = backtestDataStg && typeof backtestDataStg !== 'object' ? JSON.parse(backtestDataStg) : backtestDataStg;
    let battle_paramsStg = backtestData?.battle_params;
    let battle_params = battle_paramsStg && typeof battle_paramsStg !== 'object' ? JSON.parse(battle_paramsStg) : battle_paramsStg;
    let parameters = battle_params?.parameters;

    let battle_wip = false;
    try {
        battle_wip = backtestData?.is_wip ? backtestData?.is_wip : false;
    } catch (eWIP) {
        console.log('Wip check error', eWIP);
    }

    const {
        pairs,
        candleCounts,
        intervals,
        battleInterval,
        indicatorsWParams,
        rulesets,
        trading,
    } = parameters;

    const args = process.argv.slice(2)?.toString();
    // console.log('args', args);
    if (!battle_wip) {
        let backtestDataStg = {...backtestData};
        backtestDataStg.is_wip = true;
        backtestDataStg.dtUpdated = new Date(Date.now()).toISOString();
        await redisClient.del('backtestData');
        await redisClient.del(redixPrefix.dataIndicators + 'backtest:*');
        await redisClient.set('backtestData', JSON.stringify(backtestDataStg));
        let backtestKey = redixPrefix.dataIndicators + 'backtest:*';
        await redisClient.del(backtestKey);

        let trades = [];
        let keysKline = [];
        pairs.map(p => {
            intervals.map(iv => {
                let str = redixPrefix.dataKline + p.toLowerCase() + '_' + iv.toString();
                keysKline.push(str);
            })
        });
        var addon = args ? args + '/' : '';
        var folderName = addon + 'bin';
        var fileinitial = 'backtest';
        try {
            var files = await fnx.getlistoffiles(folderName);
            await fnx.cleanfiles(folderName, files, fileinitial)
        } catch (e) {
            console.log('error in cleaning previous files: ', e);
            process.exit(1);
        };
        // fnx.log('files', files);
        //TODO: check health of keysKlines.
        //DONE: web gui butons..
        let keysKlineDB = await redisClient.keys(redixPrefix.dataKline + '*');
        if (intervals && Array.isArray(intervals) && intervals.length !== 1) {
            //multi time frame state!,
            keysKline = keysKline.filter(function (str) { return str.includes(battleInterval.toString()); });
        };
        var packs = fnx.chunkify(keysKline, coreCPUs, true);
        let results = []
        const updateResults = async (result) => {
            let act = result?.act;
            if (act) {
                if (act == 'taskCompleted') {
                    process.send('taskCompleted ' + results.length + '/' + packs.length ) 
                    results.push(result);
                    if (results.length == packs.length) {
                        console.log(' ');
                        console.log(' ');
                        fnx.log('### taskCompleted - süre', Date.now() - dtBop);
                        let resp = {
                            backtestData,
                            results,
                        }
                        try {
                            await fnx.savetoFile(resp, './' + folderName + '/backtest.summary.json', true);
                        } catch (e) {
                            console.log('error save', e)
                        }
                        try {
                            let backtestDataStg2 = { ...backtestData };
                            backtestDataStg2.is_wip = true;
                            backtestDataStg2.dtUpdated = new Date(Date.now()).toISOString();
                            await redisClient.del('backtestData');
                            backtestDataStg2.is_wip = false;
                            backtestDataStg2.dtUpdated = new Date(Date.now()).toISOString();
                            await redisClient.set('backtestData', JSON.stringify(backtestDataStg2));
                        } catch (e) {
                            console.log('error statu update!', e)
                        }
                        process.exit(0);
                    }
                } else {
                    fnx.log('msg', result);
                }
            } else {
                fnx.log('no act from worker!')
            }
        }

        let packID = 1;
        let workerFile = './' + addon + 'backtest_worker.js';
        for (const pack of packs) {
            const worker = new Worker(workerFile)
            worker.postMessage({ packID, pack, addon });
            worker.on('message', (result) => {
                // console.log('result', result);
                updateResults(result);
                /* Execution time end */
                // `result` has the converted XML
            });
            packID++;
        };


    } else {
        //WIP ....
        console.log('error: backtest task is in progress... ');
        process.exit(1);
    }
})();
