// app.js
const Database = require('better-sqlite3');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const os = require('os');
const { Worker } = require('worker_threads');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';
async function getTargetPairs() {
  //db/gauss.db
  const pth = path.resolve(__dirname, '../../db/gauss.db');
  // console.log('dbPath', pth);
  const db = new Database(pth);
  const q = `
    SELECT dataName, dataValues, dtCreated, is_deleted
    FROM sandbox_datax
    where dataName = 'pairs'
    order by dtCreated desc
    limit 1;
  `
  const query = db.prepare(q);
  const pairs = query.all();
  let resp = [];
  try {
    resp = JSON.parse(pairs[0]?.dataValues) || [];
    console.log('# of selected pairs: ', resp.length);
  } catch (e) {
    console.log('ex', e, pairs)
    resp = [];
  }
  return resp;
}

async function getParams(dataName = 'params') {
  //db/gauss.db
  const pth = path.resolve(__dirname, '../../db/gauss.db');
  // console.log('dbPath', pth);
  const db = new Database(pth);
  const q = `
    SELECT dataName, dataValues, dtCreated, is_deleted
    FROM sandbox_datax
    where dataName = '${dataName}'
    order by dtCreated desc
    limit 1;
  `
  const query = db.prepare(q);
  const pairs = query.all();
  let resp = [];
  try {
    resp = JSON.parse(pairs[0]?.dataValues) || [];
  } catch (e) {
    console.log('ex', e, pairs)
    resp = [];
  }
  return resp;
}

//FUNDING RATES
async function getHistoricalFundingRates(targetpairs) {
  // Veri çekmek istediğimiz semboller
  const pairs = targetpairs || ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "DOGEUSDT"];

  // CPU çekirdek sayısını bul
  const cpuCount = os.cpus().length;
  console.log(`getHistoricalFundingRates: Sistemdeki CPU çekirdek sayısı: ${cpuCount}`);

  // İşlem parçalarını oluştur
  const chunkSize = Math.ceil(pairs.length / cpuCount);
  const chunks = [];
  for (let i = 0; i < pairs.length; i += chunkSize) {
    chunks.push(pairs.slice(i, i + chunkSize));
  }

  // Worker'ları başlat ve sonuçları topla
  const results = {};

  return new Promise((resolve, reject) => {
    let completedWorkers = 0;

    // Her bir chunk için worker oluştur
    chunks.forEach((chunk, index) => {
      const worker = new Worker(__dirname + '/binance_fundingrates_worker.js');

      worker.on('message', (result) => {
        if (result.success) {
          // Sonuçları birleştir
          Object.assign(results, result.data);
          completedWorkers++;

          // Tüm worker'lar tamamlandıysa işlemi bitir
          if (completedWorkers === chunks.length) {
            resolve(results);
          }
        } else {
          reject(new Error('Worker hatası: ' + result.error));
        }

        // Worker'ı kapat
        worker.terminate();
      });

      worker.on('error', (error) => {
        reject(new Error('Worker başlatma hatası: ' + error.message));
      });

      // Worker'a veri gönder
      worker.postMessage({ pairs: chunk });
    });

    // Eğer hiç pair yoksa hemen sonuç döndür
    if (chunks.length === 0) {
      resolve({});
    }
  });
}
function calculateStats_funding(data) {
  const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
  const stdDev = Math.sqrt(variance);
  return { mean, stdDev };
}
function calculateZScore_funding(value, mean, stdDev) {
  return stdDev === 0 ? 0 : Math.abs((value - mean) / stdDev);
}
async function save2db_funding(history) {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  const tableName = 'sandbox_binance_fundingrates';
  let sessionID = Date.now();
  // Drop table if exists
  const dropQuery = `DROP TABLE IF EXISTS ${tableName}`;
  const createQuery = `
    CREATE TABLE ${tableName} (
      symbol TEXT PRIMARY KEY,
      data TEXT,
      funding_rate_score float,
      reason TEXT,
      --sessionID bigint,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `;
  db.prepare(dropQuery).run();
  db.prepare(createQuery).run();

  const allFundingChanges = Object.values(history).map(rates => {
    if (rates.length < 2) return 0;
    const first = parseFloat(rates[0].fundingRate);
    const last = parseFloat(rates[rates.length - 1].fundingRate);
    return Math.abs(last - first);
  });
  const fundingStats = calculateStats_funding(allFundingChanges);
  for (const symbol in history) {
    let fundingRates = history[symbol];
    // console.log('fundingRates', symbol, fundingRates);
    const fundingChange = fundingRates.length >= 2
      ? Math.abs(parseFloat(fundingRates[fundingRates.length - 1].fundingRate) - parseFloat(fundingRates[0].fundingRate))
      : 0;
    const fundingZ = calculateZScore_funding(fundingChange, fundingStats.mean, fundingStats.stdDev);
    const reasons = [];
    if (fundingZ > 2.0) reasons.push('Anormal fonlama oranı');
    const reasonText = reasons.length > 0 ? reasons.join(' ve ') + ' nedeniyle.' : 'Normal';
    const q = `INSERT OR REPLACE INTO ${tableName} (symbol, data, funding_rate_score, reason) VALUES (?, ?, ?, ?)`;
    const query = db.prepare(q);
    query.run(symbol, JSON.stringify(fundingRates), fundingZ, reasonText);
  }
  db.close();
}
//FUNDING RATES
//FUNDING RATES

//KLINES
//KLINES
async function getKlines(targetpairs, interval = '1h', limit = 24) {
  // Veri çekmek istediğimiz semboller
  const pairs = targetpairs || ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "DOGEUSDT"];

  // CPU çekirdek sayısını bul
  const cpuCount = os.cpus().length;
  console.log(`Sistemdeki CPU çekirdek sayısı: ${cpuCount}`);

  // İşlem parçalarını oluştur
  const chunkSize = Math.ceil(pairs.length / cpuCount);
  const chunks = [];
  for (let i = 0; i < pairs.length; i += chunkSize) {
    chunks.push(pairs.slice(i, i + chunkSize));
  }

  // Worker'ları başlat ve sonuçları topla
  const results = {};

  return new Promise((resolve, reject) => {
    let completedWorkers = 0;

    // Her bir chunk için worker oluştur
    chunks.forEach((chunk, index) => {
      const worker = new Worker(__dirname + '/binance_klines_worker.js');

      worker.on('message', (result) => {
        if (result.success) {
          // Sonuçları birleştir
          Object.assign(results, result.data);
          completedWorkers++;

          // Tüm worker'lar tamamlandıysa işlemi bitir
          if (completedWorkers === chunks.length) {
            resolve(results);
          }
        } else {
          reject(new Error('Worker hatası: ' + result.error));
        }

        // Worker'ı kapat
        worker.terminate();
      });

      worker.on('error', (error) => {
        reject(new Error('Worker başlatma hatası: ' + error.message));
      });

      // Worker'a veri gönder
      worker.postMessage({ pairs: chunk, interval, limit });
    });

    // Eğer hiç pair yoksa hemen sonuç döndür
    if (chunks.length === 0) {
      resolve({});
    }
  });
}
// İstatistik hesapla
function calculateStats_klines(data) {
  const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
  const stdDev = Math.sqrt(variance);
  return { mean, stdDev };
}
function calculateZScore_klines(value, mean, stdDev) {
  return stdDev === 0 ? 0 : Math.abs((value - mean) / stdDev);
}
async function save2db_klines(history, interval) {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  const tableName = 'sandbox_binance_klines_' + interval;
  let sessionID = Date.now();
  // Drop table if exists
  const dropQuery = `DROP TABLE IF EXISTS ${tableName}`;
  const createQuery = `
    CREATE TABLE ${tableName} (
      symbol TEXT PRIMARY KEY,
      data TEXT,
      bar_volatility float,
      price_volatility float,
      volume_anomaly float,
      reason TEXT,
      --sessionID bigint,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `;
  db.prepare(dropQuery).run();
  db.prepare(createQuery).run();
  const allPriceChanges = Object.values(history).map(klines => {
    if (klines.length < 2) return 0;
    // Calculate percentage changes between consecutive bars
    const priceChanges = [];
    for (let i = 1; i < klines.length; i++) {
      const prevClose = parseFloat(klines[i - 1][4]);
      const currentClose = parseFloat(klines[i][4]);
      const change = (currentClose - prevClose) / prevClose;
      priceChanges.push(Math.abs(change));
    }
    // Return average of absolute percentage changes as volatility measure
    return priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
  });

  const allBarVotalities = Object.values(history).map(klines => {
    if (klines.length < 2) return 0;
    const barVotalities = [];
    for (let i = 1; i < klines.length; i++) {
      const barLow = parseFloat(klines[i][3]);
      const barHigh = parseFloat(klines[i][2]);
      const barOpen = parseFloat(klines[i][0]);
      const currentClose = parseFloat(klines[i][4]);
      const barVolatility = ((barOpen - barLow) + (barHigh - barOpen)) / barOpen;
      barVotalities.push(Math.abs(barVolatility));
    }
    return barVotalities.reduce((sum, volatility) => sum + volatility, 0) / barVotalities.length;
  });

  const allVolumes = Object.values(history).map(klines =>
    klines.reduce((sum, k) => sum + parseFloat(k[5]), 0)
  );
  const priceStats = calculateStats_klines(allPriceChanges);
  const volumeStats = calculateStats_klines(allVolumes);
  const barVolatilityStats = calculateStats_klines(allBarVotalities);

  for (const symbol in history) {
    let klines = history[symbol];
    // Improved volatility calculation for individual symbol
    if (klines.length < 2) {
      var priceChange = 0;
      var barVolatility = 0;
    } else {
      // Calculate percentage changes between consecutive bars
      const priceChanges = [];
      const barVotalities = [];
      for (let i = 1; i < klines.length; i++) {
        const prevClose = parseFloat(klines[i - 1][4]);
        const currentClose = parseFloat(klines[i][4]);
        const change = (currentClose - prevClose) / prevClose;
        priceChanges.push(Math.abs(change));

        const barLow = parseFloat(klines[i][3]);
        const barHigh = parseFloat(klines[i][2]);
        const barOpen = parseFloat(klines[i][0]);
        const barVolatility = ((barOpen - barLow) + (barHigh - barOpen)) / barOpen;
        barVotalities.push(Math.abs(barVolatility));
      }
      // Use average of absolute percentage changes as volatility measure
      var priceChange = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
      var barVolatility = barVotalities.reduce((sum, volatility) => sum + volatility, 0) / barVotalities.length;
    }

    const totalVolume = klines.reduce((sum, k) => sum + parseFloat(k[5]), 0);
    const priceZ = calculateZScore_klines(priceChange, priceStats.mean, priceStats.stdDev);
    const barVolatilityZ = calculateZScore_klines(barVolatility, barVolatilityStats.mean, barVolatilityStats.stdDev);
    const volumeZ = calculateZScore_klines(totalVolume, volumeStats.mean, volumeStats.stdDev);
    const reasons = [];
    if (priceZ > 2.0) reasons.push('Yüksek volatilite');
    if (volumeZ > 3.0) reasons.push('Hacim anormalliği');
    if (barVolatilityZ > 2.0) reasons.push('Bar volatilitesi');
    const reasonText = reasons.length > 0 ? reasons.join(' ve ') + ' nedeniyle.' : 'Normal';
    const q = `INSERT OR REPLACE INTO ${tableName} (symbol, data, bar_volatility, price_volatility, volume_anomaly, reason) VALUES (?, ?, ?, ?, ?, ?)`;
    const query = db.prepare(q);
    query.run(symbol, JSON.stringify(klines), barVolatilityZ, priceZ, volumeZ, reasonText);
  }
  db.close();
}
//KLINES
//KLINES

//MARKET
//MARKET
async function getmarketData(targetpairs) {
  const apiUrl = BINANCE_FUTURES_API_URL + "/fapi/v1/ticker/24hr";
  let allFundingInfo = [];
  try {
    const response = await axios.get(apiUrl, {});

    allFundingInfo = response.data;
    // Gelen veriler arasından, istediğimiz sembolleri filtreliyoruz.
    const filteredFundingInfo = allFundingInfo.filter(item =>
      targetpairs.includes(item.symbol)
    );

    // API'den dönen veriyi allFundingInfo dizisine ekle
    allFundingInfo = filteredFundingInfo;

    console.log(`market verileri başarıyla çekildi.`);

  } catch (error) {
    console.error(`Hata oluştu: veri çekilemedi. Hata: ${error.message}`);
  }
  // Fonksiyonun sonunda tüm verileri döndür
  return allFundingInfo;
}

const save2db_market = async (history) => {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  const tableName = 'sandbox_binance_market';
  let sessionID = Date.now();
  // Drop table if exists
  const dropQuery = `DROP TABLE IF EXISTS ${tableName}`;
  const createQuery = `
    CREATE TABLE ${tableName} (
      symbol TEXT PRIMARY KEY,
      priceChange TEXT,
      priceChangePercent TEXT,
      weightedAvgPrice TEXT,
      lastPrice TEXT,
      lastQty TEXT,
      openPrice TEXT,
      highPrice TEXT,
      lowPrice TEXT,
      volume TEXT,
      quoteVolume TEXT,
      openTime INTEGER,
      closeTime INTEGER,
      firstId INTEGER,
      lastId INTEGER,
      count INTEGER,
      market_price_position float,
      market_price_position_score float,
      market_price_change_percent_score float,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `;
  db.prepare(dropQuery).run();
  db.prepare(createQuery).run();

  const allPricePositions = history.map(item => {
    const lastPrice = parseFloat(item.lastPrice);
    const lowPrice = parseFloat(item.lowPrice);
    const highPrice = parseFloat(item.highPrice);
    return (lastPrice - lowPrice) / (highPrice - lowPrice);
  });

  const allPriceChangePercents = history.map(item => {
    const priceChangePercent = parseFloat(item.priceChangePercent);
    return priceChangePercent;
  });

  const pricePositionStats = calculateStats(allPricePositions);
  const priceChangePercentStats = calculateStats(allPriceChangePercents);
  for (const item of history) {
    const pricePosition = (parseFloat(item.lastPrice) - parseFloat(item.lowPrice)) / (parseFloat(item.highPrice) - parseFloat(item.lowPrice));
    const pricePositionZ = calculateZScore(pricePosition, pricePositionStats.mean, pricePositionStats.stdDev);
    const priceChangePercentPositionZ = calculateZScore(parseFloat(item.priceChangePercent), priceChangePercentStats.mean, priceChangePercentStats.stdDev);

    const q = `INSERT OR REPLACE INTO ${tableName} (
      symbol, priceChange, priceChangePercent, weightedAvgPrice, 
      lastPrice, lastQty, openPrice, highPrice, lowPrice, 
      volume, quoteVolume, openTime, closeTime, firstId, lastId, count, market_price_position, market_price_position_score, market_price_change_percent_score
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    const query = db.prepare(q);
    query.run(
      item.symbol,
      item.priceChange,
      item.priceChangePercent,
      item.weightedAvgPrice,
      item.lastPrice,
      item.lastQty,
      item.openPrice,
      item.highPrice,
      item.lowPrice,
      item.volume,
      item.quoteVolume,
      item.openTime,
      item.closeTime,
      item.firstId,
      item.lastId,
      item.count,
      pricePosition,
      pricePositionZ,
      priceChangePercentPositionZ
    );
  }
  db.close();
}

function calculateStats(data) {
  const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
  const stdDev = Math.sqrt(variance);
  return { mean, stdDev };
}
function calculateZScore(value, mean, stdDev) {
  return stdDev === 0 ? 0 : Math.abs((value - mean) / stdDev);
}

const getAnomalyList = async (targetIntervals) => {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);

  // Dynamically build the SELECT clause with interval-specific columns
  let selectColumns = `
      m.symbol,
      m.priceChange,
      m.priceChangePercent,
      m.weightedAvgPrice,
      m.lastPrice,
      m.lastQty,
      m.openPrice,
      m.highPrice,
      m.lowPrice,
      m.volume,
      m.quoteVolume,
      m.openTime,
      m.closeTime,
      m.firstId,
      m.lastId,
      m.count,
      m.created_at, 
      -- Funding Rate verileri
      f.funding_rate_score, 
      -- 24s aralığı içindeki pozisyon yüzdesi
      CASE 
          WHEN (m.highPrice - m.lowPrice) != 0 THEN 
              ROUND((m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) * 100, 2)
          ELSE 0.5 -- Hareketsizse (eşit), ortada kabul et
      END AS range_position_percent, 
      -- Fiyat yayılımı yüzdesi: (high - low) / lastPrice * 100
      ROUND((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice, 2) AS price_spread_percent, 
      ROUND((m.highPrice - m.lowPrice) * 100.0 / m.openPrice, 2) AS openPrice_spread_percent
      -- Fiyat değişim skoru: |priceChangePercent| / 5
      -- ROUND(ABS(m.priceChangePercent) * 1.0 / 5.0, 2) AS priceChangeScore, 
      -- Yayılım volatilite skoru: priceSpreadPercent / 3
      -- ROUND((m.highPrice - m.lowPrice) * 100.0 / m.openPrice / 3.0, 2) AS spread_volatility_score
  `;

  // Dynamically build the JOIN clauses for each interval
  let joinClauses = `
      FROM 
          sandbox_binance_market m
      LEFT JOIN 
          sandbox_binance_fundingrates f 
          ON m.symbol = f.symbol  `;

  // Add klines data for each interval
  const klineColumns = [];
  for (const interval of targetIntervals) {
    const table = `sandbox_binance_klines_${interval}`;
    const alias = `k_${interval}`;

    // Add columns for this interval
    klineColumns.push(
      `${alias}.price_volatility AS price_volatility_${interval}`,
      `${alias}.volume_anomaly AS volume_anomaly_${interval}`,
      `${alias}.reason AS reason_${interval}`
    );

    // Add JOIN clause for this interval
    joinClauses += `
      LEFT JOIN 
          ${table} ${alias} 
          ON m.symbol = ${alias}.symbol  `;
  }

  // Add kline columns to select
  if (klineColumns.length > 0) {
    selectColumns += `,
      -- Kline verileri
      ${klineColumns.join(',\n      ')}`;
  }

  // Build the reason_combined field dynamically
  let reasonCombinedParts = [];
  for (const interval of targetIntervals) {
    const alias = `k_${interval}`;
    reasonCombinedParts.push(`COALESCE(CASE 
                                      WHEN ${alias}.reason IS NOT NULL AND LOWER(${alias}.reason) != 'normal' 
                                      THEN '${interval}: ' || ${alias}.reason || ', ' 
                                      ELSE '' 
                                  END, '')`);
  }

  // Add funding rate reason
  reasonCombinedParts.push(`COALESCE(CASE 
                                      WHEN f.reason IS NOT NULL AND LOWER(f.reason) != 'normal' 
                                      THEN 'funding: ' || f.reason || ', ' 
                                      ELSE '' 
                                  END, '')`);

  // Add range position reason
  reasonCombinedParts.push(`CASE 
                                WHEN (m.highPrice - m.lowPrice) = 0 THEN ''
                                WHEN (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) > 0.95 
                                    THEN '24s Aralığının Sınırında (Yüksek), '
                                WHEN (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) < 0.05 
                                    THEN '24s Aralığının Sınırında (Düşük), '
                                ELSE ''
                            END`);

  const reasonCombinedField = `
      -- Birleştirilmiş neden (reason_combined): sadece anormal olanlar birleştirilir
      COALESCE(
          NULLIF(
              RTRIM(
                  ${reasonCombinedParts.join(' ||\n                  ')}
                  ,
                  ', '
              ),
              ''
          ),
          'normal'
      ) AS reason_combined`;

  selectColumns += `,\n${reasonCombinedField}`;

  // Build the total_score calculation dynamically
  let totalScoreParts = [
    "(ABS(m.priceChangePercent) / 5.0)",  // Fiyat değişimi
    "((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0)"  // Spread volatilite
  ];

  // Add klines scores for each interval
  for (const interval of targetIntervals) {
    const alias = `k_${interval}`;
    totalScoreParts.push(`COALESCE(${alias}.price_volatility, 0)`);  // Kline volatilite
    totalScoreParts.push(`(CASE WHEN ${alias}.volume_anomaly THEN 1.0 ELSE 0.0 END)`);  // Hacim anomalisi
  }

  // Add funding rate score
  totalScoreParts.push("(ABS(COALESCE(f.funding_rate_score, 0)) * 1000)");  // Funding baskısı

  // Add range position score
  totalScoreParts.push(`(CASE 
                              WHEN (m.highPrice - m.lowPrice) != 0 AND 
                                  ((m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) > 0.95 OR 
                                    (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) < 0.05)
                              THEN 2.0 
                              ELSE 0.0 
                          END)`);  // Uç pozisyon

  const totalScoreField = `
      -- Toplam Risk/Endeks Skoru
      ROUND(
          ${totalScoreParts.join(' +\n                          ')},
          2
      ) AS total_score`;

  selectColumns += `,\n${totalScoreField}`;

  // Build the anomaly_score calculation dynamically
  let anomalyScoreParts = [];

  // Add klines scores for each interval (with weights)
  for (const interval of targetIntervals) {
    const alias = `k_${interval}`;
    anomalyScoreParts.push(`COALESCE(${alias}.price_volatility, 0) * 2.0`);  // Kline volatilite
    anomalyScoreParts.push(`(CASE WHEN ${alias}.volume_anomaly THEN 1.0 ELSE 0.0 END) * 2.0`);  // Hacim anomalisi
  }

  // Add price change score (with weight)
  anomalyScoreParts.push(`(ABS(m.priceChangePercent) / 5.0) * 3.5`);

  // Add spread volatility score (with weight)
  anomalyScoreParts.push(`((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0) * 3.5`);

  // Add funding rate score (with weight)
  anomalyScoreParts.push(`ABS(COALESCE(f.funding_rate_score, 0)) * 5.0`);

  const anomalyScoreField = `
      -- Ağırlıklı Anomali Skoru
      ROUND(
          ${anomalyScoreParts.join(' +\n                          ')},
          2
      ) AS anomaly_score`;

  selectColumns += `,\n${anomalyScoreField}`;

  // Build the final query
  const query_sql = `
              SELECT *
              FROM (
                  SELECT 
                      ${selectColumns}
                  ${joinClauses}
              ) ax
              WHERE 1 = 1
                -- AND reason_combined NOT IN ('normal')
              ORDER BY anomaly_score DESC;
              `;

  const pathPre = path.resolve(__dirname) + '/';
  //create this query as view in sqlite database with name binance_anomaly // drop view if exists and create view
  const viewName = 'sandbox_binance_anomaly';
  const createViewQuery = `DROP VIEW IF EXISTS ${viewName}; `;
  const createViewQuery1 = `CREATE VIEW ${viewName} AS ${query_sql}`;
  const queryView = db.prepare(createViewQuery);
  queryView.run();
  //sleep 500ms
  new Promise(resolve => setTimeout(resolve, 100));
  const queryView1 = db.prepare(createViewQuery1);
  queryView1.run();
  console.log('View created: ', viewName);
  //sql i anomaly_sql.txt dosyasına kayıt et.
  fs.writeFileSync(pathPre + 'bin/anomaly_sql.txt', query_sql);

  const query = db.prepare(query_sql);
  const result = query.all();
  db.close();
  return result;
}

(async () => {
  let dtBOP = Date.now();
  const targetPairs = await getTargetPairs();
  console.log('targetPairs #', targetPairs.length);
  let params = {}
  try {
    params = await getParams();
    // console.log('params', params,);
  } catch (ex) {
    // console.log('params ex', ex);
  } 

  const targetIntervals = params.intervals || ['1d', '1h', '1m'];
  const candleCounts = params.candleCounts || 24;
  const battleInterval = params.battleInterval || '1m';

  // console.log('params', targetIntervals, candleCounts, battleInterval);
  const pathPre = path.resolve(__dirname) + '/';
  //fundingRates
  const fundingHistory = await getHistoricalFundingRates(targetPairs);
  fs.writeFileSync(pathPre + 'bin/binance_fundingrates.json', JSON.stringify({
    targetPairs,
    history: fundingHistory,
    elapsed: Date.now() - dtBOP,
  }, null, 2));
  console.log("Veriler fundingrates.json dosyasına kaydedildi.");
  await save2db_funding(fundingHistory);

  //klines
  // let targetIntervals = ['1d', '1h', '1m'];
  // let targetIntervals = ['1h'];
  for (const interval of targetIntervals) {
    let dtBop_Klines = Date.now();
    const history_klines = await getKlines(targetPairs, interval, candleCounts);
    fs.writeFileSync(pathPre + 'bin/binance_klines_' + interval + '.json', JSON.stringify({
      targetPairs,
      history: history_klines,
      elapsed: Date.now() - dtBop_Klines,
    }, null, 2));
    console.log("Veriler klines_" + interval + ".json dosyasına kaydedildi.");
    await save2db_klines(history_klines, interval, candleCounts);
  }
  //market
  let dtBop_market = Date.now();
  const history_market = await getmarketData(targetPairs);
  fs.writeFileSync(pathPre + 'bin/binance_market.json', JSON.stringify({
    targetPairs,
    // fundingInfo: data,
    history: history_market,
    elapsed: Date.now() - dtBop_market,
  }, null, 2));
  console.log("Veriler binance_market.json dosyasına kaydedildi.");
  await save2db_market(history_market);

  const anomalyList = await getAnomalyList(targetIntervals);
  fs.writeFileSync(pathPre + 'bin/binance_anomaly.json', JSON.stringify(anomalyList, null, 2));
  console.log("Veriler binance_anomaly.json dosyasına kaydedildi.");

  console.log('Toplam Sure', Date.now() - dtBOP);
})();
