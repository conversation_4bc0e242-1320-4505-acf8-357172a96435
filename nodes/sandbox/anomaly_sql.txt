
              SELECT *
              FROM (
                  SELECT 
                      
      m.symbol,
      m.priceChange,
      m.priceChangePercent,
      m.weightedAvgPrice,
      m.lastPrice,
      m.lastQty,
      m.openPrice,
      m.highPrice,
      m.lowPrice,
      m.volume,
      m.quoteVolume,
      m.openTime,
      m.closeTime,
      m.firstId,
      m.lastId,
      m.count,
      m.created_at, 
      -- Funding Rate verileri
      f.funding_rate_score, 
      -- 24s aralığı içindeki pozisyon yüzdesi
      CASE 
          WHEN (m.highPrice - m.lowPrice) != 0 THEN 
              ROUND((m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) * 100, 2)
          ELSE 0.5 -- Hareketsizse (eşit), ortada kabul et
      END AS range_position_percent, 
      -- Fiyat yayılımı yüzdesi: (high - low) / lastPrice * 100
      ROUND((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice, 2) AS price_spread_percent, 
      ROUND((m.highPrice - m.lowPrice) * 100.0 / m.openPrice, 2) AS openPrice_spread_percent
      -- Fiyat değişim skoru: |priceChangePercent| / 5
      -- ROUND(ABS(m.priceChangePercent) * 1.0 / 5.0, 2) AS priceChangeScore, 
      -- Yayılım volatilite skoru: priceSpreadPercent / 3
      -- ROUND((m.highPrice - m.lowPrice) * 100.0 / m.openPrice / 3.0, 2) AS spread_volatility_score
  ,
      -- Kline verileri
      k_1d.price_volatility AS price_volatility_1d,
      k_1d.volume_anomaly AS volume_anomaly_1d,
      k_1d.reason AS reason_1d,
      k_1h.price_volatility AS price_volatility_1h,
      k_1h.volume_anomaly AS volume_anomaly_1h,
      k_1h.reason AS reason_1h,
      k_1m.price_volatility AS price_volatility_1m,
      k_1m.volume_anomaly AS volume_anomaly_1m,
      k_1m.reason AS reason_1m,

      -- Birleştirilmiş neden (reason_combined): sadece anormal olanlar birleştirilir
      COALESCE(
          NULLIF(
              RTRIM(
                  COALESCE(CASE 
                                      WHEN k_1d.reason IS NOT NULL AND LOWER(k_1d.reason) != 'normal' 
                                      THEN '1d: ' || k_1d.reason || ', ' 
                                      ELSE '' 
                                  END, '') ||
                  COALESCE(CASE 
                                      WHEN k_1h.reason IS NOT NULL AND LOWER(k_1h.reason) != 'normal' 
                                      THEN '1h: ' || k_1h.reason || ', ' 
                                      ELSE '' 
                                  END, '') ||
                  COALESCE(CASE 
                                      WHEN k_1m.reason IS NOT NULL AND LOWER(k_1m.reason) != 'normal' 
                                      THEN '1m: ' || k_1m.reason || ', ' 
                                      ELSE '' 
                                  END, '') ||
                  COALESCE(CASE 
                                      WHEN f.reason IS NOT NULL AND LOWER(f.reason) != 'normal' 
                                      THEN 'funding: ' || f.reason || ', ' 
                                      ELSE '' 
                                  END, '') ||
                  CASE 
                                      WHEN (m.highPrice - m.lowPrice) = 0 THEN ''
                                      WHEN (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) > 0.95 
                                          THEN '24s Aralığının Sınırında (Yüksek), '
                                      WHEN (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) < 0.05 
                                          THEN '24s Aralığının Sınırında (Düşük), '
                                      ELSE ''
                                  END
                  ,
                  ', '
              ),
              ''
          ),
          'normal'
      ) AS reason_combined,

      -- Toplam Risk/Endeks Skoru
      ROUND(
          (ABS(m.priceChangePercent) / 5.0) +
                          ((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0) +
                          COALESCE(k_1d.price_volatility, 0) +
                          (CASE WHEN k_1d.volume_anomaly THEN 1.0 ELSE 0.0 END) +
                          COALESCE(k_1h.price_volatility, 0) +
                          (CASE WHEN k_1h.volume_anomaly THEN 1.0 ELSE 0.0 END) +
                          COALESCE(k_1m.price_volatility, 0) +
                          (CASE WHEN k_1m.volume_anomaly THEN 1.0 ELSE 0.0 END) +
                          (ABS(COALESCE(f.funding_rate_score, 0)) * 1000) +
                          (CASE 
                              WHEN (m.highPrice - m.lowPrice) != 0 AND 
                                  ((m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) > 0.95 OR 
                                    (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) < 0.05)
                              THEN 2.0 
                              ELSE 0.0 
                          END),
          2
      ) AS total_score,

      -- Ağırlıklı Anomali Skoru
      ROUND(
          COALESCE(k_1d.price_volatility, 0) * 2.0 +
                          (CASE WHEN k_1d.volume_anomaly THEN 1.0 ELSE 0.0 END) * 2.0 +
                          COALESCE(k_1h.price_volatility, 0) * 2.0 +
                          (CASE WHEN k_1h.volume_anomaly THEN 1.0 ELSE 0.0 END) * 2.0 +
                          COALESCE(k_1m.price_volatility, 0) * 2.0 +
                          (CASE WHEN k_1m.volume_anomaly THEN 1.0 ELSE 0.0 END) * 2.0 +
                          (ABS(m.priceChangePercent) / 5.0) * 3.5 +
                          ((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0) * 3.5 +
                          ABS(COALESCE(f.funding_rate_score, 0)) * 5.0,
          2
      ) AS anomaly_score
                  
      FROM 
          sandbox_binance_market m
      LEFT JOIN 
          sandbox_binance_fundingrates f 
          ON m.symbol = f.symbol  
      LEFT JOIN 
          sandbox_binance_klines_1d k_1d 
          ON m.symbol = k_1d.symbol  
      LEFT JOIN 
          sandbox_binance_klines_1h k_1h 
          ON m.symbol = k_1h.symbol  
      LEFT JOIN 
          sandbox_binance_klines_1m k_1m 
          ON m.symbol = k_1m.symbol  
              ) ax
              WHERE 1 = 1
                -- AND reason_combined NOT IN ('normal')
              ORDER BY anomaly_score DESC;
              