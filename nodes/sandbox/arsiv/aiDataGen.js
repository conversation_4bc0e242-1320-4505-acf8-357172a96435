const fs = require('fs').promises;
const fss = require('fs');
const { Worker, workerData, MessageChannel, receiveMessageOnPort } = require('worker_threads');
const path = require('path');
const XLSX = require('xlsx');
const converter = require('json-2-csv');

const fnx = require('./_.functions');
const fnxIdx = require('./_.functions.indicators');
// const repoData = require('./bin/backtest_solusdt_1m.json');
const args = process.argv.slice(2)?.toString();
(async () => {
    let targetFiles;
    var addon = args ? args + '/' : '';
    var folderName = addon + 'bin';
    var fileinitial = 'backtest';
    try {
        var files = await fnx.getlistoffiles(folderName);
        targetFiles = files.filter(str => str.indexOf('backtest_') > -1).filter(str => str.indexOf('_trades') < 0)
        // fnx.log('folderName, files', folderName, files);
        // await fnx.cleanfiles(folderName, files, fileinitial)
    } catch (e) {
        console.log('error in receiving files: ', e);
        process.exit(1);
    };

    
    console.log('targetFiles', targetFiles);

    //TODO: next candle close ?
    let resp = [];
    await Promise.all(
        targetFiles.map(async (repoDataFile) => {
            let repoData;
            try {
                repoData = JSON.parse(await fs.readFile(folderName + "/" + repoDataFile, "utf8"));
            } catch (e) {
                fnx.log('error in reading file', repoDataFile, e)
            }
            console.log('repoDataFile', repoDataFile, repoData.length);
            let nArr = JSON.parse(JSON.stringify(repoData)).slice(1);
            repoData.map((a, indexx) => {
                let candle = a.candle;
                let ix = a.indicators;
                Array.isArray(ix) && ix.length !== 0 && ix.map(i => {
                    i.indicatorParams = i.indicatorParams ? JSON.parse(i.indicatorParams) : i.indicatorParams;
                    i.indicatorAdditionalData = i.indicatorAdditionalData ? JSON.parse(i.indicatorAdditionalData) : i.indicatorAdditionalData;
                    try {
                        i.indicatorValue = JSON.parse(i.indicatorValue)
                    } catch (e) {
                        try {
                            i.indicatorValue = typeof parseFloat(i.indicatorValue) == 'number' ? parseFloat(i.indicatorValue) : i.indicatorValue;
                        } catch (e2) {
                        }
                    }
                    // i.indicatorParams = i.indicatorParams ? JSON.parse(i.indicatorParams) : i.indicatorParams;
                }
                );
                let ixAddon = {};
                Array.isArray(ix) && ix.length !== 0 && ix.map(i => {
                    if (i.indicator == 'ema') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        a1[tag + '_position'] = i.indicatorAdditionalData.emaPosition;
                        a1[tag + '_trend'] = i.indicatorAdditionalData.emaTrend;
                        a1[tag + '_distanceR'] = i.indicatorAdditionalData.emaDistanceR;
                        a1[tag + '_crossUp'] = i.indicatorAdditionalData.emaCrossUp;
                        a1[tag + '_crossDown'] = i.indicatorAdditionalData.emaCrossDown;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'psar') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        a1[tag + '_position'] = i.indicatorAdditionalData.psarPosition;
                        a1[tag + '_age'] = i.indicatorAdditionalData.age;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'supertrend') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorAdditionalData?.indicatorValue == 'below' ? 'SELL' : 'BUY';
                        a1[tag + '_position'] = i.indicatorAdditionalData.position;
                        a1[tag + '_signal'] = i.indicatorAdditionalData.actSignal;
                        a1[tag + '_age'] = i.indicatorAdditionalData.DirectionAge;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'cci') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        a1[tag + '_crossUp'] = i.indicatorAdditionalData.crossUpLevels;
                        a1[tag + '_smacrossup'] = i.indicatorAdditionalData.crossUpSMA;
                        a1[tag + '_smatrendup'] = i.indicatorAdditionalData.SMAtrendIsUp;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'obv') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'atr') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        a1[tag + '_vsCloseP'] = i.indicatorAdditionalData.atrVsClosePerc;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'adx') {
                        if (i.indicatorValue) {
                            let a1 = {}
                            let tagStg = i.indicatorTag.split('_');
                            let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                            a1[tag] = i.indicatorValue;
                            a1[tag + '_adx'] = i.indicatorAdditionalData.adx;
                            a1[tag + '_pdi'] = i.indicatorAdditionalData.pdi;
                            a1[tag + '_mdi'] = i.indicatorAdditionalData.mdi;
                            a1[tag + '_pdiPos'] = i.indicatorAdditionalData.pdiPos;
                            ixAddon = { ...ixAddon, ...a1 };
                        }
                    } else if (i.indicator == 'rsi') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        a1[tag + '_smaFast'] = i.indicatorAdditionalData.rsiSMAFast;
                        a1[tag + '_smaSlow'] = i.indicatorAdditionalData.rsiSMASlow;
                        a1[tag + '_crossUpFast'] = i.indicatorAdditionalData.rsiCrossUpFast;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'macd') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue?.MACD;
                        a1[tag + '_signal'] = i.indicatorValue.signal;
                        a1[tag + '_histogram'] = i.indicatorValue.histogram;
                        a1[tag + '_macd'] = i.indicatorAdditionalData.MACD;
                        a1[tag + '_pos'] = i.indicatorAdditionalData.MACDPos;
                        a1[tag + '_age'] = i.indicatorAdditionalData.MACDPosAge;
                        a1[tag + '_posCross'] = i.indicatorAdditionalData.MaCDPosCross;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'srsi') {
                        if (i.indicatorValue?.stochRSI) {
                            let a1 = {}
                            let tagStg = i.indicatorTag.split('_');
                            let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                            a1[tag] = i.indicatorValue?.stochRSI;
                            a1[tag + '_k'] = i.indicatorValue?.k;
                            a1[tag + '_d'] = i.indicatorValue?.d;
                            a1[tag + '_pos'] = i.indicatorAdditionalData.kPos;
                            a1[tag + '_age'] = i.indicatorAdditionalData.kPosAge;
                            ixAddon = { ...ixAddon, ...a1 };
                        }
                    } else if (i.indicator == 'roc') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'chandelierexit') {
                        if (i.indicatorValue) {
                            let a1 = {}
                            let tagStg = i.indicatorTag.split('_');
                            let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                            a1[tag] = i.indicatorValue;
                            a1[tag + '_signal'] = i.indicatorAdditionalData.signal;
                            a1[tag + '_signalChanged'] = i.indicatorAdditionalData.signalChanged;
                            a1[tag + '_age'] = i.indicatorAdditionalData.signalAge;
                            ixAddon = { ...ixAddon, ...a1 };
                        }
                    } else if (i.indicator == 'ichimoku') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue?.conversion;
                        a1[tag + '_conversion'] = i.indicatorValue.conversion;
                        a1[tag + '_base'] = i.indicatorValue.base;
                        a1[tag + '_spanA'] = i.indicatorValue.spanA;
                        a1[tag + '_spanB'] = i.indicatorValue.spanB;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'awesomeOscillator') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'volatilityIndex') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = i.indicatorValue;
                        a1[tag + '_comparison'] = i.indicatorAdditionalData.comparison;
                        ixAddon = { ...ixAddon, ...a1 };
                    } else if (i.indicator == 'candlepatterns') {
                        let a1 = {}
                        let tagStg = i.indicatorTag.split('_');
                        let tag = tagStg[2] + '_' + tagStg[3] + '_' + tagStg[1];
                        a1[tag] = typeof i.indicatorValue == 'string' ? JSON.parse(i.indicatorValue).toString() : i.indicatorValue.toString();
                        a1[tag + '_deltaPerc1'] = i.indicatorAdditionalData.deltaPerc1;
                        a1[tag + '_deltaPerc2'] = i.indicatorAdditionalData.deltaPerc2;
                        a1[tag + '_deltaPerc3'] = i.indicatorAdditionalData.deltaPerc3;
                        a1[tag + '_deltaPerc5'] = i.indicatorAdditionalData.deltaPerc5;
                        a1[tag + '_deltaPerc10'] = i.indicatorAdditionalData.deltaPerc10;
                        a1[tag + '_HLRangePerc1'] = i.indicatorAdditionalData.HLRangePerc1;
                        a1[tag + '_HLRangePerc2'] = i.indicatorAdditionalData.HLRangePerc2;
                        a1[tag + '_HLRangePerc3'] = i.indicatorAdditionalData.HLRangePerc3;
                        a1[tag + '_HLRangePerc5'] = i.indicatorAdditionalData.HLRangePerc5;
                        a1[tag + '_barTrend2'] = i.indicatorAdditionalData.barTrend2;
                        a1[tag + '_barTrend3'] = i.indicatorAdditionalData.barTrend3;
                        a1[tag + '_barTrend5'] = i.indicatorAdditionalData.barTrend5;
                        a1[tag + '_barTrend10'] = i.indicatorAdditionalData.barTrend10;
                        // a1[tag + '_age'] = i.indicatorAdditionalData.DirectionAge;
                        ixAddon = { ...ixAddon, ...a1 };
                    } 
                    //TODO:  , votalityIx

                });
                // indexx > 1490 && console.log(indexx, nArr[indexx]?.candle?.close)
                let nextVal = nArr[indexx] ? Number(nArr[indexx]?.candle?.close) : null;
                let nextValDir = nextVal ? (nextVal > Number(candle.close) ? 'UP' : nextVal < Number(candle.close) ? 'DOWN' : 'FLAT' ) : 'NA';

                Array.isArray(ix) && ix.length !== 0 && resp.push(
                    {
                        candleIndex: a.candleIndex,
                        interval: candle.interval,
                        battleInterval: candle.battleInterval,
                        symbol: candle.symbol,
                        c_openTime: candle.openTime,
                        c_openTimeHRF: candle.openTimeHRF,
                        c_open: Number(candle.open),
                        c_high: Number(candle.high),
                        c_low: Number(candle.low),
                        c_close: Number(candle.close),
                        c_nextClose: nextVal,
                        c_nextValDir: nextValDir,
                        c_volume: Number(candle.volume),
                        c_quoteVolume: Number(candle.quoteVolume),
                        c_trades: Number(candle.trades),
                        c_volumeTaker: Number(candle.volumeTaker),
                        c_quoteVolumeTaker: Number(candle.quoteVolumeTaker),
                        ...ixAddon,
                        // indicators: ix,
                    }
                )
                // return (resp)
            });
        })
    );
    

    try {
        const filePath = path.resolve(__dirname, './bin/backtest.aiData.json');
        true && await fnx.savetoFile(resp, filePath);

        const filePathcsv = path.resolve(__dirname, './bin/backtest.aiData.csv');
        const filePathxlsx = path.resolve(__dirname, './bin/backtest.aiData.xlsx');
        
        const worksheet = XLSX.utils.json_to_sheet(resp);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
        XLSX.writeFile(workbook, filePathxlsx);

        try {
            var csv = await converter.json2csv(resp, 
                {
                    delimiter: {
                        wrap: '\'', // Double Quote (") character
                        field: ';', // Comma field delimiter
                        array: ',', // Semicolon array value delimiter
                        eol: '\r\n' // Newline delimiter
                    },
                    prependHeader: true,
                    sortHeader: false,
                    trimHeaderValues: true,
                    trimFieldValues: true,

                }

            );
            csv = csv.replaceAll("undefined","");

            console.log('eCSV', filePathcsv)
            await fnx.savetoFile(csv, filePathcsv, false, {noStr: true});
        }
        catch (eCSV) {
            console.log('eCSV', eCSV)
        }

        // try {

        //     let json2csvCallback = function (err, csv) {
        //         if (err) throw err;
        //         fss.writeFile(filePathcsv, csv, 'utf8', function (err) {
        //             if (err) {
        //                 console.log('Some error occured - file either not saved or corrupted file saved.');
        //             } else {
        //                 console.log('It\'s saved!');
        //             }
        //         });
        //     };
        //     converter.json2csv(resp, json2csvCallback, //.slice(-5)
        //         {
        //             delimiter: {
        //                 wrap: '\'', // Double Quote (") character
        //                 field: ';', // Comma field delimiter
        //                 array: ',', // Semicolon array value delimiter
        //                 eol: '\r' // Newline delimiter
        //             },
        //             prependHeader: true,
        //             sortHeader: false,
        //             trimHeaderValues: true,
        //             trimFieldValues: true,

        //         });
        // }
        // catch (eCSV) {
        //     console.log('eCSV', eCSV)
        // }

    } catch (e) {
        console.log(e)
    }

})();
