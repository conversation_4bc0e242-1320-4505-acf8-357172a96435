// scraper.js

const { chromium } = require('playwright');

async function scrapeLiquidationData() {
  let browser = null;
  try {
    console.log('<PERSON>yıcı başlatılıyor...');
    // Tarayıcıyı başlat
    browser = await chromium.launch({
      headless: true // 'false' yaparsanız tarayıcının açıldığını görürsünüz
    });

    // Yeni bir sayfa oluştur
    const page = await browser.newPage();

    console.log('Coinglass tasfiye verileri sayfasına gidiliyor...');
    // Hedef URL'ye git
    await page.goto('https://www.coinglass.com/tr/LiquidationData', {
      waitUntil: 'networkidle' // Sayfadaki ağ trafiğinin durulmasını bekle
    });
    console.log('Sayfa başarıyla yüklendi.');

    // Veri tablosunun yüklenmesini bekle. Bu selector, tablonun ana ka<PERSON>ıdır.
    // Web sitesi güncellenirse bu selector'ın da güncellenmesi gerekebilir.
    const tableSelector = '.ant-table-container';
    console.log(`'${tableSelector}' seçicisine sahip tablonun yüklenmesi bekleniyor...`);
    await page.waitForSelector(tableSelector, { timeout: 30000 }); // 30 saniye bekleme süresi
    console.log('Veri tablosu başarıyla bulundu.');


    // Tablodaki tüm satırları (tr) çek ve verileri işle
    const liquidationData = await page.evaluate((selector) => {
      const rows = Array.from(document.querySelectorAll(`${selector} tbody .ant-table-row`));
      const data = [];

      rows.forEach(row => {
        // Satırdaki tüm hücreleri (td) al
        const cells = row.querySelectorAll('.ant-table-cell');
        
        // Hücrelerdeki verileri temizleyerek al
        const coinName = cells[0]?.innerText.trim() || 'N/A';
        const price = cells[1]?.innerText.trim() || 'N/A';
        const change1h = cells[2]?.innerText.trim() || 'N/A';
        const change24h = cells[3]?.innerText.trim() || 'N/A';
        const totalLiquidation = cells[4]?.innerText.trim() || 'N/A';
        const longLiquidationText = cells[5]?.querySelector('.long')?.innerText.trim() || '0%';
        const shortLiquidationText = cells[5]?.querySelector('.short')?.innerText.trim() || '0%';
        
        data.push({
          coin: coinName,
          fiyat: price,
          '1s_degisim': change1h,
          '24s_degisim': change24h,
          toplam_tasfiye: totalLiquidation,
          long_yuzdesi: longLiquidationText,
          short_yuzdesi: shortLiquidationText,
        });
      });

      return data;
    }, tableSelector); // 'tableSelector' değişkenini evaluate fonksiyonuna parametre olarak gönder

    console.log('\n--- Tasfiye Verileri Başarıyla Çekildi ---\n');
    return liquidationData;

  } catch (error) {
    console.error('Veri kazıma sırasında bir hata oluştu:', error);
    return null;
  } finally {
    if (browser) {
      console.log('Tarayıcı kapatılıyor...');
      await browser.close();
    }
  }
}

// Ana fonksiyonu çalıştır
scrapeLiquidationData().then(data => {
  if (data) {
    // Çekilen veriyi tablo formatında konsola yazdır
    console.table(data);
  }
});