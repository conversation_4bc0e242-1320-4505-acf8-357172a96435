// binance_anomaly_detector.js

const axios = require('axios');
const Database = require('better-sqlite3');
const path = require('path');
const WebSocket = require('ws');
// const { WebSocket } = require('ws'); // 'ws' paketi gerekir: npm install ws
require('dotenv').config();

// SQLite veritabanı
const dbPath = path.resolve(__dirname, 'anomalies.db');
const db = new Database(dbPath, { verbose: console.log });
console.log('Veritabanına başarıyla bağlandı.');

// Anomali tablosu
function createTable() {
    db.exec(`CREATE TABLE IF NOT EXISTS anomalies (
        symbol TEXT PRIMARY KEY,
        price_volatility REAL,
        volume_anomaly REAL,
        funding_rate_score REAL,
        liquidation_score REAL,
        total_score REAL,
        reason TEXT,
        last_updated TEXT
    )`);
    console.log('Anomaliler tablosu hazır.');
}

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';

// Takip edilecek semboller
const SYMBOLS = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'XRPUSDT', 'ADAUSDT',
    'DOGEUSDT', 'ENAUSDT', 'LTCUSDT', 'TRXUSDT', 'AVAXUSDT', 'LINKUSDT',
    'DOTUSDT', 'XNYUSDT', 'UNIUSDT', 'ETCUSDT', 'VETUSDT'
];

// WebSocket üzerinden toplanan likidasyon hacmi (son 5 dakika gibi)
const liquidationVolumes = {};
const LIQUIDATION_WINDOW_MS = 5 * 60 * 1000; // 5 dakika

// WebSocket başlat ve likidasyon verisi topla
function startLiquidationWebSocket() {
    return new Promise((resolve) => {
        console.log('WebSocket ile likidasyon verisi toplanıyor...');

        // Tüm sembolleri izlemek için filtreleme yapacağız
        const symbolsLower = SYMBOLS.map(s => s.toLowerCase() + '@forceOrder');
        const streamUrl = `wss://fstream.binance.com/stream?streams=${symbolsLower.join('/')}`;

        const ws = new WebSocket(streamUrl);

        // Başlangıçta sıfırla
        SYMBOLS.forEach(symbol => {
            liquidationVolumes[symbol] = 0;
        });

        const startTime = Date.now();

        ws.on('open', () => {
            console.log('WebSocket bağlantısı kuruldu (likidasyonlar).');
        });

        ws.on('message', (data) => {
            const packet = JSON.parse(data);
            const order = packet.data;

            if (!order || !order.s || !order.p || !order.q) return;

            const symbol = order.s;
            const price = parseFloat(order.p);
            const quantity = parseFloat(order.q);
            const side = order.S; // "BUY" (long likidasyon) veya "SELL" (short likidasyon)
            const timestamp = order.T;

            // Sadece izlenen semboller
            if (!SYMBOLS.includes(symbol)) return;

            // Belirli bir zaman penceresinde topla
            if (timestamp >= startTime - 10000) { // Son 10 saniyede olanlar
                liquidationVolumes[symbol] = (liquidationVolumes[symbol] || 0) + quantity;
            }
        });

        ws.on('error', (error) => {
            console.error('WebSocket hatası (likidasyon):', error.message);
        });

        // 30 saniye sonra veriyi topladık, kapat ve devam et
        setTimeout(() => {
            ws.close();
            console.log('WebSocket kapatıldı. Likidasyon verisi toplandı.');
            resolve();
        }, 30000); // 30 saniye toplama süresi
    });
}

// K-line verisi çek
async function getKlineData(symbol) {
    try {
        const url = `${BINANCE_FUTURES_API_URL}/fapi/v1/klines`;
        const response = await axios.get(url, {
            params: { symbol, interval: '1h', limit: 24 }
        });
        return response.data;
    } catch (error) {
        console.error(`K-line verisi hatası (${symbol}):`, error.message);
        return null;
    }
}

// Fonlama oranı verisi çek
async function getFundingRateHistory(symbol) {
    try {
        const url = `${BINANCE_FUTURES_API_URL}/fapi/v1/fundingRate`;
        const response = await axios.get(url, {
            params: { symbol, limit: 24 }
        });
        return response.data;
    } catch (error) {
        console.error(`Fonlama oranı hatası (${symbol}):`, error.message);
        return null;
    }
}

// İstatistik hesapla
function calculateStats(data) {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    return { mean, stdDev };
}

function calculateZScore(value, mean, stdDev) {
    return stdDev === 0 ? 0 : Math.abs((value - mean) / stdDev);
}

// Ana analiz fonksiyonu
async function runAnalysis() {
    console.log('Analiz başlatılıyor...');

    // 1. WebSocket ile likidasyon verisi topla
    await startLiquidationWebSocket();

    const allKlineData = {};
    const allFundingRates = {};

    // 2. K-line ve fonlama oranı verilerini topla
    for (const symbol of SYMBOLS) {
        const klineData = await getKlineData(symbol);
        const fundingRateData = await getFundingRateHistory(symbol);

        if (klineData) allKlineData[symbol] = klineData;
        if (fundingRateData) allFundingRates[symbol] = fundingRateData;
    }

    // 3. Tüm metrikler için istatistikleri hesapla
    const allPriceChanges = Object.values(allKlineData).map(klines => {
        const first = parseFloat(klines[0][1]);
        const last = parseFloat(klines[klines.length - 1][4]);
        return Math.abs(last - first) / first;
    });

    const allVolumes = Object.values(allKlineData).map(klines =>
        klines.reduce((sum, k) => sum + parseFloat(k[5]), 0)
    );

    const allFundingChanges = Object.values(allFundingRates).map(rates => {
        if (rates.length < 2) return 0;
        const first = parseFloat(rates[0].fundingRate);
        const last = parseFloat(rates[rates.length - 1].fundingRate);
        return Math.abs(last - first);
    });

    const allLiquidationValues = SYMBOLS.map(s => liquidationVolumes[s] || 0);

    const priceStats = calculateStats(allPriceChanges);
    const volumeStats = calculateStats(allVolumes);
    const fundingStats = calculateStats(allFundingChanges);
    const liquidationStats = calculateStats(allLiquidationValues);

    // 4. Anomali skorlarını hesapla ve veritabanına yaz
    const insertStmt = db.prepare(`
        INSERT OR REPLACE INTO anomalies 
        (symbol, price_volatility, volume_anomaly, funding_rate_score, liquidation_score, total_score, reason, last_updated) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const anomalyResults = [];

    db.transaction(() => {
        for (const symbol of SYMBOLS) {
            const klines = allKlineData[symbol];
            const fundingRates = allFundingRates[symbol];
            const liqVol = liquidationVolumes[symbol] || 0;

            if (!klines || !fundingRates) continue;

            const priceChange = Math.abs(parseFloat(klines[klines.length - 1][4]) - parseFloat(klines[0][1])) / parseFloat(klines[0][1]);
            const totalVolume = klines.reduce((sum, k) => sum + parseFloat(k[5]), 0);
            const fundingChange = fundingRates.length >= 2
                ? Math.abs(parseFloat(fundingRates[fundingRates.length - 1].fundingRate) - parseFloat(fundingRates[0].fundingRate))
                : 0;

            const priceZ = calculateZScore(priceChange, priceStats.mean, priceStats.stdDev);
            const volumeZ = calculateZScore(totalVolume, volumeStats.mean, volumeStats.stdDev);
            const fundingZ = calculateZScore(fundingChange, fundingStats.mean, fundingStats.stdDev);
            const liquidationZ = calculateZScore(liqVol, liquidationStats.mean, liquidationStats.stdDev);

            const totalScore = (priceZ * 1.5) + (volumeZ * 1.0) + (fundingZ * 2.0) + (liquidationZ * 2.5);

            const reasons = [];
            if (liquidationZ > 2.0) reasons.push('Yüksek likidasyon hacmi');
            if (fundingZ > 2.0) reasons.push('Anormal fonlama oranı');
            if (priceZ > 2.0) reasons.push('Yüksek volatilite');
            if (volumeZ > 2.0) reasons.push('Hacim anormalliği');

            const reasonText = reasons.length > 0 ? reasons.join(' ve ') + ' nedeniyle.' : 'Normal';

            anomalyResults.push({
                symbol,
                price_volatility: priceZ,
                volume_anomaly: volumeZ,
                funding_rate_score: fundingZ,
                liquidation_score: liquidationZ,
                total_score: totalScore,
                reason: reasonText
            });

            insertStmt.run(
                symbol,
                priceZ,
                volumeZ,
                fundingZ,
                liquidationZ,
                totalScore,
                reasonText,
                new Date().toISOString()
            );
        }
    })();

    // 5. En yüksek 10 anomaliyi göster
    const topAnomalies = anomalyResults
        .sort((a, b) => b.total_score - a.total_score)
        .slice(0, 10)
        .map(item => ({
            symbol: item.symbol,
            anomaly_score: item.total_score.toFixed(2),
            reason: item.reason,
            metrics: {
                price_volatility: item.price_volatility.toFixed(2),
                volume_anomaly: item.volume_anomaly.toFixed(2),
                funding_rate_score: item.funding_rate_score.toFixed(2),
                liquidation_score: item.liquidation_score.toFixed(2)
            }
        }));

    console.log('\n--- En Yüksek Anomaliye Sahip Kripto Para Çiftleri ---');
    console.log(JSON.stringify(topAnomalies, null, 2));

    // Veritabanını kapat
    db.close();
}

// Uygulama başlat
createTable();
runAnalysis();