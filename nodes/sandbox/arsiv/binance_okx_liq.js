
const WebSocket = require('ws');// okx-liquidation-ws.js
const axios = require('axios');

async function getLiquidationSummary() {
    const url = 'https://www.okx.com/api/v5/risk/liquidation-orders';

    try {
        const response = await axios.get(url, {
            params: {
                instType: 'FUTURES',
                uly: 'BTC-USD',  // Temel varlık (underlying)
                limit: 20
            },
            // Gerekirse User-Agent ekle (bazı durumlarda gerekebilir)
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        console.log('Likidasyon Verisi:', response.data);
    } catch (error) {
        if (error.response) {
            console.error('API Hatası:', error.response.status, error.response.data);
        } else {
            console.error('İstek Hatası:', error.message);
        }
    }
}

// Takip edilecek semboller (OKX formatında)
const TRACKED_SYMBOLS = new Set([
    'BTC-USDT',
    'ETH-USDT',
    'SOL-USDT',
    'XRP-USDT',
    'ADA-USDT'
]);

const wsUrl = 'wss://ws.okx.com:8443/ws/v5/public';
let ws;

function connect() {
    console.log('OKX WebSocket\'e bağlanılıyor...');
    ws = new WebSocket(wsUrl);

    ws.on('open', () => {
        console.log('✅ OKX WebSocket bağlantısı kuruldu.');

        const subscribeMsg = {
            op: 'subscribe',
            args: [
                { channel: 'liquidation-orders', instType: 'FUTURES' }
            ]
        };
        ws.send(JSON.stringify(subscribeMsg));
    });

    ws.on('message', (data) => {
        let msg;
        try {
            msg = JSON.parse(data.toString());
        } catch (err) {
            console.error('JSON parse hatası:', err);
            return;
        }

        // 1. Abonelik onayı veya hata mesajı mı?
        if (msg.event) {
            if (msg.event === 'subscribe') {
                console.log(`✔️ Abonelik başarılı: ${msg.arg.channel}`);
            } else if (msg.event === 'error') {
                console.error('❌ WebSocket Hatası:', msg);
            }
            return; // Veri değil, işleme alma
        }

        // 2. Gerçek veri mesajı mı? (arg ve data olmalı)
        if (msg.arg?.channel === 'liquidation-orders' && msg.data) {
            for (const order of msg.data) { // Birden fazla olabilir
                const symbol = order.instId;

                if (TRACKED_SYMBOLS.has(symbol)) {
                    const side = order.side === 'sell' ? '숏 likidasyon (Long)' : 'Long likidasyon (Short)';
                    const quantity = parseFloat(order.sz);
                    const price = parseFloat(order.px);
                    const timestamp = new Date(parseInt(order.ts)).toLocaleTimeString();

                    console.log(`\x1b[33m[LIKİDASYON]\x1b[0m ${timestamp} | ${symbol} | ${side} | Fiyat: ${price} | Miktar: ${quantity} USDT`);
                }
            }
        }
    });

    ws.on('close', (code) => {
        console.log(`❌ Bağlantı kapandı (kod: ${code}). 3 saniye sonra yeniden bağlanılıyor...`);
        setTimeout(connect, 3000);
    });

    ws.on('error', (error) => {
        console.error('WebSocket Hata:', error.message);
    });
}

console.log('OKX Likidasyon Dinleyicisi Başlatılıyor...');
console.log('Takip edilen semboller:', [...TRACKED_SYMBOLS].join(', '));
getLiquidationSummary();
connect();

process.on('SIGINT', () => {
    console.log('\n👋 Uygulama kapatılıyor...');
    ws.close();
    process.exit();
});