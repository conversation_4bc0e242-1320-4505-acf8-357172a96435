// app.js

const Database = require('better-sqlite3');
const path = require('path');
const axios = require('axios');
const fs = require('fs');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';
async function getTargetPairs() {
  //db/gauss.db
  const pth = path.resolve(__dirname, '../../db/gauss.db');
  console.log('dbPath', pth);
  const db = new Database(pth);
  const q = `
    SELECT dataName, dataValues, dtCreated, is_deleted
    FROM sandbox_datax
    where dataName = 'pairs'
    order by dtCreated desc
    limit 1;
  `
  const query = db.prepare(q);
  const pairs = query.all();
  let resp = [];
  try {
    resp = JSON.parse(pairs[0]?.dataValues) || [];
  } catch (e) {
    console.log('e', e)
    resp = [];
  }
  return resp;
}

async function getFundingRates(targetpairs) {
  // const pairs = ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "ALPINEUSDT", "DOGEUSDT"];
  const apiUrl = BINANCE_FUTURES_API_URL + "/fapi/v1/fundingInfo";

  // console.log("fonlama oranları çekiliyor...");
  
  let allFundingInfo = [];
    try {
      const response = await axios.get(apiUrl, {
        params: {
          limit: 999
        }
      });

      allFundingInfo = response.data;
      // Gelen veriler arasından, istediğimiz sembolleri filtreliyoruz.
      const filteredFundingInfo = allFundingInfo.filter(item =>
        targetpairs.includes(item.symbol)
      );

      // API'den dönen veriyi allFundingInfo dizisine ekle
      allFundingInfo = filteredFundingInfo;

      console.log(`fonlama oranları başarıyla çekildi.`);
      
    } catch (error) {
      console.error(`Hata oluştu: veri çekilemedi. Hata: ${error.message}`);
    }
  // Fonksiyonun sonunda tüm verileri döndür
  return allFundingInfo;
}

// Fonlama oranı verisi çek
async function getFundingRateHistory(symbol) {
    try {
        const url = `${BINANCE_FUTURES_API_URL}/fapi/v1/fundingRate`;
        const response = await axios.get(url, {
            params: { symbol, limit: 24 }
        });
        return response.data;
    } catch (error) {
        console.error(`Fonlama oranı hatası (${symbol}):`, error.message);
        return null;
    }
}

async function getHistoricalFundingRates(targetpairs) {
  // Veri çekmek istediğimiz semboller
  const pairs = targetpairs || ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "DOGEUSDT"];
  const allHistoricalData = {};
  for (const pair of pairs) {
    try {
      let historicData = await getFundingRateHistory(pair);
      allHistoricalData[pair] = historicData;
    } catch (error) {
      console.error(`Hata oluştu: ${pair} için veri çekilemedi. Hata: ${error.message}`);
    }
  }

  // Fonksiyonun sonunda tüm verileri döndür
  return allHistoricalData;
}

(async () => {
  // const targetPairsStg = await getTargetPairs();
  let dtBOP = Date.now();
  const targetPairs = await getTargetPairs();
  console.log ('targetPairs', targetPairs.length);
  // const data = await getFundingRates(targetPairs);
  const history = await getHistoricalFundingRates(targetPairs);
  // Verileri fundingrates.json dosyasına da kaydet
  fs.writeFileSync('binance_fundingrates.json', JSON.stringify({
    targetPairs,
    // fundingInfo: data,
    history: history,
    elapsed: Date.now() - dtBOP,
  }, null, 2));
  console.log("Veriler fundingrates.json dosyasına kaydedildi.");
})();