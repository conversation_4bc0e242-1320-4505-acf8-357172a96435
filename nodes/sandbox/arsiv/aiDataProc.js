const fs = require('fs').promises;
const fss = require('fs');
const { Worker, workerData, MessageChannel, receiveMessageOnPort } = require('worker_threads');
const path = require('path');
const XLSX = require('xlsx');
const converter = require('json-2-csv');
const fnx = require('./_.functions');
const fnxIdx = require('./_.functions.indicators');
const repoData = require('./bin/backtest.aiData.json');
const args = process.argv.slice(2)?.toString();
const brain = require('brain.js');
const _ = require('lodash');

(async () => {
    try {
        let targetPairs = ['bnbusdt', 'btcusdt'];
        let repo = Array.isArray(repoData) && repoData;
        repo = targetPairs.length !== 0 ? repo.filter(d => targetPairs.includes(d.symbol)) : repo;

        console.log('repo', repo.length);

    } catch (e) {
        console.log(e)
    }

})();
