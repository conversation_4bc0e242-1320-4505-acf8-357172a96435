// app.js

const Database = require('better-sqlite3');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const os = require('os');
const { Worker } = require('worker_threads');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';
async function getTargetPairs() {
  //db/gauss.db
  const pth = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(pth);
  const q = `
    SELECT dataName, dataValues, dtCreated, is_deleted
    FROM sandbox_datax
    where dataName = 'pairs'
    order by dtCreated desc
    limit 1;
  `
  const query = db.prepare(q);
  const pairs = query.all();
  let resp = [];
  try {
    resp = JSON.parse(pairs[0]?.dataValues) || [];
  } catch (e) {
    console.log('e', e)
    resp = [];
  }
  return resp;
}

async function getKlines(targetpairs) {
  // Veri çekmek istediğimiz semboller
  const pairs = targetpairs || ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "DOGEUSDT"];
  
  // CPU çekirdek sayısını bul
  const cpuCount = os.cpus().length;
  console.log(`Sistemdeki CPU çekirdek sayısı: ${cpuCount}`);
  
  // İşlem parçalarını oluştur
  const chunkSize = Math.ceil(pairs.length / cpuCount);
  const chunks = [];
  for (let i = 0; i < pairs.length; i += chunkSize) {
    chunks.push(pairs.slice(i, i + chunkSize));
  }
  
  // Worker'ları başlat ve sonuçları topla
  const results = {};
  
  return new Promise((resolve, reject) => {
    let completedWorkers = 0;
    
    // Her bir chunk için worker oluştur
    chunks.forEach((chunk, index) => {
      const worker = new Worker(__dirname + '/binance_klines_worker.js');
      
      worker.on('message', (result) => {
        if (result.success) {
          // Sonuçları birleştir
          Object.assign(results, result.data);
          completedWorkers++;
          
          // Tüm worker'lar tamamlandıysa işlemi bitir
          if (completedWorkers === chunks.length) {
            resolve(results);
          }
        } else {
          reject(new Error('Worker hatası: ' + result.error));
        }
        
        // Worker'ı kapat
        worker.terminate();
      });
      
      worker.on('error', (error) => {
        reject(new Error('Worker başlatma hatası: ' + error.message));
      });
      
      // Worker'a veri gönder
      worker.postMessage({ pairs: chunk });
    });
    
    // Eğer hiç pair yoksa hemen sonuç döndür
    if (chunks.length === 0) {
      resolve({});
    }
  });
}

// İstatistik hesapla
function calculateStats(data) {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    return { mean, stdDev };
}

function calculateZScore(value, mean, stdDev) {
    return stdDev === 0 ? 0 : Math.abs((value - mean) / stdDev);
}

async function save2db(history) {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  const tableName = 'sandbox_binance_klines';
  let sessionID = Date.now();
  const q = `
    CREATE TABLE IF NOT EXISTS ${tableName} (
      symbol TEXT PRIMARY KEY,
      data TEXT,
      price_volatility float,
      volume_anomaly float,
      reason TEXT,
      --sessionID bigint,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `;
  const query = db.prepare(q);
  query.run();

  // const allFundingChanges = Object.values(history).map(rates => {
  //   if (rates.length < 2) return 0;
  //   const first = parseFloat(rates[0].fundingRate);
  //   const last = parseFloat(rates[rates.length - 1].fundingRate);
  //   return Math.abs(last - first);
  // });
  // const fundingStats = calculateStats(allFundingChanges);

    // 3. Tüm metrikler için istatistikleri hesapla
    const allPriceChanges = Object.values(history).map(klines => {
        const first = parseFloat(klines[0][1]);
        const last = parseFloat(klines[klines.length - 1][4]);
        return Math.abs(last - first) / first;
    });

    const allVolumes = Object.values(history).map(klines =>
        klines.reduce((sum, k) => sum + parseFloat(k[5]), 0)
    );
    const priceStats = calculateStats(allPriceChanges);
    const volumeStats = calculateStats(allVolumes);

  for (const symbol in history) {
    let klines = history[symbol];
    // const fundingChange = fundingRates.length >= 2
    //   ? Math.abs(parseFloat(fundingRates[fundingRates.length - 1].fundingRate) - parseFloat(fundingRates[0].fundingRate))
    //   : 0;
    // const fundingZ = calculateZScore(fundingChange, fundingStats.mean, fundingStats.stdDev);

    const priceChange = Math.abs(parseFloat(klines[klines.length - 1][4]) - parseFloat(klines[0][1])) / parseFloat(klines[0][1]);
    const totalVolume = klines.reduce((sum, k) => sum + parseFloat(k[5]), 0);

    const priceZ = calculateZScore(priceChange, priceStats.mean, priceStats.stdDev);
    const volumeZ = calculateZScore(totalVolume, volumeStats.mean, volumeStats.stdDev);

    const reasons = [];
    if (priceZ > 2.0) reasons.push('Yüksek volatilite');
    if (volumeZ > 3.0) reasons.push('Hacim anormalliği');
    const reasonText = reasons.length > 0 ? reasons.join(' ve ') + ' nedeniyle.' : 'Normal';
    const q = `INSERT OR REPLACE INTO ${tableName} (symbol, data, price_volatility, volume_anomaly, reason) VALUES (?, ?, ?, ?, ?)`;
    const query = db.prepare(q);
    query.run(symbol, JSON.stringify(klines), priceZ, volumeZ, reasonText);
  }
  db.close();
}   

(async () => { 
  let dtBOP = Date.now();
  const targetPairs = await getTargetPairs();
  console.log ('targetPairs', targetPairs.length); 
  const history = await getKlines(targetPairs); 
  await save2db(history);
  fs.writeFileSync('bin/binance_klines.json', JSON.stringify({
    targetPairs,
    // fundingInfo: data,
    history: history,
    elapsed: Date.now() - dtBOP,
  }, null, 2));
  console.log("Veriler klines.json dosyasına kaydedildi.");
})();
