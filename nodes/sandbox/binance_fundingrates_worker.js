const { parentPort, workerData } = require("worker_threads");
const axios = require('axios');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';

// Fonlama oranı verisi çek
async function getFundingRateHistory(symbol) {
    try {
        const url = `${BINANCE_FUTURES_API_URL}/fapi/v1/fundingRate`;
        const response = await axios.get(url, {
            params: { symbol, limit: 24 }
        });
        return response.data;
    } catch (error) {
        console.error(`Fonlama oranı hatası (${symbol}):`, error.message);
        return null;
    }
}

// Worker thread message handler
parentPort.on("message", async (data) => {
    const { pairs } = data;
    const result = {};
    
    for (const pair of pairs) {
        try {
            let historicData = await getFundingRateHistory(pair);
            result[pair] = historicData;
        } catch (error) {
            console.error(`<PERSON><PERSON> o<PERSON>: ${pair} için veri çekilemedi. Hata: ${error.message}`);
            result[pair] = null;
        }
    }
    
    // Send result back to main thread
    parentPort.postMessage({
        success: true,
        data: result
    });
});
