# Binance Anomaly View - Column Descriptions

## Market Data Columns (from sandbox_binance_market table)
1. **symbol**: The trading pair symbol (e.g., BTCUSDT, ETHUSDT)
   - Data representation: Trading pair identifier
   - How obtained: Retrieved from Binance Futures API /fapi/v1/ticker/24hr endpoint

2. **priceChange**: Absolute price change in the last 24 hours
   - Data representation: Difference between current price and price 24 hours ago
   - How obtained: Calculated by Binance API as lastPrice - openPrice

3. **priceChangePercent**: Percentage price change in the last 24 hours
   - Data representation: Percentage change in price over the last 24 hours
   - How obtained: Calculated by Binance API as (priceChange / openPrice) * 100

4. **weightedAvgPrice**: Weighted average price in the last 24 hours
   - Data representation: Volume-weighted average price over 24 hours
   - How obtained: Calculated by Binance API based on all trades in the last 24 hours

5. **lastPrice**: Most recent traded price
   - Data representation: Last executed trade price
   - How obtained: Retrieved from Binance Futures API

6. **lastQty**: Quantity of the last executed trade
   - Data representation: Amount of the asset traded in the last transaction
   - How obtained: Retrieved from Binance Futures API

7. **openPrice**: Price at the beginning of the 24-hour period
   - Data representation: Opening price 24 hours ago
   - How obtained: Retrieved from Binance Futures API

8. **highPrice**: Highest price in the last 24 hours
   - Data representation: Maximum price reached in the last 24 hours
   - How obtained: Retrieved from Binance Futures API

9. **lowPrice**: Lowest price in the last 24 hours
   - Data representation: Minimum price reached in the last 24 hours
   - How obtained: Retrieved from Binance Futures API

10. **volume**: Total trading volume in the base asset (24h)
    - Data representation: Total amount of the base asset traded in 24 hours
    - How obtained: Retrieved from Binance Futures API

11. **quoteVolume**: Total trading volume in the quote asset (24h)
    - Data representation: Total value of trades in USDT (or quote currency)
    - How obtained: Retrieved from Binance Futures API

12. **openTime**: Timestamp when the 24-hour statistics period opened
    - Data representation: Unix timestamp in milliseconds
    - How obtained: Retrieved from Binance Futures API

13. **closeTime**: Timestamp when the 24-hour statistics period will close
    - Data representation: Unix timestamp in milliseconds
    - How obtained: Retrieved from Binance Futures API

14. **firstId**: ID of the first trade in the 24-hour period
    - Data representation: Unique identifier of the first trade
    - How obtained: Retrieved from Binance Futures API

15. **lastId**: ID of the last trade in the 24-hour period
    - Data representation: Unique identifier of the last trade
    - How obtained: Retrieved from Binance Futures API

16. **count / trades_count**: Number of trades executed in the 24-hour period
    - Data representation: Total count of trades
    - How obtained: Retrieved from Binance Futures API

17. **created_at**: Timestamp when the market data was recorded
    - Data representation: Date and time when the record was created in the database
    - How obtained: Automatically generated when inserting data into the database

## Funding Rate Column (from sandbox_binance_fundingrates table)
18. **funding_rate_score**: Z-score of funding rate changes indicating abnormality
    - Data representation: Statistical measure of how unusual the funding rate change is
    - How obtained: Calculated as the absolute z-score of the difference between first and last funding rates in the historical data

## Calculated Position Columns
19. **range_position_percent**: Current price position within the 24-hour range
    - Data representation: Percentage indicating where the current price is within the high-low range (0-100%)
    - How obtained: Calculated as ((lastPrice - lowPrice) / (highPrice - lowPrice)) * 100

20. **price_spread_percent**: Price spread as a percentage of the current price
    - Data representation: Volatility measure showing the range size relative to current price
    - How obtained: Calculated as ((highPrice - lowPrice) / lastPrice) * 100

21. **openPrice_spread_percent**: Price spread as a percentage of the opening price
    - Data representation: Volatility measure showing the range size relative to opening price
    - How obtained: Calculated as ((highPrice - lowPrice) / openPrice) * 100

## Kline Data Columns (from sandbox_binance_klines_* tables)
22. **price_volatility_{interval}**: Price volatility measure for the specific time interval
    - Data representation: Average of absolute percentage changes between consecutive bars
    - How obtained: Calculated from kline data as average of |(close[i] - close[i-1]) / close[i-1]| for all bars

23. **volume_anomaly_{interval}**: Volume anomaly score for the specific time interval
    - Data representation: Z-score indicating if volume is unusually high or low
    - How obtained: Calculated as the z-score of total volume compared to other trading pairs

24. **reason_{interval}**: Anomaly reason for the specific time interval
    - Data representation: Text description of detected anomalies (e.g., "Yüksek volatilite")
    - How obtained: Generated based on volatility and volume thresholds

## Combined Analysis Columns
25. **reason_combined**: Combined reasons for all detected anomalies
    - Data representation: Concatenated text of all anomaly reasons across different data sources
    - How obtained: Dynamically built by combining reasons from funding rates, klines, and price position

26. **total_score**: Overall risk/index score combining all factors
    - Data representation: Composite score indicating overall market activity level
    - How obtained: Calculated as sum of weighted factors including price change, spread volatility, kline volatility, volume anomalies, funding pressure, and extreme positions

27. **anomaly_score**: Weighted anomaly score highlighting unusual market behavior
    - Data representation: Composite score focusing on anomalous activity with specific weights
    - How obtained: Calculated as weighted sum of kline volatility, volume anomalies, price change, spread volatility, and funding rate scores
