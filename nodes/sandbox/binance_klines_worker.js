const { parentPort, workerData } = require("worker_threads");
const axios = require('axios');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';

async function getKlineData(symbol, interval = '1h', limit = 24) {
    try {
        const url = `${BINANCE_FUTURES_API_URL}/fapi/v1/klines`;
        const response = await axios.get(url, {
            params: { symbol, interval: interval, limit: limit }
        });
        return response.data;
    } catch (error) {
        console.error(`K-line verisi hatası (${symbol}):`, error.message);
        return null;
    }
}
// Worker thread message handler
parentPort.on("message", async (data) => {
    const { pairs, interval, limit } = data;
    const result = {};
    
    for (const pair of pairs) {
        try {
            let historicData = await getKlineData(pair, interval, limit);
            result[pair] = historicData;
        } catch (error) {
            console.error(`<PERSON><PERSON>: ${pair} i<PERSON>in veri <PERSON>. Hata: ${error.message}`);
            result[pair] = null;
        }
    }
    
    // Send result back to main thread
    parentPort.postMessage({
        success: true,
        data: result
    });
});
