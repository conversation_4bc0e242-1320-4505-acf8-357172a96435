const Database = require('better-sqlite3');
const path = require('path');

const getAnomalyList = async (targetIntervals) => {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  
  // Dynamically build the SELECT clause with interval-specific columns
  let selectColumns = `
      m.symbol,
      m.priceChange,
      m.priceChangePercent,
      m.weightedAvgPrice,
      m.lastPrice,
      m.lastQty,
      m.openPrice,
      m.highPrice,
      m.lowPrice,
      m.volume,
      m.quoteVolume,
      m.openTime,
      m.closeTime,
      m.firstId,
      m.lastId,
      m.count,
      m.created_at, 
      -- Funding Rate verileri
      f.funding_rate_score, 
      -- 24s aralığı içindeki pozisyon yüzdesi
      CASE 
          WHEN (m.highPrice - m.lowPrice) != 0 THEN 
              ROUND((m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) * 100, 2)
          ELSE 0.5 -- Hareketsizse (eşit), ortada kabul et
      END AS range_position_percent, 
      -- Fiyat yayılımı yüzdesi: (high - low) / lastPrice * 100
      ROUND((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice, 2) AS price_spread_percent, 
      -- Fiyat değişim skoru: |priceChangePercent| / 5
      ROUND(ABS(m.priceChangePercent) * 1.0 / 5.0, 4) AS priceChangeScore, 
      -- Yayılım volatilite skoru: priceSpreadPercent / 3
      ROUND((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0, 4) AS spread_volatility_score
  `;
  
  // Dynamically build the JOIN clauses for each interval
  let joinClauses = `
      FROM 
          sandbox_binance_market m
      LEFT JOIN 
          sandbox_binance_fundingrates f 
          ON m.symbol = f.symbol 
  `;
  
  // Add klines data for each interval
  const klineColumns = [];
  for (const interval of targetIntervals) {
    const table = `sandbox_binance_klines_${interval}`;
    const alias = `k_${interval}`;
    
    // Add columns for this interval
    klineColumns.push(
      `${alias}.price_volatility AS price_volatility_${interval}`,
      `${alias}.volume_anomaly AS volume_anomaly_${interval}`,
      `${alias}.reason AS reason_${interval}`
    );
    
    // Add JOIN clause for this interval
    joinClauses += `
      LEFT JOIN 
          ${table} ${alias} 
          ON m.symbol = ${alias}.symbol 
    `;
  }
  
  // Add kline columns to select
  if (klineColumns.length > 0) {
    selectColumns += `,
      -- Kline verileri
      ${klineColumns.join(',\n      ')}`;
  }
  
  // Build the reason_combined field dynamically
  let reasonCombinedParts = [];
  for (const interval of targetIntervals) {
    const alias = `k_${interval}`;
    reasonCombinedParts.push(`COALESCE(CASE 
                                      WHEN ${alias}.reason IS NOT NULL AND LOWER(${alias}.reason) != 'normal' 
                                      THEN ${alias}.reason || ', ' 
                                      ELSE '' 
                                  END, '')`);
  }
  
  // Add funding rate reason
  reasonCombinedParts.push(`COALESCE(CASE 
                                      WHEN f.reason IS NOT NULL AND LOWER(f.reason) != 'normal' 
                                      THEN f.reason || ', ' 
                                      ELSE '' 
                                  END, '')`);
  
  // Add range position reason
  reasonCombinedParts.push(`CASE 
                                      WHEN (m.highPrice - m.lowPrice) = 0 THEN ''
                                      WHEN (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) > 0.95 
                                          THEN '24s Aralığının Sınırında (Yüksek), '
                                      WHEN (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) < 0.05 
                                          THEN '24s Aralığının Sınırında (Düşük), '
                                      ELSE ''
                                  END`);
  
  const reasonCombinedField = `
      -- Birleştirilmiş neden (reason_combined): sadece anormal olanlar birleştirilir
      COALESCE(
          NULLIF(
              RTRIM(
                  ${reasonCombinedParts.join(' ||\n                  ')}
                  ,
                  ', '
              ),
              ''
          ),
          'normal'
      ) AS reason_combined`;
  
  selectColumns += `,\n${reasonCombinedField}`;
  
  // Build the total_score calculation dynamically
  let totalScoreParts = [
    "(ABS(m.priceChangePercent) / 5.0)",  // Fiyat değişimi
    "((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0)"  // Spread volatilite
  ];
  
  // Add klines scores for each interval
  for (const interval of targetIntervals) {
    const alias = `k_${interval}`;
    totalScoreParts.push(`COALESCE(${alias}.price_volatility, 0)`);  // Kline volatilite
    totalScoreParts.push(`(CASE WHEN ${alias}.volume_anomaly THEN 1.0 ELSE 0.0 END)`);  // Hacim anomalisi
  }
  
  // Add funding rate score
  totalScoreParts.push("(ABS(COALESCE(f.funding_rate_score, 0)) * 1000)");  // Funding baskısı
  
  // Add range position score
  totalScoreParts.push(`(CASE 
                              WHEN (m.highPrice - m.lowPrice) != 0 AND 
                                  ((m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) > 0.95 OR 
                                    (m.lastPrice - m.lowPrice) * 1.0 / (m.highPrice - m.lowPrice) < 0.05)
                              THEN 2.0 
                              ELSE 0.0 
                          END)`);  // Uç pozisyon
  
  const totalScoreField = `
      -- Toplam Risk/Endeks Skoru
      ROUND(
          ${totalScoreParts.join(' +\n                          ')},
          2
      ) AS total_score`;
  
  selectColumns += `,\n${totalScoreField}`;
  
  // Build the anomaly_score calculation dynamically
  let anomalyScoreParts = [];
  
  // Add klines scores for each interval (with weights)
  for (const interval of targetIntervals) {
    const alias = `k_${interval}`;
    anomalyScoreParts.push(`COALESCE(${alias}.price_volatility, 0) * 2.0`);  // Kline volatilite
    anomalyScoreParts.push(`(CASE WHEN ${alias}.volume_anomaly THEN 1.0 ELSE 0.0 END) * 2.0`);  // Hacim anomalisi
  }
  
  // Add price change score (with weight)
  anomalyScoreParts.push(`(ABS(m.priceChangePercent) / 5.0) * 3.5`);
  
  // Add spread volatility score (with weight)
  anomalyScoreParts.push(`((m.highPrice - m.lowPrice) * 100.0 / m.lastPrice / 3.0) * 3.5`);
  
  // Add funding rate score (with weight)
  anomalyScoreParts.push(`ABS(COALESCE(f.funding_rate_score, 0)) * 5.0`);
  
  const anomalyScoreField = `
      -- Ağırlıklı Anomali Skoru
      ROUND(
          ${anomalyScoreParts.join(' +\n                          ')},
          2
      ) AS anomaly_score`;
  
  selectColumns += `,\n${anomalyScoreField}`;
  
  // Build the final query
  const query_sql = `
              SELECT *
              FROM (
                  SELECT 
                      ${selectColumns}
                  ${joinClauses}
              ) ax
              WHERE 1 = 1
                -- AND reason_combined NOT IN ('normal')
              ORDER BY anomaly_score DESC;
              `;
  
  console.log('Generated SQL Query:');
  console.log(query_sql);
  
  const query = db.prepare(query_sql);
  const result = query.all();
  db.close();
  return result;
}

// Test function with different interval combinations
async function testParametricQuery() {
  console.log('Testing parametric query with different interval combinations...\n');
  
  // Test with single interval
  console.log('1. Testing with single interval [1h]:');
  try {
    const result1 = await getAnomalyList(['1h']);
    console.log(`   Query executed successfully. Returned ${result1.length} rows.\n`);
  } catch (error) {
    console.log(`   Error: ${error.message}\n`);
  }
  
  // Test with multiple intervals
  console.log('2. Testing with multiple intervals [1d, 1h, 1m]:');
  try {
    const result2 = await getAnomalyList(['1d', '1h', '1m']);
    console.log(`   Query executed successfully. Returned ${result2.length} rows.\n`);
  } catch (error) {
    console.log(`   Error: ${error.message}\n`);
  }
  
  // Test with different interval combination
  console.log('3. Testing with different intervals [4h, 15m]:');
  try {
    const result3 = await getAnomalyList(['4h', '15m']);
    console.log(`   Query executed successfully. Returned ${result3.length} rows.\n`);
  } catch (error) {
    console.log(`   Error: ${error.message}\n`);
  }
  
  console.log('Testing completed.');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testParametricQuery();
}

module.exports = { getAnomalyList };
