# Binance Anomaly Detection System

## Parametric SQL Query

The anomaly detection system now uses a parametric SQL query that can dynamically join multiple klines tables based on the provided time intervals. This allows for more flexible analysis of market data across different timeframes.

### How It Works

The `getAnomalyList` function in `binance_anomaly.js` and `anomaly_sql.js` accepts a `targetIntervals` parameter which is an array of time interval strings (e.g., `['1d', '1h', '1m']`).

For each interval in the array:
1. The function dynamically generates JOIN clauses to connect with the corresponding klines table (`sandbox_binance_klines_{interval}`)
2. Columns from each klines table are aliased with the interval suffix (e.g., `price_volatility_1h`, `volume_anomaly_1h`)
3. The scoring algorithms are updated to include data from all specified intervals

### Usage Examples

```javascript
// Single interval
const anomalies1h = await getAnomalyList(['1h']);

// Multiple intervals
const anomaliesMulti = await getAnomalyList(['1d', '4h', '1h', '15m']);

// Different combination
const anomaliesShort = await getAnomalyList(['5m', '1m']);
```

### Testing

You can test the parametric query generation with:
```bash
node nodes/sandbox/anomaly_sql.js
```

This will show the generated SQL queries for different interval combinations.

### Column Naming Convention

All klines-related columns are suffixed with the interval name:
- `price_volatility_{interval}` - Volatility score for the specific interval
- `volume_anomaly_{interval}` - Volume anomaly flag for the specific interval
- `reason_{interval}` - Anomaly reason for the specific interval

### Scoring Algorithm

The total score and anomaly score calculations now incorporate data from all specified intervals, with appropriate weighting for each metric.
