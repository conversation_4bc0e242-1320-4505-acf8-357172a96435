// app.js

const Database = require('better-sqlite3');
const path = require('path');
const axios = require('axios');
const fs = require('fs');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';
async function getTargetPairs() {
  //db/gauss.db
  const pth = path.resolve(__dirname, '../../db/gauss.db');
  console.log('dbPath', pth);
  const db = new Database(pth);
  const q = `
    SELECT dataName, dataValues, dtCreated, is_deleted
    FROM sandbox_datax
    where dataName = 'pairs'
    order by dtCreated desc
    limit 1;
  `
  const query = db.prepare(q);
  const pairs = query.all();
  let resp = [];
  try {
    resp = JSON.parse(pairs[0]?.dataValues) || [];
  } catch (e) {
    console.log('e', e)
    resp = [];
  }
  return resp;
}

async function getmarketData(targetpairs) {
  // const pairs = ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "ALPINEUSDT", "DOGEUSDT"];
  const apiUrl = BINANCE_FUTURES_API_URL + "/fapi/v1/ticker/24hr";

  // console.log("fonlama oranları çekiliyor...");
  
  let allFundingInfo = [];
    try {
      const response = await axios.get(apiUrl, {});

      allFundingInfo = response.data;
      // Gelen veriler arasından, istediğimiz sembolleri filtreliyoruz.
      const filteredFundingInfo = allFundingInfo.filter(item =>
        targetpairs.includes(item.symbol)
      );

      // API'den dönen veriyi allFundingInfo dizisine ekle
      allFundingInfo = filteredFundingInfo;

      console.log(`market verileri başarıyla çekildi.`);
      
    } catch (error) {
      console.error(`Hata oluştu: veri çekilemedi. Hata: ${error.message}`);
    }
  // Fonksiyonun sonunda tüm verileri döndür
  return allFundingInfo;
}

// sample response: 
// [
//     {
//       "symbol": "UNIUSDT",
//       "priceChange": "0.4120",
//       "priceChangePercent": "3.899",
//       "weightedAvgPrice": "10.9109",
//       "lastPrice": "10.9790",
//       "lastQty": "2",
//       "openPrice": "10.5670",
//       "highPrice": "11.1530",
//       "lowPrice": "10.5250",
//       "volume": "23499364",
//       "quoteVolume": "256398337.8080",
//       "openTime": 1755287400000,
//       "closeTime": 1755373815435,
//       "firstId": 702789463,
//       "lastId": 703448749,
//       "count": 659035
//     }
const save2db = async (history) => {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  const tableName = 'sandbox_binance_market';
  let sessionID = Date.now();
  const q = `
    CREATE TABLE IF NOT EXISTS ${tableName} (
      symbol TEXT PRIMARY KEY,
      priceChange TEXT,
      priceChangePercent TEXT,
      weightedAvgPrice TEXT,
      lastPrice TEXT,
      lastQty TEXT,
      openPrice TEXT,
      highPrice TEXT,
      lowPrice TEXT,
      volume TEXT,
      quoteVolume TEXT,
      openTime INTEGER,
      closeTime INTEGER,
      firstId INTEGER,
      lastId INTEGER,
      count INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `;
  const query = db.prepare(q);
  query.run();

  for (const item of history) {
    const q = `INSERT OR REPLACE INTO ${tableName} (
      symbol, priceChange, priceChangePercent, weightedAvgPrice, 
      lastPrice, lastQty, openPrice, highPrice, lowPrice, 
      volume, quoteVolume, openTime, closeTime, firstId, lastId, count
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
    const query = db.prepare(q);
    query.run(
      item.symbol,
      item.priceChange,
      item.priceChangePercent,
      item.weightedAvgPrice,
      item.lastPrice,
      item.lastQty,
      item.openPrice,
      item.highPrice,
      item.lowPrice,
      item.volume,
      item.quoteVolume,
      item.openTime,
      item.closeTime,
      item.firstId,
      item.lastId,
      item.count
    );
  }
  db.close();
}

(async () => {
  // const targetPairsStg = await getTargetPairs();
  let dtBOP = Date.now();
  const targetPairs = await getTargetPairs();
  console.log ('targetPairs', targetPairs.length);
  const history = await getmarketData(targetPairs);
  await save2db(history);
  fs.writeFileSync('bin/binance_market.json', JSON.stringify({
    targetPairs,
    // fundingInfo: data,
    history: history,
    elapsed: Date.now() - dtBOP,
  }, null, 2));
  console.log("Veriler binance_market.json dosyasına kaydedildi.");
})();
