// app.js

const Database = require('better-sqlite3');
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const os = require('os');
const { Worker } = require('worker_threads');

// Binance Futures API URL
const BINANCE_FUTURES_API_URL = 'https://fapi.binance.com';
async function getTargetPairs() {
  //db/gauss.db
  const pth = path.resolve(__dirname, '../../db/gauss.db');
  // console.log('dbPath', pth);
  const db = new Database(pth);
  const q = `
    SELECT dataName, dataValues, dtCreated, is_deleted
    FROM sandbox_datax
    where dataName = 'pairs'
    order by dtCreated desc
    limit 1;
  `
  const query = db.prepare(q);
  const pairs = query.all();
  let resp = [];
  try {
    resp = JSON.parse(pairs[0]?.dataValues) || [];
  } catch (e) {
    console.log('e', e)
    resp = [];
  }
  return resp;
}

async function getFundingRates(targetpairs) {
  // const pairs = ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "ALPINEUSDT", "DOGEUSDT"];
  const apiUrl = BINANCE_FUTURES_API_URL + "/fapi/v1/fundingInfo";

  // console.log("fonlama oranları çekiliyor...");
  
  let allFundingInfo = [];
    try {
      const response = await axios.get(apiUrl, {
        params: {
          limit: 999
        }
      });

      allFundingInfo = response.data;
      // Gelen veriler arasından, istediğimiz sembolleri filtreliyoruz.
      const filteredFundingInfo = allFundingInfo.filter(item =>
        targetpairs.includes(item.symbol)
      );

      // API'den dönen veriyi allFundingInfo dizisine ekle
      allFundingInfo = filteredFundingInfo;

      console.log(`fonlama oranları başarıyla çekildi.`);
      
    } catch (error) {
      console.error(`Hata oluştu: veri çekilemedi. Hata: ${error.message}`);
    }
  // Fonksiyonun sonunda tüm verileri döndür
  return allFundingInfo;
}

// Fonlama oranı verisi çek
async function getFundingRateHistory(symbol) {
    try {
        const url = `${BINANCE_FUTURES_API_URL}/fapi/v1/fundingRate`;
        const response = await axios.get(url, {
            params: { symbol, limit: 24 }
        });
        return response.data;
    } catch (error) {
        console.error(`Fonlama oranı hatası (${symbol}):`, error.message);
        return null;
    }
}

async function getHistoricalFundingRates(targetpairs) {
  // Veri çekmek istediğimiz semboller
  const pairs = targetpairs || ["BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT", "ADAUSDT", "DOGEUSDT"];
  
  // CPU çekirdek sayısını bul
  const cpuCount = os.cpus().length;
  console.log(`Sistemdeki CPU çekirdek sayısı: ${cpuCount}`);
  
  // İşlem parçalarını oluştur
  const chunkSize = Math.ceil(pairs.length / cpuCount);
  const chunks = [];
  for (let i = 0; i < pairs.length; i += chunkSize) {
    chunks.push(pairs.slice(i, i + chunkSize));
  }
  
  // Worker'ları başlat ve sonuçları topla
  const results = {};
  
  return new Promise((resolve, reject) => {
    let completedWorkers = 0;
    
    // Her bir chunk için worker oluştur
    chunks.forEach((chunk, index) => {
      const worker = new Worker(__dirname + '/binance_fundingrates_worker.js');
      
      worker.on('message', (result) => {
        if (result.success) {
          // Sonuçları birleştir
          Object.assign(results, result.data);
          completedWorkers++;
          
          // Tüm worker'lar tamamlandıysa işlemi bitir
          if (completedWorkers === chunks.length) {
            resolve(results);
          }
        } else {
          reject(new Error('Worker hatası: ' + result.error));
        }
        
        // Worker'ı kapat
        worker.terminate();
      });
      
      worker.on('error', (error) => {
        reject(new Error('Worker başlatma hatası: ' + error.message));
      });
      
      // Worker'a veri gönder
      worker.postMessage({ pairs: chunk });
    });
    
    // Eğer hiç pair yoksa hemen sonuç döndür
    if (chunks.length === 0) {
      resolve({});
    }
  });
}

// İstatistik hesapla
function calculateStats(data) {
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    return { mean, stdDev };
}

function calculateZScore(value, mean, stdDev) {
    return stdDev === 0 ? 0 : Math.abs((value - mean) / stdDev);
}

async function save2db(history) {
  const dbPath = path.resolve(__dirname, '../../db/gauss.db');
  const db = new Database(dbPath);
  const tableName = 'sandbox_binance_fundingrates';
  let sessionID = Date.now();
  const q = `
    CREATE TABLE IF NOT EXISTS ${tableName} (
      symbol TEXT PRIMARY KEY,
      data TEXT,
      funding_rate_score float,
      reason TEXT,
      --sessionID bigint,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `;
  const query = db.prepare(q);
  query.run();

  const allFundingChanges = Object.values(history).map(rates => {
    if (rates.length < 2) return 0;
    const first = parseFloat(rates[0].fundingRate);
    const last = parseFloat(rates[rates.length - 1].fundingRate);
    return Math.abs(last - first);
  });
  const fundingStats = calculateStats(allFundingChanges);
  for (const symbol in history) {
    let fundingRates = history[symbol];
    // console.log('fundingRates', symbol, fundingRates);
    const fundingChange = fundingRates.length >= 2
      ? Math.abs(parseFloat(fundingRates[fundingRates.length - 1].fundingRate) - parseFloat(fundingRates[0].fundingRate))
      : 0;
    const fundingZ = calculateZScore(fundingChange, fundingStats.mean, fundingStats.stdDev);
    const reasons = [];
    if (fundingZ > 2.0) reasons.push('Anormal fonlama oranı');
    const reasonText = reasons.length > 0 ? reasons.join(' ve ') + ' nedeniyle.' : 'Normal';
    const q = `INSERT OR REPLACE INTO ${tableName} (symbol, data, funding_rate_score, reason) VALUES (?, ?, ?, ?)`;
    const query = db.prepare(q);
    query.run(symbol, JSON.stringify(fundingRates), fundingZ, reasonText);
  }
  db.close();
}   

(async () => {
  // const targetPairsStg = await getTargetPairs();
  let dtBOP = Date.now();
  const targetPairs = await getTargetPairs();
  console.log ('targetPairs', targetPairs.length);
  // const data = await getFundingRates(targetPairs);
  const history = await getHistoricalFundingRates(targetPairs);
  // Verileri fundingrates.json dosyasına da kaydet
  await save2db(history);
  fs.writeFileSync('bin/binance_fundingrates.json', JSON.stringify({
    targetPairs,
    // fundingInfo: data,
    history: history,
    elapsed: Date.now() - dtBOP,
  }, null, 2));
  console.log("Veriler fundingrates.json dosyasına kaydedildi.");
})();
