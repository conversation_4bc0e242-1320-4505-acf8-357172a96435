const technicalIndicators = require('technicalindicators');
technicalIndicators.setConfig('precision', 6);
const { PSAR, OBV, CCI, WMA, SMA, RSI, ATR, EMA, CrossUp, CrossDown,
    ROC, ADX, AwesomeOscillator, IchimokuCloud, MACD, StochasticRSI, Stochastic, ChandelierExit,
} = technicalIndicators;

const Binance = require('node-binance-api'); 

const {
    abandonedbaby, bearishengulfingpattern, bullishengulfingpattern,
    darkcloudcover, downsidetasukigap, doji, dragonflydoji, gravestonedoji,
    bullishharami, bearishharami, bearishharamicross, bullishharamicross, bullishmarubozu,
    bearishmarubozu, eveningdojistar, eveningstar, piercingline,
    bullishspinningtop, bearishspinningtop, morningdojistar, morningstar,
    threeblackcrows, threewhitesoldiers, tweezertop, tweezerbottom, shootingstar, shootingstarunconfirmed,
    hangingman, hangingmanunconfirmed, hammerpattern, hammerpatternunconfirmed,
} = technicalIndicators;

// const calculateSTR = require('./tsIndicators/supertrendHA');
var Stock = require("stock-technical-indicators")
const { Supertrend } = require("stock-technical-indicators/study/Supertrend")
// const { ce } = require('indicatorts');
const { bb } = require('indicatorts');
const fnx = require('./_.functions');
const fnxDex = require('./_.functions.dex.js');
const tsIx = require('./tsIndicators/index')
const battleRulesets = require('../src/lib/battle.rulesets');
const battleRulesetParams = require('../src/lib/battle.ruleset.params');
const battleExchangeInfo = require('../src/lib/battle.exchangeinfo.json');
const Redis = require('ioredis');
const redixPrefix = require('../src/lib/redis.prefix.js');
const commissionRate = 0.0007;
const commissionRate_LIMIT = 0.0002;
const trade = {
    target: {
        fetchBars: 100,
    },
    indicators: {
        arrSlice: 6,
        lHistory: -5,
    }
};
const redisClientGlobal = new Redis({
    host: '127.0.0.1',
    port: 6379,
});
//CHECK:: Track onhold statu...
//CANCEL:: RESTART Butonu watch ve trader kısmındaki slidepanel e ekle!
//DONE:: Websocket conn ayarla, live feed et gui yi update lere göre
//DONE:: Bar Close icinde strategy check!
//TODO : market ticker ile trade ekranını guncelle, listede olmayan ama acık poz u olan pair leri güncellemek icin...
//TODO:: daily delta icin fetch fail ederse indir tekrar!
//DONE:: fethcinitial data conn time out soluition....
//DONE:: Wallet check burada!
//DONE:: http://localhost:3012/action/watch ekranına restart node butonu ekle!
//DONE:: additional TF Kline datasını ana frame tamamlandıktan sonra fetch etmeye basla!

//TODO:: FF2 -> open modal and show all active nodes with check submit and kill node. netstat -nlp | grep :8080 https://stackoverflow.com/questions/14790910/stop-all-instances-of-node-js-server
//DONE:: wallet check icin gerekli - await fnxSimulator.updateStats(payLoad)

//DONE: 2.Haz -> close all trades,
//DONE: 2.Haz -> close selected trades -> GUI de button, TV butonu da ekle.
//DONE: 2.Haz -> check unrealalized PNL Percentage.
//DONE: 2.Haz -> list all trades from battleNodes... -> GUI Button.

//DONE:: open trade varsa yonu, stratejideki yönden farklı ise fail!
//DONE:: ya birden fazla match ve farklı direction ise ? first long!
//DONE:: position budget < positionmax budget olmalı!!
//DONE:: aktif poz var mı yok mu kontrolu vs. addAdditionPosition -> done!
//DONE:: sonraki candle interval kontrolu. max budget kontrolu.
//DONE:: Check strategy response for direction (long short ?) yoksa da dahil et. and match with entry params //this is for bi-direction mode!
//DONE:: for multi ruleset success -> sort by weight. take the first one!
 

const calcIndicator = (exports.calcIndicator = {
    ema: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data)  && data.length !== 0 ) {
                    const period = length;
                    const lastBar = data.slice(-1)[0] ? data.slice(-1)[0] : {};
                    const { symbol, interval, openTime, openTimeHRF, closeTime, close } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_ema_' + length
                    var emaArr = EMA.calculate({ period: parseInt(period), values });

                    var calculatedEMA = emaArr.slice(-1)[0];
                    var emaTrendStg = [...emaArr].slice(-4);
                    var emaTrendTxt = subFn.fnTrend(emaTrendStg);
                    var emaTrendUp = emaTrendTxt.every(res => res == 'Up')
                    var emaTrendDown = emaTrendTxt.every(res => res == 'Down')

                    var emaTrend = emaTrendUp ? 'Up' : emaTrendDown ? 'Down' : 'Irregular'

                    var emaCrossUp = CrossUp.calculate({ lineA: emaArr.slice(-1 * trade.indicators.arrSlice), lineB: values.slice(-1 * trade.indicators.arrSlice) }).slice(-1).pop();
                    var emaCrossDown = CrossDown.calculate({ lineA: emaArr.slice(-1 * trade.indicators.arrSlice), lineB: values.slice(-1 * trade.indicators.arrSlice) }).slice(-1).pop();
                    var emaPosition = emaArr.slice(-1)[0] > Number(lastBar[source]) ? 'above' : 'below'
                    var emaDistanceR = parseFloat(parseFloat(Math.abs(calculatedEMA - Number(lastBar[source])) / calculatedEMA * 100).toFixed(3));

                    const indicatorAdditionalData = {
                        emaTrend,
                        emaTrendTxt,
                        emaCrossUp,
                        emaCrossDown,
                        emaPosition,
                        emaDistanceR,
                        emaArr: [...emaArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: emaArr.slice(-1)[0],
                    }
                    // fnx.log('data', data.slice(-1), values.slice(-3), keyTag, params, emaArr.slice(-7));
                    let respx = {
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        closeTime,
                        indicatorValue: emaArr.slice(-1)[0],
                        indicatorAdditionalData,
                    };

                    if (props.mode == 'backtest') {
                        // delete respx.symbol;
                        // delete respx.interval;
                        delete respx.indicatorParams;
                        // delete respx.openTime;
                        // delete respx.openTimeHRF;
                    }

                    resolve(respx);
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error ema', e);
                reject(false)
            }
        });
    },
    sma: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime, close } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_sma_' + length
                    var smaArr = SMA.calculate({ period: parseInt(period), values });

                    var calculatedSMA = smaArr.slice(-1)[0];
                    var smaTrendStg = [...smaArr].slice(-4);
                    var smaTrendTxt = subFn.fnTrend(smaTrendStg);
                    var smaTrendUp = smaTrendTxt.every(res => res == 'Up')
                    var smaTrendDown = smaTrendTxt.every(res => res == 'Down')

                    var smaTrend = smaTrendUp ? 'Up' : smaTrendDown ? 'Down' : 'Irregular'

                    var smaCrossUp = CrossUp.calculate({ lineA: smaArr.slice(-1 * trade.indicators.arrSlice), lineB: values.slice(-1 * trade.indicators.arrSlice) }).slice(-1).pop();
                    var smaCrossDown = CrossDown.calculate({ lineA: smaArr.slice(-1 * trade.indicators.arrSlice), lineB: values.slice(-1 * trade.indicators.arrSlice) }).slice(-1).pop();
                    var smaPosition = smaArr.slice(-1)[0] > Number(lastBar[source]) ? 'above' : 'below'
                    var smaDistanceR = parseFloat(parseFloat(Math.abs(calculatedSMA - Number(lastBar[source])) / calculatedSMA * 100).toFixed(3));

                    const indicatorAdditionalData = {
                        smaTrend,
                        smaTrendTxt,
                        smaCrossUp,
                        smaCrossDown,
                        smaPosition,
                        smaDistanceR,
                        smaArr: [...smaArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: smaArr.slice(-1)[0],
                    }
                    // fnx.log('data', data.slice(-1), values.slice(-3), keyTag, params, smaArr.slice(-7));
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: smaArr.slice(-1)[0],
                        indicatorAdditionalData,
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error sma', e);
                reject(false)
            }
        });
    },
    wma: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime, close } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_wma_' + length
                    var wmaArr = WMA.calculate({ period: parseInt(period), values });
                    var calculatedWMA = wmaArr.slice(-1)[0];
                    var wmaTrendStg = [...wmaArr].slice(-4);
                    var wmaTrendTxt = subFn.fnTrend(wmaTrendStg);
                    var wmaTrendUp = wmaTrendTxt.every(res => res == 'Up')
                    var wmaTrendDown = wmaTrendTxt.every(res => res == 'Down')
                    var wmaTrend = wmaTrendUp ? 'Up' : wmaTrendDown ? 'Down' : 'Irregular'
                    var wmaCrossUp = CrossUp.calculate({ lineA: wmaArr.slice(-1 * trade.indicators.arrSlice), lineB: values.slice(-1 * trade.indicators.arrSlice) }).slice(-1).pop();
                    var wmaCrossDown = CrossDown.calculate({ lineA: wmaArr.slice(-1 * trade.indicators.arrSlice), lineB: values.slice(-1 * trade.indicators.arrSlice) }).slice(-1).pop();
                    var wmaPosition = wmaArr.slice(-1)[0] > Number(lastBar[source]) ? 'above' : 'below'
                    var wmaDistanceR = parseFloat(parseFloat(Math.abs(calculatedWMA - Number(lastBar[source])) / calculatedWMA * 100).toFixed(3));
                    const indicatorAdditionalData = {
                        wmaTrend,
                        wmaTrendTxt,
                        wmaCrossUp,
                        wmaCrossDown,
                        wmaPosition,
                        wmaDistanceR,
                        wmaArr: [...wmaArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: wmaArr.slice(-1)[0],
                    }
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: wmaArr.slice(-1)[0],
                        indicatorAdditionalData,
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error wma', e);
                reject(false)
            }
        });
    },
    rsi: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, upperBand, middleBand, lowerBand, rsiSMAFastPeriod, rsiSMASlowPeriod } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = Array.isArray(data) ? data.slice(-1)[0] : {};
                    if (lastBar) {
                        try {
                            const { symbol, interval, openTime, openTimeHRF, closeTime, close } = lastBar;
                            const values = data.map(d => Number(d[source]));
                            const indicatorTag = keyTag + '_rsi_' + length;
                            const rsiArr = RSI.calculate({ period, values, reversedInput: false })
                            var calculatedRSI = [...rsiArr].slice(-1)[0];
                            const rsiSMAFast = SMA.calculate({ period: parseInt(rsiSMAFastPeriod), values: [...rsiArr] });
                            const rsiSMASlow = SMA.calculate({ period: parseInt(rsiSMASlowPeriod), values: [...rsiArr] });
                            var rsiCrossUpInput = {
                                lineA: [...rsiArr].slice(-15),
                                lineB: [...rsiSMAFast].slice(-15),
                            };
                            var rsiCrossUpFast = CrossUp.calculate(rsiCrossUpInput);
                            const indicatorAdditionalData = {
                                rsiSMAFast: [...rsiSMAFast].slice(-1).pop(),
                                rsiSMASlow: [...rsiSMASlow].slice(-1).pop(),
                                rsiCrossUpFast: [...rsiCrossUpFast].slice(-1).pop(),
                                rsiArr: [...rsiArr].slice(trade.indicators.lHistory),
                                sourceValue: Number(lastBar[source]),
                                indicatorValue: calculatedRSI,
                            }
                            let respx = {
                                symbol,
                                indicatorTag,
                                indicatorParams: JSON.stringify(params),
                                interval,
                                openTime,
                                openTimeHRF,
                                indicatorValue: calculatedRSI,
                                indicatorAdditionalData,
                            };

                            if (props.mode == 'backtest') {
                                delete respx.symbol;
                                delete respx.interval;
                                delete respx.indicatorParams;
                                delete respx.openTime;
                                delete respx.openTimeHRF;
                            }

                            resolve(respx);
                        }
                        catch (eC) {
                            console.log('error rsi', eC, lastBar);
                            // resolve({})
                            reject(false)
                        }
                    } else {
                        // console.log('no arr rsi');
                        // resolve({})
                        reject(false)
                    }
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error rsi', e);
                reject(false)
            }
        });
    },
    cci: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, SMAfastPeriod, levelHigh, levelLow } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime, } = lastBar;
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));

                    const indicatorTag = keyTag + '_cci_' + length
                    const cciArr = CCI.calculate({ period, low, high, close, });
                    var calculatedCCI = [...cciArr].slice(-1)[0];

                    const cciSMAFast = SMA.calculate({ period: parseInt(SMAfastPeriod), values: [...cciArr] });

                    var cciCrossUpInput = {
                        lineA: [...cciArr].slice(-15),
                        lineB: [...cciSMAFast].slice(-15),
                    };
                    var cciSMACrossUp = CrossUp.calculate(cciCrossUpInput)

                    var crossOver100pArrInput = {
                        lineA: [...cciArr].slice(-15),
                        lineB: new Array(15).fill(levelHigh)
                    }
                    var crossOver100nArrInput = {
                        lineA: [...cciArr].slice(-15),
                        lineB: new Array(15).fill(levelLow)
                    }
                    var crossOver100pArr = CrossUp.calculate(crossOver100pArrInput)
                    var crossOver100nArr = CrossUp.calculate(crossOver100nArrInput)
                    const indicatorAdditionalData = {
                        cci: calculatedCCI,
                        crossUpLevels: [...crossOver100pArr].slice(-1)[0] || [...crossOver100nArr].slice(-1)[0],
                        crossUpSMA: [...cciSMACrossUp].slice(-1)[0],
                        cciIsOverSMA: calculatedCCI > [...cciSMAFast].slice(-1)[0],
                        SMA: [...cciSMAFast].slice(-1)[0],
                        SMAtrendIsUp: subFn.fnTrend([...cciSMAFast].slice(-2)).toString() == 'Up',
                        cciArr: [...cciArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedCCI,
                    }
                    let respx = {
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        closeTime,
                        indicatorValue: calculatedCCI,
                        indicatorAdditionalData,
                    };

                    if (props.mode == 'backtest') {
                        // delete respx.symbol;
                        // delete respx.interval;
                        delete respx.indicatorParams; 
                        // delete respx.openTime;
                        // delete respx.openTimeHRF;
                    }
                    
                    resolve(respx);
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error cci', e);
                reject(false)
            }
        });
    },
    atr: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_atr_' + length
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));

                    const atrArr = ATR.calculate({ period: period, close, high, low, });
                    const calculatedATR = [...atrArr].slice(-1)[0];
                    const atrvsCloseR = parseFloat((calculatedATR / lastBar['close'] * 100).toFixed(4))

                    const indicatorAdditionalData = {
                        atr: calculatedATR,
                        atrVsClosePerc: atrvsCloseR,
                        atrArr: [...atrArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedATR,
                        openTime: openTimeHRF,
                    };

                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedATR,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error atr', e);
                reject(false)
            }
        });
    },
    roc: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_roc_' + length;
                    const rocArr = ROC.calculate({ period: period, values });
                    const calculatedROC = [...rocArr].slice(-1)[0];
                    const indicatorAdditionalData = {
                        roc: calculatedROC,
                        indicatorValue: calculatedROC,
                        rocArr: [...rocArr].slice(trade.indicators.lHistory),
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedROC,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error roc', e);
                reject(false)
            }
        });
    },
    adx: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length = 14, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_adx_' + length;
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const adxArr = ADX.calculate({ period: period, close, high, low });
                    const calculatedADXStg = [...adxArr].slice(-1)[0];
                    const calculatedADX = calculatedADXStg?.adx;
                    const pdiVSmdi = calculatedADXStg ? (calculatedADXStg.pdi > calculatedADXStg.mdi ? 'above' : 'below') : '-';
                    const indicatorAdditionalData = {
                        calculatedADX: calculatedADXStg,
                        pdiPos: pdiVSmdi,
                        adxArr: [...adxArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedADX,
                        ...calculatedADXStg,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedADX,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error adx', e);
                reject(false)
            }
        });
    },
    obv: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_obv_' + length;
                    const volume = data.map(d => Number(d['volume']));
                    const close = data.map(d => Number(d['close']));
                    const obvArr = OBV.calculate({ period, close, volume });
                    const calculatedOBV = [...obvArr].slice(-1)[0];
                    const indicatorAdditionalData = {
                        obv: calculatedOBV,
                        obvArr: [...obvArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedOBV,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedOBV,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error obv', e);
                reject(false)
            }
        });
    },
    awesomeOscillator: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, fastPeriod, slowPeriod } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_ao_' + length;
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const aoArr = AwesomeOscillator.calculate({
                        period: period,
                        fastPeriod, slowPeriod, close, high,
                        low, format: (a) => parseFloat(a.toFixed(2))
                    });
                    const calculatedAO = [...aoArr].slice(-1)[0];
                    const indicatorAdditionalData = {
                        aoArr: [...aoArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedAO,
                        ao: calculatedAO,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedAO,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error ao', e);
                reject(false)
            }
        });
    },
    psar: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, step = 0.02, max = 0.2, } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    const indicatorTag = keyTag + '_psar_' + length;
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const open = data.map(d => Number(d['open']));
                    const psarArr = PSAR.calculate({ period, step, max, high, low });
                    const calculatedPSAR = [...psarArr].slice(-1)[0];
                    var psarAge = await subFn.fnTargetAgePSAR({ index: openTime, arr: [...psarArr], close, open })
                    const indicatorAdditionalData = {
                        psarArr: [...psarArr].slice(trade.indicators.lHistory),
                        psarPosition: calculatedPSAR > Math.max(...[lastBar?.open, lastBar?.close]) ? 'above' : 'below',
                        ...psarAge,
                        sourceValue: Number(lastBar[source]),
                        psar: calculatedPSAR,
                        indicatorValue: calculatedPSAR,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedPSAR,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error psar', e);
                reject(false)
            }
        });
    },
    macd: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, fastPeriod = 5, slowPeriod = 8, signalPeriod = 3, SimpleMAOscillator = false, SimpleMASignal = false } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    let sourceStg = source || 'close';
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime, closeTimeHRF,  } = lastBar ? lastBar : {};
                    const indicatorTag = keyTag + '_macd_' + length;
                    const values = data.map(d => Number(d[sourceStg]));
                    // console.log('values', values.length, values.slice(-5), data.slice(-3), source)
                    const macdArr = MACD.calculate({
                        period, values,
                        fastPeriod, slowPeriod, signalPeriod,
                        SimpleMAOscillator, SimpleMASignal
                    });
                    const calculatedMACD = [...macdArr].slice(-1)[0];

                    var calcProc = [...macdArr].slice(-10);
                    var cMACD = [];
                    var cSignal = [];
                    var cHistogram = [];
                    calcProc.map(c => {
                        cMACD.push(c.MACD);
                        cSignal.push(c.signal);
                        cHistogram.push(c.histogram);
                    })
                    var cCrossUp = CrossUp.calculate({ lineA: cMACD, lineB: cSignal }).slice(-1).pop();
                    var cCrossDown = CrossDown.calculate({ lineA: cMACD, lineB: cSignal }).slice(-1).pop();

                    var MACDPos = calculatedMACD.MACD > calculatedMACD.signal ? 'above' : 'below';


                    var sayac = 0;
                    for (let i = 1; i <= macdArr.length; i++) {
                        var izci = [...macdArr].slice(-1 * i).shift()
                        var izciPos = izci.MACD > izci.signal;
                        if (calculatedMACD.MACD > calculatedMACD.signal !== izci.MACD > izci.signal) {
                            break;
                        } else {
                            sayac++
                        }
                    };

                    const indicatorAdditionalData = {
                        macdArr: [...macdArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedMACD,
                        MACDPos,
                        MACDPosAge: sayac,
                        MaCDPosCross: cCrossUp || cCrossDown,
                        ...calculatedMACD,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF, closeTime, closeTimeHRF, 
                        indicatorValue: calculatedMACD,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error macdArr', e);
                reject(false)
            }
        });
    },
    ichimoku: async props => {
        //https://github.com/anandanand84/technicalindicators/blob/master/test/ichimoku/IchimokuCloud.js
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, conversionPeriod, basePeriod, spanPeriod, displacement } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_ichimoku_' + length;
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const ichimokuArr = IchimokuCloud.calculate({ period, high, low, conversionPeriod, basePeriod, spanPeriod, displacement });
                    const calculatedIchimokuCloud = [...ichimokuArr].slice(-1)[0];
                    const indicatorAdditionalData = {
                        ichimokuArr: [...ichimokuArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedIchimokuCloud,
                        refCandle: lastBar,
                        ...calculatedIchimokuCloud,
                    };
                    let respx = {
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        closeTime,
                        indicatorValue: calculatedIchimokuCloud,
                        indicatorAdditionalData
                    };
                    if (props.mode == 'backtest') {
                        // delete respx.symbol;
                        // delete respx.interval;
                        delete respx.indicatorParams;
                        // delete respx.openTime;
                        // delete respx.openTimeHRF;
                    }

                    resolve(respx);
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error IchimokuCloud', e);
                reject(false)
            }
        });
    },
    srsi: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, rsiPeriod = 14, stochasticPeriod = 14, kPeriod = 3, dPeriod = 3 } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    // const rsiPeriod = rsiPeriod;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_stochasticRsi_' + length;
                    const sValues = { 
                        values, 
                        rsiPeriod, 
                        stochasticPeriod, 
                        kPeriod, dPeriod };
                    // fnx.log('StochasticRSI', sValues)
                    const srsiArr = StochasticRSI.calculate(sValues);
                    const calculatedSRSI = [...srsiArr].slice(-1)[0];

                    var calcProc = [...srsiArr].slice(-10);

                    var cStochasticRSI = [];
                    var cK = [];
                    var cD = [];
                    calcProc.map(c => {
                        cStochasticRSI.push(c.stochRSI);
                        cK.push(c.k);
                        cD.push(c.d);
                    })
                    var cCrossUp = CrossUp.calculate({ lineA: cK, lineB: cD }).slice(-1).pop();
                    var cCrossDown = CrossDown.calculate({ lineA: cK, lineB: cD }).slice(-1).pop();
                    var kPos = calculatedSRSI ? calculatedSRSI?.k > calculatedSRSI?.d ? 'above' : 'below' : 'NA';
                    var sayac = 0;
                    for (let i = 1; i <= srsiArr.length; i++) {
                        var izci = [...srsiArr].slice(-1 * i).shift()
                        var izciPos = izci.k > izci.d;
                        if (calculatedSRSI.k > calculatedSRSI.d !== izci.k > izci.d) {
                            break;
                        } else {
                            sayac++
                        }
                    };


                    const indicatorAdditionalData = {
                        srsiArr: [...srsiArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedSRSI,
                        kPos,
                        kPosAge: sayac,
                        kdPosCross: cCrossUp || cCrossDown,
                        ...calculatedSRSI,

                        // srsi: calculatedSRSI,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedSRSI,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error StochasticRSI', e);
                reject(false)
            }
        });
    },
    stochastic: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, signalPeriod = 14, stochasticPeriod = 14, kPeriod = 3, dPeriod = 3 } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    // const rsiPeriod = rsiPeriod;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const close = data.map(d => Number(d['close']));
                    const indicatorTag = keyTag + '_stochastic_' + length;
                    const srsiArr = Stochastic.calculate({ high, low, close, period, signalPeriod });
                    const calculatedSRSI = [...srsiArr].slice(-1)[0];

                    const indicatorAdditionalData = {
                        srsiArr: [...srsiArr].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calculatedSRSI,
                        stochastic: JSON.stringify(calculatedSRSI),
                        k: calculatedSRSI?.k,
                        d: calculatedSRSI?.d,
                        kPos: calculatedSRSI?.k > calculatedSRSI?.d ? 'above' : 'below',
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calculatedSRSI,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error StochasticRSI', e);
                reject(false)
            }
        });
    },
    candlepatterns: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime, closeTimeHRF,  } = lastBar;
                    const indicatorTag = keyTag + '_candlepatterns_' + length;
                    const volume = data.map(d => Number(d['volume']));
                    const close = data.map(d => Number(d['close']));
                    const open = data.map(d => Number(d['open']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const time = data.map(d => Number(d['openTime']));
                    const hlc = { time, open, close, high, low, volume, };
                    const patterns = await subFn.fnPatternChecks({ period, hlc });

                    const deltaValues = await subFn.fnDelta({
                        symbol
                    })
                    const barmetrics_1 = await subFn.fnBarChecks({
                        period: 1, ...hlc,
                    });
                    const barmetrics_2 = await subFn.fnBarChecks({
                        period: 2, ...hlc,
                    })
                    const barmetrics_3 = await subFn.fnBarChecks({
                        period: 3, ...hlc,
                    })
                    const barmetrics_5 = await subFn.fnBarChecks({
                        period: 5, ...hlc,
                    })
                    const barmetrics_10 = await subFn.fnBarChecks({
                        period: 10, ...hlc,
                    })

                    let bar10_deltaPerc = barmetrics_10?.barmetrics?.deltaPerc;
                    let bar5_deltaPerc = barmetrics_5?.barmetrics?.deltaPerc;
                    let bar3_deltaPerc = barmetrics_3?.barmetrics?.deltaPerc;
                    let bar2_deltaPerc = barmetrics_2?.barmetrics?.deltaPerc;
                    let bar1_deltaPerc = barmetrics_1?.barmetrics?.deltaPerc;

                    let bar10_HLRangePerc = barmetrics_10?.barmetrics?.HLRangePerc;
                    let bar5_HLRangePerc = barmetrics_5?.barmetrics?.HLRangePerc;
                    let bar3_HLRangePerc = barmetrics_3?.barmetrics?.HLRangePerc;
                    let bar2_HLRangePerc = barmetrics_2?.barmetrics?.HLRangePerc;
                    let bar1_HLRangePerc = barmetrics_1?.barmetrics?.HLRangePerc;


                    let bar10_barTrend = barmetrics_10?.barTrend.join(",");
                    let bar5_barTrend = barmetrics_5?.barTrend.join(",");
                    let bar3_barTrend = barmetrics_3?.barTrend.join(",");
                    let bar2_barTrend = barmetrics_2?.barTrend.join(",");
                    let bar1_barTrend = barmetrics_1?.barTrend.join(",");

                    let bar_kipir1 = barmetrics_1?.barmetrics?.kipirSum;
                    let bar_kipir3 = barmetrics_3?.barmetrics?.kipirSum;
                    let bar_kipir5 = barmetrics_5?.barmetrics?.kipirSum;
                    let bar_kipir10= barmetrics_10?.barmetrics?.kipirSum;
                    let bar_kipir1Avg = barmetrics_1?.barmetrics?.kipirAvg;
                    let bar_kipir3Avg = barmetrics_3?.barmetrics?.kipirAvg;
                    let bar_kipir5Avg = barmetrics_5?.barmetrics?.kipirAvg;
                    let bar_kipir10Avg= barmetrics_10?.barmetrics?.kipirAvg;

                    var indicatorAdditionalData = {
                        lastBar: lastBar,
                        // barmetrics_1,
                        // barmetrics_2,
                        // barmetrics_3,
                        // barmetrics_5,
                        // barmetrics_10,
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: patterns,
                        // delta: deltaValues,
                        candlepatterns: Array.isArray(patterns) ? patterns.join(",") : patterns,
                        bardeltaPerc1: bar1_deltaPerc,
                        bardeltaPerc2: bar2_deltaPerc,
                        bardeltaPerc3: bar3_deltaPerc,
                        bardeltaPerc5: bar5_deltaPerc,
                        bardeltaPerc10: bar10_deltaPerc,
                        barHLRangePerc1: bar1_HLRangePerc,
                        barHLRangePerc2: bar2_HLRangePerc,
                        barHLRangePerc3: bar3_HLRangePerc,
                        barHLRangePerc5: bar5_HLRangePerc,
                        barHLRangePerc10: bar10_HLRangePerc,
                        // barTrend1: bar1_barTrend,
                        barTrend2: bar2_barTrend,
                        barTrend3: bar3_barTrend,
                        barTrend5: bar5_barTrend,
                        barTrend10: bar10_barTrend,

                        barKipir1: bar_kipir1,
                        barKipir3: bar_kipir3,
                        barKipir5: bar_kipir5,
                        barKipir10: bar_kipir10,

                        barKipir1Avg: bar_kipir1Avg,
                        barKipir3Avg: bar_kipir3Avg,
                        barKipir5Avg: bar_kipir5Avg,
                        barKipir10Avg: bar_kipir10Avg,
                    };
                    if (deltaValues) {
                        indicatorAdditionalData = {
                            ...indicatorAdditionalData,
                            ...deltaValues
                        }
                    }
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF, closeTime, closeTimeHRF,
                        indicatorValue: patterns,
                        indicatorAdditionalData,
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error candlepatterns', e);
                reject(false)
            }
        });
    },
    supertrend: async props => {
        return new Promise(async (resolve, reject) => {
            const { params, keyTag, data, symbol, dbGauss } = props;
            try {
                function objToString(obj) {
                    var jsn = {};
                    for (var p in obj) {
                        if (p == 'ATR' || p == 'Supertrend') {
                            jsn[p] = JSON.parse(JSON.stringify(obj[p]));
                        }
                    }
                    return jsn;
                }
                if (Array.isArray(data) && data.length !== 0) { //&& data.length > params['length'])
                    const Indicator = Stock.Indicator
                    const newStudyATR = new Indicator(new Supertrend());
                    const { length, refName, source = 'close', timeFrame, multiplier = 3 } = params;
                    // console.log('supertrend_sti', params, keyTag)
                    const period = length;
                    const indicatorTag = keyTag + '_supertrend_' + length;
                    const lastBar = data.slice(-1)[0] ? data.slice(-1)[0] : {};
                    // console.log('supertrend_sti_lastBar', lastBar)
                    const {symbol = 'na', interval = 'na', openTime = 0, openTimeHRF = 'na', closeTime, closeTimeHRF} = lastBar;

                    let sTrendData = [];
                    // 
                    let limLen = length > 500 ? limLen : 500;
                    [...data].slice(-1 * (limLen * 3 )).map(d => {
                        const { openTimeHRF, open, high, low, close, volume, closeTime } = d;
                        sTrendData.push([
                            openTimeHRF, Number(open), Number(high), Number(low), Number(close), Number(volume)
                        ])
                    });
                    // var calcData2 = [...sTrendData];
                    let calc = false;
                    try {
                        calc = await newStudyATR.calculate(sTrendData, { period, multiplier });
                    } catch (eC) {
                        // fnx.log('error supertrend_sti - calcMain Fn.', symbol, eC, sTrendData.length, sTrendData.slice(-3));
                        let errData = JSON.stringify({
                            eDesc: '203Base-idx: supertrend calcMain Fn - calc failed error.',
                            error: eC,
                            symbol,
                            dataL: data.length,
                            sTrendDataLength: sTrendData.length,
                            sTrendData3: sTrendData.slice(-3),
                            indicator: 'supertrend',
                            params: params, 
                        });
                        fnx.log('error', errData);
                        reject('error Strend!')

                    }

                    if (calc) {
                        var preResult = objToString([...calc].slice(-2).shift())
                        var resp = [...calc].slice(-1).pop();
    
                        var respJson = objToString(resp)
    
                        var Supertrendz = respJson?.Supertrend;
                        Supertrendz.actSignal = Supertrendz?.Direction !== preResult?.Supertrend.Direction ? true : false;
                        Supertrendz.position = Supertrendz?.ActiveTrend > Number(lastBar[source]) ? 'above' : 'below';
                        Supertrendz.directionTxt = Supertrendz?.Direction < 0 ? 'above' : 'below';
                        var sayac = 0;
                        for (let i = 1; i <= [...sTrendData].length; i++) {
                            var izci = objToString([...calc].slice(-1 * i).shift())
                            if (Supertrendz.Direction !== izci.Supertrend.Direction) {
                                break;
                            } else {
                                sayac++
                            }
                        }
                        var changetime = [...sTrendData].slice(-1 * sayac).shift().slice(0, 1).pop();
                        Supertrendz.DirectionAge = sayac - 1;
                        if (changetime) Supertrendz.DirectionChanged = changetime;
                        const indicatorAdditionalData = {
                            sourceValue: Number(lastBar[source]),
                            indicatorValue: Supertrendz?.directionTxt,
                            ...Supertrendz,
                        };
                        let respx = {
                            symbol,
                            indicatorTag,
                            indicatorParams: JSON.stringify(params),
                            interval,
                            openTime,
                            openTimeHRF, closeTime, closeTimeHRF, 
                            indicatorValue: Supertrendz?.directionTxt,
                            indicatorAdditionalData
                        };

                        if (props.mode == 'backtest') {
                            // delete respx.symbol;
                            // delete respx.interval;
                            delete respx.indicatorParams; 
                            // delete respx.openTime;
                            // delete respx.openTimeHRF;
                        }

                        resolve(respx);
                    } else {
                        let errData = JSON.stringify({
                            eDesc: '203-idx: supertrend calcMain Fn - calc failed.',
                            error: "manual",
                            symbol,
                            indicator: 'supertrend',
                            params: params, 
                        })
                        fnx.log('error', errData);
                        reject('strend calc error')
                    }
                } else {
                    // fnx.log('error supertrend - no data!', symbol)
                    let errData = JSON.stringify({
                        eDesc: '201-idx: supertrend returned error data not enough.',
                        error: 'manual',
                        dataL: data.length,
                        symbol,
                        indicator: 'supertrend',
                        params: params, 
                    })
                    fnx.log('error', errData);
                    reject(false)
                }
            } catch (e) {
                let errData = JSON.stringify({
                    eDesc: '202-idx: supertrend returned error calc error.',
                    error: e,
                    symbol,
                    indicator: 'supertrend',
                    params: params,
                });
                fnx.log('error', errData, e);
                // await fnx.nTasks.logDBStatus({
                //     sqlClient: dbGauss,
                //     // ...task,
                //     is_wip: false,
                //     status_code: 510,
                //     status_desc: JSON.stringify(errData),
                //     modeDebug: true,
                // })
                reject(false)
            }
        });
    }, 
    kairi: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame, movType, levelUpper, levelLower } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_kairi_' + length;
                    const calcKairi = await tsIx.kairi({
                        src: values, period, movType, levelUpper, levelLower,
                    });
                    const indicatorAdditionalData = {
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calcKairi,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calcKairi,
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error kairi', e);
                reject(false)
            }
        });
    },
    volatilityIndex: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length = 30, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_vi_' + length;
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const viArrStg = await tsIx.volatilityIdx({
                        period: period, close, high, low,
                        currentSymbol: symbol,
                        closePrices: close,
                        comparisonClosePrices: high,
                    });
                    const viArrVolatility = viArrStg?.volatility && Array.isArray(viArrStg?.volatility) ? viArrStg?.volatility : [];
                    const viArrComparison = viArrStg?.comparison && Array.isArray(viArrStg?.comparison) ? viArrStg?.comparison : [];
                    
                    // fnx.log(viArr);
                    const calculatedVIStg = [...viArrVolatility].slice(-1)[0];
                    const calculatedVIStg2 = [...viArrComparison].slice(-1)[0];
                    const calc_volatility = calculatedVIStg;
                    const calc_comparison = calculatedVIStg2;
                    const indicatorAdditionalData = {
                        volatility: calc_volatility,
                        comparison: calc_comparison,
                        viArr: [...viArrVolatility].slice(trade.indicators.lHistory),
                        viComparisonArr: [...viArrComparison].slice(trade.indicators.lHistory),
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: calc_volatility,
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: calc_volatility,
                        indicatorAdditionalData
                    });
                } else {
                    fnx.log('no data')
                    reject(false)
                }
            } catch (e) {
                fnx.log('error volatilityIndex', e);
                reject(false)
            }
        });
    },
    chandelierexit: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length = 22, multiplier = 3, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_ce_' + length;
                    const close = data.map(d => Number(d['close']));
                    const high = data.map(d => Number(d['high']));
                    const low = data.map(d => Number(d['low']));
                    const openTimes = data.map(d => Number(d['openTime']));
                    const ceArrStg = await tsIx.chandelierExit({
                        atrPeriod: period,
                        atrMultiplier: multiplier,
                        closePrices: close,
                        highPrices: high,
                        lowPrices: low,
                    });
                    let ceArr_longStop = ceArrStg?.longStop && Array.isArray(ceArrStg?.longStop) ? ceArrStg?.longStop.slice(-50) : [];
                    let ceArr_shortStop = ceArrStg?.shortStop && Array.isArray(ceArrStg?.shortStop) ? ceArrStg?.shortStop.slice(-50) : [];
                    let ceArr_buySignal = ceArrStg?.buySignal && Array.isArray(ceArrStg?.buySignal) ? ceArrStg?.buySignal.slice(-50) : [];
                    let ceArr_sellSignal = ceArrStg?.sellSignal && Array.isArray(ceArrStg?.sellSignal) ? ceArrStg?.sellSignal.slice(-50) : [];
                    let dirArr = ceArrStg?.dirArr.slice(-50);
                    let dSignal = ceArrStg?.dirSignal.slice(-50);
                    const openTimesStg = [...openTimes].slice(-50);
                    let closePrices = [...close].slice(-50);
                    let cexArrStg = [];
                    let signal = [];
                    ceArr_longStop.map((c, i) => {
                        let p = {};
                        p.idx = i;
                        let cTime = openTimesStg[i] ? new Date(openTimesStg[i]).toISOString().substring(0, 16) : 0
                        p.openTime = cTime;
                        let signalChanged = null;
                        let signalStg = {};
                        if (i == 0) {
                            signalChanged = false;
                            signalStg = {
                                ix: i, 
                                openTime: cTime, 
                                value: 'buy',
                                isChanged: signalChanged,
                            };
                            signal.push(signalStg)
                        } else {
                            let loPre = ceArr_longStop[i - 1];
                            let soPre = ceArr_shortStop[i - 1];
                            let cl = closePrices[i];
                            let xSig = cl > soPre
                                ? 'buy'
                                : cl < loPre
                                    ? 'sell'
                                    : signal[i - 1].value;
                            signalChanged = xSig !== signal[i - 1]?.value;
                            signalStg = {
                                ix: i, 
                                openTime: cTime, 
                                value: xSig,
                                isChanged: signalChanged, 
                            };
                            signal.push(signalStg);
                        }
                        p.signal = signalStg?.value;
                        p.signalChanged = signalChanged;
                        p.close = closePrices[i];
                        p.longStop = c;
                        p.shortStop = ceArr_shortStop[i];
                        p.signalStg = signalStg;
                        cexArrStg.push(p);

                    });

                    var sayac = false;
                    let aSignal = signal.map(s => s.value);
                    try {
                        sayac = 0;
                        var izciRef = Array.isArray(aSignal) && aSignal.length !== 0 && [...aSignal].reverse();
                        var izciVal = izciRef[0];
                        for (let i = 1; i <= izciRef.length; i++) {
                            if (izciVal !== izciRef[i]) {
                                break;
                            } else {
                                sayac++
                            }
                        }
                    } catch (eS) {
                        fnx.log('signal age calc error', eS);
                        sayac = false;
                    }
                    var signalValue = signal.slice(-1)[0]?.value;
                    var signalChanged = signal.slice(-1)[0]?.isChanged;

                    const indicatorAdditionalData = {
                        // cexArrStg,
                        signal: signalValue,
                        signalChanged,
                        signalAge: sayac,
                        signalArr: signal.slice(-5),
                        sourceValue: Number(lastBar[source]),
                    };
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: signalValue,
                        indicatorAdditionalData
                    });
                } else {
                    fnx.log('no data')
                    reject(false)
                }
            } catch (e) {
                fnx.log('error ce calc', e);
                reject(false)
            }
        });
    },
    bb: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length = 20, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime } = lastBar;
                    // const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_bb_' + length
                    const close = data.map(d => Number(d['close']));
                    const { upper, middle, lower } = bb(close, {period});

                    // const atrArr = ATR.calculate({ period: period, close, high, low, });
                    // const calculatedBB = [...atrArr].slice(-1)[0];
                    // const atrvsCloseR = parseFloat((calculatedATR / lastBar['close'] * 100).toFixed(4))

                    const indicatorAdditionalData = {
                        // bb: bb,
                        upperArr: [...upper].slice(trade.indicators.lHistory), 
                        middleArr: [...middle].slice(trade.indicators.lHistory), 
                        lowerArr: [...lower].slice(trade.indicators.lHistory),
                        upper: [...upper].slice(-1)[0], 
                        middle: [...middle].slice(-1)[0], 
                        lower: [...lower].slice(-1)[0],
                        sourceValue: Number(lastBar[source]),
                        indicatorValue: {
                            upper: [...upper].slice(-1)[0],
                            middle: [...middle].slice(-1)[0],
                            lower: [...lower].slice(-1)[0],
                        },
                        openTime: openTimeHRF,
                    };

                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: {
                            upper: indicatorAdditionalData.upper,
                            middle: indicatorAdditionalData.middle,
                            lower: indicatorAdditionalData.lower,
                        },
                        indicatorAdditionalData
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error atr', e);
                reject(false)
            }
        });
    },
    x: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const { params, keyTag, data } = props;
                const { length, source = 'close', timeFrame } = params;
                if (Array.isArray(data) && data.length !== 0) {
                    const period = length;
                    const lastBar = data.slice(-1)[0];
                    const { symbol, interval, openTime, openTimeHRF, closeTime, close } = lastBar;
                    const values = data.map(d => Number(d[source]));
                    const indicatorTag = keyTag + '_' + length
                    // var emaArr = SMA.calculate({ period: parseInt(period), values });
                    calculateVolatilityAsync(closePrices, comparisonClosePrices, period)
                        .then(result => {
                            console.log('Volatility:', result.volatility);
                            console.log('Comparison:', result.comparison);
                        })
                        .catch(error => {
                            console.error('Error calculating volatility:', error);
                        });

                    // fnx.log('data', data.slice(-1), values.slice(-3), keyTag, params, emaArr.slice(-7));
                    resolve({
                        symbol,
                        indicatorTag,
                        indicatorParams: JSON.stringify(params),
                        interval,
                        openTime,
                        openTimeHRF,
                        indicatorValue: emaArr.slice(-1)[0]
                    });
                } else {
                    reject(false)
                }
            } catch (e) {
                fnx.log('error sma', e);
                reject(false)
            }
        });
    },
});
const subFn = {
    fnBarChecks: async (props) => {
        const { period, time, low, high, close, open, volume, } = props;
        var colors = [];
        var kipir = [];
        return new Promise(async function (resolve) {
            var f = {
                time: time.slice(-1 * period),
                open: open.slice(-1 * period),
                close: close.slice(-1 * period),
                low: low.slice(-1 * period),
                high: high.slice(-1 * period),
                volume: volume.slice(-1 * period),
            };
            for (let i = 0; i < period; i++) {
                var color = f.close[i] - f.open[i] > 0 ? 'blue' : 'red';
                var stg_kipis = (Math.abs(f.high[i] - f.low[i])) / ((f.high[i] + f.low[i]) / 2) * 100;
                kipir.push(stg_kipis);
                colors.push(color);
            }
            var colorTest = colors.every(res => res == 'blue') ? 'blue' : colors.every(res => res == 'red') ? 'red' : 'grey';

            var colorCounts = colors.reduce(function (acc, curr) {
                if (typeof acc[curr] == 'undefined') {
                    acc[curr] = 1;
                } else {
                    acc[curr] += 1;
                }
                return acc;
            }, {});

            var kipirSum = kipir.reduce(function(p, c) { return p + Number(c); }, 0)
            var kipirAvg = kipirSum / kipir.length;
            var barmetrics = {
                minOpen: Math.min(...f.open),
                maxOpen: Math.max(...f.open),
                minClose: Math.min(...f.close),
                maxClose: Math.max(...f.close),
            }

            var barmetricsHL = {
                minLow: Math.min(...f.low),
                maxLow: Math.max(...f.low),
                minHigh: Math.min(...f.high),
                maxHigh: Math.max(...f.high),
            }

            let maxn = Object.values(barmetrics);
            barmetrics.OCmin = Math.min(...maxn);
            barmetrics.OCmax = Math.max(...maxn);
            barmetrics.OCRange = Math.max(...maxn) - Math.min(...maxn)

            barmetrics.kipirAvg = kipirAvg;
            barmetrics.kipirSum = kipirSum;
            
            barmetrics = {
                ...barmetrics, openBar: f.open[0],
                closeBar: f.close[period - 1],
                deltaAmt: f.close[period - 1] - f.open[0],
                deltaPerc: (f.close[period - 1] - f.open[0]) / f.open[0] * 100,
            }
            let maxnHL = Object.values(barmetricsHL);
            let HLmin = Math.min(...maxnHL);
            let HLmax = Math.max(...maxnHL);
            let HLRange = HLmax - HLmin;
            barmetrics.HLmin = HLmin;
            barmetrics.HLmax = HLmax;
            barmetrics.HLRange = HLRange;
            barmetrics.HLRangePerc = ((HLRange / HLmin) + (HLRange / HLmax)) / 2 * 100
            var resp = {
                refTime: f.time[0],
                refTimeEn: new Date(f.time[0]).toISOString(),
                // colorC: colorTest,
                // colorA: colors,
                // colorCounts: (colorCounts), // JSON.stringify
                barTrend: (subFn.fnTrend(f.close)), //JSON.stringify
                barmetrics, //: JSON.stringify(barmetrics),
                //data: JSON.stringify(f),
            }
            resolve(resp)
        });
    },
    fnTrend: (chartArray) => {
        var ipre;
        var trend = []
        chartArray.map(i => {
            var tr = i > ipre ? 'Up' : i < ipre ? 'Down' : 'Flat';
            if (ipre) trend.push(tr);
            ipre = i;
        });
        return trend
    },
    fnTargetAgePSAR: async function (props) {
        const { arr, open, close } = props;
        return new Promise(async function (resolve) {
            var ln = arr.length;
            const cXCalc = [...arr].map((value, index) => {
                var max = Math.max(...[open[index], close[index]]);
                var pos = value > max ? 'above' : 'below'
                return pos
            });
            var cX = cXCalc.slice(-1).pop();
            var sayac = 0;
            for (let i = 1; i <= cXCalc.length; i++) {
                var izci = cXCalc.slice(-1 * i).shift();
                if (cX !== izci) {
                    break;
                } else {
                    sayac++
                }
            }
            var resp = {
                age: sayac,
                pozs: cX,
            }
            resolve(resp) //  calc.slice(-2)
        })
    },
    fnPatternChecks: async function (props) {
        const { period, hlc } = props;
        const { time, low, high, close, open, volume } = hlc
        var procValues = {
            time: time.slice((-1) * period),
            open: open.slice((-1) * period),
            close: close.slice((-1) * period),
            low: low.slice((-1) * period),
            high: high.slice((-1) * period),
            volume: volume.slice((-1) * period),
        };
        var pattern;
        var patternResult;
        var calc = [];
        var cmd;
        var patternVar = {};
        var pattern;
        var timeValues = time;
        var highValues = high;
        var closeValues = close;
        var lowValues = low;
        var openValues = open;
        var volumeValues = volume;
        //re.length !==0 && console.log([...timeValues].pop().toLocaleString(), resp ); //fnx.jsonn(re)
        return new Promise(async function (resolve) {
            if (Array.isArray(closeValues) && closeValues.length > 5) {
                pattern = "abandonedbaby";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })


                pattern = "bearishengulfingpattern";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })


                pattern = "bullishengulfingpattern";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "darkcloudcover";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })


                pattern = "downsidetasukigap";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "doji";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "dragonflydoji";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "gravestonedoji";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bullishharami";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bearishharamicross";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bullishharamicross";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bullishmarubozu";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bearishmarubozu";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bullishspinningtop";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bearishspinningtop";
                patternVar = {
                    open: openValues.slice(-1), high: highValues.slice(-1),
                    close: closeValues.slice(-1), low: lowValues.slice(-1)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "tweezertop";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "tweezerbottom";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "shootingstar";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "shootingstarunconfirmed";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "hangingman";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "hangingmanunconfirmed";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "hammerpattern";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "hammerpatternunconfirmed";
                patternVar = {
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "eveningdojistar";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "eveningstar";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "bearishharami";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "piercingline";
                patternVar = {
                    open: openValues.slice(-2), high: highValues.slice(-2),
                    close: closeValues.slice(-2), low: lowValues.slice(-2)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "morningdojistar";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "morningstar";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "threeblackcrows";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "threewhitesoldiers";
                patternVar = {
                    open: openValues.slice(-3), high: highValues.slice(-3),
                    close: closeValues.slice(-3), low: lowValues.slice(-3)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })


                pattern = "subFn.tsColorCheckRed";
                patternVar = {
                    time: timeValues.slice(-5),
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                pattern = "subFn.tsColorCheckBlue";
                patternVar = {
                    time: timeValues.slice(-5),
                    open: openValues.slice(-5), high: highValues.slice(-5),
                    close: closeValues.slice(-5), low: lowValues.slice(-5)
                }
                cmd = pattern + '(' + JSON.stringify(patternVar) + ')'
                patternResult = eval(cmd);
                calc.push({ pattern, patternResult, patternVar })

                /* { low: number[]; high: number[]; close: number[]; period: number; } */
                var re = [...calc].filter(i => i.patternResult == true);
                var resp = re.map(p => p.pattern)
                resolve(resp) //  calc.slice(-2)
            } else {
                resolve(false)
            }
        })
    },
    tsColorCheckRed: (props) => {
        var resp = subFn.tsColorCheck(props, "red");
        //console.log('tsColorCheckRed', resp)
        return resp;
    },
    tsColorCheckBlue: (props) => {
        var resp = subFn.tsColorCheck(props, "blue");
        //console.log('tsColorCheckBlue', resp)
        return resp;
    },
    tsColorCheck: (props, lfColor) => {
        const { time, open, high, close, low } = props;
        var colors = [];
        var shift = 2; //time deltaya gore 2 or 3
        var lenn = open.length;
        for (let i = shift; i <= lenn - 1; i++) {
            var color = close[i] - open[i] > 0 ? 'blue' : 'red';
            colors.push(color);
        }
        var resp = colors.every(res => res == 'blue') ? 'blue' : colors.every(res => res == 'red') ? 'red' : null;
        //console.log('color', [...time].slice(-1).pop().toLocaleString(), resp)
        var respp = lfColor == resp;
        return respp;
    },
    fnDelta: props => {
        var { redisClient, symbol } = props;
        return new Promise(async (resolve, reject) => {
            if (symbol) {
                symbol = symbol.toUpperCase();
                redisClient = redisClient || redisClientGlobal || new Redis({
                    host: '127.0.0.1',
                    port: 6379,
                });
                let redisKey = redixPrefix.dataMarket + "futuresDaily".toLowerCase();
                let valuesStg = await redisClient.get(redisKey);
                if (valuesStg) {
                    try {
                        let values = JSON.parse(valuesStg);
                        let arr = []
                        for (const pair in values) {
                            let v = {
                                symbol: pair,
                            }
                            v.priceChangePercent = values[pair]?.priceChangePercent ? parseFloat(values[pair].priceChangePercent) : null
                            v.quoteVolume = values[pair]?.quoteVolume ? parseFloat(values[pair].quoteVolume) : null
                            v.delta = values[pair]?.delta
                            arr.push(v);
                        }

                        let arrPCP = Array.isArray(arr) && arr.map(a => parseFloat(a.priceChangePercent));
                        let arrDelta = Array.isArray(arr) && arr.map(a => a.delta);
                        let arrQVolume = Array.isArray(arr) && arr.map(a => (a.quoteVolume));

                        let pairValue = values[symbol];
                        let resp = {}
                        resp.priceChangePercent = pairValue?.priceChangePercent ? parseFloat(pairValue?.priceChangePercent) : -99
                        resp.quoteVolume = pairValue?.quoteVolume ? parseFloat(pairValue?.quoteVolume) : 0
                        
                        resp.value = pairValue?.delta;
                        resp.valueSrc = pairValue?.src;
                        resp.dteventISO = pairValue?.dteventISO;
                        let stats = {}
                        stats.PCPmean = stat.calcAverage(arrPCP);
                        stats.PCPmax = stat.calcMax(arrPCP);
                        stats.PCPmin= stat.calcMin(arrPCP);
                        stats.PCPmedian= stat.calcMedian(arrPCP);
                        stats.PCPmode= stat.calcMode(arrPCP);
                        stats.PCPperc95= stat.calcQuartile(arrPCP, 95);
                        stats.PCPperc80= stat.calcQuartile(arrPCP, 80);
                        stats.PCPperc50= stat.calcQuartile(arrPCP, 50);
                        stats.PCPperc20= stat.calcQuartile(arrPCP, 20);
                        stats.PCPperc5= stat.calcQuartile(arrPCP, 5);

                        stats.PCPValue = resp.priceChangePercent;

                        stats.PCPmeanISBiggerPCPmedian = stats.PCPmean > stats.PCPmedian;

                        stats.PCPpercentile = stats.PCPValue > stats.PCPperc95 ? 95 :
                            stats.PCPValue > stats.PCPperc80 ? 80 :
                                stats.PCPValue > stats.PCPperc50 ? 50 :
                                    stats.PCPValue > stats.PCPperc20 ? 20 :
                                        stats.PCPValue > stats.PCPperc5 ? 5 : 0
                            ;

                        stats.QVolumemean = stat.calcAverage(arrQVolume);
                        stats.QVolumemax = stat.calcMax(arrQVolume);
                        stats.QVolumemin= stat.calcMin(arrQVolume);
                        stats.QVolumemedian= stat.calcMedian(arrQVolume);
                        stats.QVolumemode= stat.calcMode(arrQVolume);
                        stats.QVolumeperc95= stat.calcQuartile(arrQVolume, 95);
                        stats.QVolumeperc80= stat.calcQuartile(arrQVolume, 80);
                        stats.QVolumeperc50= stat.calcQuartile(arrQVolume, 50);
                        stats.QVolumeperc20= stat.calcQuartile(arrQVolume, 20);
                        stats.QVolumeperc5= stat.calcQuartile(arrQVolume, 5);

                        stats.QVolumeValue = resp.quoteVolume;
                        stats.QVolumepercentile = stats.QVolumeValue > stats.QVolumeperc95 ? 95 :
                            stats.QVolumeValue > stats.QVolumeperc80 ? 80 :
                                stats.QVolumeValue > stats.QVolumeperc50 ? 50 :
                                    stats.QVolumeValue > stats.QVolumeperc20 ? 20 :
                                        stats.QVolumeValue > stats.QVolumeperc5 ? 5 : 0
                            ;


                        stats.deltaMean = stat.calcAverage(arrDelta);
                        stats.deltaMax = stat.calcMax(arrDelta);
                        stats.deltaMin= stat.calcMin(arrDelta);
                        stats.deltaMedian= stat.calcMedian(arrDelta);
                        stats.deltaMode= stat.calcMode(arrDelta);
                        stats.deltaPerc95= stat.calcQuartile(arrDelta, 95);
                        stats.deltaPerc80= stat.calcQuartile(arrDelta, 80);
                        stats.deltaPerc50= stat.calcQuartile(arrDelta, 50);
                        stats.deltaPerc20= stat.calcQuartile(arrDelta, 20);
                        stats.deltaPerc5= stat.calcQuartile(arrDelta, 5);

                        stats.deltaMeanISBiggerdeltaMedian = stats.deltaMean > stats.deltaMedian;

                        stats.deltaValue = resp.value;
                        stats.deltaPercentile = stats.deltaValue > stats.deltaPerc95 ? 95 :
                            stats.deltaValue > stats.deltaPerc80 ? 80 :
                                stats.deltaValue > stats.deltaPerc50 ? 50 :
                                    stats.deltaValue > stats.deltaPerc20 ? 20 :
                                        stats.deltaValue > stats.deltaPerc5 ? 5 : 0
                            ;
                        if (stats.deltaMax) {
                            resp = {
                                ...resp,
                                ...stats,
                            }
                        }
                        resolve(resp);
                    }
                    catch (e) {
                        // fnx.log('ssz212e err', symbol, e);
                        resolve(false)
                    }
                } else {
                    // fnx.log('ssz212', symbol, 'no value');
                    resolve(false)
                }
            }
            else {
                // fnx.log('ssz21', symbol, 'no symbol');
                resolve(false)
            }
        })
    }
};
const indicators = (exports.indicators =  {
    getKlineData: async ({ redisClient, symbol, keyTag, klineRef, rawData, tickerData, interval, batch, taskID }) => {
        return new Promise(async (resolve, reject) => {
            // console.log('kline ref', klineRef, rawData.length)
            try {
                if (klineRef == 'closedBar') {
                    //no need to append
                    let rArr = [];
                    Array.isArray(rawData) && rawData.map((d, ix) => {
                        rArr.push({
                            symbol,
                            openTime: d[0],
                            openTimeHRF: new Date(d[0]),
                            closeTime: d[6],
                            closeTimeHRF: new Date(d[6]),
                            "open": d[1],
                            high: d[2],
                            low: d[3],
                            "close": d[4],
                            volume: d[5],
                            quoteVolume: d[7],
                            trades: d[8],
                            volumeTaker: d[9],
                            quoteVolumeTaker: d[10],
                            taskID: taskID,
                            batchid: batch,
                            "interval": interval,
                            symi: keyTag,
                            is_deleted: false,
                            candleIndex: ix,
                        });
                    })
                    resolve(rArr)
                } else {
                    //fetch data and append raw.
                    let d2Process;
                    if(!rawData) {
                        let dRedixKey = redixPrefix.dataKline + keyTag.toLowerCase();
                        let d2Processstg1 = await redisClient.get(dRedixKey);
                        let d2ProcessKline = JSON.parse(d2Processstg1);
                        let lastKline = Array.isArray(d2ProcessKline) && [...d2ProcessKline].slice(-1)[0];
                        let lastTicker = fnx.nBinance.tick2json(tickerData.k, true, 'array');
                        if (Array.isArray(lastKline) && Array.isArray(lastTicker)) {

                            if (lastTicker[0]>lastKline[0]) {

                                if (lastKline[0] == lastTicker[0]) {
                                    //remove last and append ticker.
                                    // fnx.log('remove last and append ticker.', keyTag, lastKline, lastTicker, )
                                    let stg1 = [...d2ProcessKline].slice(0, d2ProcessKline.length-1);
                                    d2Process = [...stg1, lastTicker];
                                } else {
                                    //append ticker.
                                    // fnx.log('append ticker.', keyTag, lastKline, lastTicker )
                                    d2Process = [...d2ProcessKline, lastTicker];
                                };
                            } else {
                                d2Process = [...d2ProcessKline];
                            }
 
                            let rArr = [];
                            Array.isArray(d2Process) && d2Process.map(d => {
                                rArr.push({
                                    symbol,
                                    openTime: d[0],
                                    openTimeHRF: new Date(d[0]),
                                    closeTime: d[6],
                                    closeTimeHRF: new Date(d[6]),
                                    "open": d[1],
                                    high: d[2],
                                    low: d[3],
                                    "close": d[4],
                                    volume: d[5],
                                    quoteVolume: d[7],
                                    trades: d[8],
                                    volumeTaker: d[9],
                                    quoteVolumeTaker: d[10],
                                    taskID: taskID,
                                    batchid: batch,
                                    "interval": interval,
                                    symi: keyTag,
                                    is_deleted: false,
                                });
                            })
                            resolve(rArr)

                        } else {
                            reject('check data scheme...', klineRef, rawData, keyTag)
                        }

                    }
                }
            } catch (e) {
                console.log('getKlineData err', e)
                reject(e);
            }
        });
    },
    calculate: async ({ dbGauss, symbol, data, keyTag, indicatorsWParams, battleID, nodeID, taskID, klineRef, interval, battleInterval = '' }) => {
        return new Promise(async (resolve, reject) => {
            let indicatorCalculationResults = []
            let errorLogs = []
            // fnx.log('indicatorsWParams', symbol, JSON.stringify(indicatorsWParams));
            for (idx of indicatorsWParams) {
                let idxName = idx.indicator
                let idxTagName = idx.refName;
                let calcStg = false;
                let ixTF = idx.battleParams.timeFrame;
                if (interval == ixTF) {

                    let res = {
                        id: idx.id,
                        // keyTag,
                        battleID: battleID || '',
                        nodeID: nodeID || '',
                        taskID: taskID || 'wsconn',
                        indicator: idxName,
                        refName: idxTagName,
                        indicatorRefName: idxTagName,
                        symbol,
                        // ...calcStg,
                        battleInterval: battleInterval || '',
                    };
                    try {
                        calcStg = await calcIndicator[idxName]({ keyTag, data, klineRef, params: idx.battleParams, dbGauss, symbol });
                        res = {
                            ...res,
                            ...calcStg,
                        };
                        res.nodeTimeFrame = interval;
                        res.battleTimeFrame = battleInterval;
                        res.indicatorParamTimeFrame = ixTF;

                        let indicatorValue = res.indicatorValue;
                        try {
                            let tmp = JSON.parse(indicatorValue);
                            res.indicatorValue = JSON.stringify(res.indicatorValue);
                        }
                        catch (e) {
                            res.indicatorValue = typeof res.indicatorValue === "object" ? JSON.stringify(res.indicatorValue) : res.indicatorValue;
                        }
                        res.indicatorAdditionalData = res.indicatorAdditionalData && JSON.stringify(res.indicatorAdditionalData) !== '{}' ? JSON.stringify(res.indicatorAdditionalData) : '';
                        indicatorCalculationResults.push(res);
                    }
                    catch (e) {
                        let errData = {
                            symbol,
                            indicator: idxName,
                            eDesc: '801-main.calculator - calcIndicator Error.',
                            error: e,
                        };
                        errorLogs.push(errData);
                        res = {
                            ...res,
                            error: 'err idx not calc',
                        };
                        indicatorCalculationResults.push(res);
                    }
                } else {
                    // fnx.log('ixTF timeFrame kapsam dışı', symbol);
                    // fnx.log('ixTF timeFrame', symbol, idxName, idxTagName, ixTF);
                    // fnx.log('node interval - interval', symbol, interval)
                    // fnx.log('battle interval - battleInterval', symbol, battleInterval);
                }
            }
            let allIdxProblem = indicatorCalculationResults.length !== 0 && indicatorCalculationResults.length == errorLogs.length;
            errorLogs.length !== 0 && fnx.log('1231 / main.calculator - calcIndicator / errors ', symbol, taskID || 'wsconn', errorLogs.length);
            
            if (errorLogs.length !== 0) {
                let errData = JSON.stringify({
                    eDesc: 'error: main.calculator - calcIndicator. task: ' + JSON.stringify(taskID || 'wsconn'),
                    error: JSON.stringify(errorLogs),
                    allError: allIdxProblem,
                })
                fnx.log('errors', errData);
            }
            if (allIdxProblem) {
                reject('idx calc error')
            } else {
                resolve(indicatorCalculationResults)
            }
        });
    },
    execCalculations: (props) => {
        const {
            redisClient,taskDesc,battleID,nodeKey,taskID,taskTag,script2Run,
            script2RunUri,batch,nodeInterval,battleInterval,taskType,wsconn,wsconnuri,
            period,periodDesc,taskParams,redisKey,is_active,process_id,port,pair,
            dtcreatedEn,id,nextRun,battleData, klineRef, rawData, tickerData = false, saveResults=false,
        } = props;
        const { nodeID,keyTag,symbol,interval, batchAdd,limit, } = taskParams;
        const {symi, nodeTasks, pairs, candleCounts, intervals, indicatorsWParams, rulesets, trading,} = battleData;
        // fnx.log('props', JSON.stringify(props));
        return new Promise(async (resolve, reject) => {
            //fetch Sta
            let refData;
            let calcResults;
            try {
                refData = await indicators.getKlineData({
                    redisClient, symbol, keyTag, klineRef, rawData, tickerData, interval, batch, taskID
                });
            } catch (e1) {
                //fnx.log('indicators.getKlineData e1c', e1)
            }
            
            try {
                if (refData && refData.length !== 0) {
                    let lastCandle = [...refData].slice(-1)
                    // fnx.log('indicators.calculate', interval, nodeInterval);
                    calcResults = await indicators.calculate({
                        symbol, keyTag, indicatorsWParams, data: refData, 
                        battleID, nodeID, taskID, klineRef, interval: nodeInterval, 
                        battleInterval: battleInterval.toString(),
                        redisClient, lastCandle
                    });

                    if(saveResults && calcResults) {
                        const taskTagLw = taskTag.toLowerCase();
                        try { 
                            let redisIndKey = redixPrefix.dataIndicators + taskTagLw;
                            await redisClient.set(redisIndKey, JSON.stringify(calcResults));
                        } catch (eIdx) {
                            fnx.log('1108 / fnGetTaskDone indicatorCalculations fnxIdx.main.start', taskTagLw)
                        }
                        try {
                            let sKey = redixPrefix.monitorIndicators + taskTagLw;
                            let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                            await redisClient.set(sKey, JSON.stringify(sStatsValue));
                        }
                        catch (eSt) {
                            modeDebug && fnx.log('1108b / stat save error', taskTagLw);
                        }
                    }
                    resolve(calcResults)
                } else {
                    reject('noRef data for calculation1.' + keyTag)
                }
            } catch (e2) {
                fnx.log('main.calculator e2', e2, keyTag)
                reject('noRef data for calculation2.')
            }
        }); 
    }, 
});
const strategies = (exports.strategies =  {
    getStrategyRulesets: ({db, params = {}}) => {
        return new Promise(async (resolve, reject) => {
            //fetch Sta
            // console.log('battleRulesets', battleRulesets)
            resolve(battleRulesets);
        }); 
    }, 
    getStrategyRulesetParams: ({db, params = {}}) => {
        return new Promise(async (resolve, reject) => {
            resolve(battleRulesetParams);
        }); 
    },
    calculate:  ({ db, dbMarket, params = {}, redisClient }) => {
        var allStrategies = [];
        const { symbol, xsmi, battle_params = {}, battleInterval,
            indicatorParams, indicatorCalculations, strategyRulesets, 
            strategyRulesetParams, lastCandle, debug = false, mode = '' } = params;
        return new Promise(async (resolve, reject) => {
            var iResults = indicatorCalculations;
            // fnx.log('sum', symbol, iResults.length);
            let dtX = Date.now();
            if (mode !== 'backtest') {
                try {
                    let results = [];
                    let keys = await redisClient.keys(redixPrefix.dataIndicators + symbol.toLowerCase() + '_*');
                    await Promise.all(
                        keys.map(async (logkey) => {
                            const value = await redisClient.get(logkey);
                            let nodeData = JSON.parse(value);
                            results = nodeData && nodeData.length !== 0 ? [...results, ...nodeData] : results; //[...results, ...nodeData] 
                        })
                    );
                    iResults = results;
                } catch (eI) {
                    fnx.log('redis error', redixPrefix.dataIndicators + symbol.toLowerCase() + '_*', eI)
                }
            } else {
                // try {
                //     //load calculated 
                //     const { intervals } = battle_params;
                //     if (Array.isArray(intervals) && intervals.length !== 1) {
                //         // debug && fnx.log('read local docs...', symbol.toLowerCase(), intervals, battleInterval);
                //         let results = [];
                //         let resultsFn = await Promise.all(
                //             intervals.filter(inv => inv !== battleInterval.toString()).map((intv, sira) => {
                //                 debug && fnx.log( symbol.toLowerCase(), 'read local docs...', intv)
                //             })
                //         );
                //     } //generic status.
                // } catch (eI) {
                //     fnx.log('multi frame error in backtest', symbol.toLowerCase(), eI)
                // }
            }
            // Array.isArray(iResults) &&  iResults.map( i => {
            //   i.indicatorRefName4Calc = i.indicatorRefName + '__' + i.interval;
            // })
            
            // fnx.log(symbol, iResults.length, Date.now() - dtX)
            if (!battleInterval) {
                fnx.log('symbol', symbol, xsmi, battleInterval)
                fnx.log('battle_params', battle_params)
                fnx.log('indicatorCalculations', indicatorCalculations)
            };
            let sayac = 0;
            for (const strategy of strategyRulesets) {
                // sayac == 0 && fnx.log(xsmi, 'strategy', JSON.stringify(strategy));
                // sayac == 0 && fnx.log(xsmi, 'iResults', JSON.stringify(iResults), iResults.length);
                let arrIdx = [];
                strategy.ruleSet.map(r => arrIdx = [...arrIdx, ...r.criteria])
                let sIdx = [...new Set(arrIdx.map(r => r.indicatorRef))];
                strategy.indicators = sIdx;

                var sName = strategy.rulesetName;
                var sID = strategy.rsgID;
                var direction = strategy.direction;
                var isSelected = true;
                var strategyResult = [];
                // debug && fnx.log(xsmi, 'strategy idx', JSON.stringify(strategy.indicators));

                for (const rule of strategy.ruleSet) {
                    var indicator = rule.key; //indicator name ref??

                    let sIdxS = [...new Set(rule.criteria.map(r => r.indicatorRef))];
                    rule.indicators = sIdxS;
                    var ruleResult = [];
                    // debug && fnx.log(xsmi, 'rule of strategy.ruleSet', JSON.stringify(rule), indicator);
                    // debug && fnx.log(xsmi, 'iResults', JSON.stringify(iResults))
                    // console.log(symbol, 'iResults', iResults)
                    var rltIndicatorResults = Array.isArray(rule?.indicators) && iResults.filter(i => rule?.indicators.includes(i.indicatorRefName));
                    var cond = rule.cond; // 1: all, 0: some
                    
                    // mode == 'backtest' && debug && console.log(' ');
                    // mode == 'backtest' && debug && console.log(' ');
                    // debug && fnx.log(xsmi, 'rltIndicatorResults', JSON.stringify(rltIndicatorResults));

                    if (rltIndicatorResults && Array.isArray(rltIndicatorResults) && rltIndicatorResults.length !== 0) {
                        for (const c of rule.criteria) { //rule set altındaki kriterler...
                            //find result type
                            let NrltIndicatorResults = [...rltIndicatorResults].find(rI => rI.indicatorRefName == c.indicatorRef);
                            // debug && fnx.log(xsmi, 'NrltIndicatorResults Ref', c.indicatorRef, NrltIndicatorResults );
                            var x = [...strategyRulesetParams].find(ixx => ixx.indicator == c.indicator);
                            // console.log(symbol, 'strategyRulesetParams x' ,x, 'indicatorRef', c.indicatorRef)
                            var y = x && [...x.resultKeys].find(cxx => cxx.name == c.item);
                            var resulttype = y ? y?.result : 'numeric'

                            let indicatorResultsFinal;
                            try {
                                indicatorResultsFinal = NrltIndicatorResults ? JSON.parse(NrltIndicatorResults?.indicatorAdditionalData) : NrltIndicatorResults?.indicatorAdditionalData
                            } catch (ex1) {
                                // fnx.log('indicatorResultsFinal error', ex1,);
                            }
                            // debug && fnx.log(xsmi, 'indicatorResultsFinal', c.indicatorRef, indicatorResultsFinal );
                            // debug && fnx.log(xsmi, 'indicatorResultsFinal ref', c.indicatorRef, 'keyzz:', x.resultKeys, c.item, y, 'keyzz ref - keyz.name' ); 
                            if (indicatorResultsFinal) {
                                var iResValue = fnx.findJSONElement(indicatorResultsFinal, c.item) //rltIndicatorResults[c.item];
                                // debug && fnx.log(xsmi, 'indicatorResultsFinal iResValue', c.indicatorRef, iResValue ); 
                                // debug && iResValue == null && fnx.log('iResValue ', xsmi, c.indicatorRef, c.item);
                                if (resulttype == 'list') iResValue = "'" + iResValue + "'";
                                var eqIsBetween = c.rule.substr(0, 3) == 'btw';
                                var eqReferenceValue = c.rule.substr(0, 3) == 'ref';
                                // console.log('eqReferenceValue', symbol, c.rule.substr(0, 3), c.rule.substr(0, 3) == 'ref')
                                // console.log('mainRefValue', symbol, c.rule, c.item, iResValue)
                                if (eqIsBetween) {
                                    var ranges = c.rule.substring(4, c.rule.length).split('|')
                                    var evStr = iResValue !== '-' ? 'isNaN(parseFloat(' + iResValue + ')) ? false : fnxCore.NumberIsBetween(parseFloat(' + iResValue + '), ' + ranges[0] + ', ' + ranges[1] + ')' : 'false';
                                } else if (eqReferenceValue) {
                                    const isNumeric = (num) => (typeof (num) === 'number' || typeof (num) === "string" && num.trim() !== '') && !isNaN(num);

                                    var newRefStr = c.rule.substring(4, c.rule.length);
                                    try {
                                        var matches = newRefStr.match(/\[(.*?)\]/);
                                        var indicatorRefName = Array.isArray(matches) && matches.length !== 0 && matches[1];
                                        var indicatorKey = newRefStr.substring(newRefStr?.indexOf(']') + 1, newRefStr.length).replace(/\s/g, '');
                                        var lookforIndR = Array.isArray(iResults) && [...iResults].find(i => i.indicatorRefName == indicatorRefName);
                                        var lookforValuesR_stg = lookforIndR && lookforIndR?.indicatorAdditionalData;
                                        var lookforValuesR = lookforValuesR_stg ? JSON.parse(JSON.stringify(JSON.parse(lookforValuesR_stg))) : false;  //typeof lookforValuesR_stg !== 'object' ? JSON.parse(lookforValuesR_stg) : lookforValuesR_stg;
                                        var data2CompareStg = lookforValuesR[indicatorKey]; // : false;
                                        var data2Compare = data2CompareStg && isNumeric(data2CompareStg) ? data2CompareStg : `'${data2CompareStg}'`;
                                        var dataCompareCondition = newRefStr.substring(0, newRefStr?.indexOf('['))
                                        var data2CompareStgX = isNumeric(data2Compare) ? data2Compare : '`' + data2Compare + '`';
                                        var evStr = data2CompareStg ? iResValue + ' ' + dataCompareCondition + ' ' + data2CompareStgX : false

                                    } catch (eIx) {
                                        fnx.log('evStr error', eIx, newRefStr);
                                        var evStr = false;
                                    }
                                } else {
                                    var evStr = iResValue + c.rule;
                                }
                                // debug && console.log('evStr', evStr);
                                c.resulttype = resulttype;
                                c.eval = evStr;

                                var result = false;
                                try {
                                    result = iResValue && eval(evStr)
                                    // mode == 'backtest' && debug && fnx.log(' Axcz  ', xsmi, evStr, result)
                                } catch (e) {
                                    debug && fnx.log('strategy calc - eval hata', iResValue, e, evStr)
                                }
                                c.result = result;
                                ruleResult.push(result);
                            } else {
                                debug && fnx.log(xsmi, 'indicatorResults not exists', c.indicatorRef );
                                ruleResult.push(false);
                            }

                        }

                        var finalresult = false;
                        if (cond == 0 && ruleResult.length !== 0) finalresult = ruleResult.some(res => res == true);
                        if (cond == 1 && ruleResult.length !== 0) finalresult = ruleResult.every(res => res == true);
                        const IndicatorEval = {
                            indicator,
                            name: rule.name,
                            result: finalresult,
                        }
                        if (mode == 'backtest' && debug) {
                            IndicatorEval.criteria = rule.criteria,
                                IndicatorEval.criteriaResults = { cond, ruleResult };
                        }
                        if (mode !== 'backtest') {
                            IndicatorEval.criteria = rule.criteria;
                            IndicatorEval.criteriaResults = { cond, ruleResult };
                            IndicatorEval.indicatorResults = rltIndicatorResults;
                        }
                        strategyResult.push(
                            IndicatorEval
                        )
                    } else {
                        debug && fnx.log('rlt indicators not found!', rule?.indicators, iResults);
                        strategyResult.push(
                            {
                                indicator,
                                name: rule.name,
                                result: false,
                            }
                        )
                    }
                }

                //cond a bakmadan all must set.
                var finalStrategyResult = strategyResult.length !== 0 && strategyResult.every(res => res.result == true);
                var strategyEval = {
                    xsmi,
                    sName,
                    direction,
                    result: finalStrategyResult,
                    openTime: lastCandle.time,
                    openTimeHRF: new Date(lastCandle.time).toISOString(),
                }

                if (mode !== 'backtest' || (mode == 'backtest' && debug)) {
                    strategyEval.symbol = symbol;
                    strategyEval.sID = sID;
                    strategyEval.lastCandle = lastCandle;
                    strategyEval.klineRefClosedBar = 0;
                    strategyEval.dtCreated = new Date(Date.now());
                    strategyEval.interval = battleInterval;
                    strategyEval.rulesetsResult = strategyResult;
                }
                // fnx.log('strategyEval', symbol, strategyEval)
                allStrategies.push(
                    strategyEval
                )
            }
            
            resolve(allStrategies);
        });
    },
});

const fnTrades = exports.fnTrades = {
    updateOpenTradesPnls: async props => {
        var {   redisClient, symbol, xsmi, lastCandle, updateDexPostData = false, 
                dexApi, clearUnrlvReduceOrders = false, ref, src, 
            } = props;
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await fnxDex.dex.getDexApi({ redisClient });
                let parameters = await fnxDex.dex.getBattleParams({ redisClient });
                let isExchange = await fnxDex.dex.isBattleInExchange({redisClient});
                let battlePairs = parameters.pairs;
                let updateDexPostDataF = updateDexPostData || Array.isArray(battlePairs) && battlePairs[0] == symbol.toUpperCase();
                // let battleType = parameters.battleType;
                // const { dex, config = {} } = battleType;
                // let isExchange = dex == 'exchange';

                // updateDexPostDataF && isExchange && fnx.log('updateOpenTradesPnls battlePairs', battlePairs[0], symbol.toUpperCase(), updateDexPostDataF);
                if (isExchange) {
                    updateDexPostDataF && await fnxDex.dex.syncPosData({
                        redisClient, dexApi, threshold: 30000, clearUnrlvReduceOrders, callRef: 'uptOpenTrdsS'
                    });
                    clearUnrlvReduceOrders && await fnxDex.dex.reconsileBattleV2({
                        redisClient, dexApi, fnClearDexUseLessReduceOrders: clearUnrlvReduceOrders,
                        fnGetOrders: ref == 'closedBar' && src == 'node.kline',
                        fnCheckDexPositionsWO_TPSL: clearUnrlvReduceOrders,
                        refCall: 'uptOpenTrds', symbol
                    });
                    // clearUnrlvReduceOrders && await fnxDex.dex.checkDexPositionsWO_TPSL({
                    //     redisClient, dexApi, battle_parameters: parameters, refCall: 'uptOpenTrds', threshold: 100,
                    // });
                    // clearUnrlvReduceOrders && await fnxDex.dex.clearDexUseLessReduceOrders({
                    //     redisClient, dexApi
                    // });
                    // ref == 'closedBar' && src == 'node.kline' && fnxDex.dex.getOrders({redisClient, dexApi, symbol, ref});
                }

                let redisKeyy = redixPrefix.dataTrades + xsmi;
                let pairTradesAllStg = await redisClient.get(redisKeyy);

                let tradesAll = JSON.parse(pairTradesAllStg);
                if (tradesAll) {
                    Array.isArray(tradesAll) && tradesAll.filter(t => t.tradeClosed !== true).map(t => {
                        let sg = t;
                        let currTrdDirection = sg.direction;
                        let closePrice = parseFloat(lastCandle.close);
                        let entryPrice = parseFloat(sg.entryPrice);
                        let entryAmount = parseFloat(sg.entryAmount);
                        let amtDelta = closePrice - entryPrice;
                        let unRealizedPnlB4Comm = (currTrdDirection == 'long' ? 1 : -1) * amtDelta * entryAmount;
                        let commission = ((closePrice + entryPrice) / 2) * entryAmount * commissionRate
                        let unRealizedPnl = unRealizedPnlB4Comm - commission;
                        sg.commission = commission;
                        sg.entryBudget = entryPrice * entryAmount;
                        sg.notional = closePrice * entryAmount;
                        sg.unRealizedPnlB4Comm = unRealizedPnlB4Comm;
                        sg.unRealizedPnl = unRealizedPnl;
                        sg.closePrice = closePrice;
                        sg.dtupdated = Date.now();
                        sg.dtupdatedEn = new Date(Date.now());
                        return sg;
                    })
                    await redisClient.set(redisKeyy, JSON.stringify(tradesAll));
                    let resp = tradesAll.filter(t => t.tradeClosed !== true);
                    resolve(resp)
                } else {
                    // fnx.log('tradesAll', tradesAll, redisKeyy)
                    resolve([])
                }
            } catch (e) {
                fnx.log(symbol, xsmi, 'updateOpenTradesPnls failed.', e)
            }
        });
    },
    calculateBarCount: async ({ symbol, lastCandle, battleInterval, lastTrade}) => {
        return new Promise(async (resolve, reject) => {
            let resp = false;
            try {
                if (lastTrade) {
                    let refLastTradeBarBop = lastTrade?.entryBarBOP || lastTrade?.entryTime;
                    let refbattleInterval = battleInterval.toString();
                    let carpanUnit = refbattleInterval.slice(-1);
                    let carpanBase = 60000; //1m
                    let carpan = carpanUnit == 'm' ? carpanBase : carpanUnit == 'h' ? 60 * carpanBase : 24 * 60 * carpanBase;
                    let refLastBarBop = lastCandle.time;
                    let barDelta = refLastBarBop - refLastTradeBarBop;
                    let barCount = barDelta / carpan;
                    barCount == undefined && fnx.log('calc error', refLastTradeBarBop, refLastBarBop, barDelta, barCount )
                    resp = barCount //additionPositionCandleInterval >= barCount
                    resolve(resp)
                    // fnx.log('ruleCheckInterval', refLastTradeBarBop, refbattleInterval, carpan, refLastBarBop, 
                    // additionPositionCandleInterval, barCount, '--->', ruleCheckInterval);
                } else {
                    resp = -1;
                    resolve(resp)
                }
            } catch (e) {
                fnx.log('error calc bar count', e)
                resolve('-99')
            }
        });
    },
    entryCheck: (entryCheckParams) => {
        return new Promise(async (resolve, reject) => {
            const { redisClient, symbol, xsmi, newBar,
                strategyRulesetSuccess, entryParams, lastCandle,
                battleInterval, debugMode = false, wallet, exit, ref,
                battleType, isExchange, dexApi, updateDexPostData = false } = entryCheckParams;
            const {
                enterPositon, actOnBarClose,
                direction, positionBudget,
                positionMaxBudget, addAdditionPosition,
                additionPositionPercentage, additionPosRefPriceIsAvg,
                additionPositionCandleInterval, useMartingaleVar,
                useMartingaleLevels,
            } = entryParams;
            // dexApi && fnx.log('xx', symbol, await dexApi.futuresTime());
            var orderConditionsMet = false;
            var trades = false;
            var allTrades = false;
            var openTrades = false;
            var lastTrade = false;
            var allTrades_4wallet = false;
            var stopLossCounter = -1;
            var takeProfitCounter = -1;

            let parameters = await fnxDex.dex.getBattleParams({redisClient});
            let battlePairs = parameters.pairs;

            // let updateDexPostDataF = newBar && updateDexPostData || Array.isArray(battlePairs) && battlePairs[0] == symbol.toUpperCase();

            // let dexTrades = newBar && updateDexPostDataF && isExchange && await fnxDex.dex.syncPosData({redisClient, 
            //     dexApi, threshold: 30000, callRef: 'entryCheck', 
            // });
    
            try {
                trades = await fnTrades.fnGetTrades({
                    redisClient,
                    export2Excel: false,
                    dexApi,
                    // symbol,
                });
                // let tradesReconsile = dexTrades ? await fnxDex.dex.reconsileBattle({
                //     redisClient,
                //     dexApi,
                //     dexTrades,
                //     battleTrades: trades,
                // })
                //     : { success: true };

                allTrades = trades.filter(t => t.pair == symbol);
                if (Array.isArray(allTrades) && allTrades.length !== 0) {
                    stopLossCounter = fnTrades.fnGeneric_getCountOfLastTradeStatus({
                        refArr: allTrades,
                    })

                    takeProfitCounter = fnTrades.fnGeneric_getCountOfLastTradeStatus({
                        refArr: allTrades, refState: 'takeProfit',
                    })

                    openTrades = Array.isArray(allTrades) && allTrades.filter(t => t.tradeClosed == false);
                    let lastTradeObj = openTrades.find(t => t.entryTime == Math.max(...openTrades.map(x => new Date(x["entryTime"]))));
                    if (lastTradeObj) {
                        lastTrade = {
                            ...lastTradeObj
                            // tradeID: allTrades[0].tradeID,
                            // tradeNo: allTrades[0].tradeNo,
                            // tradeSubNo: allTrades[0].tradeSubNo,
                            // pacalNo: allTrades[0].pacalNo,
                        }
                    }
                };
            }
            catch (eLO) {
                fnx.log('lastOpenTrade error', eLO)
            }
            let SLTPAct = false;
            if (openTrades && Array.isArray(openTrades) && openTrades.length !== 0) {
                let openTradesU = openTrades;
                try {
                    openTradesU = await fnTrades.updateOpenTradesPnls({
                        redisClient, symbol, xsmi, lastCandle, ref, dexApi
                    });
                } catch (eU) {
                    fnx.log('updateOpenTradesPnls error', eU)
                }
                let actedSLTP = await fnTrades.sLossTProfitCheck({
                    redisClient, openTrades: openTradesU,
                    lastCandle, symbol, xsmi, wallet, allTrades_4wallet,
                    exit, ref, battleType, isExchange, dexApi,
                });
                // fnx.log(symbol, 'sLossTProfitChecked', actedSLTP)
                SLTPAct = actedSLTP;
            };
            if (enterPositon && !SLTPAct) {
                var conditions = {}
                conditions.ref = {};

                let srsStg = strategyRulesetSuccess && Array.isArray(strategyRulesetSuccess) && strategyRulesetSuccess;
                let srsStgArr = srsStg && (direction !== 'both' ? srsStg.filter(s => s.direction === direction) : srsStg);

                let stateOfCreateOrder = srsStgArr && Array.isArray(srsStgArr) && srsStgArr.length !== 0;
                let directionStratgy = srsStgArr[0]?.direction;

                conditions.ref.directionStratgy = directionStratgy;
                conditions.ref.directionParams = direction;
                conditions.ref.barCountReference = additionPositionCandleInterval;
                conditions.ref.newBar = newBar;
                conditions.ref.symbol = symbol;
                conditions.ref.refPrice_closePrice = lastCandle.close;
                conditions.ref.positionMaxBudget = positionMaxBudget;

                let walletBudget = true;
                let pacalMaxPairs = true;
                let quoteSymbol = 0;
                try {// wallet check.
                    let actArr = trades.filter(t => t.tradeClosed == false);
                    let pnlSum = actArr.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                    let adet = Array.isArray(actArr) && actArr.length !== 0 ? actArr.length : 0;
                    let poz = [...new Set(actArr.map(a => a.pair))].length;
                    let quote = actArr.reduce((acc, x) => acc + parseFloat(x.entryBudget), 0);
                    quoteSymbol = actArr.filter(t => t.symbol == symbol.toUpperCase()).reduce((acc, x) => acc + parseFloat(x.entryBudget), 0);
                    allTrades_4wallet = { pnlSum, adet, position: poz, quote };
                    // console.log('allTrades_4wallet', allTrades_4wallet)
                    // fnx.log('entry check quoteSymbol', symbol, quoteSymbol)
                    walletBudget = wallet.walletBudget * wallet.walletLeverage > allTrades_4wallet.quote * 0.95;
                    pacalMaxPairs = wallet.pacalMaxPairs > allTrades_4wallet.position;
                } catch (eW) {
                    fnx.log('wallet check error', eW);
                }

                if (stateOfCreateOrder && walletBudget) {
                    conditions.positionBudget = positionBudget < positionMaxBudget;
                    conditions.positionMaxBudget2 = quoteSymbol < positionMaxBudget;
                    conditions.onBarClose = actOnBarClose ? newBar : true;
                    let barCount = false;

                    if ((!openTrades || (openTrades && Array.isArray(openTrades) && openTrades.length === 0))) {

                        conditions.direction = direction === 'both' ? true : direction === directionStratgy;
                        let orderCriterias = [
                            conditions.direction,
                            conditions.onBarClose,
                            pacalMaxPairs,
                        ];
                        // fnx.log('pacalMaxPairs', pacalMaxPairs,  wallet.pacalMaxPairs, allTrades_4wallet, allTrades_4wallet.position)
                        conditions.ref.orderCriteriasTxt = "conditions.direction, conditions.onBarClose, pacalMaxPairs";
                        conditions.ref.orderCriterias = orderCriterias

                        orderConditionsMet = orderCriterias.every(e => e === true);
                        conditions.orderConditionsMet = orderConditionsMet;
                        conditions.act = {
                            action: 'createOrder',
                            ref: {
                                refNote: 'firstOrder',
                                lastTrade,
                                openTradesNum: 0,
                                stopLossCounter,
                                takeProfitCounter,
                            }
                        }
                        // debugMode && fnx.log('create first Order', symbol)
                    } else {
                        // let lastTrade = openTrades[0];
                        // fnx.log(symbol, 'lastTrade', lastTrade, openTrades[0]);
                        conditions.barCount = false;
                        // conditions.addAdditionPosition = false;
                        conditions.ref.openTradesCount = openTrades.length;
                        conditions.ref.openTrades = openTrades;
                        conditions.addAdditionPosition = addAdditionPosition; //addition trade cunku,

                        let currTradeDir = openTrades[0].direction;
                        // debugMode && fnx.log('trades dir', openTrades.map(o => o.direction), currTradeDir);

                        conditions.ref.currTradeDir = currTradeDir;
                        // conditions.direction = currTradeDir === directionStratgy;
                        conditions.direction = currTradeDir === directionStratgy;
                        // conditions.direction && fnx.log('conditions.direction', conditions.direction, currTradeDir, directionStratgy )
                        barCount = await fnTrades.calculateBarCount({ //redisClient, 
                            symbol, lastCandle, battleInterval, lastTrade
                        });
                        barCount == undefined && fnx.log('calculateBarCount', symbol, lastCandle, battleInterval, lastTrade);
                        conditions.ref.barCountCalculated = barCount;
                        conditions.ref.barCountReference = additionPositionCandleInterval;
                        if (barCount) {
                            let ruleCheckInterval = barCount < 0 ? true : additionPositionCandleInterval <= barCount
                            conditions.barCount = ruleCheckInterval;
                        }
                        let cTradeOpenVolume = openTrades.reduce((acc, x) => acc + (parseFloat(x.entryPrice) * parseFloat(x.entryAmount)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);
                        let cTradeOpenQty = openTrades.reduce((acc, x) => acc + (parseFloat(x.entryAmount)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);

                        let AvgOpenOrdersEntryPrice = cTradeOpenQty > 0 ? cTradeOpenVolume / cTradeOpenQty : 0;
                        // fnx.log('AvgOpenOrdersEntryPrice', symbol, barCount, cTradeOpenVolume, cTradeOpenQty, AvgOpenOrdersEntryPrice)
                        // openTrades
                        //   .filter(x => x.entryPrice)
                        //   .reduce((acc,x) => acc + parseFloat(x.entryPrice), 0) / openTrades.length;

                        let refPrice = additionPosRefPriceIsAvg ? AvgOpenOrdersEntryPrice : parseFloat(lastTrade.entryPrice); // parseFloat(openTrades[0].entryPrice);
                        conditions.ref.refPrice = refPrice;
                        conditions.ref.refPrice_entryPrice_avg = AvgOpenOrdersEntryPrice;
                        conditions.ref.refPrice_entryPrice_last = lastTrade.entryPrice; // openTrades[0].entryPrice;
                        // let calcPercentagerefPriceVsClosePrice =  (Math.abs(refPrice - parseFloat(lastCandle.close)) / refPrice) * 100
                        let calcPercentagerefPriceVsClosePrice = currTradeDir === 'long' ?
                            (refPrice - parseFloat(lastCandle.close)) / parseFloat(refPrice) * 100
                            :
                            (parseFloat(lastCandle.close) - parseFloat(refPrice)) / parseFloat(refPrice) * 100
                        //additionPositionPercentage
                        conditions.ref.refPrice_calcPercentagerefPriceVsClosePrice = calcPercentagerefPriceVsClosePrice;
                        conditions.ref.refPrice_additionPositionPercentage = additionPositionPercentage;
                        conditions.additionPositionPercentage = calcPercentagerefPriceVsClosePrice >= additionPositionPercentage;

                        //position Max Budget check...
                        let OpenOrdersUsedBudget = openTrades
                            .filter(x => x.entryBudget)
                            .reduce((acc, x) => acc + parseFloat(x.entryBudget), 0)

                        conditions.ref.OpenOrdersUsedBudget = OpenOrdersUsedBudget;
                        conditions.positionMaxBudget = positionMaxBudget > OpenOrdersUsedBudget;

                        let orderConditions = [
                            conditions.direction,
                            conditions.onBarClose,
                            conditions.addAdditionPosition,
                            conditions.barCount,
                            conditions.additionPositionPercentage,
                            conditions.positionMaxBudget,
                            conditions.positionMaxBudget2,
                        ];
                        conditions.ref.orderConditions = orderConditions

                        orderConditionsMet = orderConditions.every(e => e === true);
                        conditions.orderConditionsMet = orderConditionsMet;
                        conditions.act = {
                            action: 'createOrder',
                            ref: {
                                refNote: 'additionalOrder',
                                lastTrade,
                                openTradesNum: openTrades.length
                            }
                        }
                    }
                } else {
                    // fnx.log('do nothing!!.', symbol)
                }

                resolve({
                    result: conditions.orderConditionsMet === true,
                    act: conditions.act,
                    conditions,
                    stateOfCreateOrder,
                    walletBudget,
                })
            } else {
                resolve(true);
            }
        });
    },
    fnGetTrades: ({redisClient, symbol, export2Excel = false}) => {
        return new Promise(async (resolve, reject) => {
            let trades = [];
            let resp; 
            let orders = [];
                
            let nodes = await redisClient.keys(redixPrefix.dataTrades +'*');
            if (Array.isArray(nodes)) {
                for (n of nodes) {
                    const value = await redisClient.get(n);
                    let nodeData = JSON.parse(value);
                    trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                }
            };
            if (symbol) {
                trades = trades.filter(t => t.pair == symbol)
            };
            export2Excel && fnx.log('export2Excel', export2Excel)
            if (Array.isArray(trades) && export2Excel) {
                let nodeOrders = await redisClient.keys(redixPrefix.dataOrders + '*');
                if (Array.isArray(nodeOrders)) {
                    for (n of nodeOrders) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        orders = nodeData && nodeData.length !== 0 ? [...orders, ...nodeData] : orders;
                    }
                };
                if (Array.isArray(orders)) {
                    trades.map(t => {
                        let rltOrderData = orders.find(o => o.orderID == t.orderID);
                        t.strategyID = rltOrderData?.orderSrc_successStrategyID;
                        t.strategyName = rltOrderData?.orderSrc_successStrategyName;
                        t.ref = rltOrderData?.orderSrc_ref
                    });
                }
            }
            // console.log('orders', trades)
            resp = trades;
            resolve(resp);
        });
    },
    entry: (entry_Params) => {
        return new Promise(async (resolve, reject) => {
            // fnx.log('entry -', entryParams);
            try {
                var { taskDesc, battleID, nodeKey, taskID, taskTag,
                    script2Run, script2RunUri, batch, nodeInterval, battleInterval,
                    taskType, wsconn, wsconnuri, period, periodDesc,
                    taskParams, redisKey, redisClient, is_active, process_id,
                    port, pair, id, nextRun, battleData, symbol, xsmi, wallet,
                    exit, lastCandle, debugMode, newBar, entryParams, act,
                    entryCheck, strategyRulesetSuccess, ref, battleType, isExchange, 
                    dexApi,
                } = entry_Params;
                // !taskTag && fnx.log('taskTag entry', entry_Params);
                const {
                    dexEntryOrderType = 'MARKET', 
                    dexEntryPriceSlippageRatio = 0,
                    timeInForce = 'GTC',
                    newOrderRespType = 'RESULT',
                } = battleType;

                isExchange = isExchange !== undefined ? isExchange : await fnxDex.dex.isBattleInExchange({redisClient});
                dexApi = dexApi || await fnxDex.dex.getDexApi({redisClient});

                let ttag = taskTag || entry_Params?.xsmi || entry_Params?.symi;
                let isNewBar = newBar;
                let entry = entryParams;
                const direction = strategyRulesetSuccess[0].direction;
                const refSuccessStrategyID = strategyRulesetSuccess[0].sID;
                const refSuccessStrategyName = strategyRulesetSuccess[0].sName;

                var posBudgetStg = entry.positionBudget;
                let stopLossCounter = act?.ref?.stopLossCounter;
                let takeProfitCounter = act?.ref?.takeProfitCounter;

                // if (entry.useMartingaleVar &&
                //     Array.isArray(entry.useMartingaleLevels) &&
                //     entry.useMartingaleLevels.length !== 0 &&
                //     stopLossCounter &&
                //     stopLossCounter >= 0) {
                //     let getMultiplierObj = [...entry.useMartingaleLevels].find(m => m.level == stopLossCounter);
                //     let multiplier = getMultiplierObj ? getMultiplierObj.multiplier : 1;
                //     posBudgetStg = posBudgetStg * multiplier;
                //     // console.log('stopstopLossCounter', stopLossCounter, entry.positionBudget, multiplier, posBudgetStg, );
                //     // console.log('levels', entry.useMartingaleVar, entry.useMartingaleLevels);
                // }

                // console.log(pair, 'useMartingale_', stopLossCounter, takeProfitCounter, entry)
                if (entry.useMartingale_stopLoss &&
                    Array.isArray(entry.useMartingale_stopLoss) &&
                    entry.useMartingale_stopLoss.length !== 0 &&
                    stopLossCounter &&
                    stopLossCounter >= 0) {
                    let getMultiplierObj = [...entry.useMartingale_stopLoss].find(m => m.level == stopLossCounter);
                    let multiplier = getMultiplierObj ? getMultiplierObj.multiplier : 1;
                    posBudgetStg = posBudgetStg * multiplier;
                    // console.log(pair, 'useMartingale_takeProfit', stopLossCounter, multiplier, posBudgetStg)
                }
                if (entry.useMartingale_takeProfit &&
                    Array.isArray(entry.useMartingale_takeProfit) &&
                    entry.useMartingale_takeProfit.length !== 0 &&
                    takeProfitCounter &&
                    takeProfitCounter >= 0) {
                    let getMultiplierObj = [...entry.useMartingale_takeProfit].find(m => m.level == takeProfitCounter);
                    let multiplier = getMultiplierObj ? getMultiplierObj.multiplier : 1;
                    posBudgetStg = posBudgetStg * multiplier;
                    // console.log(pair, 'useMartingale_takeProfit', takeProfitCounter, multiplier, posBudgetStg)
                }

                const orderID = fnx.generateKey({ inclTime: false });
                const posBudget = posBudgetStg;
                // console.log(pair, 'posBudget', posBudget)
                let orderAmount = posBudget / lastCandle.close;

                const deltaValues = await subFn.fnDelta({
                    symbol
                })
                let dv = {
                    symbol,
                    deltaValue: deltaValues?.deltaValue,
                    PCPValue: deltaValues?.PCPValue,
                    PCPpercentile: deltaValues?.PCPpercentile,
                    deltaPercentile: deltaValues?.deltaPercentile,
                    quoteVolume: deltaValues?.quoteVolume,
                    PCPmean: deltaValues?.PCPmean,
                    PCPmedian: deltaValues?.PCPmedian,
                    deltaMean: deltaValues?.deltaMean,
                    deltaMedian: deltaValues?.deltaMedian,
                    dteventISO: deltaValues?.dteventISO,
                }

                var orderData = {
                    symbol, 
                    orderID,
                    orderTransactionType: "createOrder",
                    orderTransactionSubType: 0,
                    orderType: direction,
                    orderPrice: lastCandle.close,
                    orderBudget: posBudget,
                    orderAmount: orderAmount,
                    orderTime: Date.now(),
                    orderTimeEn: new Date(Date.now()).toISOString(),
                    dtCreated: new Date(Date.now()).toISOString(),
                    orderNote: 'init',
                    orderSrc_ref: ref,
                    orderSrc_isNewBar: isNewBar,
                    orderSrc_successStrategyID: refSuccessStrategyID,
                    orderSrc_successStrategyName: refSuccessStrategyName,
                    orderSrc_candle: JSON.stringify(lastCandle),
                    orderSrc_candleMore: {
                        ...dv,
                    }
                };
                orderData.act = act;
                orderData.entryCheck = entryCheck;
                //TODO: ORDER I "LIMIT" OLMA DURUMU
                let refCall = 'fnTrade.entry';
                if (isExchange) {
                    // fnx.log('create trade x1 / fnTrade.entry', orderData?.symbol);
                    refCall += '.dex';
                }

                // console.log('ttag', ttag, taskTag, entry_Params?.xsmi, entry_Params?.symi);
                let redisOrderKey = redixPrefix.dataOrders + ttag.toLowerCase();
                let currOrdersStg = await redisClient.get(redisOrderKey);
                let currOrders = JSON.parse(currOrdersStg);
                let orders = currOrders ? currOrders : [];
                orders.push(orderData);
                await redisClient.set(redisOrderKey, JSON.stringify(orders));
                await fnxDex.battle.createTrade({
                    redisClient,
                    payload: orderData,
                    taskTag: ttag.toLowerCase(),
                    battleType, isExchange, dexApi, refCall, section: refCall,
                })

                // fnx.log('entry: orderData', orderData, redisTradeKey)
                resolve(true)
 
            } catch (e) {
                fnx.log('create error, ', e)
                reject(e);
            }
        });
    },
    sLossTProfitCheckDexActions: async props => {
        const {redisClient, dexApi, symbol} = props;
        return new Promise(async (resolve, reject) => {
            let currPositions = await fnxDex.dex.getDexPositionsCache({
                redisClient, dexApi, symbol: symbol.toUpperCase(),
                refCall: 'idx: sLossTProfitCheckDexActions',
            });
            // fnx.log('currPositions', symbol, currPositions);
            resolve(currPositions)
        });
    },
    sLossTProfitCheck: async ({
            redisClient, openTrades, lastCandle, symbol, xsmi, 
            wallet, exit, ref, battleType, isExchange, dexApi
        }) => {
        return new Promise(async (resolve, reject) => {
            // fnx.log('lastCandle', JSON.stringify(lastCandle));
            let calcf = [];
            let dir;
            let pair;
            let openTradesFresh;
            if (!openTrades) {
                try {
                    let redisKeyy = redixPrefix.dataTrades + xsmi;
                    let pairTradesAllStg = await redisClient.get(redisKeyy);
                    let tradesAll = JSON.parse(pairTradesAllStg);
                    openTradesFresh = tradesAll && 
                        Array.isArray(tradesAll) && 
                        tradesAll.filter(t => t.tradeClosed !== true);
                    // console.log('openTradesFresh', redisKeyy); //openTradesFresh
                } catch (e) {
                    fnx.log(symbol, xsmi, 'openTradesFresh failed.', e)
                }
            }
            openTrades = openTrades || openTradesFresh;
            let checkTP;
            let checkSL;
            if (openTrades) {
                openTrades.map(t => {
                    dir = t.direction;
                    pair = t.pair;
                });
                calcf = openTrades;
                let Samount = calcf.reduce((acc, x) => acc + (parseFloat(x.entryPrice) * parseFloat(x.entryAmount)), 0);
                let SunRealizedPnl = calcf.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                let SunRealizedPnlB4Comm = calcf.reduce((acc, x) => acc + parseFloat(x.unRealizedPnlB4Comm), 0);
                let uPnlRatioLC = (SunRealizedPnl / Samount * 100);
                
                const esik = 0.2;
    
                if (SunRealizedPnl > 0 || SunRealizedPnlB4Comm > 0 ) {
                    //state for control tp..
                    checkTP = {
                        unRealizedPnl: SunRealizedPnl,
                        unRealizedPnlB4Comm: SunRealizedPnlB4Comm,
                        amount: Samount,
                        takeProfitRatio: exit.takeProfitRatio,
                        uPNLRatioLC: uPnlRatioLC, 
                        result: uPnlRatioLC > exit.takeProfitRatio,
                        // openTrades,
                    }
    
                } else if ((SunRealizedPnl < 0 || SunRealizedPnlB4Comm < 0)) {
                    checkSL = {
                        unRealizedPnl: SunRealizedPnl,
                        unRealizedPnlB4Comm: SunRealizedPnlB4Comm,
                        amount: Samount,
                        stopLoss: exit.stopLoss,
                        stopLossUsePercentage: exit.stopLossUsePercentage,
                        stopLossPercentage: exit.stopLossPercentage,
                        uPNLRatioLC: uPnlRatioLC,
                        result: exit.stopLossUsePercentage ?
                              uPnlRatioLC * (-1) > exit.stopLossPercentage
                            : SunRealizedPnl < (exit.stopLoss * (-1)),
                    }
    
                } else {
                    //state of anonym!
                    fnx.log('state of anon', pair, dir, symbol, xsmi)
                };
            }

            isExchange = isExchange !== undefined ? isExchange : await fnxDex.dex.isBattleInExchange({redisClient});
                
            //DONE : isexch?
            let hasOpenPosition = isExchange ? (checkTP?.result || checkSL?.result) ? await fnTrades.sLossTProfitCheckDexActions({
                redisClient, dexApi, symbol: symbol.toUpperCase(),
            }) : false : false;

            if (checkTP?.result) { 
                let keepGoing = isExchange ? !hasOpenPosition || (hasOpenPosition && hasOpenPosition.length == 0) : true;
                keepGoing && await fnTrades.closeTrade({
                    redisClient,
                    symbol,
                    xsmi,
                    lastCandle,
                    closeType: 10,
                    updateNote: 'closed-checkTP',
                    closeNote: 'takeProfit',
                    ref,
                });
                // !keepGoing && fnx.log('reset checkTP tp/sl orders on dex', symbol);
                !keepGoing && isExchange && await fnxDex.dex.checkDexTPSLs({
                    redisClient, dexApi, symbol, 
                    refCall: 'takeProfitCheck',
                })
                //DONE: close if outside of the bounds.
                resolve(false)
            } else if (checkSL?.result) {
                let keepGoing = isExchange ? !hasOpenPosition || (hasOpenPosition && hasOpenPosition.length == 0) : true;
                keepGoing && await fnTrades.closeTrade({
                    redisClient,
                    symbol,
                    xsmi,
                    lastCandle,
                    closeType: 20,
                    updateNote: 'closed-checkSL',
                    closeNote: 'stopLoss',
                    ref,
                });
                // !keepGoing && fnx.log('reset checkSL tp/sl orders on dex', symbol);
                !keepGoing && await fnxDex.dex.checkDexTPSLs({
                    redisClient, dexApi, symbol, 
                    refCall: 'stopLossCheck'
                })
                //DONE: close if outside of the bounds.
                resolve(true)
            } else {
                //do nothing...
                // fnx.log('hasOpenPosition', hasOpenPosition)
                resolve(false)
            }
        });
    },
    closeTrade: async props => {
        var { redisClient, symbol, xsmi, lastCandle,
            updateNote, closeNote, closeType, ref } = props;
        return new Promise(async (resolve, reject) => {
            let isExchange = await fnxDex.dex.isBattleInExchange({ redisClient });
            // fnx.log('close trades', symbol, 'isExchange', isExchange);
            let keepOn = false;
            let dexCloseStatus;
            if (isExchange) {
                dexCloseStatus = await fnxDex.dex.closeDexTrade({
                    redisClient, symbol: xsmi.split('_')[0].toUpperCase(), 
                    refCall: 'closeTrade', section: 'fnTrades.closeTrade..'
                });
                // fnx.log('close trades dexCloseStatus', dexCloseStatus);
                if (dexCloseStatus && dexCloseStatus.success && dexCloseStatus.status == 'FILLED') {
                    fnx.log('dexCloseStatus', dexCloseStatus);
                    keepOn = true;
                } else {
                    keepOn = false;
                }
            } else {
                keepOn = true;
            }
            // fnx.log('close trades', symbol, 'keepOn', keepOn);
            if (keepOn) {
                let redisKeyy = redixPrefix.dataTrades + xsmi;
                let pairTradesAllStg = await redisClient.get(redisKeyy);
                let tradesAll = JSON.parse(pairTradesAllStg);
                // fnx.log('tradesall', symbol, JSON.stringify(tradesAll));
                tradesAll.filter(t => t.tradeClosed !== true).map(t => {
                    let sg = t;
                    let currTrdDirection = sg.direction;

                    let closePrice = !isExchange ? (parseFloat(lastCandle.close)) : (parseFloat(dexCloseStatus?.resp?.avgPrice) || parseFloat(lastCandle.close));
                    let entryPrice = parseFloat(sg.entryPrice);
                    let entryAmount = parseFloat(sg.entryAmount);

                    let realizedPips = (closePrice - entryPrice) * (currTrdDirection == 'long' ? 1 : -1);
                    let realizedPipRatio = realizedPips / entryPrice;
                    let realizedPnlB4Comm = realizedPips * entryAmount;
                    let commission = ((closePrice + entryPrice) / 2) * entryAmount * commissionRate
                    let realizedPnl = realizedPnlB4Comm - commission;

                    closeType = closeType || (realizedPnl > 0 ? 10 : 20);
                    
                    sg.closePrice = closePrice;
                    sg.closeRef = ref;
                    sg.closeNote = closeNote;
                    sg.closeType = closeType;
                    sg.tradeClosed = true;
                    sg.updateNote = updateNote;
                    sg.entryBudget = entryPrice * entryAmount;
                    sg.notional = closePrice * entryAmount;
                    sg.unRealizedPnlB4Comm = 0;
                    sg.unRealizedPnl = 0;

                    sg.realizedPips = realizedPips;
                    sg.realizedPipRatio = realizedPipRatio;
                    sg.realizedPnlB4Comm = realizedPnlB4Comm;
                    sg.commission = commission;
                    sg.realizedPnl = realizedPnl;

                    sg.closeBarEOP = lastCandle.time;
                    sg.tradeLifeTime = parseFloat(((lastCandle?.time || Date.now()) - sg?.entryTime) / 1000 / 60).toFixed(1) + 'min'
                    
                    sg.closeTime = Date.now();
                    sg.closeTimeEn = new Date(Date.now());
                    sg.dtupdated = Date.now();
                    sg.dtupdatedEn = new Date(Date.now());
                    return sg;
                })

                await redisClient.set(redisKeyy, JSON.stringify(tradesAll));
                resolve(true);
            } else {
                resolve(false)
            }
        });
    },
    fnGeneric_getCountOfLastTradeStatus: ({ refArr, refState = 'stopLoss' }) => {
        var state = false;
        var stateTarget = refState;
        var counter = 0;
        // refArr.map(t => t.closeNote = t.closeType == 20 ? 'stopLoss' : t.closeNote)
        let arx = refArr
            .filter(t => t.tradeSubNo == 1 && t.tradeClosed == true)
            .map(t => {
                t.closeNote = (t.closeType == 20 ? 'stopLoss' : t.closeNote)
                return t
            })
            .sort((a, b) => (a.entryBarBOP > b.entryBarBOP ? 1 : -1))
        arx.map(t => {
            state = t.closeNote;
            if (state == stateTarget) {
                counter++;
            } else {
                counter = 0;
            }
        });
        return counter;
    },
};

const stat = exports.stat = {
    calcAverage: arr => {
        var a = arr.slice();
        if (a.length) {
            sum = stat.sumArr(a);
            avg = sum / a.length;
            return avg;
        }
        return false;
    },
    calcMax: (arr) => {
        return Math.max(...arr);
    },
    calcMin: (arr) => {
        return Math.min(...arr);
    },
    calcMedian: (arr) => {
        var a = arr.slice();
        hf = Math.floor(a.length / 2);
        arr = stat.sortArr(a);
        if (a.length % 2) {
            // return a[hf];
            return arr[hf];
        } else {
            return (parseFloat(arr[hf - 1]) + parseFloat(arr[hf])) / 2.0;
        }
    },
    calcMode: (arr) => {
        var ary = arr.slice();
        t = ary.sort(function (a, b) {
            ary.filter(function (val) {
                val === a
            }).length - ary.filter(function (val) {
                val === b
            }).length
        });
        return t.pop();
    },
    calcQuartile: (arr, q) => {
        var a = arr.slice();
        // Turn q into a decimal (e.g. 95 becomes 0.95)
        q = q / 100;

        // Sort the array into ascending order
        data = stat.sortArr(a);

        // Work out the position in the array of the percentile point
        var p = ((data.length) - 1) * q;
        var b = Math.floor(p);

        // Work out what we rounded off (if anything)
        var remainder = p - b;

        // See whether that data exists directly
        if (data[b + 1] !== undefined) {
            return parseFloat(data[b]) + remainder * (parseFloat(data[b + 1]) - parseFloat(data[b]));
        } else {
            return parseFloat(data[b]);
        }
    },
    calcRange: (arr) => {
        mx = stat.calcMax(arr);
        mn = stat.calcMin(arr);
        return mx - mn;
    },
    sumArr: (arr) => {
        var a = arr.slice();
        return a.reduce(function (a, b) { return parseFloat(a) + parseFloat(b); });
    },
    sortArr: (arr) => {
        var ary = arr.slice();
        ary.sort(function (a, b) { return parseFloat(a) - parseFloat(b); });
        return ary;
    }

}

/*
db.getCollection('gauss.strategies').find({ruleSet: /.*ref.* /})
*/


