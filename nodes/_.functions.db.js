
const fnx = require('./_.functions');
const { v4: uuidv4 } = require('uuid');

const q = (exports.q = {
    query: (db, sql) => {
        return new Promise(async (resolve, reject) => {
            try {
                var res = db.prepare(sql).all();
                resolve(res)
            } catch (e) {
                fnx.log('fbdb query error', e);
                reject(e)
            }
        });
    },      
    listtables: ({db}) => {
        return new Promise(async (resolve, reject) => {
            try {
                let qTxt = `SELECT name
                            FROM sqlite_schema
                            WHERE   type ='table' 
                                    AND
                                    name NOT LIKE 'sqlite_%'
                                    ;`;
                // var query = fndb.query(db, qTxt, null)
                var query = db.prepare(qTxt).all()
                resolve(query);
            } catch (e) {
                console.log('generic time e', e)
            }
        });
    },
});
const cud = (exports.cud = {
    insertOne: ({ db, table, payload, setid = false }) => {
        return new Promise(async (resolve, reject) => {
            let sqlq = '';
            try {
                let fields = [];
                let fieldValues = [];
                for (const key in payload) {
                    if (payload.hasOwnProperty(key)) {
                        fields.push(key);
                        fieldValues.push(typeof (payload[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]);
                    }
                }
                if (setid) {
                    id = uuidv4();
                    fields.push(setid);
                    fieldValues.push("'" + id + "'");
                }
                sqlq += ' INSERT INTO ' + table + ' (' + fields.toString() + ')';
                sqlq += ' VALUES( ' + fieldValues.toString() + ' );'
                // console.log('sql one', sqlq);
                var res = db.prepare(sqlq).run();
                resolve(res)
            } catch (e) {
                console.log('cud insertOne e', e, sqlq)
                reject(e)
            }
        });
    }
});

/*

runQueries: (db) => {
db.all(`select hero_name, is_xman, was_snapped from hero h
inner join hero_power hp on h.hero_id = hp.hero_id
where hero_power = ?`, "Total Nerd", (err, rows) => {
rows.forEach(row => {
console.log(row.hero_name + "\t" +row.is_xman + "\t" +row.was_snapped);
});
});
},
createtable: ({db, tname, tcols}) => {
    return new Promise(async (resolve, reject) => {
        try {
            let qTxt = 'CREATE TABLE ' + tname + ' ( ' + tcols + ')';
            var query = db.prepare(qTxt);
            const adopt = db.transaction((cats) => {
                query.run();
                });
            resolve(adopt)
        } catch (e) {
            console.log('createtable e', e)
        }
    });
}, 
*/
/*
//with serialize method
db.serialize(() => {
db.run("DROP TABLE playlists");
db.run("CREATE TABLE playlists([PlaylistId] INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,[Name] NVARCHAR(120))");
db.run("INSERT INTO playlists (name) VALUES  ('Music'), ('Movies'), ('TV Shows')");
});
*/