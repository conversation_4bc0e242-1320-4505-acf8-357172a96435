require('dotenv').config();
// const { exec } = require('child_process');
const path = require('path');
const express = require('express')
const fnx = require('./_.functions');
const bodyParser = require('body-parser')
const Database = require('better-sqlite3');
const CronJobManager = require('cron-job-manager');
const Binance = require('node-binance-api');
const cors = require('cors');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const process= require('process');
process.removeAllListeners('warning');

const modeDebug = true;
const dbGauss = new Database(path.resolve(__dirname, '../db/gauss.db')); 
const dbMarket = new Database(path.resolve(__dirname, '../db/market.db'));
const dbTrades = new Database(path.resolve(__dirname, '../db/trades.db'));
dbTrades.pragma('journal_mode = WAL'); //, { verbose: fnx.log }
const args = process.argv.slice(2);
var porte = null;
const app = express();
app.set('view engine', 'ejs');
var battleData = {};
(async () => {
    
    const cronMan = new CronJobManager();
    // app.use(bodyParser.urlencoded({ extended: false }))
    // app.use(bodyParser.json({ type: 'application/*+json' }))
    app.use(bodyParser.json());
    app.use(cors());

    process.on('SIGHUP', () => {
        console.log('Got SIGHUP signal.');
        process.exit(5)
      });

    app.get('/stop', async (req, res) => {
        fnx.log(`Trader Node ${porte} stopped!`);
        //kill self!
        await fnx.nodes.killNode({db: dbGauss, process_id: process.pid, node_key: 'Market_Trader'});

        process.exit(0)
    })

    app.get('/trades/:pair?', (req, res) => fnx.nTasks.tradeslist(req, res, {
        dbTrades
    }));

    // app.get('/rising/trades/:pair?', pgTrades.viewRisingTrades);
    app.get('/checktrades', (req, res) => {
        fnx.log(`Trader Node checktrades!`);
        //kill self!
        const {battle_params = {}} = battleData;
        const {parameters = {}} = battle_params;
        const {trading = {}} =  parameters;
        const {entry, exit, wallet} = trading;
        res.json({ entry, exit, wallet, })
        // process.exit(0)
    });

    app.get('/closealltrades', (req, res) => {
        fnx.log(`Trader Node closealltrades!`);
        //kill self!
        const {battle_params = {}} = battleData;
        const {parameters = {}} = battle_params;
        const {trading = {}} =  parameters;
        const {entry, exit, wallet} = trading;
        const act = async () => {
            try {
                await fnx.nTasks.closeOpenTrades({
                    dbTrades,
                    dbMarket,
                });
                res.json({ entry, exit, wallet, })
            } catch (e) {
                res.json({ entry, exit, wallet, })
            }
        }
        act();
        // process.exit(0)
    });

    app.post('/checktrader', (req, res) => {
        const {battle_params = {}} = battleData;
        const {parameters = {}} = battle_params;
        const {trading = {}} =  parameters;
        const {entry, exit, wallet} = trading;
        const payLoad = req.body;
        const {
            symbol, lastCandle, 
            indicatorCalculations,
            strategyRulesetCalculations,
            xsmi, ref, refID, isNewBar} = payLoad;
        fnx.log(' ');
        fnx.log(`Trader Node checktrader!`, symbol, ref, refID, isNewBar, xsmi);
        // res.send('POST isteği geldi!')
        res.json({ resp: 'post geldi' })
    });

    app.get('/info', (req, res) => {
        // console.log('info')
        res.json({ battleData, })
    })
 
    const server = app.listen(130, async () => {
        const PORT = server.address().port;
        porte = PORT;
        fnx.log(`starting trade server on PORT/pID: ${PORT}`)

        let battle_params;
        try {
            const battle = await fnx.nTasks.getBattle({ db: dbGauss, battleID: false, modeTest: false });
            const battleParamsStg = JSON.parse(JSON.stringify(battle));
            try {
                battle_params = JSON.parse(battleParamsStg?.battle_params);
                battleParamsStg.battle_params = battle_params;
            } catch (eJ) {
                modeDebug && fnx.log(`battleParamsStg?.battle_params error: ${eJ}`)
            }
            // modeDebug && fnx.log('battle_params: ', JSON.stringify(battle_params));

            await fnx.nTasks.createNtable({
                sqlClient: dbTrades, target: 'trades_orders',
            });
            await fnx.nTasks.createNtable({
                sqlClient: dbTrades, target: 'trades_trades',
            });
            
            battleData = battleParamsStg
            await fnx.nodes.updateNodeProcessIDandPort({
                db: dbGauss,
                node_key: 'Market_Trader',
                pid: process.pid,
                port: PORT,
                forceInsert: true,
                battle,
                modeDebug,
            });

            /*
                await fnx.nTasks.createNtable({
                    sqlClient: dbMarket, target: nodeTask?.node_type === 'kline' ? 'kline_init' : false, symi: symi,
                });

                await fnx.nTasks.setCronJobs({...actBase, symi, 
                    tickerTimeMonitor, 
                    tickerProcessTimeMonitor, tickerProcessTimer, });
                try {
                    await fnx.nTasks.fetchInitialData({ ...actBase, symi });
                } catch (eX) {
                    console.log('ex', eX);
                }
                */
             
            fnx.log(`http://localhost:${PORT}/info`);
            // fnx.nodes.killNode({db, process_id: process.pid, node_key: node_key})
            // process.exit(1)
        } catch (a) {
            console.log('error', a)
            process.exit(1)
        }
    })
})();



