const Redis = require('ioredis');
const { parentPort, workerData, MessageChannel, receiveMessageOnPort } = require("worker_threads");
const redixPrefix = require('../src/lib/redis.prefix.js');
const fnx = require('./_.functions');
const fnxIdx = require('./_.functions.indicators');
const commissionRate = 0.0007;

(async () => {
    const redisClient = new Redis({
        host: '127.0.0.1',
        port: 6379,
    });
    let dtBOP = Date.now();
    let backtestDataStg = await redisClient.get('backtestData');
    let backtestData = backtestDataStg && typeof backtestDataStg !== 'object' ? JSON.parse(backtestDataStg) : backtestDataStg;
    let battle_paramsStg = backtestData?.battle_params;
    let battle_params = battle_paramsStg && typeof battle_paramsStg !== 'object' ? JSON.parse(battle_paramsStg) : battle_paramsStg;
    let parameters = battle_params?.parameters;
    const {
        pairs,
        candleCounts,
        intervals,
        battleInterval,
        indicatorsWParams,
        rulesets,
        trading,
    } = parameters;
    let indicatorsWParamsObj = indicatorsWParams && typeof indicatorsWParams !== 'object' ? JSON.parse(indicatorsWParams) : indicatorsWParams;

    parentPort.on("message", async (packData = {}) => {
        const { packID, pack, addon} = packData;
        let sureler = [];
        fnx.log('worker.js - pack', pack);
        for (const redisKey of pack) {

            // var transactions = [];
            const doTrade = async tradeData => {
                const { debug, candle, candleIndex, successRuleset } = tradeData;
                const { entry, exit, wallet } = trading;
                const {
                    enterPositon,
                    actOnBarClose,
                    direction,
                    positionBudget,
                    positionMaxBudget,
                    addAdditionPosition,
                    additionPositionPercentage,
                    additionPosRefPriceIsAvg,
                    additionPositionCandleInterval,
                    useMartingaleVar,
                    useMartingaleLevels,
                } = entry;

                let transaction;

                return new Promise(async (resolve, reject) => {
                    let tradesAct = trades.filter(t => t.symbol = candle.symbol)
                    const lastCandle = candle;
                    // const posBudget = entry.positionBudget;
                    var posBudgetStg = entry.positionBudget;

                    let stopLossCounter = tradesAct.length !== 0 ? fnxIdx.fnTrades.fnGeneric_getCountOfLastTradeStatus({
                        refArr: tradesAct, refState: 'stopLoss',
                    }) : -1

                    let takeProfitCounter = tradesAct.length !== 0 ? fnxIdx.fnTrades.fnGeneric_getCountOfLastTradeStatus({
                        refArr: tradesAct, refState: 'takeProfit'
                    }) : -1

                    // fnx.log('tradesAct', candle.symbol, stopLossCounter)
                    // if (entry.useMartingaleVar &&
                    //     Array.isArray(entry.useMartingaleLevels) &&
                    //     entry.useMartingaleLevels.length !== 0 &&
                    //     stopLossCounter &&
                    //     stopLossCounter >= 0) {
                    //     let getMultiplierObj = [...entry.useMartingaleLevels].find(m => m.level == stopLossCounter);
                    //     let multiplier = getMultiplierObj ? getMultiplierObj.multiplier : 1;
                    //     posBudgetStg = posBudgetStg * multiplier;
                    // } 
                    if (entry.useMartingale_stopLoss &&
                        Array.isArray(entry.useMartingale_stopLoss) &&
                        entry.useMartingale_stopLoss.length !== 0 &&
                        stopLossCounter &&
                        stopLossCounter >= 0) {
                        let getMultiplierObj = [...entry.useMartingale_stopLoss].find(m => m.level == stopLossCounter);
                        let multiplier = getMultiplierObj ? getMultiplierObj.multiplier : 1;
                        posBudgetStg = posBudgetStg * multiplier;
                        // console.log(pair, 'useMartingale_takeProfit', stopLossCounter, multiplier, posBudgetStg)
                    }
                    if (entry.useMartingale_takeProfit &&
                        Array.isArray(entry.useMartingale_takeProfit) &&
                        entry.useMartingale_takeProfit.length !== 0 &&
                        takeProfitCounter &&
                        takeProfitCounter >= 0) {
                        let getMultiplierObj = [...entry.useMartingale_takeProfit].find(m => m.level == takeProfitCounter);
                        let multiplier = getMultiplierObj ? getMultiplierObj.multiplier : 1;
                        posBudgetStg = posBudgetStg * multiplier;
                        // console.log(pair, 'useMartingale_takeProfit', takeProfitCounter, multiplier, posBudgetStg)
                    }

                    const posBudget = posBudgetStg;

                    let orderConditionsMet = false;
                    let orderConditions = false;

                    let orderPrice = Number(candle.close);
                    let orderAmount = posBudget / orderPrice;
                    let openTrades = tradesAct.filter(t => t.tradeClosed !== true);
                    let tradePayLoadStg = {
                        tradeNo: 1,
                        tradeSubNo: 1,
                        pacalNo: 1,
                        entryNote: 'firstOrder',
                    };

                    let lastTrade = false;
                    let isThisAdditionalOrder = openTrades.length !== 0;
                    if (isThisAdditionalOrder) {
                        lastTrade = openTrades.find(t => t.entryTime == Math.max(...openTrades.map(x => new Date(x["entryTime"]))));
                        tradePayLoadStg = {
                            ...tradePayLoadStg,
                            tradeNo: lastTrade.tradeNo,
                            tradeSubNo: lastTrade.tradeSubNo + 1,
                            pacalNo: lastTrade.pacalNo,
                            entryNote: 'additionalOrder'
                        }
                    } else {
                        //find last trade no and ++;
                        lastTrade = Array.isArray(tradesAct) && tradesAct.length !== 0 && tradesAct.find(t => t.entryTime == Math.max(...tradesAct.map(x => new Date(x["entryTime"]))));
                        tradePayLoadStg = {
                            ...tradePayLoadStg,
                            tradeNo: lastTrade ? lastTrade.tradeNo + 1 : 1,
                        };
                    };

                    let directionStratgy = successRuleset?.direction;

                    transaction = {
                        typ: tradePayLoadStg.entryNote,
                        candleIndex,
                        ...candle,
                        direction: directionStratgy,
                        ref: successRuleset,
                    }

                    // transactions.push(transaction);


                    var conditions = {}
                    conditions.ref = {};
                    conditions.ref.directionStratgy = directionStratgy;
                    conditions.ref.directionParams = direction;
                    conditions.ref.barCountReference = additionPositionCandleInterval;
                    if (tradePayLoadStg.tradeSubNo == 1) {
                        conditions.direction = direction === 'both' ? true : direction === directionStratgy;
                        let orderCriterias = [
                            conditions.direction,
                        ];


                        conditions.ref.orderCriterias = orderCriterias
                        orderConditionsMet = orderCriterias.every(e => e === true);
                    } else {

                        conditions.barCount = false;
                        // conditions.addAdditionPosition = false;
                        conditions.ref.openTradesCount = openTrades.length;
                        conditions.ref.openTrades = openTrades;
                        conditions.addAdditionPosition = addAdditionPosition; //addition trade cunku,
                        let currTradeDir = lastTrade.direction;

                        conditions.ref.currTradeDir = currTradeDir;
                        // conditions.direction = currTradeDir === directionStratgy;
                        conditions.direction = currTradeDir === directionStratgy;
                        // conditions.direction && fnx.log('conditions.direction', conditions.direction, currTradeDir, directionStratgy )
                        barCount = await fnxIdx.fnTrades.calculateBarCount({ //redisClient, 
                            symbol: candle.symbol, lastCandle: candle, battleInterval, lastTrade
                        });

                        conditions.ref.barCountCalculated = barCount;
                        conditions.ref.barCountReference = additionPositionCandleInterval;
                        if (barCount) {
                            let ruleCheckInterval = barCount < 0 ? true : additionPositionCandleInterval <= barCount
                            conditions.barCount = ruleCheckInterval;
                        }
                        let cTradeOpenVolume = openTrades.reduce((acc, x) => acc + (parseFloat(x.entryPrice) * parseFloat(x.entryAmount)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);
                        let cTradeOpenQty = openTrades.reduce((acc, x) => acc + (parseFloat(x.entryAmount)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);

                        let AvgOpenOrdersEntryPrice = cTradeOpenQty > 0 ? cTradeOpenVolume / cTradeOpenQty : 0;
                        let refPrice = additionPosRefPriceIsAvg ? AvgOpenOrdersEntryPrice : parseFloat(lastTrade.entryPrice); // parseFloat(openTrades[0].entryPrice);
                        conditions.ref.refPrice = refPrice;
                        conditions.ref.refPrice_entryPrice_avg = AvgOpenOrdersEntryPrice;
                        conditions.ref.refPrice_entryPrice_last = lastTrade.entryPrice; // openTrades[0].entryPrice;
                        // let calcPercentagerefPriceVsClosePrice =  (Math.abs(refPrice - parseFloat(lastCandle.close)) / refPrice) * 100
                        let calcPercentagerefPriceVsClosePrice = currTradeDir === 'long' ?
                            (refPrice - parseFloat(lastCandle.close)) / parseFloat(refPrice) * 100
                            :
                            (parseFloat(lastCandle.close) - parseFloat(refPrice)) / parseFloat(refPrice) * 100
                        //additionPositionPercentage
                        conditions.ref.refPrice_calcPercentagerefPriceVsClosePrice = calcPercentagerefPriceVsClosePrice;
                        conditions.ref.refPrice_additionPositionPercentage = additionPositionPercentage;
                        conditions.additionPositionPercentage = calcPercentagerefPriceVsClosePrice >= additionPositionPercentage;

                        //position Max Budget check...
                        let OpenOrdersUsedBudget = openTrades
                            .filter(x => x.entryBudget)
                            .reduce((acc, x) => acc + parseFloat(x.entryBudget), 0)

                        conditions.ref.OpenOrdersUsedBudget = OpenOrdersUsedBudget;
                        conditions.positionMaxBudget = positionMaxBudget > OpenOrdersUsedBudget;

                        let orderConditions = [
                            conditions.direction,
                            conditions.addAdditionPosition,
                            conditions.barCount,
                            conditions.additionPositionPercentage,
                            conditions.positionMaxBudget,
                        ];
                        conditions.ref.orderConditions = orderConditions

                        orderConditionsMet = orderConditions.every(e => e === true);
                        conditions.orderConditionsMet = orderConditionsMet;
                    }
                    var tradeData = {
                        symbol: candle.symbol,
                        pair: candle.symbol,
                        pacalNo: tradePayLoadStg.pacalNo,
                        orderID: successRuleset.sName,
                        tradeID: fnx.generateKey({ inclTime: false }),
                        tradeNo: tradePayLoadStg.tradeNo,
                        tradeSubNo: tradePayLoadStg.tradeSubNo,
                        tradeClosed: false,
                        direction: successRuleset.direction,
                        entryBarBOP: candle.openTime,
                        entryPrice: orderPrice.toString(),
                        closePrice: orderPrice.toString(),
                        entryAmount: orderAmount,
                        unRealizedPnlB4Comm: 0,
                        commission: (orderAmount * orderPrice) * commissionRate,
                        unRealizedPnl: (orderAmount * orderPrice) * commissionRate * (-1),
                        entryBudget: (orderAmount * orderPrice).toFixed(2),
                        entryTime: candle.openTime, //TODO: barclose ?
                        entryTimeEn: candle.openTimeHRF,
                        entryNote: tradePayLoadStg.entryNote,
                        dtupdated: Date.now(),
                        dtupdatedEn: new Date(Date.now()),
                        entrycandleIndex: candleIndex,
                    };
                    // orderConditionsMet && 
                    // !orderConditionsMet && fnx.log('orderConditionsMet failed', orderConditionsMet, conditions, orderConditions)
                    if (orderConditionsMet) {
                        trades.push(tradeData);
                        transaction = {
                            ...transaction,
                            tradeData: [tradeData],
                        }
                    } else {
                        transaction = {
                            typ: 'doTrade conditions failed',
                            conditions, orderConditions,
                            openTime: candle.openTime,
                            candleIndex: candleIndex,
                        };
                    }
                    debug && fnx.log('transaction', transaction)
                    resolve(transaction)
                    // debug && fnx.log('tradeData', tradeData)
                });
            };
            const updateOpenTrades = ({ cndl, candleIndex }) => {
                trades.filter(t => t.tradeClosed !== true && t.symbol == cndl.symbol).map((t, tix) => {
                    // tix == 1 && fnx.log('updateOpenTrades', cndl.symbol, t.symbol);
                    let sg = t;
                    let currTrdDirection = sg.direction;
                    let closePrice = parseFloat(cndl.close);
                    let entryPrice = parseFloat(sg.entryPrice);
                    let entryAmount = parseFloat(sg.entryAmount);
                    let amtDelta = closePrice - entryPrice;
                    let unRealizedPnlB4Comm = (currTrdDirection == 'long' ? 1 : -1) * amtDelta * entryAmount;
                    let commission = ((closePrice + entryPrice) / 2) * entryAmount * commissionRate
                    let unRealizedPnl = unRealizedPnlB4Comm - commission;
                    sg.commission = commission;
                    sg.entryBudget = closePrice * entryAmount;
                    sg.unRealizedPnlB4Comm = unRealizedPnlB4Comm;
                    sg.unRealizedPnl = unRealizedPnl;
                    sg.closePrice = closePrice;
                    sg.dtupdatecndlIndex = candleIndex;
                    sg.dtupdated = Date.now();
                    sg.dtupdatedEn = new Date(Date.now());
                    return sg;
                });
            };
            const sLossTProfitCheck = async (params = {}) => {
                const { lastCandle, ref, debug, candleIndex, candles } = params;
                const { entry, exit, wallet } = trading;
                return new Promise(async (resolve, reject) => {
                    // fnx.log('lastCandle', JSON.stringify(lastCandle));
                    let transaction;
                    let checkTP;
                    let checkSL;
                    let dir;
                    let pair;
                    const esik = 0.2
                    let openTrades = trades.filter(t => t.tradeClosed !== true && t.symbol == lastCandle.symbol);
                    if (openTrades && openTrades.length !== 0) {
                        openTrades.map(t => {
                            dir = t.direction;
                            pair = t.symbol;
                        });
                        // debug && fnx.log('sLossTProfitCheck', openTrades.length, dir, pair)
                        calcf = openTrades;
                        //TODO: entry price mi olmalı kontrol et!! pnl hesabındanki oran icin.
                        let Samount = openTrades.reduce((acc, x) => acc + (parseFloat(x.entryPrice) * parseFloat(x.entryAmount)), 0);
                        let SunRealizedPnl = openTrades.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                        let SunRealizedPnlB4Comm = openTrades.reduce((acc, x) => acc + parseFloat(x.unRealizedPnlB4Comm), 0);
                        let uPnlRatioLC = (SunRealizedPnl / Samount * 100);

                        // fnx.log(symbol, 'TPSL', pair, dir, 'ratio:', uPnlRatioLC, 'sPNL', SunRealizedPnl, 'sAmo', Samount);
                        // fnx.log(symbol, 'TPSL', 'prices', 'lcClose:', lastCandle?.close, 'ilk:', openTrades.map(o => o.closePrice).toString(), 'entryPrice:', openTrades.map(o => o.entryPrice).toString(), 'amountEntry:', openTrades.map(o => o.entryAmount).toString());
                        // fnx.log(symbol, 'openTrades', JSON.stringify(openTrades));

                        // Math.abs(uPnlRatioLC) > esik && fnx.log(pair, 'TPSL', 'SunRealizedPnl', Samount, SunRealizedPnl, uPnlRatioLC);

                        if (SunRealizedPnl > 0 || SunRealizedPnlB4Comm > 0) {
                            //state for control tp..
                            checkTP = {
                                unRealizedPnl: SunRealizedPnl,
                                unRealizedPnlB4Comm: SunRealizedPnlB4Comm,
                                amount: Samount,
                                takeProfitRatio: exit.takeProfitRatio,
                                uPNLRatioLC: uPnlRatioLC,
                                result: uPnlRatioLC > exit.takeProfitRatio,
                                // openTrades,
                            }
                        } else if ((SunRealizedPnl < 0 || SunRealizedPnlB4Comm < 0)) {
                            checkSL = {
                                unRealizedPnl: SunRealizedPnl,
                                unRealizedPnlB4Comm: SunRealizedPnlB4Comm,
                                amount: Samount,
                                stopLoss: exit.stopLoss,
                                stopLossUsePercentage: exit.stopLossUsePercentage,
                                stopLossPercentage: exit.stopLossPercentage,
                                uPNLRatioLC: uPnlRatioLC,
                                result: exit.stopLossUsePercentage ?
                                    uPnlRatioLC * (-1) > exit.stopLossPercentage
                                    : SunRealizedPnl < (exit.stopLoss * (-1)),
                            }
                        } else {
                            //state of anonym!
                            fnx.log('sLossTProfitCheck state of anon', pair, dir, SunRealizedPnl, openTrades)
                        }
                    }
                    // Math.abs(uPnlRatioLC) > esik && fnx.log(pair, 'checkSL', checkSL);
                    // Math.abs(uPnlRatioLC) > esik && fnx.log(pair, 'checkTP', checkTP);

                    // debug && fnx.log('checkTP', checkTP);
                    // debug && fnx.log('checkSL', checkSL);

                    if (checkTP?.result) {
                        await closeTrade({
                            lastCandle,
                            closeType: 10,
                            updateNote: 'closed-checkTP',
                            closeNote: 'takeProfit',
                            ref,
                            debug,
                            candleIndex,
                            candles,
                        });
                        transaction = {
                            typ: 'closed-checkTP',
                            candleIndex,
                            tradeData: openTrades,
                            ...lastCandle,
                        }
                        // transactions.push(transaction);

                        resolve(transaction)
                    } else if (checkSL?.result) {
                        await closeTrade({
                            lastCandle,
                            closeType: 20,
                            updateNote: 'closed-checkSL',
                            closeNote: 'stopLoss',
                            ref,
                            debug,
                            candleIndex,
                            candles,
                        });
                        transaction = {
                            typ: 'closed-checkSL',
                            candleIndex,
                            tradeData: openTrades,
                            ...lastCandle,
                        }
                        // transactions.push(transaction);
                        resolve(transaction)
                    } else {
                        //do nothing...
                        resolve(false)
                    }

                });
            }
            const closeTrade = async (params = {}) => {
                const { lastCandle, updateNote, closeNote, closeType, ref,
                    candleIndex, candles } = params;
                return new Promise(async (resolve, reject) => {
                    trades.filter(t => t.tradeClosed !== true && t.pair == lastCandle.symbol).map(async t => {
                        let sg = t;
                        let currTrdDirection = sg.direction;
                        let closePrice = parseFloat(lastCandle.close);
                        let entryPrice = parseFloat(sg.entryPrice);
                        let entryAmount = parseFloat(sg.entryAmount);
                        let realizedPips = (closePrice - entryPrice) * (currTrdDirection == 'long' ? 1 : -1);
                        let realizedPipRatio = realizedPips / entryPrice;
                        let realizedPnlB4Comm = realizedPips * entryAmount;
                        let commission = ((closePrice + entryPrice) / 2) * entryAmount * commissionRate
                        let realizedPnl = realizedPnlB4Comm - commission;

                        let drawDown = await findMaxBtw({
                            ref: candles,
                            ci_bop: sg.entrycandleIndex,
                            ci_eop: candleIndex,
                            direction: sg.direction,
                            typ: 'maxDrawDown',
                        });
                        let runUp = await findMaxBtw({
                            ref: candles,
                            ci_bop: sg.entrycandleIndex,
                            ci_eop: candleIndex,
                            direction: sg.direction,
                            typ: 'maxRunUp',
                        });

                        sg.drawDown = (drawDown - entryPrice) * (currTrdDirection == 'long' ? 1 : -1) * entryAmount - commission;
                        sg.runUp = (runUp - entryPrice) * (currTrdDirection == 'long' ? 1 : -1) * entryAmount - commission;

                        sg.closePrice = lastCandle.close;
                        sg.closeRef = ref;
                        sg.closeNote = closeNote;
                        sg.closeType = closeType;
                        sg.tradeClosed = true;
                        sg.updateNote = updateNote;
                        sg.entryBudget = lastCandle.close * entryAmount;
                        sg.unRealizedPnlB4Comm = 0;
                        sg.unRealizedPnl = 0;

                        sg.realizedPips = realizedPips;
                        sg.realizedPipRatio = realizedPipRatio;
                        sg.realizedPnlB4Comm = realizedPnlB4Comm;
                        sg.commission = commission;
                        sg.realizedPnl = realizedPnl;

                        sg.closecandleIndex = candleIndex;
                        sg.closeTime = lastCandle.closeTime;
                        sg.closeTimeEpoch = Date.now();
                        sg.closeTimeEn = new Date(lastCandle.closeTime);
                        sg.tradeDuration = Math.ceil((lastCandle.closeTime - t.entryTime) / 1000 / 60);
                        sg.dtupdated = Date.now();
                        sg.dtupdatedEn = new Date(Date.now());
                        return sg;
                    })

                    // await redisClient.set(redisKeyy, JSON.stringify(tradesAll));
                    resolve(true);
                });
            }
            const findMaxBtw = ({ ref, ci_bop, ci_eop, direction, typ }) => {
                return new Promise(async (resolve, reject) => {
                    let ixBop = ref.findIndex(c => c.candleIndex == ci_bop);
                    let ixEop = ref.findIndex(c => c.candleIndex == ci_eop);
                    let newArr = [...ref].slice(ixBop - 1, ixEop + 1);
                    let maxHigh = Math.max(...newArr.map(x => Number(x.high)));
                    let maxLow = Math.max(...newArr.map(x => Number(x.low)));
                    // fnx.log('findMaxBtw', ci_bop, ci_eop, typ, newArr.length, [...newArr].slice(1)[0].candleIndex, [...newArr].slice(-1)[0].candleIndex, maxHigh, maxLow);
                    if (typ == 'maxDrawDown') {
                        let resp = direction == 'long' ? maxLow : maxHigh;
                        resolve(resp);
                    } else {
                        let resp = direction == 'long' ? maxHigh : maxLow;
                        resolve(resp);
                    }
                });
            }
            const updateCandleStats = async (params = {}) => {
                const { cndl, candleIndex, preStats } = params;
                return new Promise(async (resolve, reject) => {
                    let closedTrades = trades.filter(t => t.tradeClosed == true && t.pair == cndl.symbol);
                    let openTrades = trades.filter(t => t.tradeClosed !== true && t.pair == cndl.symbol);

                    let openTradesCnt = openTrades.length;
                    let openTradesEntryVol = openTrades.reduce((acc, x) => acc + (parseFloat(x.entryPrice) * parseFloat(x.entryAmount)), 0);
                    let openTradesVol = openTrades.reduce((acc, x) => acc + (parseFloat(x.closePrice) * parseFloat(x.entryAmount)), 0);
                    let openTradesVolCC = openTrades.reduce((acc, x) => acc + (parseFloat(cndl.closePrice) * parseFloat(x.entryAmount)), 0);
                    let SunRealizedPnl = openTrades.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                    let SunRealizedPnlB4Comm = openTrades.reduce((acc, x) => acc + parseFloat(x.unRealizedPnlB4Comm), 0);

                    let additionalOrders = closedTrades.filter(t => t.entryNote !== 'firstOrder');
                    let additionalMaxOrder = Math.max(...closedTrades.map(x => x.tradeSubNo));
                    let realizedPnl = closedTrades.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
                    let realizedPnlB4Comm = closedTrades.reduce((acc, x) => acc + parseFloat(x.realizedPnlB4Comm), 0);
                    let closedTradesVol = closedTrades.reduce((acc, x) => acc + (parseFloat(x.closePrice) * parseFloat(x.entryAmount)), 0);

                    let winningTrades = closedTrades.filter(t => t.closeNote == 'takeProfit');
                    let losingTrades = closedTrades.filter(t => t.closeNote == 'stopLoss');
                    let grossLoss = losingTrades.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
                    let grossProfit = winningTrades.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
                    let maxDrawDown = Math.max(...closedTrades.map(x => x.drawDown));
                    let maxrunUp = Math.max(...closedTrades.map(x => x.runUp));
                    let maxrealizedPnl = Math.max(...preStats.map(x => x.realizedPnl));
                    // candleIndex == 1499 && fnx.log('preStats maxrealizedPnl', cndl.symbol, candleIndex, maxrealizedPnl)

                    let tradedDuration = closedTrades.reduce((acc, x) => acc + parseFloat(x.tradeDuration), 0);
                    let realizedCommision = closedTrades.reduce((acc, x) => acc + parseFloat(x.commission), 0);

                    let stats = {};
                    stats.candleIndex = candleIndex;

                    stats.symi = cndl.symi,
                        stats.symbol = cndl.symbol,
                        stats.openTrades = openTradesCnt;
                    stats.unRealizedPnl = SunRealizedPnl;
                    stats.unRealizedPnlB4Comm = SunRealizedPnlB4Comm;
                    stats.positionDirection = openTradesCnt > 0 ? openTrades[0].direction : null;
                    stats.openTradesEntryVol = openTradesEntryVol;
                    stats.unRealizedPnlB4CommRatio = SunRealizedPnlB4Comm / openTradesEntryVol * 100;
                    stats.unRealizedPnlRatio = SunRealizedPnl / openTradesEntryVol * 100;
                    stats.openTradesVol = openTradesVol;
                    stats.openTradesVolCC = openTradesVolCC;

                    stats.grossProfit = grossProfit;
                    stats.grossLoss = grossLoss;
                    stats.maxDrawDown = maxDrawDown;
                    stats.maxrunUp = maxrunUp;
                    stats.winningTrades = winningTrades.length;
                    stats.losingTrades = losingTrades.length;
                    stats.percentProfitable = winningTrades.length / closedTrades.length;
                    stats.maxrealizedPnl = maxrealizedPnl;

                    stats.closedTrades = closedTrades.length;
                    stats.additionalOrders = additionalOrders.length;
                    stats.additionalMaxOrder = additionalMaxOrder;
                    stats.realizedPnl = realizedPnl;
                    stats.realizedCommision = realizedCommision;
                    stats.realizedPnlB4Comm = realizedPnlB4Comm;
                    stats.closedTradesVol = closedTradesVol;

                    stats.tradedDuration = tradedDuration;

                    resolve(stats);
                });
                /*
                    "stats": {
                    "openOrders": 1,
                    "position_quote": 50,
                    "position_size": 0.5620503597122303,
                    "position_avg_price": 88.96,
                    "lastSymbolClosePrice": 88.94,
                    "lastTradeIndex": 1654352100000,
                    "lastOpenTradeNo": 1,
                    "lastOpenTradeEntryPrice": 88.96,
                    "dtLastOpenTradeTime": 1654352100000,
                    "dtLastOpenTradeTimeEn": "2022-06-04T14:15:00.000Z",
                    "dtStatUpdatedIndex": 0,
                    "dtStatUpdated": 1654352159999,
                    "dtStatUpdatedEn": "2022-06-04T14:15:59.999Z",
                    "unRealizedPNL": -0.01124100719424237,
                    "pips_lastPrice": -199.9999999999602,
                    "pips_avgPrice": -199.9999999999602,
                    "dtStat": 1654352162059,
                    "dtStatEn": "2022-06-04T14:16:02.059Z",
                    "realizedPNL": 0, 
                    },
                */
            }

            let dtBOP_Pack = Date.now();
            var trades = [];
            var datas = {};
            let dtDataFetch = Date.now();
            await Promise.all(
                intervals.map(async intv => {
                    let redisX = redisKey.split('_')[0] + '_' + intv
                    const redisVal = await redisClient.get(redisX);
                    let records = redisVal && typeof redisVal !== 'object' ? JSON.parse(redisVal) : redisVal;
                    sureler.push({ t: 'read data', tf: intv, data: Array.isArray(records) && records.length, sure: Date.now() - dtBOP_Pack });
                    // console.log('redisKey', redisKey, redisX, intv, records.length)
                    datas[intv] = Array.isArray(records) ? records : undefined
                }));

            // console.log('dtDataFetch sure', redisKey, Date.now() - dtDataFetch, Date.now() - dtBOP)

            if (Array.isArray(datas[battleInterval.toString()])) {
                // fnx.log('dt_', k, records.slice(-1)[0], fnx.nBinance.tick2json(records.slice(-1)[0]))
                let pair = redisKey.replace(redixPrefix.dataKline, "");
                sureler.push({ t: 'info', data: { packID, pair, datas: datas.length } });
                // fnx.log('X', packID, pair, Array.isArray(records) && records.length,);
                let calcInx;
                try {
                    calcInx = await iterateKlines({
                        redisKey, pair, battleInterval, intervals, indicatorsWParams: indicatorsWParamsObj,
                        redisClient, data: datas, fnTrade: doTrade, trades,
                        rulesets, trading, redisClient, updateOpenTrades, sLossTProfitCheck,
                        updateCandleStats,
                        // postMessage: 
                    });

                    // const redisVal = await redisClient.set('data:backtest:' + pair.toLowerCase(), JSON.stringify(calcInx));
                    // fnx.log('calcInx', pair, JSON.stringify([...calcInx].slice(-1)))

                    sureler.push({ t: 'iterate klines', sure: Date.now() - dtBOP_Pack });

                } catch (eC) {
                    console.log('error in calc', eC);
                }

                try {
                    let fold = addon ? './' + addon + 'bin/' : './bin/';
                    await fnx.savetoFile(calcInx, fold + 'backtest_' + pair + '.json', false);
                    // await fnx.savetoFile(transactions, './bin/backtest_' + pair + '_transactions.json', false);
                    await fnx.savetoFile(trades, fold + 'backtest_' + pair + '_trades.json', false);
                    sureler.push({ t: 'save results', sure: Date.now() - dtBOP_Pack });

                } catch (e) {
                    console.log('error save', e)
                }
                sureler.push({ t: 'final', sure: Date.now() - dtBOP_Pack });

                fnx.log(redisKey, 'iteration done and saved! ', Date.now() - dtBOP_Pack); //, sureler
            } else {
                fnx.log('not array', datas)
            }
        }
        // // process.exit(0);
        parentPort.postMessage({
            act: 'taskCompleted',
            packID,
            pack,
            success: true,
            dtEopEn: new Date(Date.now()),
            dtEop: Date.now(),
            dtSure: Date.now() - dtBOP,
        }
        );

    });


})();

const iterateKlines = (props) => {
    const {
        pair, data, battleInterval, intervals,
        indicatorsWParams, redisClient, rulesets, updateCandleStats,
        trades, trading, fnTrade, updateOpenTrades, sLossTProfitCheck,

    } = props;
    return new Promise(async (resolve, reject) => {
        try {
            var dtBOP = Date.now();
            var currentIndex = 0;
            var tickTrades = [];
            const symbol = pair.split('_')[0];
            const interval = pair.split('_')[1];
            let indicators2Calculate = indicatorsWParams //.filter(ip => ip.battleParams.timeFrame == interval); //ip.battleParams
            fnx.log('started', symbol, interval, );

            let candleCalculationResults = [];

            let refData = {};
            await Promise.all(
                intervals.map(async intv => {
                    try {
                        refData[intv] = await fnxIdx.indicators.getKlineData({
                            redisClient: false, symbol, keyTag: pair, klineRef: 'closedBar', rawData: data[intv],
                            tickerData: false, interval: intv, batch: '', taskID: ''
                        });
                    } catch (e1) {
                        fnx.log('indicators.getKlineData e1a', e1)
                    }
                })
            );

            let calcResults;
            let candleIndex = 0;
            let candleRefData = refData[battleInterval?.toString()];
            for (cndl of candleRefData) {
                //tick started
                //tick started
                //tick started
                let tickTransactions = []
                var bcast = {};
                const stgData = [...candleRefData].slice(0, candleIndex + 1); //index > 0 ?  : [...data].slice(0, 1)
                var i = stgData.length;
                let resp = {};
                resp.candleIndex = candleIndex;
                delete cndl.taskID;
                delete cndl.batchid;
                delete cndl.is_deleted;

                resp.candle = {
                    // pair,
                    ...cndl,
                    battleInterval: battleInterval?.toString() || '',
                    // candle: cndl
                }

                // candleIndex > 1498 && fnx.log('symLo1', cndl.symbol, cndl )
                if (trades.filter(t => t.tradeClosed !== true && t.symbol == cndl.symbol).length !== 0) {
                    updateOpenTrades({ cndl, candleIndex })
                    let slAct = await sLossTProfitCheck({
                        lastCandle: cndl,
                        ref: 'candle-' + candleIndex,
                        candleIndex,
                        debug: candleIndex > 1490,
                        candles: stgData,
                    });
                    if (slAct) {
                        tickTransactions.push(slAct)
                    }
                };

                let indicatorCalculationResults = [];
                // indicatorCalculationResults.push();

                let errorLogs = [];
                let results = await Promise.all( //indicators...
                    indicators2Calculate.map(async (indX, indicatorIndex) => {
                        let calcStg = {};
                        let indicatorName = indX.indicator;
                        let indicatorrefName = indX.refName;
                        let indicatorParams = typeof indX.battleParams !== 'object' ? JSON.parse(indX.battleParams) : indX.battleParams;
                        let res = {
                            indicator: indicatorName,
                            refName: indicatorrefName,
                            indicatorRefName: indicatorrefName,
                        };
                        if (candleIndex > indicatorParams.length && indicatorParams.timeFrame == interval) {
                            try {
                                calcStg = await fnxIdx.calcIndicator[indicatorName]({
                                    keyTag: pair, data: stgData, klineRef: 'backtest',
                                    params: indicatorParams, symbol, mode: 'backtest'
                                });

                                // (i > 1499) && fnx.log('resss1a calcStg', indicatorName, symbol, interval, calcStg);
                                res = {
                                    ...res,
                                    ...calcStg,
                                };
                                let indicatorValue = res.indicatorValue;
                                try {
                                    let tmp = JSON.parse(indicatorValue);
                                    res.indicatorValue = JSON.stringify(tmp);
                                }
                                catch (e) {
                                    res.indicatorValue = typeof res.indicatorValue === "object" ? JSON.stringify(res.indicatorValue) : res.indicatorValue;
                                }
                                res.indicatorAdditionalData = res.indicatorAdditionalData && JSON.stringify(res.indicatorAdditionalData) !== '{}' ? JSON.stringify(res.indicatorAdditionalData) : '';
                                indicatorCalculationResults.push(res);
                            }
                            catch (e) {
                                let errData = {
                                    symbol,
                                    indicator: indicatorName,
                                    eDesc: '801-main.calculator - calcIndicator Error.',
                                    error: e,
                                };
                                fnx.log('error 12', symbol, currentIndex, e)
                                errorLogs.push(errData);
                                res = {
                                    ...res,
                                    error: 'err idx not calc',
                                };
                                indicatorCalculationResults.push(res);
                            }
                        }
                        return { candleIndex, indicatorIndex }
                    }),
                );
                // candleIndex > 1490 && fnx.log('indicatorCalculationResults len1', indicatorCalculationResults.length)
                if (intervals && Array.isArray(intervals) && intervals.length > 1) {
                    await Promise.all(
                        intervals.filter(iv => iv !== battleInterval.toString()).map(async intv => {
                            try {
                                let dataStg = refData[intv];
                                dataStg = dataStg.filter(dx => parseInt(dx.openTime) <= parseInt(cndl.openTime)); 

                                let resultsSub = await Promise.all( //indicators...
                                    indicators2Calculate.map(async (indX, indicatorIndex) => {
                                        let calcStg = {};
                                        let indicatorName = indX.indicator;
                                        let indicatorrefName = indX.refName;
                                        let indicatorParams = typeof indX.battleParams !== 'object' ? JSON.parse(indX.battleParams) : indX.battleParams;
                                        let res = {
                                            indicator: indicatorName,
                                            refName: indicatorrefName,
                                            indicatorRefName: indicatorrefName,
                                            dataTimeFrame: intv,
                                        };
                                        if (candleIndex > indicatorParams.length && indicatorParams.timeFrame == intv) {
                                            try {
                                                calcStg = await fnxIdx.calcIndicator[indicatorName]({
                                                    keyTag: pair, data: dataStg, klineRef: 'backtest',
                                                    params: indicatorParams, symbol, mode: 'backtest'
                                                });

                                                res = {
                                                    ...res,
                                                    ...calcStg,
                                                };
                                                let indicatorValue = res.indicatorValue;
                                                try {
                                                    let tmp = JSON.parse(indicatorValue);
                                                    res.indicatorValue = JSON.stringify(tmp);
                                                }
                                                catch (e) {
                                                    res.indicatorValue = typeof res.indicatorValue === "object" ? JSON.stringify(res.indicatorValue) : res.indicatorValue;
                                                }
                                                res.indicatorAdditionalData = res.indicatorAdditionalData && JSON.stringify(res.indicatorAdditionalData) !== '{}' ? JSON.stringify(res.indicatorAdditionalData) : '';
                                                indicatorCalculationResults.push(res);
                                            }
                                            catch (e) {
                                                let errData = {
                                                    symbol,
                                                    indicator: indicatorName,
                                                    eDesc: '801xc-main.calculator - calcIndicator Error.',
                                                    error: e,
                                                };
                                                // fnx.log('error 123', symbol, currentIndex, e)
                                                errorLogs.push(errData);
                                                res = {
                                                    ...res,
                                                    error: 'err idx not calc',
                                                };
                                                indicatorCalculationResults.push(res);
                                            }
                                        }
                                        return { candleIndex, indicatorIndex }
                                    }),
                                );

                            } catch (e1) {
                                fnx.log('other intv error', e1)
                            }
                        })
                    );
                } else {
                    // fnx.log('dasd exception', intervals)
                }
                // candleIndex > 1490 && fnx.log('indicatorCalculationResults len2', indicatorCalculationResults.length)
                
                resp.indicators = indicatorCalculationResults;
                // candleCalculationResults.push(indicatorCalculationResults);
                if (indicatorCalculationResults && stgData && Array.isArray(stgData)) {

                    const taskTagLw = symbol.toLowerCase() + '_' + interval.toString();
                    try {
                        let redisIndKey = redixPrefix.dataIndicators + 'backtest:' + taskTagLw;
                        indicatorCalculationResults.push({
                            ref: 'backtest',
                            index: candleIndex,
                            lastCandle: cndl,
                            interval, 
                            battleInterval,
                        })
                        await redisClient.set(redisIndKey, JSON.stringify(indicatorCalculationResults));
                    } catch (eIdx) {
                        fnx.log('110812 / ', taskTagLw, eIdx)
                    }

                    try {
                        const strategyRulesetParams = await fnxIdx.strategies.getStrategyRulesetParams({})
                        const lastCandle = cndl;
                        lastCandle.time = cndl.openTime;
                        lastCandle.assetVolume = Number(cndl.quoteVolume);
                        // fnx.log('lastCandle1', lastCandle, lastKlineValue);

                        let strategyRulesetCalculations;
                        if (battleInterval == interval) {
                            var xsmi = pair;
                            try {
                                // candleIndex == 1499 && fnx.log('xx', xsmi, cndl)
                                strategyRulesetCalculations = await fnxIdx.strategies.calculate({
                                    redisClient,
                                    params: {
                                        symbol, xsmi, battleInterval,
                                        battle_params: { indicatorsWParams, trading, rulesets, intervals },
                                        indicatorParams: indicatorsWParams,
                                        indicatorCalculations: indicatorCalculationResults,
                                        strategyRulesets: rulesets, // strategyRulesets,
                                        strategyRulesetParams: strategyRulesetParams,
                                        lastCandle: lastCandle,
                                        debug: candleIndex > 1490, //candleIndex == 1498, //
                                        mode: 'backtest',
                                        modeAddon: {
                                            index: candleIndex,
                                        }
                                    }
                                });
                                // candleIndex > 1498 && fnx.log('strategyRulesetCalculations', pair, JSON.stringify(strategyRulesetCalculations), battleInterval, interval)
                                if (strategyRulesetCalculations) {
                                    try {
                                        let successRulesets = Array.isArray(strategyRulesetCalculations) && [...strategyRulesetCalculations].filter(rs => rs.result == true);
                                        resp.strategies = successRulesets;
                                        resp.strategiesMore = successRulesets.length == 0 && candleIndex > 1490 ? strategyRulesetCalculations : undefined
                                        // var { entry, exit, wallet } = trading;
                                        if (successRulesets && successRulesets.length !== 0) {
                                            let tradeAct = await fnTrade({
                                                candle: cndl, candleIndex,
                                                successRuleset: successRulesets[0],
                                                // debug: candleIndex > 1455 && candleIndex < 1461,
                                            });

                                            if (tradeAct) {
                                                tickTransactions.push(tradeAct)
                                            }

                                        };

                                    } catch (eT) {
                                        resp.strategies = false;
                                        fnx.log('checktrader trader is not working...', eT)
                                    };

                                    // let redisIndKey = redixPrefix.dataStrategies + taskTagLw;
                                    // await redisClient.set(redisIndKey, JSON.stringify(strategyRulesetCalculations));
                                    // console.log('strategyRulesetCalculations', strategyRulesetCalculations.length)
                                } else {
                                    resp.strategies = false;
                                    fnx.log('1055 / fnGetTaskDone strategyRulesetCalculations failed',)
                                }
                            } catch (eIdx) {
                                resp.strategies = false;
                                fnx.log('1054 / fnGetTaskDone strategyRulesetCalculations failed',)
                            }

                        } else {
                            fnx.log('oteher')
                            //other time frame process . no need for strategy actions!
                        }
                    }
                    catch (eSt) {
                        resp.strategies = false;
                        fnx.log('error in strat calc', eSt);
                    }
                };

                if (tickTransactions.length !== 0) {
                    resp.transactions = {
                        candleIndex,
                        tickTransactions,
                    };
                }

                let stats = await updateCandleStats({ cndl, candleIndex, preStats: candleCalculationResults.map(c => c.stats) });
                if (candleIndex == (stgData.length - 1)) {
                    stats.eopDuration = Date.now() - dtBOP;
                    stats.eopTime = new Date(Date.now());
                };
                resp.stats = stats;

                if (candleIndex == (refData[battleInterval?.toString()].length - 1)) {
                    resp.trades = trades;
                };

                // resp.trades = candleIndex>1498 ? trades : [];
                candleCalculationResults.push(resp);
                candleIndex++;
                //tick ended
                //tick ended
                //tick ended
            }
            resolve(candleCalculationResults)
        } catch (eMain) {
            fnx.log('errorM', eMain)
        }
    });
}


