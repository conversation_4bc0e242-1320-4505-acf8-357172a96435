const Redis = require('ioredis');
const MongoClient = require('mongodb').MongoClient;
const Database = require('better-sqlite3');
const cronParser = require('cron-parser');
const path = require('path');
const fnx = require('./_.functions');
const fnxDb = require('./_.functions.db');
// const nBinance = require('./_.functions.binance')
const fnxIdx = require('./_.functions.indicators');
const redixPrefix = require('../src/lib/redis.prefix.js');
// const process= require('process');
const process = require('node:process');
process.removeAllListeners('warning');
const Binance = require('node-binance-api');

const dotenv = require('dotenv');
dotenv.config({ path: '../../.env.local' });

const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const args = process.argv.slice(2);
const modeDebug = false;

const redisClient = new Redis({
    host: '127.0.0.1',
    port: 6379,
});

process.on('exit', (code) => {
//   console.log(`About to exit with code: ${code}`);
});

const fnGetTaskDone = {
    fetchPrices: async ({ connTimeOut = 10000, binanceApi, savetoFile = false }) => {
        return new Promise(async (resolve, reject) => {
            let rawData;
            try {
                rawData = await fnx.nBinance.futuresDaily({ binanceApi, connTimeOut });
                // rawData = await await fnx.promiseTimeout(binanceApi.futuresDaily(), connTimeOut);
                resolve(rawData);
            }
            catch (e) {
                reject(e)
            }
        });
    },
    
}

const saveTradeSnapshot = async ({
    redisCli,
}) => {
    // console.log('saveTradeSnapshot', new Date(new Date(Date.now())));
    redisCli = redisCli || redisClient;
    return new Promise(async (resolve, reject) => {
        let redisDataLogKey = redixPrefix.dataMarket + 'tradesLog';
        let currData = await redisClient.get(redisDataLogKey);

        let data_stat_stg = {
            dt: new Date(new Date(Date.now()).setSeconds(0, 0)).getTime(), dtISO: new Date(new Date(Date.now()).setSeconds(0, 0)).toISOString(),
                openTrades: 0, unRealizedPnl: 0, openTradesVol: 0, 
                grossProfit: 0, grossLoss: 0, winningTrades: 0, 
                losingTrades: 0, closedTrades: 0, realizedPnl: 0, 
                closedTradesVol: 0, realizedCommision: 0, netpnl: 0,
                percentProfitable: 0, tradedDurationAvg: 0, avgRealizedPnl: 0,
                closedPairPositions: 0, openPairPositions: 0

        };

        currData = currData || [];
        try {
            currData = JSON.parse(currData);
        } catch (eL) {
            console.log('eL', eL);
        }
        try {

            let trades = [];
            let nodes = await redisClient.keys(redixPrefix.dataTrades + '*');
            if (Array.isArray(nodes)) {
                var nodeData = [];
                for (n of nodes) {
                    var value = await redisClient.get(n);
                    value = value || [];
                    try {
                        nodeData = JSON.parse(value);
                    } catch (eN) {
                        console.log('eL', eL);
                    }
                    // trades.push(nodeData);
                    trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                }
            }

            let closedTradesArr = trades.filter(t => t.tradeClosed == true);
            let openTradesArr = trades.filter(t => t.tradeClosed !== true);
            let winningTradesArr = closedTradesArr.filter(t => t.closeNote == 'takeProfit');
            let losingTradesArr = closedTradesArr.filter(t => t.closeNote == 'stopLoss');
            let grossLoss = losingTradesArr.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
            let grossProfit = winningTradesArr.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);

            let openTrades = openTradesArr.length;
            let unRealizedPnl = trades.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
            let openTradesVol = openTradesArr.reduce((acc, x) => acc + (parseFloat(x.closePrice) * parseFloat(x.entryAmount)), 0);
            let winningTrades = winningTradesArr.length;
            let losingTrades = losingTradesArr.length;
            let closedTrades = closedTradesArr.length;
            let realizedPnl = trades.reduce((acc, x) => acc + (x.realizedPnl ? parseFloat(x.realizedPnl) : 0), 0);
            let closedTradesVol = closedTradesArr.reduce((acc, x) => acc + (parseFloat(x.closePrice) * parseFloat(x.entryAmount)), 0);
            let realizedCommision = closedTradesArr.reduce((acc, x) => acc + parseFloat(x.commission), 0);

            let closedPairPositions = [...new Set(closedTradesArr.map(a => a.pair))].length;
            let openPairPositions = [...new Set(openTradesArr.map(a => a.pair))].length;

            closedTradesArr.map(sg => {
                sg.tradeDuration = sg.closeTime ? Math.ceil((sg.closeTime - sg.entryTime) / 1000 / 60) : -1;
            });

            let tradedDuration = closedTradesArr.reduce((acc, x) => acc + parseFloat(x.tradeDuration), 0);
            let netpnl = unRealizedPnl + realizedPnl;
            let percentProfitable = closedTrades !== 0 ? winningTrades / closedTrades : 0;
            let tradedDurationAvg = tradedDuration / closedTrades;
            // console.log('tradedDuration / closedTrades', tradedDuration, closedTrades, closedTradesArr)
            let avgRealizedPnl = realizedPnl / closedTrades;

            data_stat_stg = {
                ...data_stat_stg,
                openTrades, 
                unRealizedPnl: parseFloat(parseFloat(unRealizedPnl).toFixed(2)), 
                openTradesVol: parseFloat(parseFloat(openTradesVol).toFixed(2)), 
                grossProfit: parseFloat(parseFloat(grossProfit).toFixed(2)),
                grossLoss: parseFloat(parseFloat(grossLoss).toFixed(2)), 
                realizedPnl: parseFloat(parseFloat(realizedPnl).toFixed(2)), 
                closedTradesVol: parseFloat(parseFloat(closedTradesVol).toFixed(2)), 
                realizedCommision: parseFloat(parseFloat(realizedCommision).toFixed(2)), 
                netpnl: parseFloat(parseFloat(netpnl).toFixed(2)),
                percentProfitable: parseFloat(parseFloat(percentProfitable).toFixed(2)), 
                tradedDurationAvg: parseFloat(parseFloat(tradedDurationAvg).toFixed(2)), 
                avgRealizedPnl: parseFloat(parseFloat(avgRealizedPnl).toFixed(2)),
                winningTrades, losingTrades, closedTrades,
                closedPairPositions, openPairPositions,
            };
            // console.log('data_stat_stg', data_stat_stg);

            currData = [...currData, {
                ...data_stat_stg
            }];
            await redisClient.set(redisDataLogKey, JSON.stringify(currData));

            resolve(data_stat_stg)

        } catch (e) {
            console.log('error', e, currData);
            currData = []
            resolve(false)
        }
    });
}

const saveMarketLog = async({data, savetoFile = true}) => {
    // console.log('task done, ticker', new Date(Date.now()).toISOString());
    let redisDataLogKey = redixPrefix.dataMarket + 'marketLog';
    let currData = await redisClient.get(redisDataLogKey);
    currData = currData || [];
    try {
        currData = JSON.parse(currData);
    } catch (e) {
        console.log('error', e, currData);
        currData = []
    }

    let arrPair = [];
    for (const pair in data) {
        arrPair.push({
            pair,
            delta: data[pair].delta,
            priceChangePercent: parseFloat(data[pair].priceChangePercent),
        })
    }
    let arrdelta = arrPair.map(a => a.delta);
    let arrpcp = arrPair.map(a => a.priceChangePercent);

    let d2Add = {
        dtCreated: new Date(Date.now()),
        // pairs: {
        //     delta: arrdelta.length,
        //     pcp: arrpcp.length,
        // },
        delta: {
            mean: fnxIdx.stat.calcAverage(arrdelta),
            median: fnxIdx.stat.calcMedian(arrdelta),
            mode: fnxIdx.stat.calcMode(arrdelta),
            max: fnxIdx.stat.calcMax(arrdelta),
        },
        pcp: {
            mean: fnxIdx.stat.calcAverage(arrpcp),
            median: fnxIdx.stat.calcMedian(arrpcp),
            mode: fnxIdx.stat.calcMode(arrpcp),
            max: fnxIdx.stat.calcMax(arrpcp),
        },
    }

    currData = [...currData, {
        ...d2Add
    }];
    await redisClient.set(redisDataLogKey, JSON.stringify(currData));

    try {
        const debuglog = false;
        const tPeriod = 60 * 24 * 3; //3gün.
        let dtBOP = Date.now()
        let client;
        let clientPromise;
        const options = {};
        const uri = process.env.MONGODB_URI; 
        client = new MongoClient(uri, options);
        clientPromise = client.connect();
        debuglog && fnx.log('db connected!', uri, Date.now() - dtBOP);
        
        const dbConn = await clientPromise;
        const db = dbConn.db('algoweb');
        const coll = db.collection('gauss.logs.futuresDaily');
        let qq = [
            { $replaceRoot: { newRoot: "$summary" } },
            {
                "$addFields": {
                    "dtCreated": {
                        "$toDate": "$dtCreated"
                    }
                }
            },
            { "$sort": { "dtCreated": -1 } },
            { "$limit": tPeriod },
            { "$sort": { "dtCreated": 1 } },
        ];
        var rawHistory = coll && await coll.aggregate(qq).toArray();
        let redisDataLogXKey = redixPrefix.dataMarket + 'futuresDailyLog';
        await redisClient.set(redisDataLogXKey, JSON.stringify(rawHistory));
        debuglog && fnx.log('rawHistory', rawHistory?.length, Date.now() - dtBOP);

    } catch(e) {
        fnx.log('mongo error ', e)
    }
    // try {
    //     let repoData = [];
    //     const filePath = path.resolve(__dirname, './bin/binance.futuresDaily_log.json');
    //     try {
    //         repoData = JSON.parse(await fnx.readFile(filePath, "utf8"));
    //     } catch (e) {
    //         fnx.log('error in reading file', repoData, filePath, e)
    //     }
    //     repoData = [...repoData, {
    //         ...d2Add, pairData: arrPair,
    //     }];
    //     savetoFile && await fnx.savetoFile(repoData, filePath);
    // }
    // catch (eF) {
    //     console.log('error read write file log', eF)
    // }
    return true;
};

const getTaskDone = async props => {
    const refID = Date.now();
    const { redisClient, taskDesc, battleID, nodeKey, taskTag, batch, nodeInterval, battleInterval = '',
        taskType, period, periodDesc, taskParams, redisKey, pair, nextRun, dtcreatedEn, battleData, ...rest } = props;
    
        return new Promise(async (resolve, reject) => {
            const taskTagLw = taskTag.toLowerCase();
            let rawData; 
        try {  
            rawData = await fnGetTaskDone.fetchPrices({binanceApi, savetoFile: false }); //taskID === 'test'
            let redisDataKey = redixPrefix.dataMarket + taskTag.split('_')[1].toLowerCase();
            let resS = {}

            for (const pair in rawData) {
                let dTmp = {
                    // symbol: pair,
                    ...rawData[pair],
                };
                let delta = (parseFloat(dTmp.lastPrice) - parseFloat(dTmp.lowPrice)) / (parseFloat(dTmp.highPrice) - parseFloat(dTmp.lowPrice)) * 100;
                dTmp.delta = delta;        
                dTmp.openTimeHRF = new Date(rawData[pair].openTime);
                dTmp.closeTimeHRF = new Date(rawData[pair].closeTime);
                dTmp.dteventISO = new Date(Date.now()).toISOString();
                dTmp.dtcreatedISO = new Date(Date.now()).toISOString();
                dTmp.src = 'ntick';
                let deltaT = Date.now() - rawData[pair].openTime;
                if (deltaT < 90000000) {
                    resS[pair] = {...dTmp}
                }
                // deltaT < 90000000 && resS.push(dTmp);
            
            }

            try {
                await saveMarketLog({data: resS})
            } catch (eL) {
                console.log('error saveMarketLog', new Date().toISOString())
            }

            try {
                await saveTradeSnapshot({redisClient,})
            } catch (eL) {
                console.log('error saveMarketLog', new Date().toISOString())
            }

            await redisClient.set(redisDataKey, JSON.stringify(resS));
            // await redisClient.set(redisDataKey, JSON.stringify(rawData));
            try {
                let sKey = redixPrefix.monitorMarket + taskTag.split('_')[1].toLowerCase();
                let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                await redisClient.set(sKey, JSON.stringify(sStatsValue));
            }
            catch (eSt) {
                modeDebug && fnx.log('stat save error', taskTag.split('_')[1].toLowerCase());
            }

            resolve(rawData)
        }
        catch(ej) {
            fnx.log(`market ticker error: getTaskDone`, taskTag, ej)
            reject(ej);
        }
    });
}

(async () => {
    const dtBOP = Date.now();
    const killConns = async (signal) => {
        console.log(`'market ticker killConns Got ${signal} signal.'`);
        redisClient && redisClient.quit();
        process.exit(0)
    };
    //`exit`, 
    //https://stackoverflow.com/questions/14031763/doing-a-cleanup-action-just-before-node-js-exits
    [`SIGINT`, `SIGUSR1`, `SIGUSR2`, `uncaughtException`, `SIGTERM`].forEach((eventType) => {
        process.on(eventType, killConns.bind(eventType));
    });

    const taskID = Array.isArray(args) && args.length !== 0 ? args[0] : 'binance.x';
    let task = {}
    let battleData = {};
    task.taskID = taskID;

    try {
        task = await fnx.klines.getTask({redisClient, taskID});
        const {
            taskDesc, battleID, nodeKey, taskTag, batch, nodeInterval, battleInterval,
            taskType, period, periodDesc, taskParams, redisKey, pair, nextRun, dtcreatedEn,
        } = task;

        battleData = await fnx.battleNode.getBattleData({
            cronParser, redisClient, redisKey
        });
        // fnx.log(redisKey, 'task started!:' );
        await getTaskDone({ 
            redisClient,
            ...task,
            battleData,
        });
        // fnx.log('exit', taskID);
        process.exit(0);
    }
    catch (e) {
        let dtElapsed = Date.now() - dtBOP 
        fnx.log('1124 / node.marketticker.js get Battle Data.. ', e);
        process.exit(0);
    } 
})();

