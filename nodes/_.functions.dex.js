const Binance = require('node-binance-api'); 
const Redis = require('ioredis');
const moment = require('moment');

const path = require('path');
const fnx = require('./_.functions');
const battleExchangeInfo = require('../src/lib/battle.exchangeinfo.json');
const redixPrefix = require('../src/lib/redis.prefix.js');
const commissionRate = 0.0007;
const commissionRate_LIMIT = 0.0002;
const dexSync_init_checkPosTPLs = true;
let dexWIP = false;
let dexActIgnoreList = {};
const battle = exports.battle = {
    fnGetTrades: ({redisClient, symbol, openOnly = false}) => {
        return new Promise(async (resolve, reject) => {
            let trades = [];
            let nodes = await redisClient.keys(redixPrefix.dataTrades +'*');
            if (Array.isArray(nodes)) {
                // for (n of nodes) {
                //     const value = await redisClient.get(n);
                //     let nodeData = JSON.parse(value);
                //     trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                // }

                await Promise.all(
                    nodes.map(async (n) => {    
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                    })
                );

            };
            trades = symbol ? trades.filter(t => t.pair == symbol.toLowerCase()) : trades;
            trades = openOnly ? trades.filter(t => t.tradeClosed == false) : trades;
            resolve(trades);
        });
    },
    createTrade: async entryParams => {
        return new Promise(async (resolve, reject) => {
            const {redisClient, payload, taskTag, isExchange = false, 
                    dexApi, actInDex = true, refCall, section,
                } = entryParams;
            let cndl = {};
            var tradeID = fnx.generateKey({inclTime: false});
            try {
                cndl = entryParams?.payload?.orderSrc_candle && JSON.parse(entryParams?.payload?.orderSrc_candle)
            }
            catch (ex) {}
            let tradeNx = {
                tradeNo: 1,
                tradeSubNo: 1,
                pacalNo: 1,
                entryNote: 'firstOrder',
                refNote: payload?.act?.note,
            }
            tradeID = tradeID + '_' + tradeNx.tradeNo + '_' + tradeNx.tradeSubNo;

            if (payload && 
                payload.act && 
                payload.act.ref && 
                payload.act.ref.refNote == 'additionalOrder' && 
                payload.act.ref.lastTrade ) {
                    let lastTrade = payload.act.ref.lastTrade;
                    let tradeNo = lastTrade.tradeNo;
                    let tradeSubNo = lastTrade.tradeSubNo + 1;
                    tradeID = lastTrade.tradeID.split('_')[0] + '_' + tradeNo + '_' + tradeSubNo;
                    tradeNx = {
                        tradeID: tradeID, 
                        tradeNo: tradeNo,
                        tradeSubNo: tradeSubNo,
                        pacalNo: lastTrade.pacalNo,
                        entryNote: 'additionalOrder'
                    }
            }

            if (payload && 
                payload.act && 
                payload.act.ref && 
                payload.act.ref.refNote == 'firstOrder' && 
                payload.act.ref.lastTrade ) {
                    let lastTrade = payload.act.ref.lastTrade;
                    fnx.log('new Trade State, preTrade: ', lastTrade);
                    tradeNx = {
                        tradeNo: lastTrade.tradeNo + 1,
                        tradeSubNo: 1,
                        pacalNo: lastTrade.pacalNo,
                        entryNote: 'firstOrder'
                    }
            }
            var dt = {BOP: Date.now()};

            var tradeData = {
                pair: payload.symbol,
                symbol: payload.symbol.toUpperCase(),
                pacalNo: tradeNx.pacalNo,
                orderID: payload.orderID,
                tradeID: tradeID,
                tradeNo: tradeNx.tradeNo,
                tradeSubNo: tradeNx.tradeSubNo,
                tradeClosed: false,
                direction: payload.orderType,
                entryBarBOP: cndl.time,
                entryPrice: payload.orderPrice.toString(),
                closePrice: payload.orderPrice.toString(),
                entryAmount: payload.orderAmount.toString(),
                unRealizedPnlB4Comm: 0,
                commission: (payload.orderAmount * payload.orderPrice) * commissionRate,
                unRealizedPnl: (payload.orderAmount * payload.orderPrice) * commissionRate * (-1),
                notional: parseFloat(payload.orderBudget), // (payload.orderAmount * payload.orderPrice).toFixed(2),
                entryBudget: parseFloat(payload.orderBudget), // (payload.orderAmount * payload.orderPrice).toFixed(2),
                entryTime: payload.orderTime,
                entryTimeEn: payload.orderTimeEn,
                entryNote: tradeNx.entryNote,
                dtupdated: Date.now(),
                dtupdatedEn: new Date(Date.now()), 
                //fields for all time!
                // closeNote: '',
                // closeType: null,
                // updateNote: '',
                // realizedPips: null,
                // realizedPipRatio: null, //after commisions,
                // realizedPnlB4Comm: null,
                // realizedPnl: null,
                // closeBarEOP: null,
                // closeTime: null,
                // closeTimeEn: null
            };

            let orderCreated = false;
            let symbol = payload.symbol.toUpperCase();
            // fnx.log('XXX battle create trade isExchange && actInDex', symbol, taskTag);

            let dexErr = '';

            if (isExchange && actInDex) {
                // fnx.log('battle create trade isExchange && actInDex', symbol, taskTag);
                try {
                    let actR = await dex.createDexTrade({
                        redisClient, dexApi, payload: tradeData, setTPSL: true,
                        threshold: 6000,
                    });
                    if (actR && actR.success) {
                        // fnx.log('createTrade - success!', symbol, actR?.response.status);
                        // await fnx.logX({redisClient, param: symbol, value: {
                        //     section: section || 'dex:createTrade',
                        //     note: 'actR && actR.success',
                        //     symbol,
                        //     refValues: {
                        //         payload: tradeData, response: actR,
                        //         orderCreated, refCall,
                        //     }
                        // }});
                        let resp = actR?.response;
                        tradeData.entryPrice = parseFloat(resp?.avgPrice);
                        tradeData.entryAmount = parseFloat(resp?.executedQty);
                        tradeData.executedQty = parseFloat(resp?.executedQty);
                        tradeData.entryBudget = parseFloat(resp?.avgPrice) * parseFloat(resp?.executedQty);
                        tradeData.notional = parseFloat(resp?.avgPrice) * parseFloat(resp?.executedQty);
                        tradeData.entryTime = resp?.updateTime;
                        tradeData.dex_orderId = resp?.orderId;
                        tradeData.dex_clientOrderId = resp?.clientOrderId;
                        tradeData.dex_type = resp?.type;
                        tradeData.dex_status = resp?.status;
                        orderCreated = true;
                        await fnx.logX({redisClient, param: symbol, value: {
                            section: section || 'dex:createTrade',
                            note: resp?.clientOrderId + ' / actR && actR.success.',
                            symbol,
                            refValues: {
                                payload: tradeData, orderCreated, refCall, response: actR,
                            }
                        }});
                    } else {
                        orderCreated = false;
                        dexErr = actR?.rDesc;
                        await fnx.logX({redisClient, param: symbol, value: {
                            section: section || 'dex:createTrade',
                            note: actR?.rDesc || (tradeData.tradeID + ' / actR && actR failed!'),
                            symbol,
                            refValues: {
                                payload: tradeData, response: actR,
                                orderCreated, refCall,
                            }
                        }});

                    }
                } catch (eT) {
                    fnx.log('createTrade - failed:', symbol, eT);
                    orderCreated = false;
                    await fnx.logX({redisClient, param: symbol, value: {
                        section: section || 'dex:createTrade',
                        note: tradeData.tradeID + ' / createTrade - failed',
                        symbol,
                        refValues: {
                            payload: tradeData, refCall,
                            error: eT, orderCreated,
                        }
                    }});
                }
            } else {
                // fnx.log('battle create trade - no dex...', symbol)
                orderCreated = true;
            }

            let orderResp = false;
            if (orderCreated) {
                let redisTradeKey = redixPrefix.dataTrades + taskTag.toLowerCase();
                let currTradesStg = await redisClient.get(redisTradeKey);
                let currTrades = JSON.parse(currTradesStg);
                let trades = currTrades ? currTrades : [];
                trades.push(tradeData);
                await redisClient.set(redisTradeKey, JSON.stringify(trades));

                await fnx.logX({redisClient, param: symbol, value: {
                    section: section || 'dex:createTrade',
                    note: tradeData?.tradeID + ' / createTrade - success',
                    symbol,
                    refValues: {
                        orderCreated, refCall,
                    }
                }});
                resolve(tradeData);
            } else {
                // fnx.log('createTrade failed1!', symbol)
                await fnx.logX({redisClient, param: symbol, value: {
                    section: section || 'dex:createTrade',
                    note: tradeData?.tradeID + ' / createTrade - failed1',
                    symbol,
                    refValues: {
                        orderCreated, refCall,
                    }
                }});
                resolve(false)
            }
        
        });
    }, 
    createBattleOrderFromDex: async props => {
        var { dexApi, dexPositions, redisClient, section,
            debug, orderAddons, section, cancelTPSLOrders = false
        } = props;
        const generateTradeData = ({positionData}) => {
            const p = positionData;
            const positionAmt = parseFloat(p.positionAmt);
            const orderID = fnx.generateKey({ inclTime: false });
            var orderData = {
                orderID,
                symbol: p.symbol.toLowerCase(),
                orderTransactionType: "createOrder",
                orderTransactionSubType: 0,
                orderType: positionAmt > 0 ? 'long' : 'short',
                orderPrice: parseFloat(p.entryPrice),
                orderBudget: parseFloat(p.entryPrice) * Math.abs(parseFloat(p.positionAmt)),
                orderAmount: Math.abs(parseFloat(p.positionAmt)),
                orderTime: parseFloat(p.updateTime),
                orderTimeEn: new Date(parseFloat(p.updateTime)).toISOString(),
                dtCreated: new Date(parseFloat(p.updateTime)).toISOString(),
                orderNote: 'init',
                orderSrc_isNewBar: false,
                orderSrc_ref: '',
                orderSrc_successStrategyID: '',
                orderSrc_successStrategyName: '',
                orderSrc_candle: {
                    time: p.updateTime,
                    close: p.entryPrice,
                },
                orderRefData: positionData,
                ...orderAddons,
            };
            orderData.act = { note: 'dexSync' };
            orderData.entryCheck = { note: 'dexSync' };
            return orderData;
        }
        return new Promise(async (resolve, reject) => {
            try {
                let battle_parameters = await dex.getBattleParams({ redisClient })
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                dexPositions = dexPositions || await dex.getDexPositionsCache({ redisClient, dexApi, refCall: 'createBattleOrderFromDex' });
                let positions = dexPositions; //?.positions;
                let resp = []
                //CHECK: Battle trades value yu al, poz varsa ekleme!!!...
                if (Array.isArray(positions)) {
                    const { battleInterval } = battle_parameters || {};
                    await Promise.all(
                        positions.map(async (p) => {
                            try {
                                let ttag = p.symbol.toLowerCase() + '_' + battleInterval.toString();
                                let orderData = generateTradeData({ positionData: p, });

                                let redisOrderKey = redixPrefix.dataOrders + ttag;
                                let currOrdersStg = await redisClient.get(redisOrderKey);
                                let currOrders = JSON.parse(currOrdersStg);
                                let orders = currOrders ? currOrders : [];
                                orders.push(orderData);
                                await redisClient.set(redisOrderKey, JSON.stringify(orders));

                                let trdA = await battle.createTrade({
                                    redisClient,
                                    payload: orderData,
                                    taskTag: ttag,
                                    isExchange: true,
                                    actInDex: false,
                                    refCall: 'createBattleOrderFromDex', section,
                                });
                                let tpsl = false;
                                if (trdA) {
                                    try {
                                        tpsl = await dex.checkDexTPSL2_single({
                                            redisClient, dexApi, section,
                                            symbol: p.symbol, force: false,
                                            refCall: trdA?.tradeID || 'createBattleOrderFromDex',
                                            closePositionIfOutOfBoundaries: false,
                                            debug: false, section: section || 'createBattleOrderFromDex',
                                            threshold: 50, cancelTPSLOrders: false,
                                        });
                                    } catch (e) {
                                        fnx.log('createbattleorderfromdex -> tp sl checkDexTPSL2_single failed', trdA.symbol, e)
                                    }
                                }
                                resp.push({
                                    symbol: p.symbol,
                                    trade: true,
                                    tpsl: tpsl,
                                })
                            } catch (e) {
                                fnx.log('createBattleOrderFromDex failed for', p?.symbol, e,)

                                resp.push({
                                    symbol: p.symbol,
                                    error: true,
                                    e,
                                })
                            }
                        }),
                    );
                    // for (const p of positions) {
                    //     let ttag = p.symbol.toLowerCase() + '_' + battleInterval.toString();
                    //     let orderData = generateTradeData({ positionData: p, });

                    //     let redisOrderKey = redixPrefix.dataOrders + ttag;
                    //     let currOrdersStg = await redisClient.get(redisOrderKey);
                    //     let currOrders = JSON.parse(currOrdersStg);
                    //     let orders = currOrders ? currOrders : [];
                    //     orders.push(orderData);
                    //     await redisClient.set(redisOrderKey, JSON.stringify(orders));

                    //     let trdA = await battle.createTrade({
                    //         redisClient,
                    //         payload: orderData,
                    //         taskTag: ttag,
                    //         isExchange: false,
                    //         actInDex: false,
                    //         refCall: 'createBattleOrderFromDex',
                    //     });
                    //     if (trdA) {
                    //         try {
                    //             await dex.checkDexTPSL2_single({
                    //                 redisClient, dexApi, 
                    //                 symbol: p.symbol, force: false, 
                    //                 refCall: trdA?.tradeID || 'createBattleOrderFromDex',
                    //                 closePositionIfOutOfBoundaries: true,
                    //                 debug: false, section: section || 'createBattleOrderFromDex',
                    //                 threshold: 50,
                    //             });
                    //         } catch (e) {
                    //             fnx.log('createbattleorderfromdex -> tp sl checkDexTPSL2_single failed', trdA.symbol, e)
                    //         }
                    //     }
                    // }
                }
                // fnx.log('battle_parameters', battle_parameters)
                resolve(resp)
            } catch (e) {
                debug && fnx.log('error in createBattleOrderFromDex', e);
                reject(e)
            }
        });
    },
    closeTradeBattleOnly: async props => {
        var {dexApi, redisClient, symbol, battleTrades, lastCandle, battle_parameters,
            lastCandleRef = 'lastDexTrade', updateNote, closeNote, closeType, ref = 'close'} = props;
        return new Promise(async (resolve, reject) => {
            try {
                battle_parameters = battle_parameters || await dex.getBattleParams({ redisClient })
                // battleTrades = battleTrades || await battle.fnGetTrades({redisClient, openOnly: true, symbol});
                let xsmi = symbol.toLowerCase() + '_' + battle_parameters?.battleInterval.toString();
                let redisKeyy = redixPrefix.dataTrades + xsmi;
                let pairTradesAllStg = await redisClient.get(redisKeyy);
                let tradesAll = JSON.parse(pairTradesAllStg);
                // fnx.log('tradesall', symbol, JSON.stringify(tradesAll));
                tradesAll.filter(t => t.tradeClosed !== true).map(t => {
                    let sg = t;
                    let currTrdDirection = sg.direction;
    
                    let closePrice = lastCandleRef == 'lastDexTrade' ? parseFloat(lastCandle.price) :  parseFloat(lastCandle.close);
                    let entryPrice = parseFloat(sg.entryPrice);
                    let entryAmount = parseFloat(sg.entryAmount);
    
                    let realizedPips = (closePrice - entryPrice) * (currTrdDirection == 'long' ? 1 : -1);
                    let realizedPipRatio = realizedPips / entryPrice;
                    let realizedPnlB4Comm = realizedPips * entryAmount;
                    let commission = ((closePrice + entryPrice) / 2) * entryAmount * commissionRate
                    let realizedPnl = realizedPnlB4Comm - commission;
    
                    let closeTypeX = closeType == 90 ? (realizedPnl > 0 ? 10 : 20) : (closeType || (realizedPnl > 0 ? 10 : 20));
                    let closeNoteX = closeType == 90 ? (realizedPnl > 0 ? 'takeProfit' : 'stopLoss') : (closeNote || (realizedPnl > 0 ? 'takeProfit' : 'stopLoss'));

                    sg.closePrice = closePrice;
                    sg.closeRef = ref;
                    sg.closeNote = closeNoteX;
                    sg.closeType = closeTypeX;
                    sg.tradeClosed = true;
                    sg.updateNote = updateNote;
                    sg.entryBudget = entryPrice * entryAmount;
                    sg.notional = closePrice * entryAmount;
                    sg.unRealizedPnlB4Comm = 0;
                    sg.unRealizedPnl = 0;
    
                    sg.realizedPips = realizedPips;
                    sg.realizedPipRatio = realizedPipRatio;
                    sg.realizedPnlB4Comm = realizedPnlB4Comm;
                    sg.commission = commission;
                    sg.realizedPnl = realizedPnl;
    
                    sg.closeBarEOP = lastCandle.time;
                    sg.closeTime = lastCandle.time;
                    sg.tradeLifeTime = parseFloat((lastCandle.time - sg?.entryTime) / 1000 / 60).toFixed(1) + 'min'
                    sg.closeTimeEn = new Date(lastCandle.time);
                    sg.dtupdated = Date.now();
                    sg.dtupdatedEn = new Date(Date.now());
                    return sg;
                })
                await redisClient.set(redisKeyy, JSON.stringify(tradesAll));
                resolve(true);
            } catch (e) {
                fnx.log('closeTradeFailed 123123', e)
                reject('close trade failed');
            }
        });
    },
};
const dex = exports.dex = {
    getBattleParams: props => {
        var { redisClient, body } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        return new Promise(async (resolve, reject) => {
            try {
                if (redisClient) {
                    const battle_init = await redisClient.get('battle_init'); 
                    if(battle_init) {
                        let biStg = JSON.parse(battle_init);
                        const {battle_params} = biStg || {};
                        // console.log('getting with redis biStg', biStg)
                        // console.log('getting with redis battleParams', battle_params)
                        let bpStg = battle_params && JSON.parse(battle_params);
                        const { parameters = {} } = bpStg;
                        resolve(parameters);
                    } else {
                        if (body) {
                            let form = JSON.parse(body);
                            const { parameters = {} } = form;
                            console.log('parameters', parameters, form)
                            resolve(parameters);
                        } else {
                            fnx.log('no battle_init from redis, checking form body and no body');
                            resolve(false)
                        }
                    }
                } else {
                    console.log('no redisClient')
                    resolve(false)
                }
            } catch (e) {
                console.log('battle_init init Error', e);
                resolve(false);
            }
        });
    },
    getDexApi: props => {
        var { body, redisClient } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        return new Promise(async (resolve, reject) => {
            try {
                let globalDexApi = await global.dexApi?.api;
                let battleType = {};
                if (!globalDexApi) {
                    let parameters = await dex.getBattleParams({redisClient, body});
                    battleType = parameters.battleType;
                    const { config = {} } = battleType;
                    const { apiKey, apiSecret, dexCode, dexTitle, testDex } = config;
                    // console.log('getDexApi', dexTitle);
                    var options = {
                        apiKey: apiKey, apiSecret: apiSecret, APIKEY: apiKey, APISECRET: apiSecret,
                        useServerTime: true, 'family': 4,
                    };
                    if (testDex) {
                        options = {
                            ...options, test: testDex,
                            httpFutures: 'https://testnet.binancefuture.com', wsFutures: 'wss://fstream.binancefuture.com',
                        }
                    }
                    const dexApi = new Binance().options({
                        ...options,
                    });
                    global.dexApi = { api: dexApi, ...config, };
                    resolve(dexApi);
                } else {
                    resolve(globalDexApi);
                }
            } catch (e) {
                console.log('dexAPi init Error', e);
                const binanceApi = new Binance().options({
                    useServerTime: true, 'family': 4,
                });
                resolve(binanceApi);
            }
        });
    },
    fnCalculateBarCount: async ({ symbol, lastCandle, battleInterval, lastTrade}) => {
        return new Promise(async (resolve, reject) => {
            let resp = false;
            try {
                if (lastTrade) {
                    let refLastTradeBarBop = lastTrade?.entryBarBOP;
                    let refbattleInterval = battleInterval.toString();
                    let carpanUnit = refbattleInterval.slice(-1);
                    let carpanBase = 60000; //1m
                    let carpan = carpanUnit == 'm' ? carpanBase : carpanUnit == 'h' ? 60 * carpanBase : 24 * 60 * carpanBase;
                    let refLastBarBop = lastCandle.time;
                    let barDelta = refLastBarBop - refLastTradeBarBop;
                    let barCount = barDelta / carpan;
                    resp = barCount //additionPositionCandleInterval >= barCount
                    resolve(resp)
                    // fnx.log('ruleCheckInterval', refLastTradeBarBop, refbattleInterval, carpan, refLastBarBop, 
                    // additionPositionCandleInterval, barCount, '--->', ruleCheckInterval);
                } else {
                    resp = -1;
                    resolve(resp)
                }
            } catch (e) {
                fnx.log('error calc bar count', e)
                resolve(false)
            }
        });
    },
    isBattleInExchange: props => {
        var { redisClient, battle_params } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            battle_params = battle_params || await dex.getBattleParams({ redisClient });
            let battleType = battle_params && battle_params?.battleType;
            const { dex: dexType, config = {} } = battleType;
            let isExchange = dexType == 'exchange';
            resolve(isExchange)
        });
    }, 
    createDexTrade: props => {
        var { redisClient, dexApi, payload, setTPSL = false, battle_params, section, threshold = 6000 } = props;
        let dtBOP = Date.now();
        return new Promise(async (resolve, reject) => {
            redisClient = redisClient || new Redis({
                host: '127.0.0.1',
                port: 6379,
            });
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                battle_params = battle_params || await dex.getBattleParams({ redisClient });
                let dexConfig = battle_params && battle_params?.battleType && battle_params?.battleType?.config
                let dexOrderType = dexConfig && dexConfig.orderType || 'MARKET';
                let symbol = payload.symbol.toUpperCase();

                let lRun = await fnx.redisLog({
                    redisClient, action: 'get',
                    param: 'createDexTrade_' + symbol,
                });
                let keepOn = threshold < (Date.now() - (lRun?.dtUpdated || 0));

                if (keepOn) {

                    lRun = await fnx.redisLog({
                        redisClient, param: 'createDexTrade_' + symbol,
                        action: 'set'
                    });

                    const ExchangeSettings = battleExchangeInfo && battleExchangeInfo[symbol];
                    const { minPrice, maxPrice, tickSize, stepSize, minQty, maxQty } = ExchangeSettings ? ExchangeSettings : {}
                    var qty = parseFloat(minQty) > payload.entryAmount ? parseFloat(minQty) : payload.entryAmount;
                    var quantity = dexApi.roundStep(qty, stepSize);

                    var entryPrice = dexApi.roundStep(payload.entryPrice, tickSize);
                    entryPrice = entryPrice > minPrice ? entryPrice : minPrice;
                    var notional = quantity * entryPrice;

                    var trading = battle_params?.trading;
                    const { entry = {}, exit, wallet } = trading || {};
                    const { positionBudget, useMartingaleVar } = entry
                    let dexFN_Side = payload.direction == 'long' ? 'BUY' : payload.direction == 'short' ? 'SELL' : 'NAX';

                    let refOrderId = payload.tradeID;
                    let saveData = {
                        symbol, tradeID: payload.tradeID, refOrderId,
                        success: true,
                        sure: Date.now() - dtBOP,
                        dtUpdated: new Date(Date.now()).toISOString(),
                        dexSide: dexFN_Side,
                        dexQty: Math.abs(quantity),
                        dexC_entryPrice: entryPrice,
                        dexC_notional: notional,
                        fn: 'createDexTrade',
                        payload: payload,
                        note: ('orderCmd.msg' || 'position create failed'),
                    };
                    // useMartingaleVar ? true : 
                    if (!useMartingaleVar && (notional > (positionBudget * (1.1)))) {
                        await fnx.logX({
                            redisClient, param: symbol, value: {
                                section: 'dex:createDexTrade',
                                note: 'createDexTrade - failed: ' + ('BUDGET_SHORTAGE'),
                                symbol,
                                refValues: {
                                    ...saveData,
                                    isSuccess: false,
                                    success: false,
                                    result: 'BUDGET_SHORTAGE',
                                    payload,
                                    entry,
                                }
                            }
                        });
                        // fnx.log('createDexTrade - failed - no budget', symbol);
                        reject('BUDGET_SHORTAGE');
                    } else {
                        //create order!
                        // fnx.log('createDexTrade started', symbol);
                        const flags = {
                            newOrderRespType: 'RESULT',
                            newClientOrderId: (payload.tradeID) || fnx.generateKey({ inclTime: false }), // + '_' + payload.tradeNo + '_' + payload.tradeSubNo
                            type: dexOrderType || 'MARKET',

                        }; //, { newOrderRespType: 'RESULT' }

                        // let orderCmd = await dexApi.futuresOrder(dexFN_Side, symbol, Math.abs(quantity), false, {
                        //     ...flags,
                        // });
                        let orderCmd = await dexBinance.futuresOrder({
                            dexApi, redisClient, refCall: 'createDexTrade',
                            note: 'newClientOrderId:' + flags.newClientOrderId + ', dexFN_Side:' + dexFN_Side + ', symbol:' + symbol + ', qty: ' + Math.abs(quantity).toString() + '....' + JSON.stringify(flags)
                        }, dexFN_Side, symbol, Math.abs(quantity), false, {
                            ...flags,
                        });
                        if (orderCmd && (orderCmd?.code || orderCmd.status == 'EXPIRED')) {
                            await fnx.logX({
                                redisClient, param: symbol, value: {
                                    section: section || 'dex:createDexTrade',
                                    note: 'createDexTrade - failed: ' + (orderCmd?.status || (orderCmd?.code + ':' + orderCmd?.msg)),
                                    symbol,
                                    refValues: {
                                        ...saveData,
                                        isSuccess: false,
                                        result: (orderCmd?.status || (orderCmd?.code + ':' + orderCmd?.msg)),
                                        response: orderCmd,
                                        payload,
                                    }
                                }
                            });
                            reject(orderCmd?.code || orderCmd.status);

                        } else if (orderCmd && orderCmd.status == 'FILLED') {
                            //...
                            //success...
                            let redisKey = redixPrefix.dex.dataLogs + 'ORDER:NEW_' + symbol + '_' + refOrderId + '_' + dexFN_Side;
                            saveData = {
                                ...saveData,
                                dexflags: flags,
                                status: orderCmd?.status || 'FILLED',
                                response: orderCmd,
                            }

                            await fnx.logX({
                                redisClient, param: symbol, value: {
                                    section: section || 'dex:createDexTrade',
                                    note: refOrderId + ' / createDexTrade - success: ' + (orderCmd?.status),
                                    symbol,
                                    refValues: {
                                        ...saveData, isSuccess: false,
                                        result: (orderCmd?.status),
                                        response: orderCmd,
                                        payload,
                                    }
                                }
                            });


                            await redisClient.set(redisKey, JSON.stringify(saveData));
                            if (orderCmd?.status == 'FILLED' && setTPSL) {
                                try {
                                    await dex.checkDexTPSL2_single({
                                        redisClient, dexApi,
                                        symbol, force: true,
                                        refCall: orderCmd?.clientOrderId || 'createNewTrade',
                                        closePositionIfOutOfBoundaries: true,
                                        debug: false, section: section || 'createDexTrade',
                                        threshold: 50,
                                    });
                                } catch (e) {
                                    fnx.log('create dex -> tp sl checkDexTPSL2_single failed', symbol, e)
                                }
                            }

                            // fnx.log('createDexTrade - success', symbol, orderCmd?.status || (orderCmd?.code + ':' + orderCmd?.msg));

                            resolve({
                                success: true,
                                action: 'NEXT',
                                status: orderCmd?.status,
                                response: orderCmd,
                            });

                        } else {
                            await fnx.logX({
                                redisClient, param: symbol, value: {
                                    section: 'dex:createDexTrade',
                                    note: 'createDexTrade - failed - binance' + (refOrderId || orderCmd?.status),
                                    symbol,
                                    refValues: {
                                        ...tradeData, isSuccess: false,
                                        result: (orderCmd?.status),
                                        response: orderCmd,
                                        payload,
                                    }
                                }
                            });
                            fnx.log('createDexTrade - failed:', symbol, orderCmd?.status || (orderCmd?.code + ':' + orderCmd?.msg));
                            reject(orderCmd.status);
                        }
                    }

                } else {
                    reject({
                        success: false,
                        rCode: 1,
                        rDesc: 'waiting for timer..',
                        symbol: (symbol || p?.symbol),
                        elapsed: (Date.now() - (lRun?.dtUpdated || 0)),
                        threshold,
                    })
                }

            }
            catch (e) {
                fnx.log('dex create position failed.', e, payload?.symbol)
                reject(e)
            }
        });
    },
    createDexTPOrder: async props => {
        var { 
                dexApi, redisClient, symbol, price, positionAmt, 
                market = false, orderID = Date.now().toString() , 
                debug = false, refOrderId = 'NA', battleTrades, section, refCall
            } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        let dtBOPTP = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                battleTrades = battleTrades || await battle.fnGetTrades({ redisClient, openOnly: true, symbol });
                let tradeID = battleTrades && Array.isArray(battleTrades) && battleTrades.length !== 0 ? battleTrades.slice(-1)[0].tradeID : refOrderId;
                tradeID += '_TP';
                let param = symbol + '_createDexTPOrder_TP';
                var addon = {
                    type: !market ? 'TAKE_PROFIT' : 'TAKE_PROFIT_MARKET',
                    workingType: 'MARK_PRICE',
                    reduceOnly: true,
                    newClientOrderId: tradeID || refOrderId
                };
                let isSuccess = true;
                let dexFN_Side = parseFloat(positionAmt) > 0 ? 'SELL' : 'BUY';
                let fnNote = 'newClientOrderId:' + addon.newClientOrderId + ', type:' + 
                            addon.type + ', dexFN_Side:' + dexFN_Side + ', symbol:' +
                            symbol + ', qty: ' + Math.abs(positionAmt).toString() + ', price: ' + 
                            Math.abs(price).toString();
                let orderTP = await dexBinance.futuresOrder({
                    dexApi, redisClient, 
                    refCall: refCall || 'createDexTPOrder',
                    note: fnNote,
                    }, dexFN_Side, symbol, Math.abs(positionAmt), price, {
                    ...addon, stopPrice: price,
                });
                if (orderTP?.code || orderTP.status == 'EXPIRED') {
                    isSuccess = false;
                }
                await fnx.logX({
                    redisClient, param: symbol, value: {
                        section: section || 'dex:createDexTPOrder',
                        note: (addon.newClientOrderId ? (addon.newClientOrderId + ' / ') : '') + 'createDexTPOrder - futuresOrder: ' + (isSuccess ? 'OK: ' : 'FAILED: ') + (orderTP?.status ? (orderTP?.status + ' ' + addon.newClientOrderId) : (orderTP?.code + ':' + orderTP?.msg)) ,
                        symbol,
                        refValues: {
                            symbol, tradeID, refOrderId, isSuccess,
                            result: orderTP?.status || (orderTP?.code + ':' + orderTP?.msg),
                            sure: Date.now() - dtBOPTP,
                            orderParams: isSuccess ? '' : fnNote,
                        }
                    }
                });
                resolve({
                    isSuccess,
                    resp: orderTP,
                });
            }
            catch (e) {
                fnx.log('error on creating tp order', e, symbol);
                reject(e);
            }
        });
    },
    createDexSLOrder: async props => {
        var {   
                dexApi, redisClient, symbol, price, positionAmt, 
                market = false, orderID = Date.now().toString(), debug = false,
                refOrderId = 'NA', battleTrades, section, refCall
            } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        }); 

        let dtBOPSL = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                battleTrades = battleTrades || await battle.fnGetTrades({ redisClient, openOnly: true, symbol });
                battleTrades && !Array.isArray(battleTrades) && fnx.log('battleTrades na array_SL', battleTrades);
                let tradeID = battleTrades && Array.isArray(battleTrades) && battleTrades.length !== 0 ? battleTrades.slice(-1)[0].tradeID : refOrderId;
                tradeID += '_SL';
                let param = symbol + '_createDexTPOrder_SL';
                var addon = {
                    type: !market ? 'STOP' : 'STOP_MARKET',
                    workingType: 'MARK_PRICE',
                    reduceOnly: true,
                    newClientOrderId: tradeID || refOrderId,
                };
                let dexFN_Side = parseFloat(positionAmt) > 0 ? 'SELL' : 'BUY';
                let isSuccess = true;
                let fnNote = 'newClientOrderId:' + addon.newClientOrderId + ', type:' + 
                                addon.type + ', dexFN_Side:' + dexFN_Side + ', symbol:' +
                                symbol + ', qty: ' + Math.abs(positionAmt).toString() + ', price: ' + 
                                Math.abs(price).toString();
                let orderSL = await dexBinance.futuresOrder(
                        {
                        dexApi, redisClient, 
                        refCall: refCall || 'createDexSLOrder',
                        note: fnNote,
                        },
                        dexFN_Side, symbol, Math.abs(positionAmt), price, {
                    ...addon, stopPrice: price,
                });
 
                if (orderSL?.code || orderSL.status == 'EXPIRED') {
                    isSuccess = false; 
                }
                await fnx.logX({redisClient, param: symbol, value: {
                    section: section || 'dex:createDexSLOrder',
                    note: (addon.newClientOrderId ? (addon.newClientOrderId + ' / ') : '') + 'createDexSLOrder - futuresOrder: ' + (isSuccess ? 'OK: ' : 'FAILED: ')+ (orderSL?.status ? (orderSL?.status + ' ' + addon.newClientOrderId) : (orderSL?.code + ':' + orderSL?.msg)),
                    symbol,
                    refValues: {
                        symbol, tradeID, refOrderId, isSuccess, 
                        result: orderSL?.status || (orderSL?.code + ':' + orderSL?.msg),
                        sure: Date.now() - dtBOPSL,
                        orderParams: isSuccess ? '' : fnNote
                    }
                }});
                resolve({
                    isSuccess,
                    resp: orderSL,
                });
            }
            catch (e) {
                debug && fnx.log('error on creating SL order', symbol, e);
                reject(e);
            }
        });
    },
    setDexTPSL: async props => {
        var {dexApi, redisClient, position, debug = false, 
            refOrderId, refCall, symbol, actTypes = ['TP', 'SL'], section} = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            symbol = symbol || position.symbol; 

            const ExchangeSettings = battleExchangeInfo && battleExchangeInfo[symbol];
            const { minPrice, maxPrice, tickSize, stepSize, minQty, maxQty, minNotional } = ExchangeSettings ? ExchangeSettings : {}
            const calcTP = ({entryPrice, positionAmt, takeProfitRatio}) => {
                let refTPR = takeProfitRatio / 100;
                let multiplier = positionAmt < 0 ? -1 : 1;
                let calc = entryPrice * (1 + (refTPR * multiplier + commissionRate * 0.5) )
                let calcTP = dexApi.roundStep(calc, tickSize);
                calcTP = calcTP > minPrice ? calcTP : minPrice; 
                return {calc, calcTP}
            };

            const calcSL = ({entryPrice, positionAmt, stopLoss, stopLossUsePercentage, stopLossPercentage}) => {
                let refSLR = stopLossPercentage / 100;
                let multiplier = positionAmt > 0 ? -1 : 1;
                let calc = entryPrice * (1 + (refSLR * multiplier + commissionRate * 0.5) );
                let slPrice = entryPrice - stopLoss / positionAmt * (positionAmt > 0 ? 1 : -1);
                calc = stopLossUsePercentage ? calc : slPrice;
                //CHECK: stopLoss u USD olarak calistirma işi dex te.... - 
                let calcSL = dexApi.roundStep(calc, tickSize);
                calcSL = calcSL > minPrice ? calcSL : minPrice; 
                return {calc, calcSL, calcSLPrice: slPrice}
            };

            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                position = position || await dex.getDexPositionsCache({redisClient, dexApi, threshold: 100, symbol, refCall: 'setDexTPSL'})[0];
                let battle_parameters = await dex.getBattleParams({ redisClient });

                if (position) {

                    const { trading = {} } = battle_parameters || {};
                    const { entry, exit = {}, wallet } = trading;
                    const { takeProfitRatio, stopLoss, stopLossUsePercentage, stopLossPercentage } = exit;
                    let tp  = calcTP({
                        entryPrice: parseFloat(position.entryPrice), 
                        positionAmt: parseFloat(position.positionAmt), 
                        takeProfitRatio,
                    });
                    let sl  = calcSL({
                        entryPrice: parseFloat(position.entryPrice), 
                        positionAmt: parseFloat(position.positionAmt), 
                        stopLoss, stopLossUsePercentage, stopLossPercentage,
                    });
                    let dtAddOn = ''; //moment().format('DD_hhmmss');
                    debug && fnx.log('set tp', position.symbol, position.entryPrice, takeProfitRatio, tp, (parseFloat(position.entryPrice) * (1 + takeProfitRatio / 100)));
                    debug && fnx.log('set sl', position.symbol, position.entryPrice, stopLossPercentage, sl);
                    let tpAct = Array.isArray(actTypes) && actTypes.includes('TP') && await dex.createDexTPOrder({
                        dexApi, redisClient, symbol: position.symbol, 
                        price: tp.calcTP, positionAmt: parseFloat(position.positionAmt), 
                        refCall, section: section || 'setDexTPSL',
                        refOrderId: refOrderId ? refOrderId + '_' + dtAddOn + '_TP' : position.symbol + '_' + dtAddOn + '_tp_init',
                    });
                    let slAct = Array.isArray(actTypes) && actTypes.includes('SL') && await dex.createDexSLOrder({
                        dexApi, redisClient, symbol: position.symbol, 
                        price: sl.calcSL, refCall, section: section || 'setDexTPSL',
                        positionAmt: parseFloat(position.positionAmt), 
                        refOrderId: refOrderId ? refOrderId + '_' + dtAddOn + '_SL' : position.symbol + '_' + dtAddOn + '_sl_init',
                    });
                    
                    resolve({
                        tp: tpAct,
                        sl: slAct,
                        actTypes,
                    })
                } else {
                    fnx.log('set tpsl failed no position', 
                        symbol, refCall,
                        await dex.getDexPositionsCache({redisClient, dexApi, refCall: 'setDexTPSL2'}))
                    reject('setDexTPSL - failed - no position')
                }
            } catch (e) {
                fnx.log('error in setDexTPSL', symbol, e);
                reject(e)
            }
        });

    },
    setDexLeverages: props => {
        var { dexApi, redisClient, debug } = props;
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let battle_parameters = await dex.getBattleParams({ redisClient });
                const {pairs, trading = {} } = battle_parameters;
                const {wallet = {} } = trading;
                const {walletLeverage} = wallet;
                await Promise.all(
                    pairs.map(async (symbol) => {
                        await dexApi.futuresLeverage(symbol, walletLeverage)
                    })
                );
                resolve('futuresLeverage done')
            } catch (e) {
                fnx.log('error in leverage setup', e)
                reject(e)
            }
        });
    },
    getOrders: async props => {
        var { redisClient, dexApi, symbol, force = false, ref, threshold = 30000, section } = props;
        redisClient = redisClient || new Redis({ host: '127.0.0.1', port: 6379, });
        dexApi = dexApi || await dex.getDexApi({ redisClient });
        return new Promise(async (resolve, reject) => {
            try {
                if (!symbol) {
                    reject(false);
                } else {
                    let redisLogParam = 'getOrders_' + (symbol || p?.symbol);
                    let lRun = await fnx.redisLog({
                        redisClient, action: 'get',
                        param: redisLogParam,
                    });
                    let keepOn = threshold < (Date.now() - (lRun?.dtUpdated || 0));
                    symbol = symbol ? symbol.toUpperCase() : symbol;

                    var resp = {};
                    var stats = {}
                    let dtBOP = Date.now();
                    stats.bop = dtBOP;
                    let redisKeyA = redixPrefix.dex.data + 'ORDERS:' + symbol;

                    if (keepOn) {

                        var fOrders = await dexBinance.futuresAllOrders(
                            {
                                dexApi, redisClient,
                                refCall: 'getOrders',
                                note: 'symbol:' + symbol + ((', ref:' + ref) || ''),
                            }, symbol);
    
                        stats.orders = new Date(Date.now());
                        stats.ordersD = Date.now() - stats.bop;
                        if (Array.isArray(fOrders)) {

                            let dtS = {
                                dtUpdated: Date.now(),
                                dtUpdatedISO: new Date(Date.now()).toISOString(),
                                dataLength: fOrders.length,
                                data: fOrders,
                            };
                            redisClient && await redisClient.set(redisKeyA, JSON.stringify(dtS));

                            // let redisKey = redisKeyA + o.orderId + '_' + o.clientOrderId;
                                    
                            // await Promise.all(
                            //     fOrders.map(async (o) => {
                            //         let redisKey = redisKeyA + o.orderId + '_' + o.clientOrderId;
                            //         redisClient && await redisClient.set(redisKey, JSON.stringify(o));
                            //     }),
                            // );
                            stats.eop = new Date(Date.now());
                            stats.eopD = Date.now() - stats.bop;
    
                            lRun = await fnx.redisLog({
                                redisClient, param: redisLogParam, action: 'set'
                            });
    
                            await fnx.logX({
                                redisClient, param: symbol, value: {
                                    section: section || 'dex:getOrders',
                                    note: 'getOrders - futuresAllOrders: ' + symbol,
                                    symbol,
                                    refValues: {
                                        symbol, stats,
                                    }
                                }
                            });
    
                            resolve(fOrders);
                        } else {
                            fnx.log('getOrders error not isArray', symbol, fOrders,)
                            reject('not array in getorders', symbol)
                        }
                    } else {
                        //get from cache!
                        let trades = [];
                        let nodes = await redisClient.keys(redixPrefix.dex.data + 'ORDERS:' + symbol + '*');
                        if (Array.isArray(nodes)) {
                            // for (n of nodes) {
                            //     const value = await redisClient.get(n);
                            //     let nodeData = JSON.parse(value);
                            //     trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                            // }
                            await Promise.all(
                                nodes.map(async (n) => {
                                    const value = await redisClient.get(n);
                                    let nodeData = JSON.parse(value);
                                    trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                                })
                            );
                        };
                        resolve(trades);
                    }
                }
            } catch (e) {
                fnx.log('getOrders error ', symbol, e)
                reject(e)
            }
        });
    },
    getDexPositions: async props => {
        var {redisClient, dexApi} = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        let BinanceApi = dexApi || await dex.getDexApi({redisClient});
        return new Promise(async (resolve, reject) => {
            const fxOO = async () => {
                var fOpenOrders = await BinanceApi.futuresOpenOrders();
                return fOpenOrders;
            };
            const fxAc= async () => {
                var fPRisk = await BinanceApi.futuresPositionRisk();
                fPRisk = Array.isArray(fPRisk) && fPRisk.filter(b => parseFloat(b.positionAmt) !== 0);
                Array.isArray(fPRisk) && fPRisk.map(fp => {

                    let dir = parseFloat(fp.positionAmt) < 0 ? 'SELL' : 'BUY';
                    let dirMultiplier = parseFloat(fp.positionAmt) < 0 ? -1 : 1;
                    let notionalBOP = parseFloat(fp.entryPrice) * parseFloat(fp.positionAmt) ;
                    let commission = (notionalBOP + parseFloat(fp.notional)) * dirMultiplier * commissionRate;                    
                    let unRealizedProfit = parseFloat(fp.unRealizedProfit);
                    let unRealizedPnl = unRealizedProfit - commission;

                    fp.dtUpdated = Date.now();
                    fp.dtUpdatedISO = new Date(Date.now()).toISOString();
                    fp.updateTimeISO = new Date(fp.updateTime).toISOString();

                    fp.direction = dir;
                    fp.notionalBOP = notionalBOP;
                    fp.commission = commission;
                    fp.unRealizedPnl = unRealizedPnl;
                    fp.unRealizedPnlRC = unRealizedPnl / notionalBOP * dirMultiplier;
                    
                });
                return fPRisk;

            };
            try {
                var arr = [
                    fxAc(),
                    fxOO(),
                ];
                const reflect = (p) => p.then(
                    (v) => ({ v, status: 'fulfilled' }), (e) => ({ e, status: 'rejected' }),
                );
                var resp = {};
                var stats = {}
                let dtBOP = Date.now();
                stats.bop = new Date(Date.now());
                Promise.all(arr.map(reflect))
                    .then(async (results) => {
                        const rAccount = results[0].v;
                        const rOOrders = results[1].v;
                        let pStg = [];

                        let redisKey = redixPrefix.dex.data + 'dexopenorders';
                        redisClient && await redisClient.set(redisKey, JSON.stringify({
                            dtUpdated: Date.now(),
                            dtUpdatedISO: new Date(Date.now()).toISOString(),
                            data: rOOrders
                        }));

                        resp.orders = rOOrders;

                        let BattleOpenTrades = await battle.fnGetTrades({redisClient, openOnly: true});

                        Array.isArray(rAccount) && rAccount.map((p, i) => { 
                            let tpslOrders =
                                Array.isArray(rOOrders) && rOOrders.filter(o => o.symbol == p.symbol && (o.reduceOnly == true || o.closePosition == true) && o.status == 'NEW'); 
                            Array.isArray(tpslOrders) && tpslOrders.map(t => {
                                t.gaussOrderType = p.direction == 'BUY' ?
                                    (parseFloat(t.price) > parseFloat(p.entryPrice) ? 'takeProfit' : 'stopLoss')
                                    :
                                    p.direction == 'SELL' ?
                                        (parseFloat(t.price) < parseFloat(p.entryPrice) ? 'takeProfit' : 'stopLoss')
                                        : 'NA';
                                pStg.push(t);
                            });
                            p.takeProfit = Array.isArray(tpslOrders) && tpslOrders.filter(tp => tp.gaussOrderType == 'takeProfit');
                            p.stopLoss = Array.isArray(tpslOrders) && tpslOrders.filter(tp => tp.gaussOrderType == 'stopLoss');
                            let boTrades = Array.isArray(BattleOpenTrades) && BattleOpenTrades.filter(bt => bt.pair == p.symbol.toLowerCase());
                            let boTradesStg = boTrades && boTrades.map(b => b.tradeID.split('_')[0])
                            let boTrade = Array.isArray(boTradesStg) && boTradesStg.length !== 0 && [...new Set([...boTradesStg])];
                            // fnx.log(p.symbol, boTradesStg, boTrade.toString());
                            p.gaussTradeID = boTrade ? boTrade.toString(): '';
                        });
                        resp.positions = rAccount;

                        redisKey = redixPrefix.dex.data + 'dexpositions';
                        await redisClient.set(redisKey, JSON.stringify({
                            dtUpdated: Date.now(),
                            dtUpdatedISO: new Date(Date.now()).toISOString(),
                            dtProcessTime: Date.now() - dtBOP,
                            data: rAccount}));

                        let OtherOpenOrders = Array.isArray(rOOrders) ? rOOrders.filter(r => !pStg.find(a => r.orderId == a.orderId)) : [];
                        !Array.isArray(rOOrders) && fnx.log('error 12029 rOOrders', rOOrders);

                        resp.otherOpenOrders = OtherOpenOrders;
                        redisKey = redixPrefix.dex.data + 'dexopenorders_others';
                        await redisClient.set(redisKey, JSON.stringify({
                            dtUpdated: Date.now(),
                            dtUpdatedISO: new Date(Date.now()).toISOString(),
                            data: OtherOpenOrders}));

                        stats.eop = new Date(Date.now());
                        stats.sure = Date.now() - dtBOP;
                        resp.stats = stats;

                        // fnx.log('resp', JSON.stringify(resp));
                        resolve(resp)
                    })
                    .catch((e) => {
                        console.log('get accOo', e)
                        resp.error = true;
                        resolve(resp)
                    });

            } catch (e) {
                console.log('get getDexPos failed', e)
                reject(e)
            }
        });
    },
    getDexPositionsCache: async props => {
        var { redisClient, dexApi, symbol, debug = false, 
            ageLimit = 5000, force = false, 
            ageCheckRequired = true, refCall,
        } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        return new Promise(async (resolve, reject) => {
            try {
                let rKey = redixPrefix.dex.data + 'dexpositions';
                let dexpositionsStg = await redisClient.get(rKey);
                let dexpositions = JSON.parse(dexpositionsStg);
                const {dtUpdated, data} = dexpositions || {};
                let ageCheck = force ? false : ( ageCheckRequired ? ((Date.now() - dtUpdated) < ageLimit ) : true);
                if (ageCheck) {
                    let resp = Array.isArray(data) ? [...data] : [];
                    resp = symbol ? resp.filter(d => d?.symbol == symbol.toUpperCase()) : resp;
                    resolve(resp);
                } else {
                    let posData = await dex.getDexPositions({redisClient, dexApi});
                    let positions = posData?.positions;
                    // let resp = positions;
                    let resp = Array.isArray(positions) ? [...positions] : [];
                    resp = symbol ? resp.filter(d => d.symbol == symbol.toUpperCase()) : resp;
                    resolve(resp);
                }
            }
            catch (e) {
                fnx.log('error in getDexPositionsCache', e, refCall);
                resolve(false);
            }
        });
    },
    getDexOpenOrdersCache: async props => {
        var { redisClient, dexApi, symbol, debug = false, ageLimit = 5000, 
            ageCheckRequired = true, force = false} = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                let rKey = redixPrefix.dex.data + 'dexopenorders';
                let dexpositionsStg = await redisClient.get(rKey);
                let dexpositions = JSON.parse(dexpositionsStg);
                const {dtUpdated, data} = dexpositions;
                // let ageCheck = ageCheckRequired ? ((Date.now() - dtUpdated) < ageLimit ) : true;
                let ageCheck = force ? false : ( ageCheckRequired ? ((Date.now() - dtUpdated) < ageLimit ) : true);
                
                if (ageCheck) {
                    let resp = Array.isArray(data) ? [...data] : [];
                    resp = symbol ? resp.filter(d => d?.symbol == symbol.toUpperCase()) : resp;
                    resolve(resp);
                } else {
                    let posData = await dex.getDexPositions({redisClient, dexApi});
                    let positions = posData?.orders;
                    let resp = positions;
                    resp = symbol ? resp.filter(d => d.symbol == symbol.toUpperCase()) : resp;
                    resolve(resp);
                }
            }
            catch (e) {
                fnx.log('error in getDexOpenOrdersCache', e);
            }
        });
    }, 
    cancelDexOpenOrders: async props => {
        var { redisClient, dexApi, symbol, all, forceFetch = true, 
            onlyCloseOrderTypes = false, debug = false, section } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        return new Promise(async (resolve, reject) => {
            dexApi = dexApi || await dex.getDexApi({ redisClient });
            try {
                // var fOpenOrders = await dexApi.futuresOpenOrders();
                let respDT = Date.now();
                var fOpenOrders = await dexBinance.futuresOpenOrders({
                    dexApi, redisClient, refCall: 'cancelDexOpenOrders'
                });
                
                fOpenOrders = !symbol ? fOpenOrders : fOpenOrders.filter(fo => fo.symbol == symbol.toUpperCase());
     
                if (Array.isArray(fOpenOrders)) {
                    if (fOpenOrders.length !== 0) {
                        debug && fnx.log('Open Orders', fOpenOrders.length)
                        let counter = 0;
                        await Promise.all(
                            fOpenOrders.map(async (o) => {
                                if (onlyCloseOrderTypes) {
                                    // (o.reduceOnly || o.closePosition) && fnx.log('cancel order', symbol, o.symbol, o.type, o.status);
                                    // (o.reduceOnly || o.closePosition) && await dexApi.futuresCancel(o.symbol, { orderId: o.orderId.toString() });
                                    if ((o.reduceOnly || o.closePosition)) {
                                        (o.reduceOnly || o.closePosition) && await dexBinance.futuresCancel({
                                            dexApi, redisClient, refCall: 'cancelDexOpenOrders',
                                            note: 'cOnlyReduce:' + o.symbol + ', orderID:' + o.orderId.toString()
                                        }, o.symbol, { orderId: o.orderId.toString() });
                                        counter++;
                                    }
                                } else {
                                    // await dexApi.futuresCancelAll(o.symbol);
                                    await dexBinance.futuresCancelAll({
                                        dexApi, redisClient, refCall: 'cancelDexOpenOrders', 
                                        note: 'cAll:' +  o.symbol + '',
                                    }, o.symbol);
                                    counter++
                                }
                            })
                        );
                        await fnx.logX({redisClient, param: symbol, value: {
                            section: section || 'dex.cancelDexOpenOrders',
                            note: 'cancelDexOpenOrders - ' + (counter.toString()),
                            symbol: symbol || 'GAUSS_GENERIC',
                            refValues: {
                                countTotal: fOpenOrders.length,
                                sure: Date.now() - respDT,
                            }
                        }});

                        resolve(true);
                    } else {
                        debug && fnx.log('no open order - no need to cancel!', fOpenOrders);
                        resolve(true);
                    }
                } else {
                    fnx.log('fOpenOrders response array degil', fOpenOrders)
                    reject(false)
                }
            } catch (e) {
                fnx.log('failed closing open orders', e);
                reject(e)
            }
        });
    },
    mngFailCount: props => {
        var {   redisClient, param, action = 'set', value, 
            } = props;
        return new Promise(async (resolve, reject) => {

            let rediskey = redixPrefix.dex.dataLogs + '_fnfailCount';
            redisClient = redisClient || new Redis({
                host: '127.0.0.1',
                port: 6379,
            });
            let nVal = {}
            let currValStg = await redisClient.get(rediskey);
            let xcurrVal = currValStg ? JSON.parse(currValStg) : {};

            if (action == 'get') {
                let iData = xcurrVal && xcurrVal[param] ? xcurrVal[param] : {  dtU: new Date(Date.now()).toISOString(), sayac: 0 };
                resolve({ ...iData })
            } else {
                //increase sayac..
                let currV = xcurrVal[param] || {dtC: new Date(Date.now()).toISOString()}
                let currSayac = xcurrVal && xcurrVal[param] ? xcurrVal[param].sayac : 1;
                let ySayac = value || (currSayac ? currSayac + 1 : 1);
                let respx = {  ...currV, dtU: new Date(Date.now()).toISOString(), sayac: ySayac }
                xcurrVal[param] = respx;
                await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                resolve({...respx})
            }
        });
    },
    mngActList: props => {
        var {   redisClient, param, action = 'check', values, 
                dexRetryPeriod = 60000 * 5, sayacPeriod = 2000, refParam
            } = props;
        return new Promise(async (resolve, reject) => {

            let rediskey = redixPrefix.dex.dataLogs + '_fnignoreList';
            redisClient = redisClient || new Redis({
                host: '127.0.0.1',
                port: 6379,
            });
            let nVal = {}
            let currValStg = await redisClient.get(rediskey);
            let xcurrVal = currValStg ? JSON.parse(currValStg) : {};

            if (action == 'check') {
                let iData = xcurrVal ? xcurrVal[param] : false;
                let currSayac = iData?.sayac || 0;
                let rule1 = iData && iData.dtExpire && ((iData?.dtExpire - Date.now()) > 0);
                let rule2 = iData && iData.sayacExpire && (iData.sayacExpire > currSayac);
                let rule0 = !iData;
                if ([rule0, rule1, rule2].some(r => r == true)) {
                    //can act..
                    xcurrVal[param] = false;
                    await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                    resolve({act: true, action: 'check',})
                } else {
                    //increase sayac..
                    ySayac = currSayac ? currSayac + 1 : 1;
                    nVal = {
                        ...iData,
                        sayac: ySayac,
                        dtUpdated: Date.now(),
                        dtUpdatedISO: new Date(Date.now()),
                    };
                    xcurrVal[param] = nVal;
                    await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                    resolve({act: false, action: 'check'})
                }

            } else if (action == 'add') {
                nVal = {
                    sayac: 1,
                    sayacExpire: sayacPeriod,
                    dtAdded: Date.now(),
                    dtExpire: Date.now() + dexRetryPeriod,
                    dtExpireISO: new Date(Date.now() + dexRetryPeriod),
                    dtUpdated: Date.now(),
                    dtUpdatedISO: new Date(Date.now()),
                    values,
                };
                xcurrVal[param] = nVal;
                
                await dex.mngFailCount({
                    param: refParam,
                    action: 'set', value: 1
                });

                await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                resolve({act: true, action: 'add'})
            }

        });

    },
    mngDexActIgnoreList: props => {
        var { redisClient, symbol, values,
            dexRetryPeriod = 60000 * 5, sayacPeriod = 2000, } = props;
        return new Promise(async (resolve, reject) => {
            try {
                let rediskey = redixPrefix.dex.dataLogs + '_fnignoreAct';
                redisClient = redisClient || new Redis({
                    host: '127.0.0.1',
                    port: 6379,
                });
                let nVal = {}
                let currValStg = await redisClient.get(rediskey);
                let xcurrVal = currValStg ? JSON.parse(currValStg) : {};
                
                let iData = xcurrVal ? xcurrVal[symbol] : false;
                let rule1 = iData && iData.dtUpdated && ((Date.now() - iData?.dtUpdated) > dexRetryPeriod);
                let rule2 = iData && iData.sayac && (iData.sayac > sayacPeriod);
                let rule0 = !iData;
                let ignoreAct = false;
                if ([rule0, rule1, rule2].some(r => r == true)) {
                    //..if in blacklist remove. -> sayac ı sıfırla ve 
                    nVal = {
                        sayac: 1,
                        dtUpdated: Date.now(),
                        dtUpdatedISO: new Date(Date.now()),
                        values,
                    };
                    ignoreAct = true;
                    xcurrVal[symbol] = nVal;
                    await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                    resolve({
                        act: ignoreAct,
                        vals: nVal,
                    });

                } else {
                    // update sayac && dtUpdated..
                    ySayac = iData?.sayac ? iData?.sayac + 1 : 1;
                    nVal = {
                        ...iData,
                        sayac: ySayac,
                    };
                    xcurrVal[symbol] = nVal;
                    await redisClient.set(rediskey, JSON.stringify(xcurrVal));
                    resolve({
                        act: ignoreAct,
                        vals: nVal,
                    });
                }

            } catch (e) {
                log('save log error', e);
                reject(false);
            }
        });
    },
    checkDexTPSLs: async props => {
        var { 
                redisClient, dexApi, symbol, debug = false, 
                dexRetryPeriod = 60000 * 5, sayacPeriod = 2000, refCall
            } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        var symbol = symbol.toUpperCase();
        return new Promise(async (resolve, reject) => {
            try {
                let doActCloseTrade = await dex.closeDexTrade({
                    redisClient, dexApi, symbol,
                    refCall: refCall || 'checkDexTPSLs',
                    section: 'checkDexTPSLs' + (refCall ? ' / ' + refCall : ''),
                });
                //DONE: close battle trades if success!
                await fnx.redisLog({
                    redisClient, param: 'checkDexTPSLs_' + symbol, 
                    action: 'set', value: {
                        success: true, doActCloseTrade
                    }
                });
                // fnx.log('dexActIgnoreList', xcurrVal);
                resolve(true);
            }
            catch (e) {
                fnx.log('error in checkDexTPSLs', symbol, e);
                resolve(false);
            }
        });


    },
    checkDexTPSL2_all: async props => {
        var {   redisClient, dexApi, positions, battle_parameters, refCall,
                closePositionIfOutOfBoundaries = false, cancelTPSLOrders = true,
                force = false, debug = false, section, closePositionOnly = false,
                threshold = 2* 60 * 1000,
            } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({redisClient});
                positions = positions || await dex.getDexPositionsCache({
                    redisClient, dexApi, force, refCall: 'checkDexTPSL2_all',
                });

                battle_parameters = battle_parameters || await dex.getBattleParams({ redisClient });
                Array.isArray(positions) && await Promise.all(
                    positions.map(async (p) => {
                        try {
                            await dex.checkDexTPSL2_single({
                                redisClient, dexApi, battle_parameters, 
                                position: p, force: false, refCall,
                                closePositionIfOutOfBoundaries, section,
                                cancelTPSLOrders, closePositionOnly,
                                threshold
                            });
                        }
                        catch (e) {
                            await fnx.logX({
                                redisClient, param: p?.symbol, value: {
                                    section: section || 'checkDexTPSL2_all',
                                    note: 'checkDexTPSL2_all / checkDexTPSL2_single error!',
                                    position: p,
                                    error: e,
                                    refCall,
                                }
                            });
                            fnx.log('checkDexTPSL2_single failed', p, e)
                        }
                    })
                );
                resolve(true)

            }
            catch (e) {
                fnx.log('checkDexTPSL2_all tpsl cehck failed1', e);
                reject(e);
            }
        });
    },
    checkDexTPSL2_single: async props => {
        var { redisClient, dexApi, symbol, position, 
            battle_parameters, refCall = 'initBattle', threshold = 2 * 60 * 1000,
            closePositionIfOutOfBoundaries = false, force = false, debug = false,
            cancelTPSLOrders = true, actTypes = ['TP', 'SL'],
            section, closePositionOnly = false,
        } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                position = position || await dex.getDexPositionsCache({
                    redisClient, dexApi, symbol, force, refCall: 'checkDexTPSL2_single'
                });
                let p = Array.isArray(position) ? position[0] : position;

                let lRun = await fnx.redisLog({
                    redisClient, action: 'get',
                    param: 'checkDexTPSL2_single_' + (symbol || p?.symbol),
                });
                let keepOn = threshold < (Date.now() - (lRun?.dtUpdated || 0));

                if (keepOn) {
                    debug && fnx.log('position to set tpsl', symbol, symbol || p?.symbol, p, position)
                    battle_parameters = battle_parameters || await dex.getBattleParams({ redisClient });
                    const { trading = {} } = battle_parameters || {};
                    const { entry, exit = {}, wallet } = trading;
                    const { takeProfitRatio, stopLoss, stopLossUsePercentage, stopLossPercentage } = exit;
                    let unRealizedPnlRC = p?.unRealizedPnlRC ? p.unRealizedPnlRC : 0;
                    let unRealizedPnl = p?.unRealizedPnl ? p.unRealizedPnl : 0;
                    let pSLMultiplier = -1;
                    let refTPR = takeProfitRatio / 100;
                    let refSLR = stopLossPercentage / 100;
                    let tpCheck = unRealizedPnlRC > refTPR;
                    let slCheck = stopLossUsePercentage ? unRealizedPnlRC < refSLR * pSLMultiplier : stopLoss < unRealizedPnl;
                    let actClose = [tpCheck, slCheck].some(p => p == true);
                    if (actClose && closePositionIfOutOfBoundaries) {
                        try {
                            let actCloseResp = await dex.closeDexTrade({
                                dexApi, redisClient, section: section || 'checkDexTPSL2_single',
                                symbol: symbol || p.symbol,
                                positionAmt: parseFloat(p.positionAmt),
                                refCall: refCall || 'checkdextpsl2',
                            });

                            await fnx.logX({
                                redisClient, param: symbol || p.symbol, value: {
                                    section: section || 'checkDexTPSL2_single',
                                    note: 'checkDexTPSL2_single / closePosition: ' + JSON.stringify(actCloseResp),
                                    symbol: symbol || p.symbol,
                                    resp: {
                                        position: p,
                                        actCloseResp,
                                        refTPR, refSLR, tpCheck, slCheck, actClose, closePositionIfOutOfBoundaries
                                    },
                                }
                            });
                        } catch (e) {
                            //fnx.log('syncDex checkDexTPSL2_single', symbol || p.symbol, 'closePosition');
                            await fnx.logX({
                                redisClient, param: symbol || p.symbol, value: {
                                    section: section || 'checkDexTPSL2_single',
                                    note: 'checkDexTPSL2_single / closePosition: error!',
                                    symbol: symbol || p.symbol,
                                    resp: {
                                        e,
                                        position: p,
                                        refTPR, refSLR, tpCheck, slCheck, actClose, closePositionIfOutOfBoundaries
                                    },
                                }
                            });
                        }
                    } else {
                        try {
                            if (!closePositionOnly) {
                                //set tp / sl
                                let numberOfOpenOrders = 0;
                                try {
                                    let numberOfOpenOrdersX = (symbol || p.symbol) && await dex.getDexOpenOrdersCache({
                                        redisClient, dexApi, symbol: symbol || p.symbol
                                    });
                                    numberOfOpenOrders = Array.isArray(numberOfOpenOrdersX) ? numberOfOpenOrdersX.length : 0;
                                    // fnx.log('numb oo', symbol || p.symbol, numberOfOpenOrders)
                                } catch (eoo) {
                                    fnx.log('numberOfOpenOrders calc error', eoo);
                                }

                            // debug && fnx.log('syncDex checkDexTPSL2_single', symbol || p.symbol, 'set tp / sl');
                            numberOfOpenOrders !== 0 && cancelTPSLOrders && await dex.cancelDexOpenOrders({
                                dexApi, redisClient, symbol: symbol || p.symbol,
                                section: section || 'checkDexTPSL2_single',
                                onlyCloseOrderTypes: true
                            });
                            numberOfOpenOrders !== 0 && cancelTPSLOrders && await fnx.logX({
                                redisClient, param: symbol || p.symbol, value: {
                                    section: section || 'checkDexTPSL2_single',
                                    note: 'tpsl_single / ' + (numberOfOpenOrders !== 0 ? 'cancelled open reduce orders: ' + numberOfOpenOrders : 'no open order / no need to cancel orders.'),
                                    symbol: symbol || p.symbol,
                                    resp: {
                                        position: p, refCall,
                                    },
                                }
                            });
                            let tpActTPSL = p && await dex.setDexTPSL({
                                redisClient, dexApi, position: p, refOrderId: refCall, actTypes, section: section || 'checkDexTPSL2_single',
                            });
                            let isSuccessTP = tpActTPSL && tpActTPSL?.tp?.isSuccess;
                            let isSuccessSL = tpActTPSL && tpActTPSL?.sl?.isSuccess;
                            let isSuccess = isSuccessTP && isSuccessSL ? 'Success 2/ALL' : (isSuccessTP ? 'Success 1/TP': isSuccessSL ? 'Success 1/SL ': 'Success 0/X')
                            
                            p && await fnx.logX({
                                redisClient, param: symbol || p.symbol, value: {
                                    section: section || 'checkDexTPSL2_single',
                                    note: refCall + ' / checkDexTPSL2_single setDexTPSL / ' + isSuccess,
                                    symbol: symbol || p.symbol,
                                    resp: {
                                        position: p, refCall, actTypes, tpActTPSL
                                    },
                                }
                            });

                            !p && fnx.log('checkDexTPSL2_single p not found', symbol)
                            } else {
                                // fnx.log('closePositionOnly mode!', symbol || p?.symbol);
                            }
                        }
                        catch (e) {
                            fnx.log('error checkDexTPSL2_single', e)
                            await fnx.logX({
                                redisClient, param: symbol || p.symbol, value: {
                                    section: section || 'checkDexTPSL2_single',
                                    note: refCall + ' / checkDexTPSL2_single / setDexTPSL error',
                                    symbol: symbol || p.symbol,
                                    resp: {
                                        position: p, refCall, e
                                    },
                                }
                            });
                        }
                    }

                    lRun = await fnx.redisLog({
                        redisClient, param: 'checkDexTPSL2_single_' + (symbol || p?.symbol),
                        action: 'set'
                    });

                    resolve(true)
                } else {
                    reject({
                        rCode: 1,
                        rDesc: 'waiting for timer..',
                        symbol: (symbol || p?.symbol),
                        elapsed: (Date.now() - (lRun?.dtUpdated || 0)),
                        threshold,
                    })
                }
            }
            catch (e) {
                fnx.log('checkDexTPSL2_single tpsl cehck failed2', symbol, e);
                reject(e);
            }
        });
    },
    closeDexTrade: async props => {
        var {redisClient, dexApi, symbol, typ = 'MARKET', battleTrades, section,
            ratio = 1, flags, refCall} = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        //DONE : cancel dex position!
        return new Promise(async (resolve, reject) => {
            let dtBOP = Date.now();
            dexApi = dexApi || await dex.getDexApi({ redisClient });
            // let battle_parameters = await battle.getBattleParams({ redisClient })
            let battle_params = await dex.getBattleParams({ redisClient });

            let doActCloseTrade = await dex.mngDexActIgnoreList({
                redisClient, symbol, values: { fn: refCall },
                dexRetryPeriod: 60000 * 2, sayacPeriod: 250,
            })
            if (doActCloseTrade && doActCloseTrade.act == true) {

                let currPositions = await dex.getDexPositionsCache({
                    redisClient, dexApi, symbol: symbol.toUpperCase(),
                    refCall: 'closeDexTrade',
                });
                let position = currPositions ? currPositions[0] : false;
                let isSuccess = true;

                if (position) {
                    try {
                        battleTrades = battleTrades || await battle.fnGetTrades({ redisClient, openOnly: true, symbol });
                        // fnx.log('battleTrades close?', battleTrades);
                        let tradeID = battleTrades && Array.isArray(battleTrades)  && battleTrades.length !== 0 ? battleTrades.slice(-1)[0].tradeID : refCall;
                        tradeID += '_closeTrade';

                        const { positionAmt } = position;
                        let dexFNM = parseFloat(positionAmt) > 0
                            // ? (typ !== 'MARKET' ? dexApi.futuresSell : dexApi.futuresMarketSell)
                            // : (typ !== 'MARKET' ? dexApi.futuresBuy : dexApi.futuresMarketBuy);
                            ? (typ !== 'MARKET' ? dexBinance.futuresSell : dexBinance.futuresMarketSell)
                            : (typ !== 'MARKET' ? dexBinance.futuresBuy : dexBinance.futuresMarketBuy);

                        const ExchangeSettings = battleExchangeInfo && battleExchangeInfo[symbol.toUpperCase()];
                        const { minPrice, maxPrice, tickSize, stepSize, minQty, maxQty } = ExchangeSettings ? ExchangeSettings : {}

                        let oAmount = Math.abs(dexApi.roundStep(parseFloat(positionAmt) * ratio, stepSize));
                        let refOrderId = ((refCall || '')  + (refCall ? '_' : '') + ''); // moment().format('DD_hhmmss');
                        let resp = await dexFNM({
                            dexApi, redisClient, refCall: 'closeDexTrade', 
                            note: 'closeDexTrade:' +  symbol + ', clientOrderId:' + (tradeID || refOrderId) + ', amount: ' + oAmount,
                        }, symbol, oAmount, {
                            reduceOnly: true,
                            newOrderRespType: 'RESULT',
                            clientOrderId: tradeID || refOrderId,
                        });
                        let respDT = Date.now();
                        if (resp?.code) {
                            isSuccess = false;
                        }
                        // isSuccess && await redisClient.set(redisKey, JSON.stringify(saveData));
                        await fnx.logX({redisClient, param: symbol, value: {
                            section: section || 'dex:closeDexTrade',
                            note: 'closeDexTrade - ' + (resp?.status || (resp?.code + ':' + resp?.msg)),
                            symbol,
                            refValues: {
                                symbol, tradeID, refOrderId, isSuccess, 
                                fCode: (tradeID || refOrderId) + '_E' + resp?.code,
                                status: resp?.code ? 'BINANCE_REJECTED__' + resp.code : resp?.status,
                                result: resp?.status || (resp?.code + ':' + resp?.msg),
                                vars: { symbol, oAmount, refCall: props.refCall + '_clsDx' },
                                note: (resp.msg || 'closeDexPosition'),
                                sure: Date.now() - respDT,
                            }
                        }});

                        resolve({
                            symbol,
                            success: resp?.code ? false : true,
                            status: resp?.code ? 'BINANCE_REJECTED__' + resp.code : resp.status,
                            resp: resp,
                            currPositions
                        })
                    } catch (e) {
                        fnx.log('failed close position error', e)
                        reject('failed close position error')
                    }
                } else {
                    //no position
                    resolve({resp: false, desc: 'no position'} )
                }
            } else {
                resolve({resp: false, desc: 'do act failed / mngDexActIgnoreList...'})
            }
        });
    },
    checkDexPositionsWO_TPSL: async props => {
        //TPSL si olmayan orderlara ekleme yapar.

        var { redisClient, dexApi, dexTrades, battle_parameters, refCall, threshold = 60000, section } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                battle_parameters = battle_parameters || await dex.getBattleParams({ redisClient });

                dexTrades = dexTrades || await dex.getDexPositionsCache({ redisClient, dexApi, threshold: threshold, refCall: 'checkDexPositionsWO_TPSL' });
                let dexPositionsWOTP = Array.isArray(dexTrades) && dexTrades.filter(p => !(Array.isArray(p.takeProfit) && p.takeProfit.length !== 0));
                let dexPositionsWOSL = Array.isArray(dexTrades) && dexTrades.filter(p => !(Array.isArray(p.stopLoss) && p.stopLoss.length !== 0));
                
                Array.isArray(dexPositionsWOTP) && Array.isArray(dexPositionsWOTP) && await Promise.all(
                    dexPositionsWOTP.map(async (p) => {
                        await dex.checkDexTPSL2_single({
                            redisClient, dexApi, battle_parameters, 
                            position: p, force: false, refCall: refCall || 'poswoTPSL',
                            closePositionIfOutOfBoundaries: false,
                            actTypes: ['TP'], threshold, section: section || 'poswoTPSL'
                        })
                    })
                );
                Array.isArray(dexPositionsWOSL) && Array.isArray(dexPositionsWOSL) && await Promise.all(
                    dexPositionsWOSL.map(async (p) => {
                        await dex.checkDexTPSL2_single({
                            redisClient, dexApi, battle_parameters, 
                            position: p, force: false, refCall: refCall || 'poswoTPSL',
                            closePositionIfOutOfBoundaries: false,
                            actTypes: ['SL'], threshold
                        })
                    })
                );
                resolve(true)
            } catch (e) {
                // fnx.log('checkDexPositionsWO_TPSL e', e);
                reject(e)
            }
        });
    },
    reconsileBattleV2: async props => {
        var {redisClient, dexApi, dexTrades, dexOrders, battleTrades, refCall, symbol, section, 
            fnClearDexUseLessReduceOrders, fnGetOrders, fnCheckDexPositionsWO_TPSL} = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            let checkBattleStarted = false;
            let battlestarted = await redisClient.get('appVars:battlestarted');
            if (parseFloat(battlestarted) == 1) {
                checkBattleStarted = true;
            };

            let runFnCondition = [checkBattleStarted].every(e => e == true);
            if (runFnCondition) {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                dexTrades = dexTrades || await dex.syncPosData({ redisClient, dexApi, threshold: 30000 });
                // dexOrders = dexOrders || await dex.getOrders({redisClient, dexApi, symbol});
                battleTrades = battleTrades || await battle.fnGetTrades({ redisClient, openOnly: true });
                // check battle vs dex;
                let battlePairs = [...new Set(battleTrades.map(r => r.pair.toUpperCase()))];
                let dexPairs = [...new Set(dexTrades.map(r => r.symbol.toUpperCase()))];
                let sharedPairs = battlePairs.filter(p => dexPairs.includes(p));
                // fnx.log('reconsileBattle', JSON.stringify(sharedPairs));
                let battleOnlyPairs = battlePairs.filter(p => !dexPairs.includes(p));
                let dexOnlyPairs = dexPairs.filter(p => !battlePairs.includes(p));

                // fnx.log('battlestarted', battlestarted);
                //mutabakat adimlari:
                //1. ok ayrisan pair leri bul. ok

                //2. ok-partial... ortak pair lerdeki adetleri eşle. ayrıca tradeID ile position id leri check et.
                //2.1 -> Eşleşmeme durumunda order bazında mutabakat... // get orders!!!!

                //3. dex te aktif, battle pasif ise get orders... order bazında mutabakat. order ı yeniden aç. dexteki guncel avg price ile.

                //4. ok dex te pasif, battle da aktif ise, get orders... son filled order a göre battle dakini kapat !!1 burayı detaylandır.

                //DONE: check addiionalt order tradeNO.... clientOrderID...
                // fnx.log('battleOnlyPairs', battleOnlyPairs, 'dexOnlyPairs', dexOnlyPairs, 'sharedPairs', sharedPairs, dexTrades[0])
                await Promise.all(
                    [...dexTrades].filter(dt => sharedPairs.includes(dt.symbol)).map(async (p) => {
                        let bTrades = battleTrades.filter(bt =>
                            bt.tradeClosed == false &&
                            bt.pair == p.symbol.toLowerCase()
                        );
                        let cEntryAmount = bTrades.reduce((acc, x) => acc + (parseFloat(x.entryAmount)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);
                        let cEntryVolume = bTrades.reduce((acc, x) => acc + (parseFloat(x.entryBudget)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);
                        let check1 = (Math.abs(cEntryAmount) / Math.abs(parseFloat(p.positionAmt)) - 1) * 100;
                        let check2 = (Math.abs(cEntryVolume) / Math.abs(parseFloat(p.notionalBOP)) - 1) * 100;
                        if (check1 < 0.3 && check2 < 0.3) {
                            //no need to check order level
                        } else {
                            fnx.log('dex vs battle amounts not matched ', p.symbol, 'position', p, 'battle ', bTrades);
                            fnx.log(refCall, '!!!!check positionAmt', check1, cEntryAmount, Math.abs(parseFloat(p.positionAmt)));
                            fnx.log(refCall, '!!!!check notionalBOP', check2, cEntryVolume, Math.abs(parseFloat(p.notionalBOP)));
                            //TODO: close existing and create new with new sayac... check tpsl also...
                        }

                    })
                );

                if (dexOnlyPairs.length !== 0 || battleOnlyPairs.length !== 0) {
                    //get dex orders... // start
                    let newArrStg = [];
                    newArrStg = dexOnlyPairs.length !== 0 ? [...newArrStg, ...dexOnlyPairs] : [...newArrStg];
                    newArrStg = battleOnlyPairs.length !== 0 ? [...newArrStg, ...battleOnlyPairs] : [...battleOnlyPairs];

                    let newArr = [...new Set([...newArrStg])];
                    let refCallAdon = dexOnlyPairs.length !== 0 ? ' dexOnly:' + dexOnlyPairs.length : '';
                    refCallAdon += battleOnlyPairs.length !== 0 ? ' battleOnlyPairs:' + battleOnlyPairs.length : '';

                    // set referans repo..
                    let dexOrders = {};
                    await Promise.all(
                        newArr.map(async (o) => {
                            try {
                                // fnx.log('fetching orders for', o);
                                let sdexOrders = await dex.getOrders({
                                    redisClient, dexApi, refCall: 'reconsileBattleV2' + refCallAdon + ', ' + o, threshold: 10000,
                                    symbol: o || 'GAUSS_GENERIC',
                                });
                                dexOrders[o] = sdexOrders.slice(-10); 
                                //TODO : check 10? , check getorder threshold: 5000
                            }
                            catch (e) {
                                fnx.log('fnGetOrders e', e)
                            }

                        })
                    );
                    //get dex orders... // completed...

                    if (dexOnlyPairs.length !== 0) {
                        dexOnlyPairs.length !== 0 && fnx.log(refCall, 'dexOnlyPairs', dexOnlyPairs);
                        let dexThreshold = 6000;
                        await Promise.all(
                            dexOnlyPairs.map(async (o) => {
                                try {

                                    let lRun = await fnx.redisLog({
                                        redisClient, action: 'get',
                                        param: 'createDexTrade_' + o,
                                    });
                                    let keepOn = dexThreshold < (Date.now() - (lRun?.dtUpdated || 0));

                                    if (keepOn) {

                                        fnx.log('dexOnlyPairs - check orders...');
                                        let battleLastTradeArr = battleTrades.reverse().filter(bt => bt.symbol == o);
                                        let battleLastTrade = Array.isArray(battleLastTradeArr) && battleLastTradeArr[0];

                                        let dposition = [...dexTrades].find(dt => dt.symbol == o);
                                        let dos = Array.isArray(dexOrders[o]) && dexOrders[o].reverse().filter(so => so.status == 'FILLED').slice(-20);
                                        if (dposition || (dposition && battleLastTrade && dposition.updateTime > battleLastTrade.entryTime)) {
                                            //create order in battle...
                                            await battle.createBattleOrderFromDex({
                                                redisClient, dexApi, dexPositions: [...dexTrades].filter(dt => dt.symbol == o),
                                                section: section || 'dexSync: reconsileDexBattle',
                                                orderAddons: {
                                                    orderSrc_ref: 'dexSync - dexOnlyPairs',
                                                    orderSrc_successStrategyID: 'dexSync - dexOnlyPairs',
                                                    orderSrc_successStrategyName: 'dexSync - dexOnlyPairs',
                                                },
                                                debug: true,
                                            });

                                            await fnx.logX({
                                                redisClient, param: o, value: {
                                                    section: section || 'dexSync: reconsileDexBattle',
                                                    note: 'dexOnly - createBattleOrderFromDex - ' + (o),
                                                    symbol: o,
                                                    refValues: {
                                                        dposition, dos, o, battleLastTrade
                                                    }
                                                }
                                            });

                                        } else {
                                            fnx.log('check dex vs battle...',)
                                            fnx.log('check dex vs battle... battleLastTradeArr', battleLastTradeArr,);
                                            fnx.log('check dex vs battle... dposition', dposition,);
                                            fnx.log('check dex vs battle... dos', dos,);
                                        }
                                        //close olması gereken close olmadı mı ?

                                        //eger close olması gereken degilse, dex order create time ı, battle dan sonra olan filled olanlara bak. varsa position bilgisi ile create et ve tpsl sini ekle..

                                    }
                                    else {
                                        await fnx.logX({
                                            redisClient, param: o, value: {
                                                section: section || 'dexSync: reconsileDexBattle',
                                                note: 'waiting timer... dexOnly - createBattleOrderFromDex - ' + (o),
                                                symbol: o,
                                                refValues: {
                                                    keepOn
                                                }
                                            }
                                        });
                                    }

                                } catch (e) {
                                    fnx.log('e dexOnlyPairs', e, o)
                                }
                            }));
                    };

                    if (battleOnlyPairs.length !== 0) { 
                        await Promise.all(
                            battleOnlyPairs.map(async (o) => {
                                try {
                                    // fnx.log(refCall, 'battleOnlyPairs WIP', o, battleOnlyPairs);
                                    let battleTrade = battleTrades.find(bt => bt.symbol == o);
                                    fnx.log('battleV', o, battleTrade.entryTime);
                                    let soos = Array.isArray(dexOrders[o]) && dexOrders[o].reverse().filter(so => so.status == 'FILLED' && so.reduceOnly == true && so.time > battleTrade.entryTime);
                                    // fnx.log(refCall, 'battleOnlyPairs WIP', o, battleOnlyPairs, soos, !soos && '!f', !soos && dexOrders[o]); 
                                    //DONE: trade create date vs. soo date
                                    if (soos && Array.isArray(soos) && soos.length !== 0) {
                                        let lastTradeRef = soos[0];
                                        //DONE: closeType stopLoss yada tp olmalı! --> order data da bunu yonetiyoruz.

                                        await battle.closeTradeBattleOnly({
                                            redisClient, symbol: o,
                                            lastCandleRef: 'lastDexTradeN',
                                            closeNote: 'stopDexSync-90',
                                            closeType: 90,
                                            updateNote: 'clsDexcheck0', //battleonly
                                            ref: 'closed-dexcheck',
                                            lastCandle: {
                                                close: lastTradeRef.avgPrice,
                                                time: lastTradeRef.time,
                                            },
                                        });

                                        await fnx.logX({
                                            redisClient, param: o, value: {
                                                section: section || 'dexSync: reconsileDexBattle',
                                                note: 'battleOnly - closeTradeBattleOnly - ' + (o),
                                                symbol: o,
                                                refValues: {
                                                    soos, battleTrade, o, dexOrders: dexOrders[o], lastTradeRef,
                                                }
                                            }
                                        });

                                    } else {
                                        fnx.log('cannot close battle only pair...')
                                    }
                                }
                                catch (e) {
                                    fnx.log('battleOnlyPairs WIP err', o, e);
                                }
                            }));
                    };

                } else {
                    //do nothing!
                }
                if (fnClearDexUseLessReduceOrders) {
                    try {
                        await dex.clearDexUseLessReduceOrders({
                            redisClient, dexApi, refCall
                        });
                    } catch (e) {
                        fnx.log('clearDexUseLessReduceOrders e', e)
                    }
                };
                if (fnCheckDexPositionsWO_TPSL) {
                    try {
                        await dex.checkDexPositionsWO_TPSL({
                            redisClient, dexApi, refCall, threshold: 60000,
                            section: 'reconsile:checkDexPositionsWO_TPSL',
                        });
                    }
                    catch (e) {
                        // fnx.log('fnCheckDexPositionsWO_TPSL e', e)
                        await fnx.logX({
                            redisClient, param: e?.symbol || 'GAUSS_GENERIC', value: {
                                section: 'reconsile:checkDexPositionsWO_TPSL',
                                note: e?.rDesc || 'checkDexPositionsWO_TPSL',
                                symbol: e?.symbol || 'GAUSS_GENERIC',
                                e
                            }
                        });
                    }
                };
                resolve(true)
            }
        });
    },
    clearDexUseLessReduceOrders: async props => {
        var {redisClient, dexApi, dexTrades, dexOrders, refCall} = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            dexApi = dexApi || await dex.getDexApi({ redisClient });
            dexTrades = dexTrades || await dex.getDexPositionsCache({redisClient, dexApi, threshold: 30000, refCall: 'clearDexUseLessReduceOrders'});
            dexOrders = dexOrders || await dex.getDexOpenOrdersCache({redisClient, dexApi, threshold: 30000});
            
            let dexPairs = Array.isArray(dexTrades) && [...new Set(dexTrades.map(r => r.symbol.toUpperCase()))];
            let oOrdersX = dexOrders.filter(o => (o.reduceOnly == true || o.closePosition == true) && !dexPairs.includes(o.symbol));
            // fnx.log('oOrders', dexOrders, dexPairs)
            oOrdersX.length !== 0 && fnx.log('useless order orders2Delete', [...new Set(oOrdersX.map(r => r.symbol))], oOrdersX.length);
            if (oOrdersX && Array.isArray(oOrdersX) && oOrdersX.length !== 0) {
                await Promise.all(
                    oOrdersX.map(async (o) => {
                        // await dexApi.futuresCancel(o.symbol, { orderId: o.orderId.toString() });
                        await dexBinance.futuresCancel({
                            dexApi, redisClient, refCall: refCall || 'clearDexUseLessReduceOrders', 
                            note: 'orders2Delete - symbol:' +  o.symbol + ', orderID:' + o.orderId.toString()
                        }, o.symbol, { orderId: o.orderId.toString() })
                    })
                );
                await fnx.logX({redisClient, param: 'GAUSS_GENERIC', value: {
                    section: 'dex:clearDexUseLessReduceOrders',
                    note: 'cleared dex useless reduce orders..',
                    symbol: 'GAUSS_GENERIC',
                    refValues: {
                        oOrdersX,
                    }
                }});
                //TODO: Aynı poz için açılmış birden fazla TP/SL var mı ?
            }
            resolve(true)
        });
    },
    syncPosData: async props => {
        var { redisClient, dexApi, threshold = 5000, symbol, callRef, clearUnrlvReduceOrders = false } = props;

        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });

        let dtBOP = Date.now();
        let redisKey = redixPrefix.monitor + 'dex:syncPosData';

        const uPos = async propsz => {
            const { refBOP = dtBOP, lastU } = propsz || {};
            return new Promise(async (resolve, reject) => {
                let posData;
                let taskStarted = Date.now();
                let dtU = lastU ? lastU.dtupdatedEn : undefined;
                let lg = {
                    taskStarted,
                    task: 'WIP',
                };
                if (dtU) lg.dtupdatedEn = dtU;

                try {
                    await redisClient.set(redisKey, JSON.stringify(lg));
                    posData = await dex.getDexPositions({ redisClient, dexApi });
                    let taskCompleted = Date.now();
                    await redisClient.set(redisKey, JSON.stringify({
                        dtupdatedEn: new Date(Date.now()).toISOString(),
                        taskStarted,
                        taskCompleted,
                        timeElapsed: taskCompleted - taskStarted,
                        task: 'DONE',
                    }));
                    dexWIP = false;
                }
                catch (e) {
                    let taskCompleted = Date.now();
                    await redisClient.set(redisKey, JSON.stringify({
                        dtupdatedEn: new Date(Date.now()).toISOString(),
                        taskStarted,
                        taskCompleted,
                        timeElapsed: taskCompleted - taskStarted,
                        task: 'FAILED',
                        e: e.toString(),
                    }));
                    dexWIP = false;
                }
                resolve(posData);
            });
        }

        return new Promise(async (resolve, reject) => {
            dexApi = dexApi || await dex.getDexApi({ redisClient });
            try {
                let posData;
                let lastUStg = await redisClient.get(redisKey);
                let lastU = lastUStg && JSON.parse(lastUStg);
                if (!dexWIP) {
                    // callRef && fnx.log('syncPosData', symbol, callRef);
                    dexWIP = true;
                    if (lastU) {
                        // let dtDelta = ;
                        if (lastU?.dtupdatedEn && ((Date.now() - new Date(lastU?.dtupdatedEn)) < threshold)) {
                            // fnx.log('syncPosData noFetch... ', (Date.now() - new Date(lastU.dtupdatedEn)), threshold);
                            posData = await dex.getDexPositionsCache({ redisClient, dexApi, 
                                ageCheckRequired: false, refCall: 'syncPosData' });
                            dexWIP = false;
                            resolve(posData);
                        } else {
                            // fnx.log('syncPosData fetch2... ', lastU, (Date.now() - new Date(lastU.dtupdatedEn)), threshold);
                            if (lastU.task !== 'WIP') {
                                // fnx.log('syncPosData start fetch', Date.now() - lastU?.taskStarted);
                                await fnx.logX({
                                    redisClient, param: 'GAUSS_GENERIC', value: {
                                        section: 'dex:syncPosData',
                                        note: 'syncPosData - fetch: ms.' + (Date.now() - lastU?.taskStarted),
                                        symbol: 'GAUSS_GENERIC',
                                    }
                                });
                                posData = await uPos({ lastU })?.positions;
                            } else {
                                // fnx.log('syncPosData fetch2... allready workInProgress', Date.now() - lastU?.taskStarted);
                                posData = await dex.getDexPositionsCache({ redisClient, dexApi, 
                                    ageCheckRequired: false, refCall: 'syncPosData2' });
                            }
                            dexWIP = false;
                            resolve(posData);
                        }
                    } else {
                        //
                        await fnx.logX({
                            redisClient, param: 'GAUSS_GENERIC', value: {
                                section: 'dex:syncPosData',
                                note: 'syncPosData - init fetch',
                                symbol: 'GAUSS_GENERIC',
                            }
                        });
                        posData = await uPos()?.positions || await dex.getDexPositionsCache({ redisClient, dexApi, ageCheckRequired: false, refCall: 'syncPosData3' });
                        dexWIP = false;
                        resolve(posData);
                    }
                } else {
                    // fnx.log('syncPosData is working....')
                }
            }
            catch (e) {
                fnx.log('e sync posdata', e);
            }
        });
    },
    syncDexWBattle: async props => {
        var { dexApi, dexData, redisClient, debug = true, section } = props;
        return new Promise(async (resolve, reject) => {
            let dtBOP = Date.now();
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let battle_parameters = await dex.getBattleParams({ redisClient });
                let checkPosTPLs = dexSync_init_checkPosTPLs;
                let dtBop3 = Date.now();
                // debug && await fnx.redisLog({redisClient, param: 'syncDexWBattle_0asetDexLeverages', value: {
                //     success: true,
                //     elapsed: Date.now() - dtBop3,
                // }});
                let dtBop2 = Date.now();
                dexData = dexData || await dex.getDexPositions({ redisClient, dexApi });
                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section: section || 'start_battle',
                        note: 'syncDexWBattle / DexPositions got!',
                        symbol: 'GAUSS_GENERIC',
                        resp: {
                            sure: Date.now() - dtBop2,
                            dexData
                        },
                    }
                });

                let positions = dexData?.positions;
                // debug && fnx.log('syncDexWBattle getDexPositions', positions.length, 'sure', Date.now() - dtBOP, Date.now() - dtBop2, 'leverageSure', dtBop2 - dtBop3);

                if (Array.isArray(positions) && positions.length !== 0) {
                    if (checkPosTPLs) {
                        let dtCTFromDexX1 = Date.now();

                        await dex.cancelDexOpenOrders({ dexApi, redisClient, all: true, 
                            onlyCloseOrderTypes: true, section });
                        await fnx.logX({
                            redisClient, param: 'GAUSS_GENERIC', value: {
                                section: section || 'start_battle',
                                note: 'syncDexWBattle / cancelDexOpenOrders done!',
                                symbol: 'GAUSS_GENERIC',
                                resp: {
                                    sure: Date.now() - dtCTFromDexX1,
                                },
                            }
                        });
                        let dtBop2a = Date.now();
                        let sP = 0;
                        let dtCTFromDexX = Date.now();
                        await dex.checkDexTPSL2_all({redisClient, dexApi, 
                            positions: positions, section,
                            debug: true, closePositionIfOutOfBoundaries: true, 
                            refCall: 'syncDexBattle_cpo', cancelTPSLOrders: false,
                            closePositionOnly: true, threshold: 100,
                        })
                        await fnx.logX({
                            redisClient, param: 'GAUSS_GENERIC', value: {
                                section: 'start_battle',
                                note: 'syncDexWBattle / closed out of bounds positions!',
                                symbol: 'GAUSS_GENERIC',
                                resp: {
                                    sure: Date.now() - dtCTFromDexX,
                                },
                            }
                        });

                        dexData = await dex.getDexPositions({ redisClient, dexApi, });
                        positions = dexData?.positions;
                    }

                    let dtCTFromDex = Date.now()
                    await battle.createBattleOrderFromDex({
                        redisClient, dexApi, dexPositions: positions,
                        section: section || 'start_battle',
                        orderAddons: {
                            orderSrc_ref: 'dexSync',
                            orderSrc_successStrategyID: 'dexSync',
                            orderSrc_successStrategyName: 'dexSync',
                        },
                        debug: true,
                    });

                    debug && await fnx.redisLog({redisClient, param: 'syncDexWBattle_4final', value: {
                        success: true,
                        elapsed: Date.now() - dtBOP,
                    }});
                }
                fnx.log('syncDex complated', Date.now() - dtBOP)
                resolve(battle_parameters)
            } catch (e) {
                debug && fnx.log('error in sync dex', e);
                reject(e)
            }
        });

    },
};

const dexBinance = exports.dexBinance = {
    futuresUserTrades: (props, ...args) => {
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        var { redisClient, dexApi, refCall, logMe = true, note} = props;
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresUserTrades(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresUserTrades',
                    value: {
                        section: 'futuresUserTrades', note: note || '', refCall,
                        worked: true, 
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresUserTrades', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresUserTrades',
                    value: {
                        section: 'futuresUserTrades', note: note || '', refCall,
                        worked: false, e: e, 
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    }, 
    futuresOrder: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresOrder(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresOrder',
                    value: {
                        section: 'futuresOrder', note: note || '', refCall,
                        worked: true, 
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresOrder', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresOrder',
                    value: {
                        section: 'futuresOrder', note: note || '', refCall,
                        worked: false, e: e, 
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresLeverage: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresLeverage(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresLeverage',
                    value: {
                        section: 'futuresLeverage', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresLeverage', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresLeverage',
                    value: {
                        section: 'futuresLeverage', note: note || '', refCall,
                        worked: false, e: e,
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresAllOrders: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresAllOrders(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresAllOrders',
                    value: {
                        section: 'futuresAllOrders', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresAllOrders', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresAllOrders',
                    value: {
                        section: 'futuresAllOrders', note: note || '', refCall,
                        worked: false, e: e,
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresOpenOrders: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresOpenOrders(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresOpenOrders',
                    value: {
                        section: 'futuresOpenOrders', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresOpenOrders', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresOpenOrders',
                    value: {
                        section: 'futuresOpenOrders', note: note || '', refCall,
                        worked: false, e: e,
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresCancel: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresCancel(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresCancel',
                    value: {
                        section: 'futuresCancel', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresCancel', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresCancel',
                    value: {
                        section: 'futuresCancel', note: note || '', refCall,
                        worked: false, e: e,
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresCancelAll: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresCancelAll(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresCancelAll',
                    value: {
                        section: 'futuresCancelAll', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresCancelAll', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresCancelAll',
                    value: {
                        section: 'futuresCancelAll', note: note || '', refCall,
                        worked: false, e: e,
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresSell: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresSell(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresSell',
                    value: {
                        section: 'futuresSell', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresSell', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresSell',
                    value: {
                        section: 'futuresSell', note: note || '', refCall,
                        worked: false, e
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresMarketSell: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresMarketSell(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresMarketSell',
                    value: {
                        section: 'futuresMarketSell', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresMarketSell', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresMarketSell',
                    value: {
                        section: 'futuresMarketSell', note: note || '', refCall,
                        worked: false, e
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresBuy: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresBuy(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresBuy',
                    value: {
                        section: 'futuresBuy', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresBuy', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresBuy',
                    value: {
                        section: 'futuresBuy', note: note || '', refCall,
                        worked: false, e
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
    futuresMarketBuy: (props, ...args) => {
        var { redisClient, dexApi, refCall, logMe = true, note } = props;
        redisClient = redisClient || new Redis({
            host: '127.0.0.1',
            port: 6379,
        });
        return new Promise(async (resolve, reject) => {
            try {
                dexApi = dexApi || await dex.getDexApi({ redisClient });
                let resp = await dexApi.futuresMarketBuy(...args);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresMarketBuy',
                    value: {
                        section: 'futuresMarketBuy', note: note || '', refCall,
                        worked: true,
                    }, lastN: 1000,
                });
                resolve(resp)
            } catch (e) {
                fnx.log('error dexBinance.futuresMarketBuy', e);
                logMe && await fnx.logX({
                    redisClient, folder: 'fnBinance:',
                    param: 'futuresMarketBuy',
                    value: {
                        section: 'futuresMarketBuy', note: note || '', refCall,
                        worked: false, e
                    }, lastN: 1000,
                });
                reject(e)
            }
        });
    },
}