require('dotenv').config();
// const { exec } = require('child_process');
const path = require('path');
const fss = require('fs');
const shell = require('shelljs');
const fs = require('fs').promises;
const express = require('express')
const fnx = require('./_.functions');
const bodyParser = require('body-parser')
const Database = require('better-sqlite3');
const CronJobManager = require('cron-job-manager');
const cronParser = require('cron-parser');
const moment = require('moment');
const Binance = require('node-binance-api');
const cors = require('cors');
const Redis = require('ioredis');
const spawn = require('child_process').spawn;
const childProcess = require('child_process');
const redixPrefix = require('../src/lib/redis.prefix.js');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const process = require('process');
process.removeAllListeners('warning');

const tickerTimeMonitor = {}
const tickerProcessTimeMonitor = {}
const tickerProcessTimeEMonitor = {}
const tickerProcessTimer = 5000;
var kline_initialDataLoaded = false;
var kline_fetchDataSuccess = true;
var nodeLog = [];

const redisKeys = {
    battle_init: 'battle_init',
}
const timeouts = {
    kline: 5000,
    klineInit: 10000,
    marketTicker: 10000,
}

const db = new Database(path.resolve(__dirname, '../db/gauss.db'));
const dbMarket = new Database(path.resolve(__dirname, '../db/market.db')); //, { verbose: fnx.log }
const dbTrades = new Database(path.resolve(__dirname, '../db/trades.db'));
dbTrades.pragma('journal_mode = WAL'); //, { verbose: fnx.log }
const args = process.argv.slice(2);
var porte = null;
const app = express();
app.set('view engine', 'ejs');
var battleData = {};
var redisKey;
const debugLog = true;
(async () => {
    const redisClient = new Redis({
        host: '127.0.0.1',
        port: 6379,
    });
    const cronMan = new CronJobManager();
    // redisClient.connect()
    
    const killConns = async (signal) => {
        signal !== 0 && console.log(`'killConns - ${signal} signal.'`);
        redisClient && redisClient.quit();
        // process.exit(1)
    };
      //https://stackoverflow.com/questions/14031763/doing-a-cleanup-action-just-before-node-js-exits
    [`exit`, `SIGINT`, `SIGUSR1`, `SIGUSR2`, `uncaughtException`, `SIGTERM`].forEach((eventType) => {
        process.on(eventType, killConns.bind(eventType));
    });

    app.use(bodyParser.json());
    app.use(cors());
    let node_key = '0';

    app.get('/stop', (req, res) => {
        debugLog && fnx.log(`Node ${porte} stopped!`);
        //kill self!
        redisClient && redisClient.quit();
        process.exit(1)
    })
    app.get('/list', (req, res) => {
        var jobs = fnx.battleNode.listCronJobs(cronMan.listCrons());
        res.send({ jobs })
    })
    app.get('/trades/:pair?', async (req, res) => fnx.battleNode.trades_getlist(req, res, {
        redisClient
    }));
    app.get('/info', async (req, res) => {
        fnx.log('info')
        let tickerProcessTimeMonitorHR = {}
        if (Object.keys(tickerProcessTimeMonitor).length > 0) {
            for (var k in tickerProcessTimeMonitor) {
                tickerProcessTimeMonitorHR[k] = new Date(tickerProcessTimeMonitor[k]).toISOString()
            }
        };

        let tickerTimeMonitorHR = {}
        if (Object.keys(tickerTimeMonitor).length > 0) {
            for (var k in tickerTimeMonitor) {
                tickerTimeMonitorHR[k] = new Date(tickerTimeMonitor[k]).toISOString()
            }
        };

        let battleDataStg = await fnx.battleNode.getBattleData({
            cronParser,
            redisClient,
            redisKey,
        });

        battleData = { ...battleData, ...battleDataStg };
        // redisClient && redisClient.quit();

        res.send({
            tickerTimeMonitor,
            tickerTimeMonitorHR, tickerProcessTimeMonitor,
            tickerProcessTimeMonitorHR, battleData,
        })
    });
    app.get('/stopconns', (req, res) => {
        const { node_key } = battleData;
        debugLog && fnx.log('stopconns', node_key)
        const fnk = async () => {
            await fnx.battleNode.ws_stopSubscriptions({ binanceApi });
        };
        fnk();
        res.json({ node_key })
    });
    app.get('/socketreconn', (req, res) => {
        const { redisKey } = battleData;
        fnx.log('socketreconn', redisKey)
        const fnk = async () => {
            await fnx.battleNode.ws_stopSubscriptions({ binanceApi });
            battleData = await fnx.battleNode.getBattleData({
                cronParser, redisClient, redisKey,
            });
            const actBase = {
                redisClient, redisKey, cronMan, binanceApi, battleData,
            };
            await fnx.battleNode.ws_setSubscription({
                ...actBase, ...battleData,
                tickerTimeMonitor, tickerProcessTimeEMonitor,
                tickerProcessTimeMonitor, tickerProcessTimer,
                modeDebug: debugLog,
            })
        };
        fnk();
        res.json({ redisKey })
    });
    const server = app.listen(0, async () => {
        const PORT = server.address().port;
        porte = PORT;
        try {
            let ax = Array.isArray(args) && args.length !== 0 && args[0];
            if (ax) {
                redisKey = ax.split("=")[1];
            } else {
                debugLog && fnx.log(`stopping server -> no key`);
                process.exit(5)
            };
            await fnx.battleNode.updateNodeProcessIDandPort({
                redisClient, redisKey, pid: process.pid, port: PORT,
            });
            battleData = await fnx.battleNode.getBattleData({
                cronParser, redisClient, redisKey, pid: process.pid, port: PORT,
            });
            if (battleData) {
                await fnx.battleNode.addLog({
                    redisClient, redisKey, logField: 'logs',
                    logValue: {
                        dtcreatedEn: new Date(Date.now()),
                        log: 'node ready', logMore: '',
                    }
                });

                const actBase = {
                    redisClient, redisKey, db, dbMarket, dbTrades,
                    cronMan, binanceApi, battleData,
                };

                await setCronJob({
                    ...actBase, ...battleData,
                    tickerTimeMonitor, tickerProcessTimeEMonitor,
                    tickerProcessTimeMonitor, tickerProcessTimer,
                    modeDebug: debugLog,
                });

                try {
                    await fetchInitialData({ ...actBase, ...battleData, modeDebug: false });
                } catch (eInit) {
                    // debugLog && fnx.log('kline initial data error', battleData.symi, eInit)
                    await fnx.battleNode.addLog({
                        redisClient, redisKey, logField: 'errors',
                        logType: 'error',
                        logValue: {
                            dtcreatedEn: new Date(Date.now()),
                            errorDesc: 'kline initial data error',
                            error: eInit,
                        }
                    });
                }

                await fnx.battleNode.ws_setSubscription({
                    ...actBase, ...battleData,
                    tickerTimeMonitor, tickerProcessTimeEMonitor,
                    tickerProcessTimeMonitor, tickerProcessTimer,
                    modeDebug: debugLog,
                })

            } else {
                await fnx.battleNode.addLog({
                    redisClient, redisKey, logField: 'errors',
                    logType: 'error',
                    logValue: {
                        dtcreatedEn: new Date(Date.now()),
                        errorDesc: 'init battle data error',
                    }
                });
            }

        } catch (a) {
            debugLog && fnx.log('server error', redisKey, a)
            process.exit(1)
        }
    })
})();

const fetchInitialData = async ({
    binanceApi, redisClient, redisKey, symi, period, batch, nodeInterval, nodeTasks, candleCounts, intervals, battleInterval,
    indicatorsWParams, rulesets, trading, nextRun, modeDebug = false,
}) => {
    return new Promise(async (resolve, reject) => {
        // fnx.log('fetchInitialData redisKey', redisKey);
        // fnx.log('fetchInitialData symi', symi);

        let currSec = new Date().getSeconds();
        let fetchTimeLimitSec = 50;
        let isThisKline = redisKey !== redixPrefix.node + 'market_ticker'
        let letsStart = isThisKline ? parseFloat(currSec) < fetchTimeLimitSec : (parseFloat(currSec) > 22 || parseFloat(currSec) < 10);

        // modeDebug && fnx.log(symi, redisKey, 'fetch initial Data1', 
        //     letsStart, 
        //     currSec, 
        //     fetchTimeLimitSec);
        // modeDebug && fnx.log(symi, redisKey, 'fetch initial Data2', 
        //         currSec, 
        //         fetchTimeLimitSec);
        if (letsStart) {
            let sleepLimit = (59 - currSec) * 1000
            let rawData;
            let taskParamz;
            let nodeTaskz = nodeTasks?.tasks
            if (Array.isArray(nodeTaskz)) {
                try {
                    for (c of nodeTaskz) {
                        const dtBOP = Date.now();
                        let {
                            battleID, nodeKey, batch, nodeInterval,
                            taskDesc, taskID, taskTag, battleInterval,
                            script2Run, script2RunUri, taskType,
                            period, periodDesc,
                            taskParams
                        } = c;

                        const symbol = taskTag.split('_')[0];
                        const taskTagLw = taskTag.toLowerCase();
                        taskParamz = typeof taskParams !== 'object' ? JSON.parse(taskParams) : taskParams;
                        if (taskDesc === 'klineLoader') {
                            let batchNum = batch ? parseInt(batch) : 0;
                            let lotAdd = taskParamz.batchAdd ? taskParamz.batchAdd : 0;
                            let sleepTimeStg = (batchNum + lotAdd) * 1000;
                            let sleepTime = sleepLimit > sleepTimeStg ? sleepTimeStg : sleepLimit
                            await fnx.sleep(sleepTime);
                            rawData = await fnx.battleNode.fetchKline({
                                binanceApi,
                                connTimeOut: timeouts.klineInit,
                                symbol,
                                interval: nodeInterval,
                                limit: candleCounts,
                                savetoFile: false
                            }); //taskID === 'test'
                            kline_initialDataLoaded = true;
                            let redisDataKey = redixPrefix.dataKline + taskTagLw;
                            await redisClient.set(redisDataKey, JSON.stringify(rawData));
                            modeDebug && fnx.log('fetchInitialData done!', taskTag, 'batch:' + batchNum,);
                            try {
                                //save to stats;
                                //https://github.com/binance/binance-spot-api-docs/blob/master/rest-api.md#klinecandlestick-data
                                // let sRedisKey = redisKeys.fetchMonitor;
                                let lastKlineValue = [...rawData].slice(-1)[0][0];
                                let sStatsValue = {
                                    dtupdatedEn: new Date(Date.now()).toISOString(),
                                    barCount: [...rawData].length,
                                    // typ: 'kline',
                                    dataOpen: lastKlineValue,
                                    dataOpenEn: new Date(lastKlineValue).toISOString(),
                                };

                                // modeDebug && fnx.log('stat save', sRedisKey, symbol, taskTagLw, JSON.stringify(sStatsValue));
                                await redisClient.set(redixPrefix.monitorKline + taskTagLw, JSON.stringify(sStatsValue));
                                await redisClient.set(redixPrefix.monitorKlineInit + taskTagLw, JSON.stringify(sStatsValue));
                            }
                            catch (eSt) {
                                modeDebug && fnx.log('stat save error', symbol);
                            }
                            modeDebug && fnx.log('fetch initial Data Stat Saved', symi)


                        } else if (taskDesc === 'marketTicker') {
                            if (taskTag == 'mt_futuresDaily') {
                                modeDebug && fnx.log('marketTicker', taskTag);
                                try {
                                    rawData = await fnx.nBinance.futuresDaily({
                                        binanceApi,
                                        connTimeOut: timeouts.marketTicker,
                                    });
                                    kline_initialDataLoaded = true;
                                    let redisDataKey = redixPrefix.dataMarket + taskTag.split('_')[1].toLowerCase();
                                    await redisClient.set(redisDataKey, JSON.stringify(rawData));
                                    modeDebug && fnx.log('fetchInitialData done!', taskTag,);
                                    try {
                                        let sKey = redixPrefix.monitorMarket + taskTag.split('_')[1].toLowerCase();
                                        let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                                        await redisClient.set(sKey, JSON.stringify(sStatsValue));
                                    }
                                    catch (eSt) {
                                        modeDebug && fnx.log('stat save error', taskTag.split('_')[1].toLowerCase());
                                    }
                                }
                                catch (e) {
                                    reject(e)
                                }


                            } else if (taskTag == 'mt_fundingRate') {
                                modeDebug && fnx.log('mt_fundingRate', taskTag);
                                try {
                                    rawData = await fnx.nBinance.futuresFundingRate({
                                        binanceApi,
                                        connTimeOut: timeouts.marketTicker,
                                    });
                                    kline_initialDataLoaded = true;
                                    let redisDataKey = redixPrefix.dataMarket + taskTag.split('_')[1].toLowerCase();
                                    await redisClient.set(redisDataKey, JSON.stringify(rawData));
                                    modeDebug && fnx.log('fetchInitialData done!', taskTag,);
                                    try {
                                        let sKey = redixPrefix.monitorMarket + taskTag.split('_')[1].toLowerCase();
                                        let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                                        await redisClient.set(sKey, JSON.stringify(sStatsValue));
                                    }
                                    catch (eSt) {
                                        modeDebug && fnx.log('stat save error', taskTag.split('_')[1].toLowerCase());
                                    }
                                }
                                catch (e) {
                                    reject(e)
                                }

                            } else if (taskTag == 'mt_markPrice') {
                                modeDebug && fnx.log('mt_markPrice', taskTag);
                                try {
                                    rawData = await fnx.nBinance.futuresMarkPrice({
                                        binanceApi,
                                        connTimeOut: timeouts.marketTicker,
                                    });
                                    kline_initialDataLoaded = true;
                                    let redisDataKey = redixPrefix.dataMarket + taskTag.split('_')[1].toLowerCase();
                                    await redisClient.set(redisDataKey, JSON.stringify(rawData));
                                    modeDebug && fnx.log('fetchInitialData done!', taskTag,);
                                    try {
                                        let sKey = redixPrefix.monitorMarket + taskTag.split('_')[1].toLowerCase();
                                        let sStatsValue = { dtupdatedEn: new Date(Date.now()).toISOString(), };
                                        modeDebug && fnx.log('stats done!', sKey, sStatsValue);
                                        await redisClient.set(sKey, JSON.stringify(sStatsValue));
                                    }
                                    catch (eSt) {
                                        modeDebug && fnx.log('stat save error', taskTag.split('_')[1].toLowerCase());
                                    }
                                }
                                catch (e) {
                                    reject(e)
                                }

                            } else {
                                modeDebug && fnx.log('!!!?? taskdesc', taskDesc);
                            }

                        } else {
                            modeDebug && fnx.log('unknwond taskdesc', taskDesc);
                        }
                        let dtElapsed = Date.now() - dtBOP
                    }
                    resolve(rawData)
                }
                catch (e) {
                    modeDebug && fnx.log('fetchInitialData err2', e);
                    reject('fetchInitialData - task failed')
                }
            } else {
                modeDebug && fnx.log(symi, 'fetchInitialData error - no task ', nodeTaskz);
                reject('fetchInitialData error - no task')
            }
        } else {
            // modeDebug && fnx.log(symi, isThisKline, 'too late', currSec, fetchTimeLimitSec, letsStart );
            reject('too late for initial data load.')
        }
        // if (batch && currSec > 30) {
        //     let batchS = currSec + batch;
        //     batchS = batchS > 59 ? 59 : batchS;
        //     let t2Wait = batchS - currSec * 1000;
        //     setTimeout(async () => {
        //         fnx.log('load initial data for', symi, nextRun, batch, period, );
        //         kline_initialDataLoaded = true;
        //     }, t2Wait);
        // } else {
        //     resolve(true);
        // }
        /*
        [Sat, 15 Jun 2024 20:31:42 GMT] aevousdt_5m fetch initial data { dt: 2024-06-15T20:35:06.000Z, sec: 203.49 }
        [Sat, 15 Jun 2024 20:31:42 GMT] aevousdt_1m fetch initial data { dt: 2024-06-15T20:32:01.000Z, sec: 18.49 }
        [Sat, 15 Jun 2024 20:31:42 GMT] aevousdt_15m fetch initial data { dt: 2024-06-15T20:45:09.000Z, sec: 806.489 }
        [Sat, 15 Jun 2024 20:31:42 GMT] market_ticker fetch initial data { dt: 2024-06-15T20:32:20.000Z, sec: 37.489 }
        */
    });
};



const setCronJob = async (props) => {
    return new Promise(async (resolve, reject) => {
        const {
            redisClient, redisKey, symi, db, cronMan, nodeTasks, dbMarket, binanceApi, dbTrades,
            tickerTimeMonitor, tickerProcessTimeMonitor, tickerProcessTimeEMonitor,
            tickerProcessTimer, modeDebug = false
        } = props;
        // fnx.log(symi, 'setCronJob');
        const jnodeTasks = typeof nodeTasks !== 'object' ? JSON.parse(nodeTasks) : nodeTasks;
        try {
            if (Array.isArray(jnodeTasks?.tasks)) {
                for (c of jnodeTasks?.tasks) {
                    let {
                        battleID, nodeKey,
                        taskDesc, taskID, taskTag,
                        script2Run, script2RunUri,
                        period, periodDesc, nodeInterval,
                        battleInterval, batch, taskType,
                        taskParams, wsconn, wsconnuri,
                    } = c;
                    let nodeTask = taskParams && JSON.parse(taskParams);
                    // modeDebug && fnx.log('add cron', taskID, script2Run);
                    cronMan.add(taskID, period, () => {
                        // let wsconnList = binanceApi.futuresSubscriptions();
                        // fnx.log('debug connList', wsconnList);
                        // modeDebug && fnx.log('cronMan run task', taskID, taskTag, script2Run);
                        runCronJob({
                            act: 'cronTask', 
                            ...props, battleID, nodeKey, taskDesc,
                            taskID, taskTag, script2Run, script2RunUri,
                            wsconn, wsconnuri,
                            period, periodDesc, nodeInterval, battleInterval,
                            batch, taskType, taskParams
                        });
                    });
                    // modeDebug && fnx.log('task set', taskID, taskDesc, taskTag, periodDesc)
                    cronMan.start(taskID)
                    if (nodeInterval !== '1m' && batch) {
                        let moniPeriodS = parseInt(period.split(' ')[0]) + 1;
                        let moniPeriod = `${moniPeriodS} * * * * *`
                        // fnx.log('cronn add moni', symi, nodeInterval, period, batch, moniPeriod);

                        cronMan.add(taskID + '-mon', moniPeriod, () => {
                            // modeDebug && fnx.log('run runCronMonitorJob', taskID, taskTag);
                            runCronMonitorJob({
                                act: 'cronMonitorTask',
                                ...props, battleID, nodeKey, taskDesc,
                                taskID, taskTag, script2Run, script2RunUri,
                                wsconn, wsconnuri,
                                period, periodDesc, nodeInterval, battleInterval,
                                batch, taskType, taskParams
                            });
                        });
                        // modeDebug && fnx.log('task monitor set', taskID + '-mon', moniPeriod)
                        cronMan.start(taskID + '-mon')

                    }
                }
            };
            resolve(jnodeTasks?.tasks);
        } catch (err) {
            fnx.log('setCronJobs err.', err)

            await fnx.battleNode.addLog({
                redisClient, redisKey, logField: 'errors',
                logType: 'error',
                logValue: {
                    dtcreatedEn: new Date(Date.now()),
                    errorDesc: 'error in set setCronJobs',
                    error: err,
                }
            });


            reject(err);
        }

        // resolve(true);
    });
};


const runCronMonitorJob = async vars => {
    const { act, redisClient, redisKey,
        db, dbMarket, dbTrades,
        cronMan, binanceApi,
        nodeTasks, symi, tickerTimeMonitor,
        tickerProcessTimeMonitor, tickerProcessTimeEMonitor, tickerProcessTimer,
        battleID, nodeKey, taskID, taskTag,
        script2Run, wsconn, wsconnuri,
        period, periodDesc, nodeInterval, battleInterval,
        batch, taskType, taskParams
        // dtStarted,  taskDesc,   script2RunUri, 
    } = vars;
    try {
        const filePath = path.resolve(__dirname, act);
        let refbattleInterval = taskTag.split('_')[1];
        let carpanUnit = refbattleInterval.slice(-1);
        let carpanBase = 60000; //1m
        let carpan = carpanUnit == 'm' ? carpanBase : carpanUnit == 'h' ? 60 * carpanBase : 24 * 60 * carpanBase;
        let refBase = refbattleInterval.slice(0, -1);
        let limitSec = carpan * parseFloat(refBase);

        let refSecs = {
            kline: limitSec,
            klineinit: 20000,
            indicators: 10000,
            klineticker: 10000,
        };
        let typs2Track = ["kline", "klineticker"];

        let resp = [];
        let monData;
        let monAct;
        let keystats = await redisClient.keys(redixPrefix.monitor + '*');
        if (Array.isArray(keystats)) {
            for (k of keystats) {
                let statStg = await redisClient.get(k);
                let stat = statStg && typeof statStg !== 'object' ? JSON.parse(statStg) : statStg;
                let tag = k.split(':')[2];
                let statyp = k.split(':')[1];
                let key = tag.split('_')[0];
                let interval = tag.split('_')[1];
                resp.push({
                    pair: key,
                    interval,
                    tag,
                    statType: statyp,
                    stat,
                });
            }
        }
        if (Array.isArray(resp) && resp.length !== 0 && taskTag) {
            monData = resp.filter(m => m.tag == taskTag.toLowerCase())
        }
        if (monData) {
            monAct = monData.map(m => {
                let x = {
                    typ: m.statType,
                    dtEn: m.stat.dtupdatedEn,
                    dt: new Date(m.stat.dtupdatedEn),
                    dtDiff: new Date(Date.now()) - new Date(m.stat.dtupdatedEn)
                };
                return x
            });
        }
        // fnx.log('monAct', taskTag, monAct, carpanUnit, refBase, carpan );
        for (t of typs2Track) {
            let actValueStg = Array.isArray(monAct) && monAct.find(a => a.typ == t );
            if (!actValueStg) {
               // fnx.log('actValueStg', monAct, t)
            }
            let actValue = actValueStg?.dtDiff;
            let refValue = refSecs[t];
            if (actValue > refValue) {
                // fnx.log('xx', taskTag, t, actValue, refValue)
                fnx.log(taskTag, '!!!DO RECONN ACTIVITIES', t, actValue, refValue)
                if (t == 'klineticker') {
                    try {
                        await fnx.battleNode.ws_stopSubscriptions({ binanceApi });
                        battleData = await fnx.battleNode.getBattleData({
                            cronParser, redisClient, redisKey,
                        });
                        const actBase = {
                            redisClient, redisKey, cronMan, binanceApi, battleData,
                        };
                        await fnx.battleNode.ws_setSubscription({
                            ...actBase, ...battleData,
                            tickerTimeMonitor, tickerProcessTimeEMonitor,
                            tickerProcessTimeMonitor, tickerProcessTimer,
                            modeDebug: false,
                        });
                        // fnx.log(taskTag, 'klineticker reconnected')
                    } catch (eT) {
                        fnx.log(taskTag, 'klineticker error', eT)
                    }
                } else if (t == 'kline') {
                    setTimeout(function () {
                        process.on("exit", function () {
                            require("child_process").spawn(process.argv.shift(), process.argv, {
                                cwd: process.cwd(),
                                detached : true,
                                stdio: "inherit"
                            });
                        });
                        // fnx.log(taskTag, 'kline exit');
                        process.exit();
                    }, 5000);
                    // fnx.log(taskTag, 'kline reconnected');
                } else {
                    //noAct.
                };
            } else {
                //noAct.
            }
        }
    } catch (e) {
        fnx.log(e)
    }
}

const runCronJob = async vars => {
    const { act, redisClient, redisKey,
        db, dbMarket, dbTrades,
        cronMan, binanceApi,
        nodeTasks, symi, tickerTimeMonitor,
        tickerProcessTimeMonitor, tickerProcessTimeEMonitor, tickerProcessTimer,
        battleID, nodeKey, taskID, taskTag,
        script2Run, wsconn, wsconnuri,
        period, periodDesc, nodeInterval, battleInterval,
        batch, taskType, taskParams
        // dtStarted,  taskDesc,   script2RunUri, 
    } = vars;
    try {
        const filePath = path.resolve(__dirname, script2Run);
        fss.access(filePath, fs.F_OK, async (err) => {
            if (err) {
                fnx.log(symi, 'error in run cronTask', err)
                cronMan.stop(taskID);
                console.error(err)

                await fnx.battleNode.addLog({
                    redisClient, redisKey, logField: 'errors',
                    logType: 'error',
                    logValue: {
                        dtcreatedEn: new Date(Date.now()),
                        errorDesc: 'error in run cronTask script2run',
                        error: err,
                    }
                });

                return
            }

            let cmd = './nodes/' +  script2Run;
            // fnx.log('run cmd: ', cmd);
            // runScript(cmd, taskID, function (err) {
            //     if (err) throw err;
            //     console.log('finished running  ' + script2Run);
            //     return
            // });
            // shell.exec(cmd, {}, function(code, stdout, stderr) {
            //     if (code != 0) return (new Error(stderr));
            //     // fnx.log('stdout', taskID, stdout)
            //     return(stdout) ;
            //   });

            let cronCmd = 'node ' + filePath + ' ' + taskID;
            // fnx.log('cronCmd', cronCmd);
            let TaskRaw = shell.exec(cronCmd, { async: true }, function(code, stdout, stderr) {
                if (code != 0) return (new Error(stderr));
                // fnx.log('stdout', taskID, stdout)
                return(stdout) ;
              });
            
            let Task = TaskRaw.code;
            // fnx.log('TaskRaw code', Task);

            if (Task !== 0 && Task !== undefined) {
                fnx.log(taskID, script2Run + ' shellExec err', Task)
                return
            }
            else {
                Task !== undefined && log('OK', taskID, Task);
                return
            }


        });
        // wsconn && nTasks.checkWsConnState({...vars})
    } catch (e) {
        fnx.log(symi, 'runCronJob error', e)
        await fnx.battleNode.addLog({
            redisClient, redisKey, logField: 'errors',
            logType: 'error',
            logValue: {
                dtcreatedEn: new Date(Date.now()),
                errorDesc: 'runCronJob error',
                error: e,
            }
        });
        return e
    }
}

//https://stackoverflow.com/questions/34096458/passing-node-flags-args-to-child-process
//https://stackoverflow.com/questions/22646996/how-do-i-run-a-node-js-script-from-within-another-node-js-script
function runScript(scriptPath, params, callback) {
    // keep track of whether callback has been invoked to prevent multiple invocations
    var invoked = false;
    var child = childProcess.fork(scriptPath, [params]);
    // listen for errors as they may prevent the exit event from firing
    child.on('childProcess error', function (err) {
        console.log('error', err)
        if (invoked) return;
        invoked = true;
        callback(err);
    });

    // execute the callback once the process has finished running
    child.on('childProcess exit', function (code) {
        console.log('exit', code)
        if (invoked) return;
        invoked = true;
        var err = code === 0 ? null : new Error('exit code ' + code);
        callback(err);
    });

}