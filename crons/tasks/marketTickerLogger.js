const Redis = require('ioredis');
const MongoClient = require('mongodb').MongoClient;
const Binance = require('node-binance-api');
const dotenv = require('dotenv');
const process = require('node:process');
process.removeAllListeners('warning');

dotenv.config({ path: '../../.env.local' });

const redisClient = new Redis({
    host: '127.0.0.1',
    port: 6379,
});

const binanceApi = new Binance().options({});

const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args);

const debuglog = false;
const redisRecCount4Mongo = 3;

process.on('exit', (code) => {
//   console.log(`About to exit with code: ${code}`);
});

let redisDataLogKey = 'data:' + 'futuresDaily';
let timers = {};
const sleep = exports.sleep = (ms = 300) => {
    return new Promise(resolve => setTimeout(resolve, ms));
}

const promiseTimeout = exports.promiseTimeout = (promise, to = 3500) => {
    let timeout = new Promise((resolve, reject) => {
        let id = setTimeout(() => {
            clearTimeout(id);
            reject('Timed out in ' + to + 'ms.')
        }, to);
    });
    return Promise.race([
        promise,
        timeout,
    ]);
};
const futuresDaily = async ({ binanceApi, connTimeOut, savetoFile = false }) => {
    let dtBOP = Date.now()
    return new Promise(async (resolve, reject) => {
        let rawData;
        const connTimeOutV = connTimeOut || 10000;
        try {
            rawData =  await promiseTimeout(binanceApi.futuresDaily(), connTimeOutV);
            timers.binanceDataFetch = Date.now() - dtBOP;
            resolve(rawData);
        }
        catch (e) {
            timers.binanceDataFetch = Date.now() - dtBOP;
            reject(e)
        }
    });
};

const saveData2Redis = async ({redisCli, data2Save, dataKeyIndex}) => {
    redisCli = redisCli || redisClient;
    let dtBOP = Date.now()
    return new Promise(async (resolve, reject) => {
        let currData = [];
        try {
            currData = await redisClient.get(redisDataLogKey);
            currData = currData || [];
            try {
                currData = JSON.parse(currData);
            } catch (eL) {
                debuglog && log('no existing redis data');
            }
            let currData2Save = currData.filter(d => d[dataKeyIndex] !== data2Save[dataKeyIndex])
            currData2Save = [...currData2Save, {
                ...data2Save
            }];
            await redisCli.set(redisDataLogKey, JSON.stringify(currData2Save));
            let recCount = 0;
            recCount = Array.isArray(currData2Save) ? currData2Save.length : 0;
            timers.save2Redis = Date.now() - dtBOP;
            resolve(recCount)
        } catch (e) {
            timers.save2Redis = Date.now() - dtBOP;
            debuglog && log('error save 2 db', e);
            reject(e)
        }
    });
}

const clearDataFromRedis = async ({redisCli, ids2Delete = []}) => {
    redisCli = redisCli || redisClient;
    let dtBOP = Date.now()
    return new Promise(async (resolve, reject) => {
        let currData = [];
        try {
            currData = await redisClient.get(redisDataLogKey);
            currData = currData || [];
            try {
                currData = JSON.parse(currData);
            } catch (eL) {
                debuglog && log('no existing redis data');
            }
            let currData2Save = currData.filter(d => !ids2Delete.includes(d['dtCreated']) )
            currData2Save = [...currData2Save];
            await redisCli.set(redisDataLogKey, JSON.stringify(currData2Save));
            let recCount = 0;
            recCount = Array.isArray(currData2Save) ? currData2Save.length : 0;
            timers.clearRedis = Date.now() - dtBOP;
            resolve(recCount)
        } catch (e) {
            timers.clearRedis = Date.now() - dtBOP;
            debuglog && log('error save 2 db', e);
            reject(e)
        }
    });
}

const save2MongoDBAct = async ({ redisCli, }) => {
    redisCli = redisCli || redisClient;
    return new Promise(async (resolve, reject) => {
        let dtBOP = Date.now()
        try {
            let client;
            let clientPromise;
            const options = {};
            const uri = process.env.MONGODB_URI; // your mongodb connection string

            let currData = [];
            try {
                currData = await redisCli.get(redisDataLogKey);
                currData = currData || [];
                try {
                    currData = JSON.parse(currData);
                } catch (eL) {
                    debuglog && log('no existing redis data');
                }
            } catch (eC) {
                debuglog && log('error save 2 db', eC);
            }

            if (Array.isArray(currData) && currData.length !== 0) {
                let ids2Delete = currData.map(c => c.dtCreated);
                client = new MongoClient(uri, options);
                clientPromise = client.connect();
                debuglog && log('db connected!')
                timers.MongoConn2DB = Date.now() - dtBOP;
                const dbConn = await clientPromise;
                const db = dbConn.db('algoweb')
                const coll = db.collection('gauss.logs.futuresDaily');
                let res = await coll.insertMany(currData);
                debuglog && log("Number of documents inserted: " + res.insertedCount);
                dbConn.close();
                timers.Mongosave2DB = Date.now() - dtBOP;
                debuglog && log('clear thesee', ids2Delete);
                await clearDataFromRedis({redisCli, ids2Delete });
                resolve('save2MongoDBAct');
            } else {
                timers.Mongosave2DB = Date.now() - dtBOP;
                resolve(true);
            }
        } catch (e) {
            timers.Mongosave2DB = Date.now() - dtBOP;
            debuglog && log('error save2MongoDBAct!', e);
            reject(e)
        }
    });
};

const act = async ({ binanceApi, redisCli }) => {
    return new Promise(async (resolve, reject) => {
        let dtBOP = Date.now();
        try {

            let data = await futuresDaily({ binanceApi, connTimeOut: 10000 });
            let data2Save = {
                data,
                dtCreated: Date.now(),
                dt: new Date(new Date(Date.now()).setSeconds(0, 0)).getTime(),
                dtISO: new Date(new Date(Date.now()).setSeconds(0, 0)).toISOString(),
            }
            let dataKeyIndex = 'dt';
            let save2DBAct = await saveData2Redis({
                redisCli, data2Save, dataKeyIndex
            });
            try {
                debuglog && log('logs in redis', save2DBAct)
                save2DBAct && save2DBAct > redisRecCount4Mongo && await save2MongoDBAct({redisCli});
            } catch (eM) {
                debuglog && log('error in save2MongoDBAct', eM)
            }
            timers.actSure = Date.now() - dtBOP;
            resolve(data2Save);
        } catch (e) {
            timers.actSure = Date.now() - dtBOP;
            debuglog && log('error act!', e)
            reject(e);
        }
    });
};
(async () => {
    const dtBOP = Date.now();
    const killConns = async (signal) => {
        log(`'market ticker killConns Got ${signal} signal.'`);
        redisClient && redisClient.quit();
        process.exit(0)
    };
    [`SIGINT`, `SIGUSR1`, `SIGUSR2`, `uncaughtException`, `SIGTERM`].forEach((eventType) => {
        process.on(eventType, killConns.bind(eventType));
    });
    try {
        await act({binanceApi, redisClient});
        log('futuresDaily task Completed', Date.now() - dtBOP, timers)
        process.exit(0);
    }
    catch (e) {
        let dtElapsed = Date.now() - dtBOP 
        log('1124 / marketticker.js error', dtElapsed, e, timers);
        process.exit(0);
    } 
})();
