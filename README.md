https://github.com/jakecyr/stock-price-prediction/tree/master


31Aug.
* wallet check..
* max position volume check!
* barcount !"barCountCalculated": null,
* martingale check!
* dexsync te kapalı dex poz unu battle da kaparken gmt time issue var gibi. ? 
burası calismiyor. aynı poz icindeki birden fazla trade durumu olabiliyor... 
burası saglıklı calısmıyor.
DONE order sonunda hemen tp sl acılmalı!
* tpsl clean problem...
* parca parca close olan orderlar ??
DONE * stateOfCreateOrder da log atma!


DONE * tp/sl dex te poz sonrası acılmıs mı kontrol....
DONE * tl/sl si olmsyanları duzenle... once ekle, hata olursa close market...
DONE * dex te order acinca, battle a yansımıyor ??
DONE * dex reconn periodically / 60sec,
DONE close trade manually.
DONE dex reconn -> aktif pozu olmayan reduce order ları cancel et. / 60 sec
DONE * dex tpsl ignore list ---> testnet issue. / redis te!
DONE * tp/sl dex execute olduysa, close trade also!...


17/aug
DONE: trade battle type -> simulator degilse check dex... sync dex icin sor. close etme opsiyonu icin dex e yonlnedir
DONE: sync dex positions -> active trades!
DONE: cancel limit orders / non-reduce only.
DONE: clear dex
DONE: battle duration timer ekle!

2/temmuz
DONE: last price ok, avg price hatalı gibi on entry check
DONE: trade no otomatik artmıyor!
DONE: macd position...

DONE: 17-06 -> Battle Nodes 

reconn - ok..
view ok - 
restart ok - 
kill ok - 
killall ok -
info.. ok - 
nodetasks... -> websocket -> ok
list datagrid  -cancelled -. 

market ticker --ok.
kline initial data load monitor on gui ok..
errors gui -- ok
cronjob kline save --ok
modedebug ile console a yazdır error ları... ok
wsconn data append -ok.
market ticker crons - ok,
watch a ticker append. - ok
trades - kline - ok
wallet check - ok
trades - ticker price update -- ok
trades gui close all - ok
trades gui summary - ok
trades gui list all trades - ok..
trades gui list strategy perf - ok..
trades -> check additiional trade... -- ok...

cron monitor actions!
restart all - 
trades - ticker - sl check / close
trades -> check readiness for trade! (data, connectivity vs check)




cronjob -> task monitor acts!
tanersubasi@MacBook-Pro-M2 sv-gauss % chmod +x ./nodes/node.kline.js
tanersubasi@MacBook-Pro-M2 sv-gauss % chmod +x ./nodes/node.marketTicker.js
tanersubasi@MacBook-Pro-M2 sv-gauss % chmod +x ./nodes/node.marketMore.js  

/* todo-ok:: 
    market ticker subscription
    market ticker periodic / not ws.
    check ws connectivity -> per pair!
    repair ws connectivity -> button to reconnect! | button to unsubs.
    pages: nodes! | node tasks!.
    
*/

DONE: node re-start function -> 9/5/2024 -> done!

DONE: barclose ise wsocket i ignore et entry icin. -> kline completed icin bekle. entry function ı oraya dahil et.!
DONE: take profit ve stop loss fonksihyonları -> 
DONE: trades gui
TODO: kline timeout ettiginde entry icin kontrol mekanikleri. burayı manuel tetikleme fonksiyonu.


TODO: ws conn ile open time lar farklı ise kline ı download et tekrar _> 30.sn de.
TODO: chart tf icin her dk 40.sn de kline ve wsconn kontrolu.
TODO: support tf ler icin her 3 dk 50.sn de kline ve wsconn kontrolu.



notes!

error codes:
E101 / Kline Save 2 DB Error,
E100 / Kline GetTaskDone error,


WITH tables AS (SELECT name FROM sqlite_master WHERE type = 'table' and name like 'kline_%') 
SELECT GROUP_CONCAT(sql, ' UNION ALL ') sql
FROM ( 
  SELECT 'SELECT ' || 
    (
      SELECT GROUP_CONCAT(col) columns
      FROM (
        SELECT pti.name col
        FROM tables t CROSS JOIN pragma_table_info(t.name) pti
        GROUP BY col
        HAVING COUNT(*) = (SELECT COUNT(*) FROM tables)
      )
    ) || ' FROM ' || name AS sql
  FROM tables
);
 

LIST UNION ALL TABLES

WITH tables AS (SELECT name FROM sqlite_master WHERE type = 'table' and name like 'kline_%') 
SELECT GROUP_CONCAT(sql, ' UNION ALL ') sql
FROM ( 
  SELECT 'SELECT ' || 
    (
      SELECT GROUP_CONCAT(col) columns
      FROM (
        SELECT pti.name col
        FROM tables t CROSS JOIN pragma_table_info(t.name) pti
        GROUP BY col
        HAVING COUNT(*) = (SELECT COUNT(*) FROM tables)
      )
    ) || ' FROM ' || name AS sql
  FROM tables
);


binance orders:

[
  {
    "orderId": 4051210264,
    "symbol": "BTCUSDT",
    "status": "FILLED",
    "clientOrderId": "web_Vu5IBfO4GlV4LhQbFySj",
    "price": "0",
    "avgPrice": "65800.40000",
    "origQty": "0.002",
    "executedQty": "0.002",
    "cumQuote": "131.60080",
    "timeInForce": "GTC",
    "type": "MARKET",
    "reduceOnly": false,
    "closePosition": false,
    "side": "SELL",
    "positionSide": "BOTH",
    "stopPrice": "0",
    "workingType": "CONTRACT_PRICE",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "priceProtect": false,
    "origType": "MARKET",
    "time": 1721981521348,
    "updateTime": 1721981521348
  },
  {
    "orderId": 4051303714,
    "symbol": "BTCUSDT",
    "status": "NEW",
    "clientOrderId": "web_b9DPcic0dy3Zc6kB1UdW",
    "price": "65000",
    "avgPrice": "0.00000",
    "origQty": "0.002",
    "executedQty": "0",
    "cumQuote": "0",
    "timeInForce": "GTC",
    "type": "LIMIT",
    "reduceOnly": false,
    "closePosition": false,
    "side": "BUY",
    "positionSide": "BOTH",
    "stopPrice": "0",
    "workingType": "CONTRACT_PRICE",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "priceProtect": false,
    "origType": "LIMIT",
    "time": 1721997527011,
    "updateTime": 1721997527011
  },
  {
    "orderId": 4051311524,
    "symbol": "BTCUSDT",
    "status": "FILLED",
    "clientOrderId": "web_Y1kq3eZwhyV5fqWwvHCH",
    "price": "0",
    "avgPrice": "66447.60000",
    "origQty": "0.002",
    "executedQty": "0.002",
    "cumQuote": "132.89520",
    "timeInForce": "GTC",
    "type": "MARKET",
    "reduceOnly": true,
    "closePosition": false,
    "side": "BUY",
    "positionSide": "BOTH",
    "stopPrice": "0",
    "workingType": "CONTRACT_PRICE",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "priceProtect": false,
    "origType": "MARKET",
    "time": 1722000129008,
    "updateTime": 1722000129008
  },
  {
    "orderId": 4051312237,
    "symbol": "BTCUSDT",
    "status": "FILLED",
    "clientOrderId": "web_uvA8R13iFnzKdHs3BUr5",
    "price": "0",
    "avgPrice": "66645.20000",
    "origQty": "0.002",
    "executedQty": "0.002",
    "cumQuote": "133.29040",
    "timeInForce": "GTC",
    "type": "MARKET",
    "reduceOnly": false,
    "closePosition": false,
    "side": "BUY",
    "positionSide": "BOTH",
    "stopPrice": "0",
    "workingType": "CONTRACT_PRICE",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "priceProtect": false,
    "origType": "MARKET",
    "time": 1722000422334,
    "updateTime": 1722000422334
  },
  {
    "orderId": 4051312262,
    "symbol": "BTCUSDT",
    "status": "FILLED",
    "clientOrderId": "web_ffeF2ct39yIOhu6n7kv8",
    "price": "0",
    "avgPrice": "66541.90000",
    "origQty": "0.002",
    "executedQty": "0.002",
    "cumQuote": "133.08380",
    "timeInForce": "GTC",
    "type": "MARKET",
    "reduceOnly": false,
    "closePosition": false,
    "side": "BUY",
    "positionSide": "BOTH",
    "stopPrice": "0",
    "workingType": "CONTRACT_PRICE",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "priceProtect": false,
    "origType": "MARKET",
    "time": *************,
    "updateTime": *************
  },
  {
    "orderId": **********,
    "symbol": "BTCUSDT",
    "status": "FILLED",
    "clientOrderId": "web_HTIujSrcPlrwNR8MANO2",
    "price": "0",
    "avgPrice": "66084.70000",
    "origQty": "0.004",
    "executedQty": "0.004",
    "cumQuote": "264.33880",
    "timeInForce": "GTC",
    "type": "MARKET",
    "reduceOnly": true,
    "closePosition": false,
    "side": "SELL",
    "positionSide": "BOTH",
    "stopPrice": "0",
    "workingType": "CONTRACT_PRICE",
    "priceMatch": "NONE",
    "selfTradePreventionMode": "NONE",
    "goodTillDate": 0,
    "priceProtect": false,
    "origType": "MARKET",
    "time": 1722000478503,
    "updateTime": *************
  }
]


binance trades

[
  {
    "symbol": "BTCUSDT",
    "id": 287708369,
    "orderId": 4051210264,
    "side": "SELL",
    "price": "65800.40",
    "qty": "0.002",
    "realizedPnl": "0",
    "quoteQty": "131.60080",
    "commission": "0.05264032",
    "commissionAsset": "USDT",
    "time": 1721981521348,
    "positionSide": "BOTH",
    "maker": false,
    "buyer": false
  },
  {
    "symbol": "BTCUSDT",
    "id": 287718330,
    "orderId": 4051311524,
    "side": "BUY",
    "price": "66447.60",
    "qty": "0.002",
    "realizedPnl": "-1.29440000",
    "quoteQty": "132.89520",
    "commission": "0.05315808",
    "commissionAsset": "USDT",
    "time": 1722000129008,
    "positionSide": "BOTH",
    "maker": false,
    "buyer": true
  },
  {
    "symbol": "BTCUSDT",
    "id": 287718460,
    "orderId": 4051312237,
    "side": "BUY",
    "price": "66645.20",
    "qty": "0.002",
    "realizedPnl": "0",
    "quoteQty": "133.29040",
    "commission": "0.05331616",
    "commissionAsset": "USDT",
    "time": 1722000422334,
    "positionSide": "BOTH",
    "maker": false,
    "buyer": true
  },
  {
    "symbol": "BTCUSDT",
    "id": 287718463,
    "orderId": 4051312262,
    "side": "BUY",
    "price": "66541.90",
    "qty": "0.002",
    "realizedPnl": "0",
    "quoteQty": "133.08380",
    "commission": "0.********",
    "commissionAsset": "USDT",
    "time": *************,
    "positionSide": "BOTH",
    "maker": false,
    "buyer": true
  },
  {
    "symbol": "BTCUSDT",
    "id": *********,
    "orderId": **********,
    "side": "SELL",
    "price": "66084.70",
    "qty": "0.004",
    "realizedPnl": "-2.********",
    "quoteQty": "264.33880",
    "commission": "0.********",
    "commissionAsset": "USDT",
    "time": *************,
    "positionSide": "BOTH",
    "maker": false,
    "buyer": false
  }
]

account

{
    "data": {
        "feeTier": 0,
        "canTrade": true,
        "canDeposit": true,
        "canWithdraw": true,
        "feeBurn": true,
        "tradeGroupId": -1,
        "updateTime": 0,
        "multiAssetsMargin": false,
        "totalInitialMargin": "43.********",
        "totalMaintMargin": "0.********",
        "totalWalletBalance": "2996.********",
        "totalUnrealizedProfit": "2.********",
        "totalMarginBalance": "2998.********",
        "totalPositionInitialMargin": "5.********",
        "totalOpenOrderInitialMargin": "37.********",
        "totalCrossWalletBalance": "2996.********",
        "totalCrossUnPnl": "2.********",
        "availableBalance": "2952.********",
        "maxWithdrawAmount": "2952.********",
        "assets": [
            {
                "asset": "USDT",
                "walletBalance": "2996.********",
                "unrealizedProfit": "2.********",
                "marginBalance": "2998.********",
                "maintMargin": "0.********",
                "initialMargin": "43.********",
                "positionInitialMargin": "5.********",
                "openOrderInitialMargin": "37.********",
                "maxWithdrawAmount": "2952.********",
                "crossWalletBalance": "2996.********",
                "crossUnPnl": "2.********",
                "availableBalance": "2952.********",
                "marginAvailable": true,
                "updateTime": 1722105447980
            }
        ],
        "positions": [
            {
                "symbol": "BTCUSDT",
                "initialMargin": "10.70025172",
                "maintMargin": "0.********",
                "unrealizedProfit": "2.********",
                "positionInitialMargin": "5.********",
                "openOrderInitialMargin": "5.20000000",
                "leverage": "25",
                "isolated": false,
                "entryPrice": "67705.0",
                "breakEvenPrice": "67732.082",
                "maxNotional": "3000000",
                "positionSide": "BOTH",
                "positionAmt": "0.002",
                "notional": "137.50629321",
                "isolatedWallet": "0",
                "updateTime": 1722105447980,
                "bidNotional": "130",
                "askNotional": "0",
                "direction": "BUY"
            }
        ]
    },
    "stats": 2052
}