const {exec} = require('child_process'); 
const args = process.argv.slice(2);
(async () => {
    console.log('args', args)
    let portStg;
    args.length !== 0 && args.map(a => {
        let ax = a.split("=");
        console.log(a);
        if(ax[0] == '-port'){
            portStg = ax[1]
        } 
    })
    let cmd = 'kill -9 ' + portStg;
    exec(cmd, (err, stdout, stderr) => { //ps aux | grep node
        if (err) {
          // node couldn't execute the command
          console.log('err', err, portStg)
          return;
        }
        // the *entire* stdout and stderr (buffered)
        console.log(`stdout: ${stdout}`);
        console.log(`stderr: ${stderr}`);
      });

})();