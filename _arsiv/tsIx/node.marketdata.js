
const Binance = require('node-binance-api');
const Database = require('better-sqlite3');
const path = require('path');
const fnx = require('../../../nodes/_.functions');
const fnxSelf = require('./node.marketdata.fnx');
let dbFile = path.resolve(__dirname, '../db/binance.db');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const args = process.argv.slice(2);
(async () => {
    const db = new Database(dbFile); //, { verbose: fnx.log }  console. a log icin..

    db.pragma('journal_mode = WAL');
    // db.pragma('journal_mode = OFF');
    // db.pragma('synchronous = OFF');

    const task = Array.isArray(args) && args.length !== 0 ? args[0] : 'binance.futuresExchangeInfo';
    switch (task) {
        case 'binance.futuresDaily':
            await fnxSelf.binance.futuresDaily({
                db,
                task,
                binanceApi
            });
            break;
        case 'binance.futuresFundingRate':
            await fnxSelf.binance.futuresFundingRate({
                db,
                task,
                binanceApi,
            });
            break;
        case 'binance.futuresQuote':
            await fnxSelf.binance.futuresQuote({
                db,
                task,
                binanceApi,
            });
            break;
        case 'binance.futuresPrices':
            await fnxSelf.binance.futuresPrices({
                task,
                binanceApi,
                db,
            });
            break;
        case 'binance.futuresExchangeInfo':
            await fnxSelf.binance.futuresExchangeInfo({
                task,
                binanceApi,
                db,
            });
            break;
        case 'binance.futuresMarkPrice':
            await fnxSelf.binance.futuresMarkPrice({
                task,
                binanceApi
            });
            break;

        default:
            let text = "Looking forward to the Weekend";
            console.log(text);
            process.exit(0);
    }

})();