//@version=5
indicator(title="Rate Of Change", shorttitle="TSI.ROC", overlay=true, format=format.price, precision=2, timeframe="", timeframe_gaps=true)
length = input.int(9, minval=1)
source = input(close, "Source")
roc = 100 * (source - source[length])/source[length]
plot(roc, color=#2962FF, title="ROC")
hline(0, color=#787B86, title="Zero Line") 

smoothK = input.int(3, "K", minval=1)
smoothD = input.int(3, "D", minval=1)
lengthRSI = input.int(14, "RSI Length", minval=1)
lengthStoch = input.int(14, "Stochastic Length", minval=1)
src = input(close, title="RSI Source")
rsi1 = ta.rsi(src, lengthRSI)
k = ta.sma(ta.stoch(rsi1, rsi1, rsi1, lengthStoch), smoothK)
d = ta.sma(k, smoothD)
kdCross = ta.crossover(k, d)
checkCondition = (roc > 0 and kdCross and k < 30 and (k[0] > k[1]) and (d[0]> d[1]) and (roc[0] > roc[1]))
//plotchar( checkCondition, char='❄', size = size.small) //condition for hybrit ssl and adx and stochRSI
plotshape(checkCondition, style=shape.xcross, size = size.small, location = location.bottom)