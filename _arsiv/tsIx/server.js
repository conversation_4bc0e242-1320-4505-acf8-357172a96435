require('dotenv').config();
// const { exec } = require('child_process');
const path = require('path');
const express = require('express')
const fnx = require('../../../nodes/_.functions');
const fnxDb = require('../../../nodes/_.functions.db');
// const fnxBnc = require('./_.functions.binance');
// const cors = require('cors');
const bodyParser = require('body-parser')
const Database = require('better-sqlite3');
const CronJobManager = require('cron-job-manager')
const cronstrue = require('cronstrue');
// console.log(path.resolve(__dirname, '..'));
const db = new Database(path.resolve(__dirname, '../db/binance.db')); //, { verbose: fnx.log }
const args = process.argv.slice(2);
const app = express();
(async () => {
    const cronMan = new CronJobManager();
    app.use(bodyParser.json());
    let taskID = 0;
    args.length !== 0 && args.map(a => {
        let ax = a.split("=");
        if (ax[0] == '-taskid') {
            taskID = ax[1]
        }
    });

    app.get('/stop', (req, res) => {
        const que = req.query;
        fnx.log('send success')
        process.send && process.send({ result: true, resultText: 'Success', time: fnx.timestamp() });
        process.exit(0)
    })
    app.get('/list', (req, res) => {
        var jobs = fnx.tasks.listCronJobs(cronMan.listCrons());
        res.send({ jobs })
    })

    const logServerStartStop = async port => {
        //check taskID
        try {
            let qS1 = `SELECT EXISTS(SELECT 1 FROM core_nodes cn WHERE is_deleted = 0 and task_id = ${taskID}) as chec;`
            // let qS1 = `SELECT 1 FROM core_nodes cn WHERE is_deleted = 0 and task_id = ${taskID};`
            let res = db.prepare(qS1).all();
            if (res && Array.isArray(res) && res[0]?.chec == 0) {
                const data2Post = {
                    process_id: process.pid,
                    port: port,
                    task_id: taskID,
                    task_name: 'Test Task',
                    is_active: true
                }
                await fnxDb.cud.insertOne({
                    db,
                    table: 'core_nodes',
                    payload: data2Post,
                    setid: 'id',
                })
            } else if (res && Array.isArray(res) && res[0]?.chec  == 1) {
                let qq = `  
                    UPDATE core_nodes
                    SET is_active = true, process_id = ${process.pid}, port = ${port}
                    where task_id = ` + taskID;
                db.prepare(qq).run();
            } else {
                console.log('!!HATA!', res)
            }
        }
        catch (e) {
            console.log('ts ex', e)
        }
    }

    const executeTask = async ptask => {
        // fnx.log('get Task', ptask);
        if (ptask.type === 'marketdata') {
            // fnx.log('set tasks')
            fnx.tasks.setCronJobs({
                subtasks: ptask.props.subtasks,
                cronMan,
            })
            // fnx.log('taskset')
        }
    } 
    const server = app.listen(0, async () => {
        const PORT = server.address().port;
        logServerStartStop(PORT)
        executeTask(fnx.tasks.getTask(taskID));
        fnx.log(`listening on PORT/pID: ${PORT}`, process.pid)
    })

})();