
const { exec } = require('child_process');
const path = require('path');
const Database = require('better-sqlite3');
const db = new Database(path.resolve(__dirname, '../db/binance.db'), {  });
const args = process.argv.slice(2);
(async () => {
    let dt = Date.now();
    db.exec(`
                DROP TABLE if exists core_nodes;
                create table core_nodes (
                    id text primary key not null,
                    process_id int not null,
                    port int not null,
                    task_id int not null,
                    task_name text not null,
                    is_active bool not null DEFAULT(true),
                    is_deleted bool not null DEFAULT(false),
                    dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                    dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                );
                DROP TABLE if exists core_tasks;
                create table core_tasks (
                    task_id int primary key not null,
                    task_name text not null
                );
            `);
    process.exit(0)
})();



/*

    createTables: (newdb) => {
        newdb.exec(`
        create table hero (
            hero_id int primary key not null,
            hero_name text not null,
            is_xman text not null,
            was_snapped text not null
        );
        insert into hero (hero_id, hero_name, is_xman, was_snapped)
            values (1, 'Spiderman', 'N', 'Y'),
                   (2, 'Tony Stark', 'N', 'N'),
                   (3, 'Jean Grey', 'Y', 'N');
    
        create table hero_power (
            hero_id int not null,
            hero_power text not null
        );
    
        insert into hero_power (hero_id, hero_power)
            values (1, 'Web Slinging'),
                   (1, 'Super Strength'),
                   (1, 'Total Nerd'),
                   (2, 'Total Nerd'),
                   (3, 'Telepathic Manipulation'),
                   (3, 'Astral Projection');
            `, ()  => {
                fndb.runQueries(newdb);
        });
    },   
    initTables: (dbConn) => {
        return new Promise(async (resolve, reject) => {
            try {
                dbConn.exec(`
                        create table core_nodes (
                            n_id int primary key not null,
                            process_id int not null,
                            port int not null,
                            task_id int not null,
                            task_name text not null,
                            isactive bool not null,
                            isdeleted bool not null
                        );
                        create table core_tasks (
                            task_id int primary key not null,
                            task_name text not null
                        );
                `);
                resolve(true)
            } catch (e) {
                fnx.log('fbdb query error', e);
                reject(e)
            }
        });
    },
    */