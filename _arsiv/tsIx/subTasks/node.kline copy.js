const Binance = require('node-binance-api');
const Database = require('better-sqlite3');
const path = require('path');
const fnx = require('./_.functions');
const fnxSelf = require('./node.marketdata.fnx');
let dbFMarket = path.resolve(__dirname, '../db/market.db');
let dbFGauss = path.resolve(__dirname, '../db/gauss.db');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const args = process.argv.slice(2);
(async () => {
    const dbMarket = new Database(dbFMarket); //, { verbose: fnx.log }  console. a log icin..
    const dbGauss = new Database(dbFGauss); //, { verbose: fnx.log }  console. a log icin..
    const task = Array.isArray(args) && args.length !== 0 ? args[0] : 'binance.futuresExchangeInfo';
    fnx.log(task);
})();