
const Binance = require('node-binance-api');
const binance = new Binance().options({
  // APIKEY: '<key>',
  // APISECRET: '<secret>'
});

// const Binance = require('binance-api-node').default
// Authenticated client, can make signed calls
// const binance = new Binance({
//   // apiKey: 'xxx',
//   // apiSecret: 'xxx',
//   // getTime: xxx,
// });


(async () => {
  // console.log('binance', binance)
  // Getting latest price of a symbol
  // console.log(await binance.futuresCandles({ symbol: 'BTCUSDT' }))

  // binance.prices(function (error, ticker) {
  //   console.log("prices()", ticker);
  //   console.log("Price of BNB: ", ticker.BNBBTC);
  // });

  // binance.futuresPrices(function (error, ticker) {
  //   if (error) {
  //     console.log('error')
  //     return false;
  //   }
  //   console.log("futuresPrices()", ticker);
  //   // console.log("Price of BNB: ", ticker.BNBBTC);
  // });
  console.info( await binance.futuresPrices() );
})();