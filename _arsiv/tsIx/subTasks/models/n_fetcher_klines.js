
const Binance = require('node-binance-api');
const Database = require('better-sqlite3');
const path = require('path');
const fnx = require('../_.functions');
let dbFile = path.resolve(__dirname, '../../db/binance.db');
const repoDBSchemas = require('./_db_schemas.json');
const repoTask = require('./_tmpData.json');
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const modeDebug = false;
const args = process.argv.slice(2);
(async () => {
    const db = new Database(dbFile); //, { verbose: fnx.log }  console. a log icin..

    db.pragma('journal_mode = WAL');
    
    const nodeParentID = Array.isArray(args) && args.length !== 0 ? args[0] : '0';
    const argTask = Array.isArray(args) && args.length !== 0 ? args[1] : 'noTask';
    
    const nodeCode = 100;
    let logNodeData = {
        db,
        nodeParentID: nodeParentID,
        nodeCode: nodeCode.toString(),
        nodeTask: argTask,
        nodeStatus: 0,
        nodeStatusDesc: 'node started',
        is_active: true,
    }
    await fnx.logNodeWIPStatus(logNodeData);

    const task = repoTask[argTask];
    const dtstarted = Date.now();
    let logData = {
        db,
        logTable: 'log_binance_fetch_klines',
        logTableColumns: repoDBSchemas['log_binance_fetch_klines']["columns"],
        service: argTask,
        serviceDesc: JSON.stringify(task),
        dtstarted,
    }
    let rawData = null;
    try {
        if (task?.symbol && task?.interval) {
            logNodeData = {
                ...logNodeData,
                nodeStatusDesc: 'data fetching',
            }
            await fnx.logNodeWIPStatus(logNodeData);
            const connTimeOut = task.connTimeOut || 300;
            rawData = await fnx.promiseTimeout(binanceApi.futuresCandles(task?.symbol, task?.interval), connTimeOut)
            /*
            https://binance-docs.github.io/apidocs/futures/en/#kline-candlestick-data
            Response:
                [
                [
                    1499040000000,      // Open time
                    "0.01634790",       // Open
                    "0.80000000",       // High
                    "0.01575800",       // Low
                    "0.01577100",       // Close
                    "148976.11427815",  // Volume
                    1499644799999,      // Close time
                    "2434.19055334",    // Quote asset volume
                    308,                // Number of trades
                    "1756.87402397",    // Taker buy base asset volume
                    "28.46694368",      // Taker buy quote asset volume
                    "17928899.62484339" // Ignore.
                ]
                ]
            */
            const dtcompleted = Date.now();

            logNodeData = {
                ...logNodeData,
                nodeStatusDesc: 'data fetched',
                nodeStatus: 1,
                is_active: false,
            }
            await fnx.logNodeWIPStatus(logNodeData);

            logData = {
                ...logData,
                klines: task?.symbol + '_' + task?.interval,
                dtcompleted,
                status_code: 200,
                status_desc: task?.symbol + '_' + task?.interval,
                task_timer: dtcompleted - dtstarted
            };
            try {
                await fnx.logTransaction(logData);
            }
            catch (eE) {
                fnx.log('logTransactionError1')
            }
            modeDebug && fnx.log(rawData.length);
            process.exit(0);
        } else {
            modeDebug && fnx.logError({ log: { desc: 'error in task', log: task } });
            const dtcompleted = Date.now();
            logData = {
                ...logData,
                klines: '_',
                dtcompleted,
                status_code: 404,
                status_desc: 'task ref not found',
                task_timer: dtcompleted - dtstarted
            };
            try {
                await fnx.logTransaction(logData);
            }
            catch (eE) {
                modeDebug && fnx.log('logTransactionError2', eE)
            }
            process.exit(1)
        }
    }
    catch (e) {
        modeDebug && fnx.logError({ log: { desc: 'error in task', log: e } });
        const dtcompleted = Date.now();
        logData = {
            ...logData,
            klines: task ? task?.symbol + '_' + task?.interval : '_',
            dtcompleted,
            status_code: 500,
            status_desc: 'error' + e,
            task_timer: dtcompleted - dtstarted
        };
        try {
            await fnx.logTransaction(logData);
        }
        catch (eE) {
            modeDebug && fnx.log('logTransactionError3', eE)
        }
        process.exit(1)
    }

})();
