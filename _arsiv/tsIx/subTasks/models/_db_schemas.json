{"log_binance_fetch_klines": {"columns": ["klines", "status_code", "status_desc", "fnservice", "fnservice_desc", "dtstarted", "dtcompleted", "task_timer"], "initQ": "create table IF NOT EXISTS log_binance_fetch_klines ( klines text NOT NULL DEFAULT(''), status_code int not null DEFAULT(0), status_desc text not null DEFAULT(''), dtstarted DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP), dtcompleted <PERSON><PERSON><PERSON>IME NOT NULL DEFAULT (CURRENT_TIMESTAMP), task_timer integer not null DEFAULT(0), fnservice text NOT NULL, fnservice_desc text not null DEFAULT(''), dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP), dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) )"}}