[{"task_id": 1, "type": "marketdata", "props": {"subtasks": [{"key": "binance.futuresDaily", "script2Run": "node.marketdata.js", "jobDesc": "futuresDaily At every minute", "period": "*/10 * * * * *", "periodDesc": "At every minute"}, {"key": "binance.futuresFundingRate", "script2Run": "node.marketdata.js", "jobDesc": "futuresFundingRate At every minute", "period": "*/15 * * * * *", "periodDesc": "At every 5th minute"}, {"key": "binance.futuresQuote", "script2Run": "node.marketdata.js", "jobDesc": "futuresQuote At every minute", "period": "*/12 * * * * *", "periodDesc": "At every 5th minute"}]}}, {"task_id": 2, "type": "battle", "props": {"parameters": {"pairs": ["BTCUSDT", "ETHUSDT"], "intervals": ["5m", "15m"], "limit": 500, "timeoutSource": 1500, "trading": {"entry": 0, "takeprofit": 0, "stoploss": 0, "strategies": 0}}, "subtasks": {}}}]