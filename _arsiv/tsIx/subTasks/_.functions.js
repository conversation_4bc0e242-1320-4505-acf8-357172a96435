const { exec } = require('child_process');
const shell = require('shelljs');
const path = require('path');
const fs = require('fs').promises;
const fss = require('fs');
const fnxDb = require('./_.functions.db');
const repoDBSchemas = require('./models/_db_schemas.json');


const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args)

const logError = exports.logError = async (params) => {
    return new Promise(async function (resolve, reject) {
        try {
            log(params.desc || 'error', params.log);
            resolve(true)
        } catch (err) {
            log(params.desc || 'error', params.log);
            log('error in log.', err)
            reject(err);
        }
    });
}

const savetoFile = exports.savetoFile = async (dt, filename, debg = false) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
        try {
            const basePath = path.resolve(process.cwd(), filename);
            var res = await fs.writeFile(filename, JSON.stringify(dt), 'utf8');
            debg && console.log("data is saved.", filename, Date.now() - bop);
            resolve(res)
        } catch (err) {
            log('dosya kadedilemedi.', filename, err)
            reject(err);
        }
    });
}
const readFile = exports.readFile = (filename) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
        try {
            var resp = fss.readFileSync(filename, { encoding: 'utf8', flag: 'r' }); //
            resolve(resp.toString());
        } catch (err) {
            log('readFile error.', filename, err)
            reject(err);
        }
    });
}
const sleep = exports.sleep = (ms = 300) => {
    return new Promise(resolve => setTimeout(resolve, ms));
}//dt, tabl

const savetoDBJSON = exports.savetoDBJSON = async ({
    db, rawData, tableName, time1Field = null,
}) => {
    return new Promise(async (resolve, reject) => {
        let arr = rawData;
        try {
            let batchid = Date.now();
            let sq_pre = `update ${tableName} set is_deleted = true where 1=1`;
            db.exec(sq_pre);
            arr.map(a => {
                a.batchid = batchid;
                if (time1Field) {
                    a[time1Field + 'HRF'] = new Date(a[time1Field]).toISOString();
                }
            });
            let sql = await generateSQL({ payload: arr, table: tableName })
            var res = db.exec(sql);
            resolve(res);
        }
        catch (e) {
            reject(e)
        }
    });
}
const savetoDBJSONArr = exports.savetoDBJSONArr = async ({
    db, rawData, tableName, time1Field = null, time2Field = null, debug = false }) => {
    return new Promise(async (resolve, reject) => {
        try {
            let arr = await json2arr(rawData);
            let batchid = Date.now();
            let sq_pre = `update ${tableName} set is_deleted = true where batchid not in (${batchid})`;
            db.exec(sq_pre);
            arr.map(a => {
                a.batchid = batchid;
                delete a.lastUpdateId;
                if (time1Field) {
                    a[time1Field + 'HRF'] = new Date(a[time1Field]).toISOString();
                }
                if (time2Field) {
                    a[time2Field + 'HRF'] = new Date(a[time2Field]).toISOString();
                }
            })
            let sql = await generateSQL({ payload: arr, table: tableName });
            // debug && console.log('sql', sql)
            var res = db.exec(sql);
            resolve(res);
        }
        catch (e) {
            reject(e)
        }
    });
};


const generateSQL = async ({ payload, table }) => {
    // 
    let ssql = '';
    let arr = payload;
    return new Promise(async (resolve, reject) => {
        for (a of arr) {
            let pl = await getJSONFields(a);
            ssql = ssql + 'INSERT INTO ' + table + ' (' + pl.fields.toString() + ') VALUES (' + pl.fieldValues.toString() + ');\n\n'
        }
        resolve(ssql)
    });
};

const getJSONFields = payload => {
    return new Promise(async (resolve, reject) => {
        let resp = {}
        let fields = [];
        let fieldValues = [];
        let tmp = JSON.parse(JSON.stringify(payload))
        try {
            for (const key in tmp) {
                if (tmp.hasOwnProperty(key)) {
                    fields.push(key);
                    fieldValues.push(typeof (tmp[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]);
                }
            }
            resp = { fields, fieldValues };
            resolve(resp);
        }
        catch (e) {
            reject(e);
        }
    });
}
const json2arr = dt => {
    let arr = [];
    return new Promise(async (resolve, reject) => {
        if (dt) {
            try {
                for (const key in dt) {
                    if (dt.hasOwnProperty(key)) {
                        arr.push(dt[key]);
                        //   console.log(`${key} : ${JSON.stringify(dt[key])}`)
                    }
                }
                resolve(arr)
            }
            catch (e) {
                reject(e)
            }
        } else {
            reject('no value')
        }
    });
}

const promiseTimeout = exports.promiseTimeout = (promise, to = 3500) => {
    // Create a promise that rejects in <ms> milliseconds
    let timeout = new Promise((resolve, reject) => {
        let id = setTimeout(() => {
            clearTimeout(id);
            reject('Timed out in ' + to + 'ms.')
        }, to);
    });

    // Returns a race between our timeout and the passed in promise
    return Promise.race([
        promise,
        timeout,
    ]);
};

const forEachPromise = exports.forEachPromise = (items, fn, context) => {
    return items.reduce(function (promise, item) {
        return promise.then(function () {
            return fn(item, context);
        }).catch((e) => console.log('for each PromiseError', e));
    }, Promise.resolve());
}

const logTransaction = exports.logTransaction = async props => {
    const { db } = props;
    return new Promise(async (resolve, reject) => {
        let table = props.logTable;
        let fields = props.logTableColumns;
        let fieldValues;
        try {
            switch (table) {
                case 'log_binance_fetch_klines':
                    fieldValues = [
                        "'" + props.klines.toString() + "'", 
                        props.status_code, 
                        "'" + props.status_desc.toString() + "'",
                        "'" + props.service.toString()  + "'", 
                        "'" + (props.serviceDesc ? props.serviceDesc.toString() : '-')  + "'",
                        "'" + new Date(props.dtstarted).toISOString() + "'", 
                        "'" + new Date(props.dtcompleted).toISOString() + "'", 
                        props.task_timer
                    ]
                    break;
                default:
                    fieldValues = [
                        "'" + props.service.toString() + "'", props.isSuccess, "'" + props.status_desc.toString() + "'",
                        "'" + new Date(props.dtstarted).toISOString() + "'", "'" + new Date(props.dtcompleted).toISOString() + "'", props.task_timer
                    ]
            }
        } catch (eC) {
            log('fieldValues error', fields, props)
            reject(eC)
        }

        try {
            //create table if not exists...
            let sqlCreateTable = repoDBSchemas[table]["initQ"]
            var res = db.exec(sqlCreateTable);
        }
        catch (ePre) {
            log('create table error', ePre)
        }

        try {
            let sql = `
                    INSERT INTO ${table} (${fields.toString()}) 
                    VALUES (${fieldValues.toString()})`;
            var res = db.exec(sql);
            resolve(res);
        }
        catch (e) {
            log('rec  error', e, `
            INSERT INTO ${table} (${fields.toString()}) 
            VALUES (${fieldValues.toString()})`)
            reject(e)
        }
    });
}

const logNodeWIPStatus = exports.logNodeWIPStatus = async props => {
    const { db } = props;
    return new Promise(async (resolve, reject) => {
        let table = 'log_nodes_wip_status';
        try {
            var res = db.exec(`
            create table IF NOT EXISTS log_nodes_wip_status (
                id text primary key not null,
                nodeParentID text not null DEFAULT('0'),
                nodeCode text not null DEFAULT('0'),
                nodeTask text not null DEFAULT(' '),
                is_active bool not null DEFAULT(true),
                nodeStatus int not null DEFAULT(0),
                nodeStatusDesc text not null DEFAULT(' '),
                process_id int not null DEFAULT(0),
                port int not null DEFAULT(0),
                is_deleted bool not null DEFAULT(false),
                dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
            );
            `);
        }
        catch (ePre) {
            log('create table error', ePre)
        }

        const data2Post = {
            nodeParentID: props.nodeParentID,
            nodeCode: props.nodeCode,
            nodeTask: props.nodeTask,
            nodeStatus: props.nodeStatus,
            nodeStatusDesc: props.nodeStatusDesc,
            process_id: process.pid,
            port: '000',
        }
        //check exists
        try {
            let qS1 = `SELECT EXISTS(SELECT 1 FROM ${table} cn WHERE is_deleted = 0 and nodeParentID = '${props.nodeParentID}' and nodeCode = '${props.nodeCode}' and nodeTask = '${props.nodeTask}') as chec;`
            let res = db.prepare(qS1).all();
            if (res && Array.isArray(res) && res[0]?.chec == 0) {
                await fnxDb.cud.insertOne({
                    db,
                    table: table,
                    payload: data2Post,
                    setid: 'id',
                })
            } else if (res && Array.isArray(res) && res[0]?.chec  == 1) {
                let qq = `  
                    UPDATE ${table}
                    SET is_active = ${props.is_active}, nodeStatus = ${props.nodeStatus}, nodeStatusDesc = '${props.nodeStatusDesc}', dtupdated = CURRENT_TIMESTAMP
                    where nodeParentID = '${props.nodeParentID}' and nodeCode = '${props.nodeCode}' and nodeTask = '${props.nodeTask}'`;
                db.prepare(qq).run();
                resolve(true)
            } else {
                console.log('!!HATA!', res)
                reject(false)
            }

        } catch (es1) {
            console.log('node log rec error', es1, data2Post)
            reject(false)
        }
    });
}
