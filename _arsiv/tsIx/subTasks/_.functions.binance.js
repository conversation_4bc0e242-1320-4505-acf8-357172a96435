require('dotenv').config();
const Binance = require('binance-api-node').default
// import Binance from 'binance-api-node'
const client = Binance();

// Authenticated client, can make signed calls
// const client2 = Binance({
//     apiKey: 'xxx',
//     apiSecret: 'xxx',
//     getTime: xxx,
//   })

const generic = (exports.generic = {
    time: () => {
        return new Promise(async (resolve, reject) => {
            try {
                client.time().then(time => {
                    resolve(time)
                })
                .catch(e => console.log('time error', e));
            } catch (e) {
                console.log('generic time e', e)
            }
        });
    }
});