require('dotenv').config();
// const { exec } = require('child_process');
const path = require('path');
const express = require('express')
const fnx = require('./_.functions');
// const fnxDb = require('./_.functions.db');
// const fnxBnc = require('./_.functions.binance');
// const cors = require('cors');
const bodyParser = require('body-parser')
const Database = require('better-sqlite3');
const CronJobManager = require('cron-job-manager');
const { error } = require('console');
// const cronstrue = require('cronstrue');
// console.log(path.resolve(__dirname, '..'));
const db = new Database(path.resolve(__dirname, '../db/gauss.db')); //, { verbose: fnx.log }
const args = process.argv.slice(2);
var porte = null;
const app = express();
(async () => {
    const cronMan = new CronJobManager();
    app.use(bodyParser.json());
    let node_key = 0;

    let tIX = args.findIndex(a => a == '-node_key');
    let arxIndex = tIX > -1 ? tIX + 1 : false;

    if (arxIndex) {
        node_key = args[arxIndex];
    } else {
        await fnx.savetoFile('test', 'test.txt', true);
        process.kill(process.pid, 'SIGHUP');
    }

    process.on('SIGHUP', () => {
        console.log('Got SIGHUP signal.');
        process.exit(5)
      });

    app.get('/stop', (req, res) => {
        fnx.log(`Node ${porte} stopped!`);
        process.exit(0)
    })
    app.get('/list', (req, res) => {
        var jobs = fnx.tasks.listCronJobs(cronMan.listCrons());
        res.send({ jobs })
    }) 
    const executeTask = async ptask => {
        // fnx.log('get Task', ptask);
        if (ptask.type === 'marketdata') {
            // fnx.log('set tasks')
            fnx.tasks.setCronJobs({
                subtasks: ptask.props.subtasks,
                cronMan,
            })
            // fnx.log('taskset')
        }
    } 
    const server = app.listen(0, async () => {
        const PORT = server.address().port;
        console.log(`node ${PORT}`);
        porte = PORT;
        try {
            fnx.log(`starting server on PORT/pID: ${PORT}`, process.pid)
            await fnx.nodes.updateNodeProcessIDandPort({
                db,
                node_key,
                pid: process.pid,
                port: PORT,
            })
            let nodeTask = await fnx.nodes.getNode({
                db,
                taskID: node_key
            });
            console.log(`node ${PORT}`, nodeTask);

            // executeTask(nodeTask);
            fnx.log(`listening on PORT/pID: ${PORT}`, process.pid)
        } catch (a) {
            console.log('error', a)
            process.kill(process.pid, 'SIGHUP');
            // process.exit(1)
        }
    })

})();