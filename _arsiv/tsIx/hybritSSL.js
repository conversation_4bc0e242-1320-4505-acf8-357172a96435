const technicalIndicators = require('technicalindicators');
technicalIndicators.setConfig('precision', 6);
const { PSAR, OBV, CCI, WMA, SMA, RSI, ATR, EMA, CrossUp, CrossDown,
    ROC, ADX, AwesomeOscillator, IchimokuCloud, MACD, StochasticRSI,
    TrueRange,
} = technicalIndicators;
const lHistory = -5
var tulind = require('tulind');
// console.log("Tulip Indicators version is:", tulind.version);
const HybritSSL = exports.HybritSSL = async (props) => {
    const {
        src = 'close', hlc, atrlen = 14, mult = 1, smoothing = 'SMA',
        //BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES
        maType = 'HMA', len1 = 200, //SSL1 / Baseline Type, SSL1 / Baseline Length 
        SSL2Type = 'JMA', len2 = 5, //SSL2 / Continuation Type, SSL 2 Length
        SSL3Type = 'HMA', len3 = 15, //EXIT Type, EXIT Length
        kidiv = 1, // Kijun MOD Divider
        useTrueRange = true, multy = 0.2, //Base Channel Multiplier
        symbol,
        jurik_phase = 3, //* Jurik (JMA) Only - Phase
        jurik_power = 1, //* Jurik (JMA) Only - Power
    } = props;

    //ATR upper - lower band    //ATR upper - lower band
    //ATR upper - lower band
    //ATR upper - lower band
    var atr_slen = await calcAtr({ marketData: hlc, period: atrlen, smoothing })
    var closeArr = hlc.close;
    let upper_band = [];
    let lower_band = [];
    // let closee = [];
    var stg_atr_slen = atr_slen.slice(atrlen * (-1)).reverse()
    var stg_close = closeArr.slice(atrlen * (-1)).reverse()
    for (let i = 0; i < stg_atr_slen.length; i++) {
        // closee.push(stg_close[i])
        upper_band.push(stg_atr_slen[i] * mult + stg_close[i]);
        lower_band.push(stg_close[i] - stg_atr_slen[i] * mult);
    }
    //ATR upper - lower band    //ATR upper - lower band
    //ATR upper - lower band
    //ATR upper - lower band

    ////BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES
    ////BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES
    ////BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES

    ///SSL 1 and SSL2
    var emaHigh = await fnMA({ type: maType, src: hlc.high, len: len1 })
    var emaLow = await fnMA({ type: maType, src: hlc.low, len: len1 })

    var maHigh = await fnMA({type: SSL2Type, src: hlc.high, len: len2, jurik_phase, jurik_power})
    var maLow = await fnMA({type: SSL2Type, src: hlc.low, len: len2, jurik_phase, jurik_power})

    ///EXIT
    var ExitHigh = await fnMA({type: SSL3Type, src: hlc.high, len: len3})
    var ExitLow = await fnMA({type: SSL3Type, src: hlc.low, len: len3})

    ///Keltner Baseline Channel
    var BBMC = await fnMA({ type: maType, src: hlc.close, len: len1 })
    var Keltma = await fnMA({type: maType, src: hlc.close, len: len1, symbol, srcI: 'keltma!'})
    
    
    let rangeList = [];
    let upperk = [];
    let lowerk = [];
    for (let i = 0; i < hlc.high.length - 1; i++) {
        rangeList.push(useTrueRange ? trueRange({
            high: hlc.high[i],
            low: hlc.low[i],
            close: hlc.close[i]
        }) : (hlc.high[i] - hlc.low[i]));
        // upperk.push(Keltma[i] + rangema * multy)
    }
    var rangema_stg = EMA.calculate({period: len1, values: rangeList})
    var rangema = Array.isArray(rangema_stg) && rangema_stg.slice(Keltma.length * -1) //.reverse()
    for (let i = 0; i < Keltma.length; i++) {
        upperk.push(Keltma[i] + rangema[i] * multy)
        lowerk.push(Keltma[i] - rangema[i] * multy)
    }

    //SSL1 VALUES
    let Hlv = [];
    let sslDown = [];
    let closeStg = hlc.close.slice(-1 * emaHigh.length);
    for (let i = 0; i < emaHigh.length; i++) {
        var hlv_stg = closeStg[i] > emaHigh[i] ? 1 : closeStg[i] < emaLow[i] ? -1 : (i !== 0 ? Hlv[i - 1] : 0)
        Hlv.push(hlv_stg)
        sslDown.push(Hlv[i] < 0 ? emaHigh[i] : emaLow[i])
    }

    //SSL2 VALUES
    let Hlv2 = [];
    let sslDown2 = [];
    let closeStg2 = hlc.close.slice(-1 * maHigh.length);
    for (let i = 0; i < maHigh.length; i++) {
        var hlv2_stg = closeStg2[i] > maHigh[i] ? 1 : closeStg2[i] < maLow[i] ? -1 : (i !== 0 ? Hlv2[i - 1] : 0)
        Hlv2.push(hlv2_stg)
        sslDown2.push(Hlv2[i] < 0 ? maHigh[i] : maLow[i])
    }

    //COLORS
    var color_bar = []
    var color_ssl1 = []
    let closeStg2s = hlc.close.slice(-1 * upperk.length);
    for (let i = 0; i < upperk.length; i++) {
        var color_bar_stg = closeStg2s[i] > upperk[i] ? 'blue' : closeStg2s[i] < lowerk[i] ? 'red' : 'gray'
        color_bar.push(color_bar_stg)
        var color_ssl1_stg = closeStg2s[i] > sslDown[i] ? 'blue' : closeStg2s[i] < sslDown[i] ? 'red' : 'na'
        color_ssl1.push(color_ssl1_stg)
    }

    ////BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES
    ////BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES
    ////BASELINE / SSL1 / SSL2 / EXIT MOVING AVERAGE VALUES


    return new Promise(async (resolve) => {
        var resp = {};
        resp.atrArr = stg_atr_slen.slice(lHistory); //[...atr_slen].slice(-15).reverse();
        resp.atr = [...atr_slen].slice(-1).pop();
        resp.SSL2s = sslDown2.slice(lHistory);
        resp.SSL2 = [...sslDown2].slice(-1).pop();
        resp.maBaseLines = BBMC.slice(lHistory);
        resp.maBaseLine = [...BBMC].slice(-1).pop();
        resp.baselineUpperChannels = upperk.slice(lHistory);
        resp.baselineUpperChannel = [...upperk].slice(-1).pop();
        resp.baselineLowerChannels = lowerk.slice(lHistory);
        resp.baselineLowerChannel = [...lowerk].slice(-1).pop();
        resp.SSL1s = sslDown.slice(lHistory);
        resp.SSL1 = [...sslDown].slice(-1).pop();
        resp.barColors = color_bar.slice(lHistory * 2);
        resp.barColor = [...color_bar].slice(-1).pop();
        resp.barColor2 = [...color_bar].slice(-2).toString();
        resp.SSL1_colors = color_ssl1.slice(lHistory*2);
        resp.SSL1_color = [...color_ssl1].slice(-1).pop();
        resp.SSL1_color2 = [...color_ssl1].slice(-2).toString();
        // resp.upper_band = upper_band;
        // resp.lower_band = lower_band;
        // resp.maHigh = maHigh
        // resp.maLow = maLow
        // resp.emaHigh = emaHigh;
        // resp.emaLow = emaLow;
        // resp.ExitHigh = ExitHigh;
        // resp.ExitLow = ExitLow;
        // resp.Keltma = Keltma;
        // resp.rangema = rangema;

        resolve(resp);
    });
}

const trueRange = ({ high, low, close }) => {
    var trueRange = Math.max(
        high - low,
        high - close,
        close - low
    );
    return trueRange;
}

const calcAtr = props => {
    const { marketData, period, smoothingX = 'WMA' } = props
    return new Promise(async (resolve) => {
        //list of true values
        let trueRangeList = [];
        for (let i = 1; i - 1 < marketData.high.length - 1; i++) {
            trueRangeList.push(trueRange({
                high: marketData.high[i],
                low: marketData.low[i],
                close: marketData.close[i]
            }));
        }
        // calculate ATR
        const ATRXX = (trueRangeList, timePeriod, smoothing = 'WMA') => {
            var ma; // = WMA.calculate({ period: timePeriod, values: [...trueRangeList] })
            if (smoothing == 'SMA') {
                ma = SMA.calculate({ period: timePeriod, values: [...trueRangeList] })
            } else if (smoothing == 'EMA') {
                ma = EMA.calculate({ period: timePeriod, values: [...trueRangeList] })
            } else if (smoothing == 'WMA') {
                ma = WMA.calculate({ period: timePeriod, values: [...trueRangeList] })
            }
            return ma;
        };
        const ATRx = ATRXX(trueRangeList, period, smoothingX)
        // fnxCore.logger.info('ATRx', ATRx)
        resolve(ATRx)

    })
}

const fnMA = (props) => {
    const {type, src, len, size = 33, srcI, symbol} = props;
    var resp = {};
    return new Promise(async (resolve) => {
        if(type == 'JMA') {
            const {jurik_phase, jurik_power} = props;
            var calc_stg = await calcJMA({
                values: src, jurik_phase, jurik_power, len
            });
            var calc = Array.isArray(calc_stg) && calc_stg.slice(size * (-1)) // .reverse()  ma(SSL2Type, high, len2)
            resp = calc
        } else if(type == 'HMA') {
            var calc_stg = await calcHMA({ values: src, len })
            var calc = Array.isArray(calc_stg[0]) && calc_stg[0].slice(size * (-1)) //.reverse()
            resp = calc
        }
        resolve(resp);
    });
}

const calcJMA = props => {
    /**
    * Calculate the JMA (Jurik-Moving-Average) based on this formula from everget tradingview indicator
    * https://www.tradingview.com/script/nZuBWW9j-Jurik-Moving-Average/
    */
    const { values, jurik_phase, jurik_power, len } = props;
    var phaseRatio = jurik_phase < -100 ? 0.5 : jurik_phase > 100 ? 2.5 : jurik_phase / 100 + 1.5
    var beta = 0.45 * (len - 1) / (0.45 * (len - 1) + 2);
    var alpha = Math.pow(beta, jurik_power)
    var ma1 = 0, det0 = 0, det1 = 0, ma2 = 0, jma = 0;
    var respo = []
    // 
    return new Promise(async (resolve) => {
        values.forEach(function (value, ix) {
            ma1 = (1 - alpha) * value + alpha * ma1;
            det0 = (value - ma1) * (1 - beta) + beta * det0;
            ma2 = ma1 + phaseRatio * det0;
            det1 = (ma2 - jma) * Math.pow(1 - alpha, 2) + Math.pow(alpha, 2) * det1;
            jma = jma + det1;
            respo.push(jma);
        });
        resolve(respo);
    });
}

const calcHMA = props => {
    const { values, len } = props;
    return new Promise(async (resolve) => {
        tulind.indicators.hma.indicator([values], [len], function (err, results) {
            resolve(results);
        });
    });
}