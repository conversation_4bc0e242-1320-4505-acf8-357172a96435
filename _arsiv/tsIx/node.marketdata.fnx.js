
const path = require('path');
const fnx = require('../../../nodes/_.functions');

const binance = exports.binance = {
    getTask: taskID => {
        let task = Array.isArray(tasksData) && tasksData.find(t => t.task_id = taskID);
        return task;
    },
    futuresDaily: async (pr = {}) => {
        const {task, binanceApi, db} = pr;
        try {
            var dt = Date.now();
            await fn.futuresDaily({ binanceApi, db, lastN: 5 });
            await fnx.logTransaction({
                db,
                service: 'binance.futuresDaily',
                isSuccess: true,
                dtstarted: (dt),
                dtcompleted: (Date.now()),
                task_timer: Date.now() - dt,
                status_desc: ''
            });
            return true
        } catch (e) {
            console.log('futuresDaily error', e)
            await fnx.logTransaction({
                db,
                service: 'binance.futuresDaily',
                isSuccess: false,
                dtstarted: new Date(dt),
                dtcompleted: new Date(Date.now()),
                task_timer: Date.now() - dt,
                status_desc: e.toString(),
            });
            return false
        }
    },
    futuresFundingRate: async (pr = {}) => {
        const {task, binanceApi, db} = pr;
        try {
            var dt = Date.now();
            await fn.futuresFundingRate({ 
                binanceApi, db, lastN: 5 
            });
            await fnx.logTransaction({
                db,
                service: 'binance.futuresFundingRate',
                isSuccess: true,
                dtstarted: (dt),
                dtcompleted: (Date.now()),
                task_timer: Date.now() - dt,
                status_desc: ''
            });
            return true
        } catch (e) {
            console.log('futuresFundingRate error', e)
            await fnx.logTransaction({
                db,
                service: 'binance.futuresFundingRate',
                isSuccess: false,
                dtstarted: new Date(dt),
                dtcompleted: new Date(Date.now()),
                task_timer: Date.now() - dt,
                status_desc: e.toString(),
            });
            return false
        }
    },
    futuresQuote: async (pr = {}) => {
        const {task, binanceApi, db} = pr;
        try {
            await fn.futuresQuote({ binanceApi, db, lastN: 5 });
            return true
        } catch (e) {
            console.log('futuresQuote error', e)
            return false
        };
    },
    futuresMarkPrice: async (pr = {}) => {
        const {task, binanceApi} = pr;
        try {
            await fn.futuresMarkPrice({ binanceApi });
            return true
        } catch (e) {
            console.log('futuresMarkPrice error', e)
            return false
        }
    },
    futuresPrices: async (pr = {}) => {
        const {task, binanceApi, db} = pr;
        try {
            await fn.futuresPrices({ db, binanceApi, savetoFile: true });
            return true
        } catch (e) {
            console.log('futuresPrices error', e)
            return false
        }
    },
    futuresExchangeInfo: async (pr = {}) => {
        const {task, binanceApi} = pr;
        try {
            await fn.futuresExchangeInfo({ binanceApi });
            return true
        } catch (e) {
            console.log('futuresExchangeInfo error', e)
            return false
        };
    },
};

const fn = {
    futuresDaily: async ({
        db,
        binanceApi, 
        cb = false, 
        savetoFile = false,
        save2db = true, 
        tableName = 'act_binance_futuresDaily',
        lastN = 5, 
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresDaily.json');
        return new Promise(async (resolve, reject) => {
            try {
                await fnx.clearOldData({ db, tableName, lastN, })
                let rawData = await binanceApi.futuresDaily();
                savetoFile && await fnx.savetoFile(rawData, filePath);
                save2db && await fnx.savetoDBJSONArr({db, rawData, tableName
                    , time1Field: 'openTime', time2Field: 'closeTime'})
            resolve(cb ? rawData : true)
            } catch (e) {
                reject (e)
            }
        });
    },
    futuresQuote: async ({
        db,
        binanceApi, 
        cb = false, 
        savetoFile = false,
        save2db = true, 
        tableName = 'act_binance_futuresQuote',
        lastN = 5, 
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresQuote.json');
        return new Promise(async (resolve, reject) => {
            try {
                await fnx.clearOldData({ db, tableName, lastN, })
                let rawData = await binanceApi.futuresQuote();
                savetoFile && await fnx.savetoFile(rawData, filePath);
                save2db && await fnx.savetoDBJSONArr({
                    db, rawData, tableName, time1Field: 'time',
                    debug: false,
                })
            resolve(cb ? rawData : true)
            } catch (e) {
                reject (e)
            }
        });
    },
    futuresFundingRate: async ({
        db,
        binanceApi,
        cb = false,
        lastN = 5,
        savetoFile = false,
        save2db = true,
        tableName = 'act_binance_futuresFundingRate',
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresFundingRate.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresFundingRate();
                savetoFile && await fnx.savetoFile(rawData, filePath);
                save2db && await fnx.savetoDBJSON({db, rawData, tableName, time1Field: 'fundingTime'})
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
    futuresMarkPrice: async ({cb = false, binanceApi}) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresMarkPrice.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresMarkPrice();
            await fnx.savetoFile(rawData, filePath);
            resolve(cb ? rawData : true)
            } catch (e) {
                reject (e)
            }
        });
    },
    futuresExchangeInfo: async ({cb = false, binanceApi}) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresExchangeInfo.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresExchangeInfo();
            await fnx.savetoFile(rawData, filePath);
            resolve(cb ? rawData : true)
            } catch (e) {
                reject (e)
            }
        });
    },
    futuresPrices: async ({
            db,
            binanceApi, 
            cb = false, 
            savetoFile = false,
            save2db = true, 
            tableName = 'act_binance_futuresPrices' 
        }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresPrices.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresPrices();
                savetoFile && await fnx.savetoFile(rawData, filePath);
                save2db && await fnx.savetoDBJSONArr({db, rawData, tableName})
                resolve(cb ? rawData : true)
            } catch (e) {
                reject (e)
            }
        });
    },
}