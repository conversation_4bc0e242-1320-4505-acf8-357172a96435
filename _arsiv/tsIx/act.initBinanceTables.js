
const { exec } = require('child_process');
const path = require('path');
const Database = require('better-sqlite3');
const db = new Database(path.resolve(__dirname, '../db/binance.db'), {  });
db.pragma('journal_mode = WAL');
// db.pragma('journal_mode = OFF');
// db.pragma('synchronous = OFF');
const args = process.argv.slice(2);
(async () => {
    let dt = Date.now(); // PRIMARY KEY
    db.exec(`
                DROP TABLE if exists act_binance_futuresDaily;
                create table act_binance_futuresDaily (
                    symbol text NOT NULL DEFAULT('')
                    ,priceChange        real
                    ,priceChangePercent real
                    ,weightedAvgPrice   real
                    ,lastPrice          real
                    ,lastQty            integer
                    ,openPrice          real
                    ,highPrice          real
                    ,lowPrice           real
                    ,volume             real
                    ,quoteVolume        real
                    ,openTime           integer
                    ,closeTime          integer
                    ,firstId            integer
                    ,lastId             integer
                    ,count              integer
                    ,batchid            integer not null DEFAULT(0),
                    openTimeHRF text not null DEFAULT(''),
                    closeTimeHRF text not null DEFAULT(''),
                    is_deleted bool not null DEFAULT(false),
                    dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                    dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                );

                DROP TABLE if exists act_binance_futuresFundingRate;
                create table act_binance_futuresFundingRate (
                    symbol text NOT NULL DEFAULT(''),
                    fundingRate text,
                    fundingTime integer,
                    fundingTimeHRF text not null DEFAULT(''),
                    batchid integer not null DEFAULT(0),
                    is_deleted bool not null DEFAULT(false),
                    dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                );

                DROP TABLE if exists act_binance_futuresQuote;
                create table act_binance_futuresQuote (
                    symbol text NOT NULL DEFAULT(''),
                    bidPrice text,
                    bidQty text,
                    askPrice text,
                    askQty text,
                    time integer,
                    timeHRF text not null DEFAULT(''),
                    batchid integer not null DEFAULT(0),
                    is_deleted bool not null DEFAULT(false),
                    dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                );

                DROP TABLE if exists log_binance_fetches;
                create table log_binance_fetches (
                    fnservice text NOT NULL,
                    is_success bool not null DEFAULT(false),
                    status_desc text not null DEFAULT(''),
                    dtstarted DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                    dtcompleted DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                    task_timer integer not null DEFAULT(0),
                    dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                    dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                );
                

            `);
    process.exit(0)
})();



/*

    createTables: (newdb) => {
        newdb.exec(`
        create table hero (
            hero_id int primary key not null,
            hero_name text not null,
            is_xman text not null,
            was_snapped text not null
        );
        insert into hero (hero_id, hero_name, is_xman, was_snapped)
            values (1, 'Spiderman', 'N', 'Y'),
                   (2, 'Tony Stark', 'N', 'N'),
                   (3, 'Jean Grey', 'Y', 'N');
    
        create table hero_power (
            hero_id int not null,
            hero_power text not null
        );
    
        insert into hero_power (hero_id, hero_power)
            values (1, 'Web Slinging'),
                   (1, 'Super Strength'),
                   (1, 'Total Nerd'),
                   (2, 'Total Nerd'),
                   (3, 'Telepathic Manipulation'),
                   (3, 'Astral Projection');
            `, ()  => {
                fndb.runQueries(newdb);
        });
    },   
    initTables: (dbConn) => {
        return new Promise(async (resolve, reject) => {
            try {
                dbConn.exec(`
                        create table core_nodes (
                            n_id int primary key not null,
                            process_id int not null,
                            port int not null,
                            task_id int not null,
                            task_name text not null,
                            isactive bool not null,
                            isdeleted bool not null
                        );
                        create table core_tasks (
                            task_id int primary key not null,
                            task_name text not null
                        );
                `);
                resolve(true)
            } catch (e) {
                fnx.log('fbdb query error', e);
                reject(e)
            }
        });
    },
    */