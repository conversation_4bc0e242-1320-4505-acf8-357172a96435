/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import { signIn, signOut, useSession } from 'next-auth/react'

import { appvars } from '@/lib/constants'
import * as React from 'react';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import ProTip from '@/_components/_tmp/ProtoTip';
import Link from '@/_components/_tmp/Link';
import Copyright from '@/_components/_tmp/Copyright';
import Header from '@/_components/components/core.header';

export default function Home(props) {
  const { data: session } = useSession();
  return (
    <>

      <Header />
      <Container maxWidth="sm">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
            Material UI - Next.js example
          </Typography>
          <Link href="/about" color="secondary">
            Go to the about page
          </Link>
          <ProTip />
          <div>
            <h1>Hello World</h1>
            {session && (
              <button onClick={() => signOut()}>Signout</button>
            )}
            {session && (
              <div>
                <small>Signed in as</small>
                <br />
                <strong>{session.user.email || session.user.name}</strong>
                <br />
                {JSON.stringify(session)}
                <br />
                {JSON.stringify(props.appvars)}
              </div>
            )}
          </div>

          <Copyright />
        </Box>
      </Container>

    </>
  )
}

export async function getServerSideProps() {
  return { props: { appvars } }
}

