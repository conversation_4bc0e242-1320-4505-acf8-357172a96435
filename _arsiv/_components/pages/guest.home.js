/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import { signIn, signOut, useSession } from 'next-auth/react'

import { appvars } from '@/lib/constants'
import * as React from 'react';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import ProTip from '@/_components/_tmp/ProtoTip';
import Copyright from '@/_components/_tmp/Copyright';
import Headerx from '@/_components/components/core.header';

import Header from "@/_components/components/header/Header";

export default function Home(props) {
    const { data: session } = useSession();
    return (
        <>
            <Header />
            <Container maxWidth="sm">
                <Box sx={{ my: 4 }}>
                    <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
                        Material UI - Next.js example
                    </Typography>
                    <ProTip />
                    <div>
                        <h1>Hello World</h1>
                        {(
                            <button onClick={() => signIn()}>SignIn</button>
                        )}
                    </div>

                    <Copyright />
                </Box>
            </Container>

        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

