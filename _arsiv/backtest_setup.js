/* eslint-disable react/display-name */
/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import { signIn, signOut, useSession } from 'next-auth/react'
import Head from 'next/head'

import { appvars } from '../../lib/constants'
import * as React from 'react';

export default function Home(props) {
    const { data: session } = useSession();
    return (
        <>
            <Head>
                <title>Gauss Algo</title>
            </Head>
            <UserBacktestSetup {...props} />
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}



import { useState, useEffect, useContext, Suspense, 
    useRef,
    forwardRef,
    useImperativeHandle, } from "react";
  import { useRouter } from 'next/router'
//   import Head from 'next/head'
//   import { signIn, signOut, useSession } from 'next-auth/react'
//   import { appvars } from '../../lib/constants'
  import { Box, Button, Container, Stack, Typography } from '@mui/material';
  import { red } from '@mui/material/colors';
  import Tab from '@mui/material/Tab';
  import TabContext from '@mui/lab/TabContext';
  import TabList from '@mui/lab/TabList';
  import TabPanel from '@mui/lab/TabPanel';
  import IconButton from '@mui/material/IconButton';
  
  import Card from '@mui/material/Card';
  import CardHeader from '@mui/material/CardHeader';
  import CardContent from '@mui/material/CardContent';
  import Avatar from '@mui/material/Avatar';
  import Divider from '@mui/material/Divider';
  import List from '@mui/material/List';
  import ListItem from '@mui/material/ListItem';
  import ListItemButton from '@mui/material/ListItemButton';
  import ListItemIcon from '@mui/material/ListItemIcon';
  import ListItemText from '@mui/material/ListItemText';
  import ArrowRight from '@mui/icons-material/ArrowRight';
  import Collapse from '@mui/material/Collapse';
  import Modal from '@mui/material/Modal'; 
  import LoadingButton from '@mui/lab/LoadingButton';
  
  import MoreVertIcon from '@mui/icons-material/MoreVert';
  import ArrowForwardRoundedIcon from '@mui/icons-material/ArrowForwardRounded';
  import AddchartIcon from '@mui/icons-material/Addchart';
  import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
  import AddToPhotosIcon from '@mui/icons-material/AddToPhotos';
  import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
  import DisabledByDefaultIcon from '@mui/icons-material/DisabledByDefault';
  import NotificationImportantRounded from '@mui/icons-material/NotificationImportantRounded';
  import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';
  import FileDownloadIcon from '@mui/icons-material/FileDownload';
  import CloseIcon from '@mui/icons-material/Close';
  
  import Intervals from '../../_components/pages/user.action.battle.intervals'
  import Pairs from '../../_components/pages/user.action.battle.pairs';
  import Indicators from '../../_components/pages/user.action.battle.indicators';
  import Trading from '../../_components/pages/user.action.battle.trading';
  
  
  import _ from 'lodash';
  
  import AppLayout from '../../lib/layouts/layout.user'
  import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

  const UserBacktestSetup = props => {
    const { ...rest } = props;
    const refModalFiles = useRef();
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false);
    const [battleLoader, setbacktestLoader] = useState(false);
    const [battleLoader2, setbacktestLoader2] = useState(false);
    const [backtestListLoader, setbacktestListLoader] = useState(false);
    const [activeBacktestData, setactiveBacktestData] = useState(false);
    const { status, data: session } = useSession({
      required: true,
      onUnauthenticated() {
        signIn();
      },
    })
  
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
  
    const [open, setOpen] = React.useState(false);
    const [battleParams, setbattleParamsFN] = React.useState({});
  
    const [sbattleParams, ssetbattleParamsFN] = useStickyState(BattleDefaultParameters, 'backtestParameters');
  
    const saveBattleParams = async () => {
      let data2Post = battleParams;
      let uri = '/api/pub/data/savebattleparameters'
      try {
        const res = await fetch(uri, {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + token,
            'X-Host': 'Subanet.com',
          },
          body: JSON.stringify(data2Post),
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          return
        }
  
        const datax = await res.json();
  
        if (!datax.error) {
          // console.log('resp', JSON.stringify(datax));
          alert('params saved!')
        } else {
          console.log('err desc', datax);
          alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
        }
      }
      catch (e) {
        console.log('e', e)
        alert('Error Code: 981', e)
      }
  
    }
    const listSavedBattleParams = async () => {
  
      let uri = '/api/pub/data/listsavebattleparameters'
      try {
        const res = await fetch(uri, {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + token,
            'X-Host': 'Subanet.com',
          },
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          return
        }
  
        const datax = await res.json();
  
        if (!datax.error) {
          refModalFiles && refModalFiles.current && refModalFiles.current.showModal(datax);
        } else {
          console.log('err desc', datax);
          alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
        }
      }
      catch (e) {
        console.log('e', e)
        alert('Error Code: 981', e)
      }
  
  
    }
    const showLocalcache = () => {
      let varx = window.localStorage.getItem('battleParameters');
      console.log('showLocalcache', varx)
    }
    useEffect(() => {
      const getList = async () => {
        colorMode.setCurrPage('/backtest/setup');
        // showLocalcache();
        drawerCollapsed && colorMode.collapseDrawer(true);
        let list = await battleList();
      }
      getList();
    }, []);
  
    useEffect(() => {
      if (!sbattleParams) {
        // console.log('no Params here!')
      } else {
        setbattleParamsFN(sbattleParams);
      }
    }, [sbattleParams])
  
    const clearCache = () => {
      window.localStorage.setItem('battleParameters', null);
      setbattleParamsFN({})
    }
    const backtestStart = async (battleParams, NG = false) => {
      let data2Post;
      if (NG) {
        data2Post = {
          "type": "battle",
          "dtCreated": new Date(Date.now()).toISOString(),
          "parameters" : {
            ...battleParams,
            "socketUse": true,
            "socketRefreshFrequency": 10000,
            "timeoutSource": 1500,
            "limit": battleParams.candleCounts,
  
          }
        };
      } else {
        data2Post = {
          "type": "battle",
          "dtCreated": new Date(Date.now()).toISOString(),
          ...battleParams} || {
          "type": "battle",
          "dtCreated": new Date(Date.now()).toISOString(),
          "parameters": {
            "pairs": ["BTCUSDT", "ETHUSDT"], //ok
            "intervals": ["1m", "5m"], //ok
            "battleInterval": "1m", //ok
            "socketUse": true, //ok
            "socketRefreshFrequency": 10000, //ok
            // "dataRefreshIntervals": {
            //   "1m": ['15,30,45 * * * * *'],
            //   "5m": ['20 */3 * * * *', '40 */2 * * * *'],
            // },
            "limit": 500, //ok
            "timeoutSource": 1500,
            "indicatorsWParams": [ //ok
              { id: 1,
                indicator: "ema",
                refName: "ema9",
                params: {length: 9, source: 'close', timeFrame: '1m',},
              },
              { id: 17,
                indicator: "ema",
                refName: "ema21",
                params: {length: 21, source: 'close', timeFrame: '1m',},
              },
              { id: 2,
                indicator: "sma",
                refName: "sma9",
                params: {length: 9, source: 'close', timeFrame: '1m',},
              },
              { id: 3,
                indicator: "ema",
                refName: "ema7_tf2",
                params: {length: 7, source: 'close', timeFrame: '5m',},
              },
              { id: 4,
                indicator: "rsi",
                refName: "rsi9",
                params: {length: 9, source: 'close', timeFrame: '1m', 
                  upperBand: 70, middleBand: 50, lowerBand: 10,
                  rsiSMAFastPeriod: 8, rsiSMASlowPeriod: 14
                },
              },
              { id: 5,
                indicator: "cci",
                refName: "cci20",
                params: {length: 20, source: 'close', timeFrame: '1m', 
                levelHigh: 100, levelLow: -100, SMAfastPeriod: 8
                },
              },
              { id: 6,
                indicator: "atr",
                refName: "atr14",
                params: {length: 14, source: 'close', timeFrame: '1m',},
              },
              { id: 7,
                indicator: "roc",
                refName: "roc9",
                params: {length: 9, source: 'close', timeFrame: '1m',},
              },
              { id: 8,
                indicator: "adx",
                refName: "adx14",
                params: {length: 14, source: 'close', timeFrame: '1m',},
              },
              { id: 9,
                indicator: "obv",
                refName: "obv19",
                params: {length: 19, source: 'close', timeFrame: '1m',},
              },
              { id: 10,
                indicator: "awesomeOscillator",
                refName: "ao14",
                params: {length: 14, source: 'close', timeFrame: '1m', fastPeriod: 5, slowPeriod: 34},
              },
              { id: 11,
                indicator: "psar",
                refName: "psar14",
                params: {length: 14, source: 'close', timeFrame: '1m', step: 0.02, max: 0.2},
              },
              { id: 12,
                indicator: "macd",
                refName: "macd14",
                params: {length: 14, source: 'close', timeFrame: '1m', 
                  fastPeriod: 5, slowPeriod: 8, signalPeriod: 3,
                  SimpleMAOscillator: false, SimpleMASignal: false
                },
              },
              {
                id: 13,
                indicator: "ichimoku",
                refName: "ichi14",
                params: {
                  length: 14, source: 'close', timeFrame: '1m',
                  conversionPeriod: 9, basePeriod: 26,
                  spanPeriod: 52, displacement: 26
                },
              },
              {
                id: 14,
                indicator: "srsi",
                refName: "srsi14",
                params: {
                  length: 14, source: 'close', timeFrame: '1m',
                  stochasticPeriod: 14, kPeriod: 3, dPeriod: 3
                },
              },
              {
                id: 15,
                indicator: "candlepatterns",
                refName: "cp14",
                params: {
                  length: 14, source: 'close', timeFrame: '1m'
                },
              },
              {
                id: 16,
                indicator: "kairi",
                refName: "kairi14",
                params: {
                  length: 14, source: 'close', timeFrame: '1m', 
                  movType: 'EMA', levelUpper: 10, levelLower: -10
                },
              },
              
            ],
            "tradingSetup": {
              "platforms": ["tradeSimulator"], // tradeSimulator | binanceTrader
              "entry": {
                "enterPosition": true,
                "direction": "long",
                "pozBudget": 70,
                "maxBudget": 370,
                "addNews": true,
                "additionalPozRatio": "0.05",
                "useAvgAsRefPrice": false,
                "waitForBarClose": true,
                "candleInterval": 3
              },
              "exit": {
                "takeProfit": "0.70",
                "stopLoss": "50",
                "useTrailingStop": false,
                "waitForBarClose": false,
                "useStopLossRatio": true,
                "stopLossRatio": "2"
              },
              "wallet": {
                "wMaxBudget": "400",
                "leverage": "7",
                "maxPair": "41",
                "pacalTPModeActive": false,
                "pacalTPTypeNetPNL": false,
                "pacalTPAmount": "4",
                "pacalSLAmount": "30"
              },
              "strategies": [
                1,
                2
              ],
              "takeprofit": 0,
              "stoploss": 0,
            }
          },
          "props": {}
        };
      }
  
      setbacktestLoader(true);
      let uri = '/api/pub/data/backtestStart'
      try {
        const res = await fetch(uri, {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + token,
            'X-Host': 'Subanet.com',
          },
          body: JSON.stringify(data2Post),
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          setbacktestLoader(false)
          return
        }
  
        const datax = await res.json()
        setbacktestLoader(false)
        await battleList();
  
        if (!datax.error) {
          console.log('backtestStart', datax)
          setbacktestLoader(false)
        } else {
          console.log('err desc', datax);
          setbacktestLoader(false)
          alert('backtest action failed! \n'); //JSON.stringify(values, null, 2)
        }
      }
      catch (e) {
        console.log('e', e)
        alert('Error Code: 981', e)
        setbacktestLoader(false)
      }
  
    }
  
    const battleStop = async ({purge = false}) => {
  
      setbacktestLoader2(true);
      let uri = '/api/pub/data/backtestStop'
      let pData = {battleData: activeBacktestData};
      pData.purge = purge;
      try {
        const res = await fetch(uri, {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + token,
            'X-Host': 'Subanet.com',
          },
          body: JSON.stringify(pData),
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          setbacktestLoader2(false)
          return
        }
  
        const datax = await res.json()
        setbacktestLoader2(false);
        await battleList();
  
        if (!datax.error) {
          setbacktestLoader2(false)
        } else {
          console.log('err desc', datax);
          setbacktestLoader2(false)
          alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
        }
      }
      catch (e) {
        console.log('battle stop e', e)
        alert('Error Code: 981', e)
        setbacktestLoader2(false)
      }
    }
  
    const battleList = async () => {
      setbacktestListLoader(true);
      let uri = '/api/pub/data/backtestList'
      try {
        const res = await fetch(uri, {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + token,
            'X-Host': 'Subanet.com',
          },
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          setbacktestListLoader(false)
          return
        }
  
        const datax = await res.json();
        setactiveBacktestData(datax);
        setbacktestListLoader(false)
  
        if (!datax.error) {
          setbacktestListLoader(false)
        } else {
          console.log('err desc', datax);
          setbacktestListLoader(false)
          alert('backtest action failed! \n'); //JSON.stringify(values, null, 2)
        }
      }
      catch (e) {
        console.log('e', e)
        alert('Error Code: 981', e)
        setbacktestListLoader(false)
      }
  
    }
  
    const setbattleParams = val => {
      let currV = JSON.parse(JSON.stringify(battleParams));
      let nVal = {
        ...currV,
      };
      let {fname, fvar} = val;
      nVal[fname] = fvar;
      console.log('set backtestParams', battleParams, nVal)
      setbattleParamsFN(nVal);
      ssetbattleParamsFN(nVal);
    }
  
    const hardReset = async (w) => {
      setbacktestLoader2(true);
      let uri = '/api/pub/data/backtesthardreset' + '?1=1'
      uri += w ? '&f=1' : '';
      try {
        const res = await fetch(uri, {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer ' + token,
            'X-Host': 'Subanet.com',
          },
          body: JSON.stringify({pData: true}),
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          setbacktestLoader2(false)
          return
        }
  
        const datax = await res.json()
        setbacktestLoader2(false);
  
        if (!datax.error) {
          setbacktestLoader2(false)
        } else {
          console.log('err desc', datax);
          setbacktestLoader2(false)
          alert('backtest action failed! \n'); //JSON.stringify(values, null, 2)
        }
      }
      catch (e) {
        console.log('hard reset e', e)
        alert('Error Code: 981', e)
        setbacktestLoader2(false)
      }
    }
    return (
      <>
        <Head>
          <title>Gauss Algo - Backtest</title>
        </Head>
        <AppLayout session={session} {...props}
          pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
          pgBread={<span>x</span>}
        >
          <Stack className="" sx={{mt: '20px'}}>
            {/* <Input /> */}
            <Box sx={{
              m: 2,
            }}>
  
              <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h5" component="div">
                    {bull}Backtest&nbsp;{bull}&nbsp;
                    {/* <Chip variant="filled" color={'primary'} label={'%'} /> */}
                  </Typography>
                </Box>
                <Stack sx={{ flexDirection: 'row' }}>
                  <Box sx={{ borderWidth: 0, p: 0, pb: 0, borderRightWidth: 0.5,  }}>
                    <Typography sx={{ fontSize: 11, px: 1, fontStyle: 'italic' }}>Cache: </Typography>
                    <Stack sx={{ flex: 1, flexDirection: 'row', alignItems: 'center', }}>
                      <LoadingButton title="Clear" onClick={clearCache} sx={{ p: 0, m: 0 }}>Clear</LoadingButton>
                      <Button onClick={showLocalcache} sx={{ p: 0, m: 0 }}>View</Button>
                    </Stack>
                  </Box>
  
                  <Box sx={{ borderWidth: 0, p: 0, pb: 0, ml: 1 }}>
                    <Typography sx={{ fontSize: 11, px: 1, fontStyle: 'italic', minWidth: '200px' }}>Backtest: </Typography>
                    <Stack sx={{ borderRightWidth: 0.5, flex: 1, flexDirection: 'row', alignItems: 'center', }}>
                      {(
                        Array.isArray(activeBacktestData) &&
                        activeBacktestData.length === 0)
                        || (
                          !activeBacktestData)
                        ||
                        (
                          typeof activeBacktestData === 'object' && activeBacktestData[0] == undefined) && (
                          <>
                            <LoadingButton title="Start" onClick={() => backtestStart(battleParams, true)} sx={{ p: 0, m: 0 }}>Start</LoadingButton>
                            <LoadingButton loading={battleLoader2} disabled={battleLoader2} sx={{ p: 0, m: 0, ml: 1 }} onClick={() => battleStop({ purge: true })}>
                              Purge DB
                            </LoadingButton>
                          </>
  
                        )}
                      {activeBacktestData && typeof activeBacktestData === 'object' && activeBacktestData[0] && activeBacktestData[0]['battleID'] !== undefined && (
                        <>
                          {/* {JSON.stringify(activeBacktestData[0])} */}
                          <LoadingButton loading={battleLoader2} disabled={battleLoader2} sx={{ p: 0, m: 0 }} onClick={() => battleStop({})}>
                            Stop
                          </LoadingButton>
                          <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} loading={battleLoader2} disabled={battleLoader2} onClick={() => battleStop({ purge: true })}>
                            Stop w Purge
                          </LoadingButton>
                          <br />
                        </>
                      )}
                      <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => hardReset()}>
                        Reset
                      </LoadingButton>
                      <LoadingButton sx={{ p: 0, m: 0, ml: 1, }}  onClick={() => hardReset('f')}>
                        FF
                      </LoadingButton>
                      {/* <Button onClick={showLocalcache} sx={{ p: 0, m: 0 }}>&nbsp;</Button> */}
                    </Stack>
                  </Box>
                </Stack>
              </Stack>
  
              <hr />
              <Box
                sx={{
                  bgcolor: open ? 'rgba(255, 255, 255, 0.2)' : null,
                  pb: open ? 0 : 0,
                }}
              >
                <ListItemButton
                  alignItems="flex-start"
                  onClick={() => setOpen(!open)}
                  sx={{
                    px: 3,
                    pt: 2.5,
                    pb: open ? 0 : 0,
                    bgcolor: open ? '#BFCFE7' : '#F8EDFF',
                    '&:hover, &:focus': { '& svg': { opacity: open ? 1 : 0 } },
                  }}
                >
                  <ListItemText
                    primary="SET BACKTEST PARAMETERS"
                    primaryTypographyProps={{
                      fontSize: 15,
                      fontWeight: 'medium',
                      lineHeight: '20px',
                      mb: '2px',
                    }}
                    secondary="&nbsp;"
                    secondaryTypographyProps={{
                      noWrap: true,
                      fontSize: 12,
                      lineHeight: '16px',
                      color: open ? 'rgba(0,0,0,0)' : 'rgba(0,0,0,0.5)',
                    }}
                    sx={{ my: 0 }}
                  />
                  <KeyboardArrowDown
                    sx={{
                      mr: -1,
                      opacity: 0,
                      transform: open ? 'rotate(-180deg)' : 'rotate(0)',
                      transition: '0.2s',
                    }}
                  />
                </ListItemButton>
                
                <ShowSavedFiles ref={refModalFiles} />
  
                <Collapse in={!open}>
                  <BattleParams {...props} 
                    battleParams={battleParams} 
                    setbattleParams={setbattleParams}
                    listSavedBattleParams={listSavedBattleParams}
                    saveBattleParams={saveBattleParams} />
                </Collapse>
                
              </Box>
  
              <br />
              {/* <BattleParams {...props} /> */}
              <Box>
                {activeBacktestData && typeof activeBacktestData === 'object' && activeBacktestData[0] && activeBacktestData[0]['battleID'] !== undefined && (
                  <>
                  {/* {JSON.stringify(activeBacktestData[0])} */}
                  <LoadingButton loading={battleLoader2} disabled={battleLoader2} variant="outlined" onClick={() => battleStop({})}>
                    Stop Battle  
                  </LoadingButton>
                  <LoadingButton sx={{marginLeft: 5}} loading={battleLoader2} disabled={battleLoader2} variant="outlined" onClick={() => battleStop({purge: true})}>
                    Stop Battle w Purge 
                  </LoadingButton>
                  <br />
                  </>
                )}
                {/* <LoadingButton loading={battleLoader2} disabled={battleLoader2} variant="outlined" onClick={() => battleStop({})}>
                  Stop Battle  
                </LoadingButton> */}
              </Box>
              <br />
            </Box>
          </Stack>
        </AppLayout>
      </>
    )
  }


const bull = (
    <Box
        component="span"
        sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
        •
    </Box>
  );
  
  const BattleParams = props => {
    const { activeBacktestData, user } = props;
    const { token, refreshToken } = user ? user : {};
    const [value, setValue] = React.useState('1');
  
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
  
    return (
      <>
        <Box sx={{ width: '100%', typography: 'body1' }}>
          <TabContext value={value}>
            <Stack sx={{ borderBottom: 1, borderColor: 'divider', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <TabList onChange={handleChange} aria-label="lab API tabs example">
                <Tab label="Base" value="1" />
                <Tab label="Indicators" value="2" />
                <Tab label="Rulesets" value="3" />
                <Tab label="Trading" value="4" />
                <Tab label="Summary" value="5" />
              </TabList>
              <Stack sx={{mx: 2, flexDirection: 'row', alignItems: 'center'}}>
  
              <Typography sx={{fontSize: '11px'}}>Parameters:&nbsp;&nbsp; </Typography>
              <LoadingButton sx={{ p: 0, m: 0, ml: 1, }}  onClick={() => props.listSavedBattleParams()}>
                        Load 
                      </LoadingButton>
                      &nbsp;&nbsp;
                      <LoadingButton sx={{ p: 0, m: 0, ml: 1, }}  onClick={() => props.saveBattleParams()}>
                        Save 
                      </LoadingButton>
  
              </Stack>
            </Stack>
            <TabPanel value="1"><BattleParamsPairs {...props} /></TabPanel>
            <TabPanel value="2"><Indicators {...props} /></TabPanel>
            <TabPanel value="3">
              <Suspense fallback={<div>loading...</div>}>
                <Strategies {...props} />
              </Suspense>
              </TabPanel>
            <TabPanel value="4">
              <Suspense fallback={<div>loading...</div>}>
                <Trading {...props} />
              </Suspense>
            </TabPanel>
            <TabPanel value="5">
              <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                navigator.clipboard.writeText(JSON.stringify(props.battleParams));
  
              }}>
                <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                  <code style={{fontSize: '12px'}}>{JSON.stringify(props.battleParams, null, 4)}</code>
                </pre>
              </Box>
            </TabPanel>
          </TabContext>
        </Box>
      </>
    )
  }
  
  const BattleParamsPairs = props => {
    const setParameters = vals => { 
      props.setbattleParams && props.setbattleParams(vals);
    } 
    return (
      <>
        <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
            <Typography variant="button" display="block" gutterBottom>
              Intervals 
            </Typography>
          </Box>
  
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
            <Intervals {...props} multi={true} callbackFN={setParameters} />
          </Box>
        </Stack>
  
        <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
            <Typography variant="button" display="block" gutterBottom>
            Battle Interval
            </Typography>
          </Box>
  
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
            <Intervals {...props} candleCounts={true} callbackFN={setParameters} />
          </Box>
        </Stack>
  
        <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
            <Typography variant="button" display="block" gutterBottom>
              Pairs
            </Typography>
          </Box>
  
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
            <Pairs {...props} callbackFN={setParameters} />
          
          </Box>
        </Stack>
  
      </>
    )
  }
  
  function useStickyState(defaultValue, key) {
    const [value, setValue] = React.useState(defaultValue);
    // console.log('useStickyState', defaultValue, key)
    React.useEffect(() => {
      const stickyValue = window.localStorage.getItem(key);
      if (stickyValue && stickyValue !== 'null') {
        // console.log('stickyValue2', JSON.parse(stickyValue))
        setValue(JSON.parse(stickyValue));
      } else {
        const fData = async (vx = {}) => {
          window.localStorage.setItem(key, JSON.stringify(vx));
          setValue(JSON.parse(JSON.stringify(vx)));
        }
        fData(defaultValue);
      }
    }, [key]);
  
    React.useEffect(() => {
      window.localStorage.setItem(key, JSON.stringify(value));
    }, [key, value]);
  
    return [value, setValue];
  }
  
  const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));
  const Strategies = props => {
    const refShowRuleSet = useRef();
    const [data, setdata] = React.useState([]);
    const [selected, setSelected] = useState([]);
    const [selectedIndicators, setselectedIndicators] = useState([]);
    const [indicators, setindicators] = React.useState([]);
  
    function fnFetchIx() {
      return new Promise(async (resolve) => {
        try {
          var uri = '/api/pub/data/indicatorsandparams'
          // console.log(uri)
          const data = await fetcher(uri)
          resolve(data.data)
        } catch (e) {
          console.log('fetch err indicatorsandparams', e)
        }
      });
    }
    function BattleParamConverter(vx) {
      let resp = []
      vx.map(v => {
        let params = {};
        for (let p in v.params) {
          params[p] = v.params[p].default
        }
  
        let a = {
          id: v.id,
          indicator: v.indicator,
          refName: params.refName,
          battleParams: params
        }
        resp.push(a)
      })
      return resp
    }
  
  
    function fnFetch() {
      return new Promise(async (resolve) => {
        try {
          var uri = '/api/pub/data/rulesetsgroup'
          // console.log(uri)
          const data = await fetcher(uri)
          let data2Proc = Array.isArray(data?.data?.data) ? data?.data?.data : [];
          data2Proc.map(a => a.ruleSet = a.ruleSet && typeof a.ruleSet !== 'object' ? JSON.parse(a.ruleSet) : a.ruleSet);
          data2Proc.map(d => d.indicators = getIndicatorsInRuleSet(d));
          resolve(data2Proc)
        } catch (e) {
          console.log('fetch err rulesetsgroup', e)
        }
      });
    }
  
  
    useEffect(() => {
      const getX = async () => {
        let ixx = await fnFetch();
        setdata(ixx);
      };
      const getIX = async () => {
        let ixx = await fnFetchIx();
        setindicators(ixx);
      };
      getX();
      getIX();
      // console.log('props strate', JSON.stringify(props));
    }, []);
  
    // useEffect(() => {
    //   console.log('selected', selected)
    // }, [selected]);
  
  
  
    useEffect(() => {
      if (props.battleParams) {
        const InitBoxes = async () => {
          let arrX = props.battleParams.indicatorsWParams
          let ax = Array.isArray(arrX) && [...new Set(arrX.map(a => a.indicator))];
          // console.log('1battleParams setselectedIndicators', ax);
          setselectedIndicators(ax);
        }
        InitBoxes();
      }
    }, [])
  
  
    useEffect(() => {
      if (props.battleParams) {
        let parami = props.battleParams.rulesets
        const InitBoxes = async () => {
          let arrX = props.battleParams.indicatorsWParams
          let ax = Array.isArray(arrX) && [...new Set(arrX.map(a => a.indicator))];
          // console.log('2battleParams setselectedIndicators', ax);
          setSelected(parami);
          setselectedIndicators(ax);
        }
        parami && !_.isEqual(parami, selected) && InitBoxes();
      }
    }, [props.battleParams])
  
  
    useEffect(() => {
      // console.log('eff selectedIndicators data', data, selectedIndicators);
      if (Array.isArray(selectedIndicators) && selectedIndicators.length !== 0 && data.length !== 0) {
  
        let arrBase = JSON.parse(JSON.stringify(data));
        arrBase.map(a => a.avail = a.indicators.map(ai => selectedIndicators.includes(ai)).every(arr => arr === true));
        !_.isEqual(data, arrBase) && setdata(arrBase);
        // !_.isEqual(data, arrBase) && console.log('set', arrBase);
  
        let arrBaseS = JSON.parse(JSON.stringify(selected));
        arrBaseS.map(a => a.avail = a.indicators.map(ai => selectedIndicators.includes(ai)).every(arr => arr === true));
        !_.isEqual(selected, arrBaseS) && setSelected(arrBaseS);
  
      }
  
    }, [selectedIndicators, data])
  
    const getIndicatorsInRuleSets = (rulesetJson, setRuleSet = true) => {
      let arr = rulesetJson
      setRuleSet && Array.isArray(arr) && arr.map(a => a.ruleSet = JSON.parse(a.ruleSet));
      let ind = [];
      Array.isArray(arr) && arr.map(a => a.ruleSet.map(xx => xx.criteria.map(cc => ind.push(cc))))
      let inx = [...new Set(ind.map(r => r.indicator))];
      return inx;
    };
  
    const getIndicatorsInRuleSet = (rulesetJson, withRefs = false) => {
      let arr = rulesetJson
      let ind = [];
      arr.ruleSet.map(xx => xx.criteria.map(cc => ind.push(cc)));
      let inx = [...new Set(ind.map(r => !withRefs ? r.indicator : {
        indicator: r.indicator, indicatorRef: r.indicatorRef
      }))];
      return inx;
    }
  
    const AddRuleSet = inx => {
      let stg = [...selected];
      let uTaskIndex = stg.findIndex(t => t.rsgID == inx.rsgID);
      if (uTaskIndex < 0) {
        inx.id = Date.now() + Math.floor(Math.random() * 10);
        // inx.ruleSet = inx.ruleSet ? JSON.parse(JSON.s) : [];
        stg = [...stg, inx];
        setSelected(stg);
  
        let aIX = inx; // setPostData(inx);
        aIX.ruleSet = aIX.ruleSet && typeof aIX.ruleSet !== 'object' ? JSON.parse(aIX.ruleSet) : aIX.ruleSet
  
        let currBIX = Array.isArray(props.battleParams.rulesets) ? [...props.battleParams.rulesets] : [];
        currBIX.push(aIX);
        props.setbattleParams && props.setbattleParams({ fname: 'rulesets', fvar: currBIX });
  
      }
    }
  
    const RemoveRuleSet = inx => {
      let stg = [...selected];
      let uTaskIndex = stg.findIndex(t => t.id == inx.id);
      stg.splice(uTaskIndex, 1);
      setSelected([...stg]);
      props.setbattleParams && props.setbattleParams({ fname: 'rulesets', fvar: stg });
    }
  
    const viewRuleset = inx => {
      inx.ruleSet = inx.ruleSet ? JSON.parse(JSON.stringify(inx.ruleSet)) : [];
      refShowRuleSet.current && refShowRuleSet.current.showRuleset(inx)
    }
  
    const isRulesetSelected = rux => {
      // console.log('selected rules', selected, rux);
      let tmp = selected.map(s => s.rsgID);
      let resp = tmp.includes(rux.rsgID);
      return resp;
    }
  
    const addIndicators = s => {
      if (props.battleParams) {
        let currIX = props.battleParams?.indicatorsWParams;
        let reqInx = getIndicatorsInRuleSet(s, true);
        let d2Push = [];
  
        if (Array.isArray(reqInx)) {
          reqInx.map(r => {
            let ixData = indicators.find(ix => ix.indicator == r.indicator);
            let params = {};
            for (let p in ixData.params) {
              params[p] = ixData.params[p].default
            };
  
            let battleIXData = {
              id: (+new Date * Math.random()).toString(36).substring(0, 6),
              indicator: r.indicator,
              refName: r.indicatorRef,
              battleParams: params
            }
            d2Push.push(battleIXData)
          });
        };
        // console.log('currIX', currIX);
        // console.log('d2Push', d2Push);
        if (Array.isArray(currIX)) {
          currIX = [...currIX, ...d2Push];
        } else {
          currIX = [...d2Push];
        }
        props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: currIX } );
        console.log('currIX2B', currIX);
  
      } else {
        alert('battleParams x?')
      }
  
      return true;
    }
  
    return (
      <>
        <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 250, width: 250 }}>
            <Card sx={{ maxWidth: 345 }}>
              <CardHeader
                sx={{ background: '#ffddaa' }}
                avatar={
                  <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                    <NotificationImportantRounded />
                  </Avatar>
                }
                action={
                  <IconButton aria-label="settings">
                    <MoreVertIcon />
                  </IconButton>
                }
                title="Rulesets"
                subheader="Select rulesets"
              />
              <Divider />
              <CardContent sx={{ padding: 0 }}>
                <List
                  sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}
                  component="nav"
                  aria-labelledby="nested-list-subheader"
                  dense={true}
                >
                  {Array.isArray(data) && data.map((i, ix) => {
                    // let indx = getIndicatorsInRuleSet(i);
                    // console.log('isRulesetSelected', isRulesetSelected(i));
                    return (
                      <ListItemButton key={ix.toString()} sx={{backgroundColor: isRulesetSelected(i) ? '#ffcc00' : null}}>
  
                        <ListItemIcon style={{ minWidth: '20px' }}>
                          {i?.avail && !isRulesetSelected(i) && <ArrowForwardRoundedIcon fontSize="10" />}
                          {i?.avail && isRulesetSelected(i) && <ArrowRight fontSize="10" />}
                          {!i?.avail && (
                            <DisabledByDefaultIcon sx={{ width: 18 }} />
                          )}
                        </ListItemIcon>
                        <ListItemText onClick={() => viewRuleset(i)} primary={i.rulesetName} />
                        <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                          <RemoveRedEyeIcon sx={{ width: 16 }} onClick={() => viewRuleset(i)} />
                        </IconButton>
                        &nbsp;
                        {true && ( //i?.avail
                          <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                            <AddToPhotosIcon sx={{ width: 18 }} onClick={() => AddRuleSet(i)} />
                          </IconButton>
                        )}
                        <Divider />
                      </ListItemButton>
                    )
                  })}
                </List>
              </CardContent>
              <Divider />
              {/* <Typography variant="caption">unavailable rulesets!</Typography>
              <br />
              <Divider />
              <Typography variant="caption">add new ruleset</Typography> */}
            </Card>
          </Box>
  
          <Box sx={{ borderBottom: 1, borderLeftWidth: 1, pl: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, minWidth: '300px' }}>
            <Typography variant="button" bgcolor={red[100]} sx={{ px: 2 }} display="block" gutterBottom>
              Selected Rulesets {selected && Array.isArray(selected) && selected.length !== 0 && selected.length}
            </Typography>
            <Divider />
  
            <List
              sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}
              component="nav"
              aria-labelledby="nested-list-subheader"
              dense={true}
            >
              {Array.isArray(selected) && selected.length !== 0 && selected.map((s, ix) => {
                return (
  
                  <ListItemButton key={ix.toString()} sx={{ bgcolor: s.avail ? 'background.paper' : 'red' }}>
                    <ListItemText onClick={() => viewRuleset(s)} primary={s.rulesetName} secondary={s.avail ? '' : 'indicators are not matching'} />
                    
                    {!s.avail && (
                      <>
                        &nbsp;&nbsp;
                        <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                          <AddchartIcon sx={{ width: 22 }} onClick={() => addIndicators(s)} />
                        </IconButton>
                        &nbsp;&nbsp;
                      </>
                    )}<IconButton aria-label="settings" sx={{ padding: '4px' }}>
                      <RemoveRedEyeIcon sx={{ width: 16 }} onClick={() => viewRuleset(s)} />
                    </IconButton>
                    <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                      <DeleteForeverIcon sx={{ width: 22 }} onClick={() => RemoveRuleSet(s)} />
                    </IconButton>
                  </ListItemButton>
                )
              })}
            </List>
          </Box>
  
          <Box sx={{ borderBottom: 1, borderLeftWidth: 1, pl: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
  
            <ShowStrategyRules ref={refShowRuleSet} />
  {JSON.stringify(selectedIndicators)}
          </Box>
        </Stack>
      </>
    )
  }
  
  const ShowStrategyRules = forwardRef((props, ref) => {
    const [rulesetData, setrulesetData] = useState(false)
    useImperativeHandle(ref, () => ({
        async showRuleset(ix) {
            ix.ruleSet = ix.ruleSet && typeof ix.ruleSet !== 'object' ? JSON.parse(ix.ruleSet) : ix.ruleSet
            setrulesetData({...ix})
        },
    }));
    return (
      <>
        <Typography onClick={() => setrulesetData(false)} variant="button" bgcolor={red[100]} sx={{ px: 2 }} display="block" gutterBottom>
          Ruleset Info {rulesetData ? ' / X' : ''}
        </Typography>
        {rulesetData ? '' + rulesetData?.rulesetName : ''}
        <Divider />
  
        <Box sx={{ minWidth: '400px', overflow: 'auto', maxHeight: '450px', m: 0, wordBreak: 'break-all' }} onClick={() => {
          navigator.clipboard.writeText(JSON.stringify(rulesetData));
        }}>
          {rulesetData && <pre sx={{ maxWidth: '400px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
            <code className='text-xs' style={{ fontSize: '11px' }}>{JSON.stringify(rulesetData, null, 4)}</code>
          </pre>}
        </Box>
  
      </>
    )
  });
  
  
  const BattleDefaultParameters = {
    "pairs": [
      "BTCUSDT",
      "ETHUSDT",
      "SOLUSDT",
      "BTCUSDC",
      "DOGEUSDT",
      "ENAUSDT",
      "WIFUSDT",
      "ONGUSDT",
      "XRPUSDT",
      "TONUSDT",
      "SAGAUSDT",
      "ETHUSDC",
      "ORDIUSDT",
      "BOMEUSDT",
      "1000PEPEUSDT",
      "BNBUSDT",
      "ONTUSDT",
      "JTOUSDT",
      "TIAUSDT",
      "WLDUSDT",
      "AVAXUSDT",
      "BCHUSDT",
      "ADAUSDT",
      "SUIUSDT",
      "1000SHIBUSDT",
      "MATICUSDT",
      "NEARUSDT",
      "FILUSDT"
    ],
    "candleCounts": 300,
    "intervals": [
      "5m",
      "1m"
    ],
    "battleInterval": [
      "1m"
    ],
    "indicatorsWParams": [
      {
        "id": 1714940724602,
        "indicator": "rsi",
        "refName": "rsiRef",
        "battleParams": {
          "refName": "rsiRef",
          "length": 9,
          "source": "close",
          "timeFrame": "1m",
          "upperBand": 70,
          "middleBand": 50,
          "lowerBand": 10,
          "rsiSMAFastPeriod": 8,
          "rsiSMASlowPeriod": 14
        }
      },
      {
        "id": 1714940753917,
        "indicator": "ema",
        "refName": "emaRef_1m",
        "battleParams": {
          "refName": "emaRef_1m",
          "length": 9,
          "source": "close",
          "timeFrame": "1m"
        }
      },
      {
        "id": 1714940762781,
        "indicator": "ema",
        "refName": "emaRef_5m",
        "battleParams": {
          "length": 9,
          "source": "close",
          "timeFrame": "5m",
          "refName": "emaRef_5m"
        }
      }
    ],
    "rulesets": [
      {
        "rsgID": "1714815539538-bn53ea",
        "rulesetName": "Buy_rsi_ema1m_ema5m",
        "direction": "long",
        "cond": 0,
        "ruleSet": [
          {
            "id": "RuleSet1714815328746",
            "indicators": [
              "rsi"
            ],
            "criteria": [
              {
                "id": "Rule1714815336121",
                "name": "RSI Kriteri",
                "indicator": "rsi",
                "indicatorRef": "rsiRef",
                "item": "indicatorValue",
                "rule": "<40"
              },
              {
                "id": "Rule1714815358545",
                "name": "RSI Kriteri 2",
                "indicator": "rsi",
                "indicatorRef": "rsiRef",
                "item": "rsiSMAFast",
                "rule": "<40"
              }
            ],
            "name": "ruleset1",
            "key": "ruleset1_key",
            "cond": 1
          },
          {
            "id": "RuleSet1714815422961",
            "indicators": [
              "ema"
            ],
            "criteria": [
              {
                "id": "Rule1714815435893",
                "name": "ema1m",
                "indicator": "ema",
                "indicatorRef": "emaRef_1m",
                "item": "emaTrend",
                "rule": "== `Up`"
              },
              {
                "id": "Rule1714815499850",
                "name": "ema5m",
                "indicator": "ema",
                "indicatorRef": "emaRef_5m",
                "item": "emaTrend",
                "rule": "== `Up`"
              }
            ],
            "name": "ruleset2_ema",
            "key": "ruleset2_ema_key",
            "cond": 1
          }
        ],
        "is_deleted": 0,
        "dtupdated": "2024-05-04 09:38:59",
        "indicators": [
          "rsi",
          "ema"
        ],
        "id": 1714940712506
      }
    ],
    "trading": {
      "entry": {
        "enterPositon": true,
        "actOnBarClose": true,
        "direction": "long",
        "positionBudget": 100,
        "positionMaxBudget": 400,
        "addAdditionPosition": true,
        "additionPositionPercentage": 1,
        "additionPosRefPriceIsAvg": true,
        "additionPositionCandleInterval": 3,
        "useMartingaleVar": false
      },
      "exit": {
        "takeProfitRatio": 2,
        "stopLoss": 50,
        "stopLossUsePercentage": true,
        "stopLossPercentage": 4,
        "useTrailingStop": true,
        "actOnBarClose": true
      },
      "wallet": {
        "usePacalMode": true,
        "walletBudget": 400,
        "walletLeverage": 20,
        "pacalTakeProfitUSD": 100,
        "pacalStopLossUSD": 400,
        "pacalMaxPairs": 20
      }
    }
  }
  
  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4,
  };
  
  
  const ShowSavedFiles = forwardRef((props, ref) => {
    const [open, setOpen] = React.useState(false);
    const [filelist, setfilelist] = React.useState([]);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);
    useImperativeHandle(ref, () => ({
      async showModal(files) {
        if (files && Array.isArray(files.data)) {
          setfilelist(files.data)
          handleOpen();
        } else {
          alert('files list error')
        }
      },
    }));
    const fetchFile = fl => {
      return new Promise(async (resolve, reject) => {
        let uri = '/api/pub/data/loadbattleparameters?file=' + fl
        try {
          const res = await fetch(uri, {
            method: 'GET',
            headers: {
              'X-Host': 'Subanet.com',
            },
          })
          if (!res.ok) {
            var message = `An error has occured: ${res.status} - ${res.statusText}`;
            alert(message);
            reject(false);
          }
          const datax = await res.json();
          if (!datax.error) {
            // console.log('datax', datax.filedata);
            let value = datax.filedata.data;
            resolve(value)
  
          } else {
            console.log('err desc', datax);
            alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            reject(false);
          }
        }
        catch (e) {
          console.log('e', e)
          alert('Error Code: 981', e)
          reject(false);
        }
  
      });
    }
    const viewFile = async (file) => {
      console.log('loadFile', file);
      let value = await fetchFile(file);
      console.log(value);
    };
    const loadFile = async (file) => {
      console.log('loadFile', file);
      let value = await fetchFile(file);
      window.localStorage.setItem('backtestParameters', JSON.stringify(value));
      setTimeout(function () {
        window.location.reload(1);
      }, 300);
      // 
    };
    const deleteFile = async (file) => {
      let uri = '/api/pub/data/deletebattleparametersfile?file=' + file
      if (confirm("Delete file! - " + file)) { 
  
        try {
          const res = await fetch(uri, {
            method: 'GET',
            headers: {
              'X-Host': 'Subanet.com',
            },
          })
          if (!res.ok) {
            var message = `An error has occured: ${res.status} - ${res.statusText}`;
            alert(message);
            return
          }
          const datax = await res.json();
          if (!datax.error) {
            // console.log('datax', datax.filedata);
            handleClose();
            return
          } else {
            console.log('err desc', datax);
            alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            return
          }
        }
        catch (e) {
          console.log('e', e)
          alert('Error Code: 981', e)
          return
        }
  
      } else {
        console.log('cancelled')
      }
      
    };
    return (
      <div>
        <Modal
          open={open}
          onClose={handleClose}
          aria-labelledby="modal-modal-title"
          aria-describedby="modal-modal-description"
        >
          <Box sx={style}>
            <Typography id="modal-modal-title" variant="h6" component="h2" color={'black'}>
              Saved Parameters
            </Typography>
  
            <Divider />
              <CardContent sx={{ padding: 0 }}>
                {Array.isArray(filelist) && filelist.length !== 0 && (
                <List
                  sx={{ width: '100%', bgcolor: 'background.paper' }}
                  component="nav"
                  aria-labelledby="nested-list-subheader"
                  dense={true}
                >
                  {Array.isArray(filelist) && filelist.map((i, ix) => {
                    // let indx = getIndicatorsInRuleSet(i);
                    // console.log('isRulesetSelected', isRulesetSelected(i));
                    return (
                      <ListItemButton key={ix.toString()} sx={{ backgroundColor: null, p: 0,  }}>
                        <ListItemIcon style={{ minWidth: '10px' }}>
                          <ArrowForwardRoundedIcon fontSize="10" />
                        </ListItemIcon>
                        <ListItemText sx={{ color: 'black', textWrap: 'nowrap', wordBreak: 'keep-all' }} onClick={() => viewFile(i)} primary={i} />
                        <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                          <DeleteForeverIcon sx={{ width: 16 }} onClick={() => deleteFile(i)} />
                        </IconButton>
                        &nbsp;
                        {true && ( //i?.avail
                          <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                            <FileDownloadIcon sx={{ width: 18 }} onClick={() => loadFile(i)} />
                          </IconButton>
                        )}
                        <Divider />
                      </ListItemButton>
                    )
                  })}
                </List>
                )}
                {(Array.isArray(filelist) && filelist.length == 0) && (
                  <Typography onClick={() => handleClose()}  sx={{color: 'black', my: 2, p: 1, backgroundColor: 'yellow'}}>No File</Typography>
                )}
              </CardContent>
              <Divider />
  
            <IconButton aria-label="settings" sx={{ padding: '4px', fontSize: '12px' }}>
              <CloseIcon sx={{ width: 18 }} onClick={() => handleClose()} ></CloseIcon>
              <Typography sx={{mx: 2, fontSize: 12}} onClick={() => handleClose()}>Close</Typography>
            </IconButton>
          </Box>
        </Modal>
      </div>
    );
  
  });