/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import * as React from 'react';
import '@/lib/styles/globals.css'
import { SessionProvider, useSession } from "next-auth/react"
import PropTypes from 'prop-types';
import Head from 'next/head';
import { AppCacheProvider } from '@mui/material-nextjs/v14-pagesRouter';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import theme from '@/lib/styles/theme';

export default function App(props) {
  const {
    Component,
    pageProps: { session, ...pageProps },
  } = props;
  return (
    <SessionProvider session={session}
      refetchInterval={5 * 60}
      refetchOnWindowFocus={true}>
      {Component.auth ? (
        <Auth>

          <AppCacheProvider {...props}>
            <Head>
              <meta name="author" content="Taner Subasi" />
              <meta name="description" content="GaussAlgo" />
              <meta name="viewport" content="initial-scale=1, width=device-width" />
            </Head>
            <ThemeProvider theme={theme}>
              {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
              <CssBaseline />
              <Component {...pageProps} />
            </ThemeProvider>
          </AppCacheProvider>

        </Auth>
      ) : (

        <AppCacheProvider {...props}>
          <Head>
              <meta name="author" content="Taner Subasi" />
              <meta name="description" content="GaussAlgo" />
            <meta name="viewport" content="initial-scale=1, width=device-width" />
          </Head>
          <ThemeProvider theme={theme}>
            {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
            <CssBaseline />
            <Component {...pageProps} />
          </ThemeProvider>
        </AppCacheProvider>
      )}
    </SessionProvider>
  )
}

function Auth({ children }) {
  const { status } = useSession({ required: true })
  if (status === "loading") {
    return <div>Loading...</div>
  }
  return children
}
