/* eslint-disable import/no-anonymous-default-export */
// pages/api/auth/[...nextauth].js
import NextAuth from 'next-auth'
// import Providers from 'next-auth/providers'

import GithubProvider from "next-auth/providers/github"

import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials";
import { MongoDBAdapter } from "@next-auth/mongodb-adapter"

const providers = [
    GithubProvider({
    clientId: String(process.env.GITHUB_ID),
    clientSecret: String(process.env.GITHUB_SECRET)
    }),
    CredentialsProvider({
        name: 'Credentials',
        credentials: {
            username: { label: "Username", type: "text", placeholder: "userName" },
            password: { label: "Password", type: "password" }
        },
        authorize: async (credentials) => {
            try {
                // Authenticate user with credentials
                const dbConn = await clientPromise;
                const user = await fnxAuth.login({
                    dbConn, credentials
                })
                if (user.data) {
                    user.data.name = user.data.fullName
                    user.data.sub = user.data.phone
                    /* Create JWT Payload */
                    const payload = {
                        id: user.data.userCode,
                        email: user.data.email,
                        phone: user.data.phone,
                        fullName: user.data.fullName,
                        role: user.data.role,
                        roleCode: user.data?.roles?.roleCode,
                        isAdmin: user.data?.roles?.isAdmin,
                        roleType: user.data?.roles?.type,
                        refObject: user.data.refObject,
                        tenantID: user.data.tenantID,
                    };
                    /* Sign token */
                    const token = await fnxAuth.jwtx.sign({ payload, lifeTime: '2d' });
                    const refreshToken = await fnxAuth.jwtx.sign({ payload, lifeTime: '30d' });
                    await fnxAuth.saveToken({ dbConn, payload, token: refreshToken, saveLogin: true })
                    user.data.token = token;
                    user.data.refreshToken = refreshToken;
                    //save to DB!
                    return user.data;
                }
                return null;
            } catch (e) {
                throw new Error(e);
            }
        }
    })
];

const callbacks = {
    jwt: async (props) => {
        var { token, user } = props;
        if (user) {
            // fnx.logger.info('callback jwt props user', user);
            token.username = user.email
            token.fullName = user.fullName
            token.phone = user.phone
            token.user = user
        }
        return Promise.resolve(token);
    },
    session: async (props) => {
        // fnx.logger.info('callback session props', props);
        var { session, token } = props;
        session.user = token.user;
        return Promise.resolve(session)
    },
}


const options = {
    database: process.env.DATABASE_URL,

    session: {
        strategy: "jwt",
        maxAge: 30 * 24 * 60 * 60 // 30 days
    },
    providers,
    callbacks,
    xpages: {
        signIn: '/auth/SignInSide',
    },
    secret: process.env.JWT_SECRET,
    theme: {
        colorScheme: "light",
    },

}
export default (req, res) => NextAuth(req, res, options)