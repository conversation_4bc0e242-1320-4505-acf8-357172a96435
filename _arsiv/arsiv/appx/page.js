/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import { useSession } from 'next-auth/react';
import { appvars } from '../../lib/constants'
// import PgHeader from '../components/generic/pg.header'
// import PgGuestHome from '../components/pages/guest.home.js'

export default function Home(props) {
  const { data: session } = useSession();
  return (
    <>
      <p>Hello</p>
    </>
  )
}

export async function getServerSideProps() {
  return { props: { appvars } }
}

