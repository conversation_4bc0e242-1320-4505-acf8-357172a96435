/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import 'src/lib/styles/globals.css'
import { SessionProvider, useSession } from "next-auth/react"
import { ChakraProvider, extendTheme } from "@chakra-ui/react";

const colors = {
  brand: {
    900: '#1a365d',
    800: '#153e75',
    700: '#2a69ac',
  },
}
const theme = extendTheme({ colors });

export default function App({
  Component,
  pageProps: { session, ...pageProps },
}) {
  return (
    <SessionProvider session={session} 
      refetchInterval={5 * 60} 
      refetchOnWindowFocus={true}>
      {Component.auth ? (
        <Auth>
          <ChakraProvider theme={theme}>
              <Component {...pageProps} />
          </ChakraProvider>
        </Auth>
      ) : (
        <ChakraProvider theme={theme}>
          <Component {...pageProps} />
        </ChakraProvider>
      )}
    </SessionProvider>
  )
}

function Auth({ children }) {
  const { status } = useSession({ required: true })
  if (status === "loading") {
    return <div>...</div>
  } 
  return children
}
