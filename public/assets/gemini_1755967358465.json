{"prompt": "\nYou are an expert cryptocurrency market analyst specializing in Binance Futures data analysis. Your task is to analyze various market metrics and provide clear definitions for the following key variables used in our anomaly detection system:\n\n#### **Data header descriptions::\n**symbol**: The trading pair symbol (e.g., BTCUSDT, ETHUSDT). Retrieved from Binance Futures API /fapi/v1/ticker/24hr endpoint \n**priceChange**: Absolute price change in the last 24 hours. Calculated by Binance API as lastPrice - openPrice. Difference between current price and price 24 hours ago\n**priceChangePercent**: Percentage price change in the last 24 hours. Calculated by Binance API as (priceChange / openPrice) * 100.\n**weightedAvgPrice**: Weighted average price in the last 24 hours. Calculated by Binance API based on all trades in the last 24 hours. Volume-weighted average price over 24 hours\n**lastPrice**: Most recent traded price.Retrieved from Binance Futures API. Last executed trade price\n**openPrice**: Price at the beginning of the 24-hour period \n**highPrice**: Highest price in the last 24 hours. Maximum price reached in the last 24 hours  \n**lowPrice**: Lowest price in the last 24 hours. Minimum price reached in the last 24 hours  \n**volume**: Total trading volume in the base asset (24h). Retrieved from Binance Futures API \n**quoteVolume**: Total trading volume in the quote asset (24h). Total value of trades in USDT (or quote currency) Retrieved from Binance Futures API\n**openTime**: Timestamp when the 24-hour statistics period opened. Unix timestamp in milliseconds \n**closeTime**: Timestamp when the 24-hour statistics period will close. Unix timestamp in milliseconds \n**count / trades_count**: Number of trades executed in the 24-hour period. Retrieved from Binance Futures API    \n**range_position_percent**: Current price position within the 24-hour range. Calculated as ((lastPrice - lowPrice) / (highPrice - lowPrice)) * 100. \n**price_spread_percent**: Price spread as a percentage of the current price. Calculated as ((highPrice - lowPrice) / lastPrice) * 100. \n**openPrice_spread_percent**: Price spread as a percentage of the opening price. Calculated as ((highPrice - lowPrice) / openPrice) * 100. \n**klines_{interval}**: JSON array containing all kline data for the trading pair.Retrieved from Binance Futures API. Raw kline data for the trading pair. [\n        [\n            1499040000000,      // Kline open time\n            \"0.01634790\",       // Open price\n            \"0.80000000\",       // High price\n            \"0.01575800\",       // Low price\n            \"0.01577100\",       // Close price\n            \"148976.11427815\",  // Volume\n            1499644799999,      // Kline Close time\n            \"2434.19055334\",    // Quote asset volume\n            308,                // Number of trades\n            \"1756.87402397\",    // Taker buy base asset volume\n            \"28.46694368\",      // Taker buy quote asset volume\n            \"0\"                 // Unused field, ignore.\n        ]\n        ]\n\n**fundingData**: JSON array containing historical funding data for the trading pair. Retrieved from Binance Futures API\n    - Data representation: Concatenated text of all anomaly reasons across different data sources\n\naşağıda binance tüm pair ler için; 24hr ticker price change statistics yani 24 hour rolling window price change statistics verileri ve her pair detayında en son kline verileri var. \n\nBunlara bakarak; risk istahı yüksek bir analist olarak hızlı, scalp trading icin en uygun pairler var ise bunlardan en iyi olanların maksimum 5 pair için nedenlerini de acıklayarak öneride bulunman bekleniyor. Sadece veriye dayalı, tekrarlanabilir ve şeffaf bir çıktı üret. Seçtiğin adaylar için seçim kriterlerini de açıklayarak sun.\n\n- yüklediğim metni tamamen ayrıştır ve her bir çiftin kline’larını, volatilitesini, hacim anomalilerini, likiditesini ve fiyat pozisyonlarını teker teker incele.\n- her pair deki anomaliyi; pair 'in kendi değerleri arasındaki anomaliyi de degerlendir; ancak piyasa genelindeki anomaliyi de dikkate al. bu degerlendirmeni tablonda göster.\n- Sectigin pair leri bir tabloda nedenleriyle göster, her pair icin bir confidence score hesapla.\n- Pairleri sectikten sonra; hızlı scalp trading stratejisi için seçilen kripto çift için giriş yönü, giriş seviyeleri, hedef zaman aralığı, öneri geçerlilik süresi, risk hesaplama mantığı, stop-loss ve take-profit önerileri sun. Önerileri sunarken hesaplama mantığını açıklayarak sun.\n\n#### **Simdiliki Zaman: 2025-08-23T19:42:20.728Z\n\n\n#### JSON Çıktı Formatı:\nTüm çıktıyı, başka bir uygulama tarafından işlenebilecek tek bir JSON formatında, aşağıdaki anahtar ve yapıya sadık kalarak sun:\n\n{\n  \"analysisTimestamp\": \"Analizin yapıldığı zaman damgası (ISO 8601 formatında)\",\n  \"disclaimer\": \"Finansal tavsiye olmadığına dair uyarı metni.\",\n  \"marketAnalysis\": {\n    \"overallCondition\": \"Piyasanın genel durumu hakkında kısa bir özet.\",\n    \"observation\": \"BTC pariteleri ile altcoinler arasındaki anomali ve ayrışma hakkında detaylı gözlem.\"\n  },\n  \"selectionCriteria\": [\n    {\n      \"criterion\": \"Kriter Adı (Örn: Yüksek Volatilite)\",\n      \"description\": \"Kriterin ne anlama geldiği ve nasıl ölçüldüğünün açıklaması.\"\n    }\n  ],\n  \"recommendedPairs\": [\n    {\n      \"symbol\": \"Parite Sembolü (Örn: MEMEUSDT)\",\n      \"confidenceScore\": \"Güven Skoru (100 üzerinden bir sayı)\",\n      \"analysis\": {\n        \"marketAnomaly\": \"Paritenin piyasa geneline göre gösterdiği anomali.\",\n        \"internalAnomaly\": \"Paritenin kendi iç dinamiklerindeki (volatilite, hacim vb.) anomali.\"\n      }\n    }\n  ],\n  \"strategies\": [{\n    \"symbol\": \"recommendedPair sembolü\",\n    \"direction\": \"İşlem Yönü (Long/Short)\",\n    \"logic\": \"Bu yönün seçilme mantığı.\",\n    \"entryLevels\": [\n      {\n        \"type\": \"Giriş türü (Primary/Secondary)\",\n        \"level\": \"Giriş fiyat seviyesi\",\n        \"logic\": \"Bu seviyenin seçilme mantığı.\"\n      }\n    ],\n    \"timeframe\": {\n      \"tradeDuration\": \"Önerilen işlem süresi - dakika\",\n      \"recommendationValidity\": \"Önerinin geçerlilik süresi - dakika\"\n    },\n    \"riskManagement\": {\n      \"stopLoss\": {\n        \"level\": \"Zarar durdurma seviyesi\",\n        \"logic\": \"Stop-loss seviyesinin belirlenme mantığı.\"\n      },\n      \"riskCalculationLogic\": \"Pozisyon büyüklüğünün nasıl ayarlanması gerektiğine dair mantık.\"\n    },\n    \"takeProfitLevels\": [\n      {\n        \"level\": \"Kâr al seviyesi\",\n        \"riskRewardRatio\": \"Risk/Ödül oranı\",\n        \"logic\": \"Kâr al seviyesinin hesaplama mantığı.\"\n      }\n    ]\n  }]\n}\n\n#### **Referans Veriler**\n\n[{\"symbol\":\"SAPIENUSDT\",\"priceChange\":\"0.0665500\",\"priceChangePercent\":\"33.297\",\"weightedAvgPrice\":\"0.2489851\",\"lastPrice\":\"0.2664200\",\"openPrice\":\"0.1998700\",\"highPrice\":\"0.2870100\",\"lowPrice\":\"0.1997200\",\"volume\":\"1344305696\",\"quoteVolume\":\"334712062.4327600\",\"openTime\":1755877800000,\"count\":3629525,\"range_position_percent\":76.41,\"price_spread_percent\":32.76,\"openPrice_spread_percent\":43.67,\"klines_1m\":[[1755962820000,\"0.2614900\",\"0.2620600\",\"0.2611400\",\"0.2619200\",\"257972\",1755962879999,\"67504.1663100\",1039,\"162633\",\"42561.9864100\",\"0\"],[1755962880000,\"0.2619200\",\"0.2619900\",\"0.2612500\",\"0.2615300\",\"209517\",1755962939999,\"54820.0531700\",939,\"109118\",\"28549.8920200\",\"0\"],[1755962940000,\"0.2615300\",\"0.2626500\",\"0.2612800\",\"0.2616200\",\"498158\",1755962999999,\"130527.7869400\",1871,\"272965\",\"71525.7484400\",\"0\"],[1755963000000,\"0.2616200\",\"0.2623100\",\"0.2598400\",\"0.2622300\",\"735928\",1755963059999,\"192123.8414800\",3192,\"472729\",\"123435.4777300\",\"0\"],[1755963060000,\"0.2622400\",\"0.2643200\",\"0.2621200\",\"0.2631100\",\"674366\",1755963119999,\"177653.2048400\",3126,\"391590\",\"103180.1551400\",\"0\"],[1755963120000,\"0.2630800\",\"0.2649800\",\"0.2630200\",\"0.2640600\",\"517947\",1755963179999,\"136897.2871500\",2088,\"284622\",\"75221.6440800\",\"0\"],[1755963180000,\"0.2640700\",\"0.2654900\",\"0.2640700\",\"0.2645300\",\"520992\",1755963239999,\"138052.9908800\",1941,\"253683\",\"67231.6822400\",\"0\"],[1755963240000,\"0.2645300\",\"0.2651500\",\"0.2641100\",\"0.2646000\",\"399291\",1755963299999,\"105646.1338300\",1601,\"163813\",\"43348.8785700\",\"0\"],[1755963300000,\"0.2646000\",\"0.2657700\",\"0.2646000\",\"0.2651000\",\"383285\",1755963359999,\"101690.4093700\",1513,\"203166\",\"53890.4000300\",\"0\"],[1755963360000,\"0.2651000\",\"0.2652000\",\"0.2646200\",\"0.2646400\",\"213130\",1755963419999,\"56454.3498300\",1013,\"91745\",\"24306.1345800\",\"0\"],[1755963420000,\"0.2646400\",\"0.2694300\",\"0.2642800\",\"0.2690100\",\"2120495\",1755963479999,\"568056.3218900\",7774,\"1336045\",\"357790.3640400\",\"0\"],[1755963480000,\"0.2690900\",\"0.2770000\",\"0.2680900\",\"0.2728800\",\"4788820\",1755963539999,\"1306196.0376900\",13563,\"2908879\",\"793450.4227600\",\"0\"],[1755963540000,\"0.2728900\",\"0.2748800\",\"0.2718100\",\"0.2732500\",\"1393601\",1755963599999,\"380746.8520100\",4241,\"802033\",\"219115.4428000\",\"0\"],[1755963600000,\"0.2732500\",\"0.2741400\",\"0.2696800\",\"0.2709400\",\"1673016\",1755963659999,\"454476.9256900\",3890,\"581604\",\"157883.2324000\",\"0\"],[1755963660000,\"0.2709600\",\"0.2714600\",\"0.2694400\",\"0.2697400\",\"942207\",1755963719999,\"254709.5905300\",2841,\"368810\",\"99735.4657900\",\"0\"],[1755963720000,\"0.2697300\",\"0.2706100\",\"0.2688300\",\"0.2697000\",\"593940\",1755963779999,\"160119.0853100\",2310,\"331254\",\"89313.9791300\",\"0\"],[1755963780000,\"0.2697100\",\"0.2698700\",\"0.2676600\",\"0.2676900\",\"801872\",1755963839999,\"215536.3688200\",2440,\"245977\",\"66151.1395300\",\"0\"],[1755963840000,\"0.2676900\",\"0.2686600\",\"0.2671600\",\"0.2685600\",\"861684\",1755963899999,\"230915.1858700\",3133,\"511451\",\"137114.4684000\",\"0\"],[1755963900000,\"0.2685800\",\"0.2695500\",\"0.2677000\",\"0.2677000\",\"685335\",1755963959999,\"184232.3660700\",2246,\"343742\",\"92443.1707400\",\"0\"],[1755963960000,\"0.2677600\",\"0.2679600\",\"0.2644600\",\"0.2652400\",\"1348756\",1755964019999,\"359024.8374900\",4373,\"506845\",\"134955.1283700\",\"0\"],[1755964020000,\"0.2652400\",\"0.2668100\",\"0.2650100\",\"0.2660500\",\"889349\",1755964079999,\"236541.7991400\",2098,\"615726\",\"163773.1009400\",\"0\"],[1755964080000,\"0.2660000\",\"0.2661200\",\"0.2648600\",\"0.2648900\",\"459802\",1755964139999,\"122010.1359600\",1619,\"182659\",\"48483.0076900\",\"0\"],[1755964140000,\"0.2648900\",\"0.2654700\",\"0.2638000\",\"0.2650700\",\"821282\",1755964199999,\"217329.8840000\",2701,\"515159\",\"136373.8068600\",\"0\"],[1755964200000,\"0.2650700\",\"0.2670600\",\"0.2649800\",\"0.2669000\",\"479156\",1755964259999,\"127472.5936200\",1744,\"345794\",\"92023.3666700\",\"0\"]]},{\"symbol\":\"MEMEUSDT\",\"priceChange\":\"0.0009500\",\"priceChangePercent\":\"36.440\",\"weightedAvgPrice\":\"0.0037311\",\"lastPrice\":\"0.0035570\",\"openPrice\":\"0.0026070\",\"highPrice\":\"0.0043230\",\"lowPrice\":\"0.0025530\",\"volume\":\"274556052296\",\"quoteVolume\":\"1024395346.7343130\",\"openTime\":1755877800000,\"count\":3348357,\"range_position_percent\":56.72,\"price_spread_percent\":49.76,\"openPrice_spread_percent\":67.89,\"klines_1m\":[[1755962820000,\"0.0035510\",\"0.0035640\",\"0.0035470\",\"0.0035520\",\"179243549\",1755962879999,\"637306.0480720\",1479,\"102954933\",\"366138.2059960\",\"0\"],[1755962880000,\"0.0035530\",\"0.0035620\",\"0.0035530\",\"0.0035600\",\"81466425\",1755962939999,\"289874.6961670\",796,\"46841065\",\"166670.7383410\",\"0\"],[1755962940000,\"0.0035610\",\"0.0035670\",\"0.0035550\",\"0.0035620\",\"89723198\",1755962999999,\"319584.9886390\",891,\"51928525\",\"184980.6412640\",\"0\"],[1755963000000,\"0.0035630\",\"0.0035870\",\"0.0035600\",\"0.0035800\",\"198224847\",1755963059999,\"708500.5256270\",1620,\"116623319\",\"416790.3342030\",\"0\"],[1755963060000,\"0.0035810\",\"0.0035990\",\"0.0035690\",\"0.0035990\",\"137109885\",1755963119999,\"491464.0741820\",1445,\"86968922\",\"311801.7717170\",\"0\"],[1755963120000,\"0.0035990\",\"0.0036090\",\"0.0035960\",\"0.0036040\",\"148217426\",1755963179999,\"533988.9510270\",1659,\"67812158\",\"244337.0883580\",\"0\"],[1755963180000,\"0.0036040\",\"0.0036090\",\"0.0035810\",\"0.0035810\",\"150927789\",1755963239999,\"542843.3316580\",1399,\"61940127\",\"222978.3970220\",\"0\"],[1755963240000,\"0.0035820\",\"0.0035930\",\"0.0035730\",\"0.0035870\",\"87333671\",1755963299999,\"313082.4468350\",964,\"38222040\",\"137090.3248240\",\"0\"],[1755963300000,\"0.0035880\",\"0.0035890\",\"0.0035680\",\"0.0035760\",\"90065612\",1755963359999,\"322331.6009770\",947,\"29983889\",\"107264.3684240\",\"0\"],[1755963360000,\"0.0035750\",\"0.0035900\",\"0.0035750\",\"0.0035890\",\"51346423\",1755963419999,\"183830.7343560\",786,\"32533296\",\"116491.0874890\",\"0\"],[1755963420000,\"0.0035890\",\"0.0036030\",\"0.0035860\",\"0.0035980\",\"84273969\",1755963479999,\"302966.9412050\",1061,\"49282534\",\"177189.0471460\",\"0\"],[1755963480000,\"0.0035990\",\"0.0036010\",\"0.0035920\",\"0.0035980\",\"47562991\",1755963539999,\"171101.7733470\",805,\"28973157\",\"104247.5159290\",\"0\"],[1755963540000,\"0.0035970\",\"0.0035990\",\"0.0035730\",\"0.0035730\",\"120238456\",1755963599999,\"431280.3387750\",1500,\"53022748\",\"190303.7370700\",\"0\"],[1755963600000,\"0.0035740\",\"0.0035800\",\"0.0035630\",\"0.0035650\",\"85209654\",1755963659999,\"304252.1852470\",1179,\"43912379\",\"156822.4687820\",\"0\"],[1755963660000,\"0.0035660\",\"0.0035800\",\"0.0035640\",\"0.0035740\",\"92298317\",1755963719999,\"329736.6497250\",1139,\"59057741\",\"211000.2875080\",\"0\"],[1755963720000,\"0.0035740\",\"0.0035860\",\"0.0035630\",\"0.0035820\",\"74966770\",1755963779999,\"268187.1293770\",1111,\"45146468\",\"161575.9468510\",\"0\"],[1755963780000,\"0.0035830\",\"0.0035830\",\"0.0035710\",\"0.0035720\",\"50631673\",1755963839999,\"181098.1873250\",797,\"24057432\",\"86051.8840570\",\"0\"],[1755963840000,\"0.0035710\",\"0.0035720\",\"0.0035440\",\"0.0035470\",\"183836555\",1755963899999,\"653330.5136820\",1766,\"64489423\",\"229192.6036570\",\"0\"],[1755963900000,\"0.0035480\",\"0.0035570\",\"0.0035260\",\"0.0035540\",\"267672374\",1755963959999,\"947298.8460560\",2556,\"124883601\",\"442245.9203890\",\"0\"],[1755963960000,\"0.0035540\",\"0.0035650\",\"0.0035470\",\"0.0035630\",\"127350950\",1755964019999,\"452878.1388360\",1281,\"82620359\",\"293835.9086100\",\"0\"],[1755964020000,\"0.0035630\",\"0.0035700\",\"0.0035570\",\"0.0035620\",\"90094139\",1755964079999,\"321099.8148860\",983,\"38503359\",\"137239.8431520\",\"0\"],[1755964080000,\"0.0035620\",\"0.0035620\",\"0.0035420\",\"0.0035550\",\"86878765\",1755964139999,\"308416.6597600\",1090,\"40484364\",\"143744.7584860\",\"0\"],[1755964140000,\"0.0035540\",\"0.0035630\",\"0.0035480\",\"0.0035610\",\"95066910\",1755964199999,\"337870.9880330\",1069,\"39788129\",\"141474.2694420\",\"0\"],[1755964200000,\"0.0035620\",\"0.0035630\",\"0.0035510\",\"0.0035570\",\"51350638\",1755964259999,\"182648.3520840\",658,\"21644842\",\"76984.2141030\",\"0\"]]},{\"symbol\":\"AIOTUSDT\",\"priceChange\":\"0.0723100\",\"priceChangePercent\":\"4.595\",\"weightedAvgPrice\":\"1.6303887\",\"lastPrice\":\"1.6460600\",\"openPrice\":\"1.5737500\",\"highPrice\":\"1.8638600\",\"lowPrice\":\"1.4719300\",\"volume\":\"81776875\",\"quoteVolume\":\"133328092.1442300\",\"openTime\":1755877800000,\"count\":1132718,\"range_position_percent\":44.43,\"price_spread_percent\":23.81,\"openPrice_spread_percent\":24.9,\"klines_1m\":[[1755962820000,\"1.6461800\",\"1.6469500\",\"1.6448600\",\"1.6451900\",\"6230\",1755962879999,\"10253.9656300\",161,\"2221\",\"3656.2018100\",\"0\"],[1755962880000,\"1.6449400\",\"1.6464800\",\"1.6434900\",\"1.6459800\",\"14404\",1755962939999,\"23691.8198100\",269,\"9876\",\"16245.1023800\",\"0\"],[1755962940000,\"1.6459800\",\"1.6474600\",\"1.6447300\",\"1.6467100\",\"17658\",1755962999999,\"29072.9789600\",224,\"16270\",\"26787.7000600\",\"0\"],[1755963000000,\"1.6467500\",\"1.6485000\",\"1.6439200\",\"1.6439200\",\"20886\",1755963059999,\"34399.7263000\",348,\"11055\",\"18212.1404300\",\"0\"],[1755963060000,\"1.6441200\",\"1.6444500\",\"1.6426300\",\"1.6426300\",\"10801\",1755963119999,\"17754.0454100\",269,\"4396\",\"7226.6303700\",\"0\"],[1755963120000,\"1.6426100\",\"1.6479500\",\"1.6416700\",\"1.6468000\",\"27286\",1755963179999,\"44870.2703400\",437,\"14227\",\"23404.2273500\",\"0\"],[1755963180000,\"1.6467500\",\"1.6490500\",\"1.6461600\",\"1.6490500\",\"40221\",1755963239999,\"66281.9361700\",310,\"18789\",\"30960.1319200\",\"0\"],[1755963240000,\"1.6489400\",\"1.6507700\",\"1.6484400\",\"1.6502700\",\"19506\",1755963299999,\"32179.4768400\",291,\"13023\",\"21486.4155100\",\"0\"],[1755963300000,\"1.6502600\",\"1.6533200\",\"1.6497700\",\"1.6526700\",\"31426\",1755963359999,\"51904.4739300\",423,\"22568\",\"37273.2164500\",\"0\"],[1755963360000,\"1.6522000\",\"1.6522000\",\"1.6475300\",\"1.6493300\",\"31232\",1755963419999,\"51527.4930200\",404,\"8091\",\"13348.6356400\",\"0\"],[1755963420000,\"1.6489500\",\"1.6510000\",\"1.6465000\",\"1.6502400\",\"29405\",1755963479999,\"48479.2988300\",397,\"16262\",\"26809.6802800\",\"0\"],[1755963480000,\"1.6498400\",\"1.6519200\",\"1.6494800\",\"1.6507300\",\"19801\",1755963539999,\"32686.3084300\",265,\"10497\",\"17329.3282300\",\"0\"],[1755963540000,\"1.6506100\",\"1.6506900\",\"1.6466700\",\"1.6473600\",\"20709\",1755963599999,\"34137.0388300\",334,\"8099\",\"13351.2668600\",\"0\"],[1755963600000,\"1.6475600\",\"1.6499100\",\"1.6460000\",\"1.6473900\",\"56394\",1755963659999,\"92915.2817900\",515,\"45955\",\"75717.4940900\",\"0\"],[1755963660000,\"1.6477400\",\"1.6484400\",\"1.6442800\",\"1.6479200\",\"27487\",1755963719999,\"45249.3292800\",397,\"11939\",\"19656.9595800\",\"0\"],[1755963720000,\"1.6484200\",\"1.6492400\",\"1.6475400\",\"1.6479300\",\"24024\",1755963779999,\"39604.2777200\",287,\"15147\",\"24973.1350900\",\"0\"],[1755963780000,\"1.6480100\",\"1.6495200\",\"1.6470000\",\"1.6490200\",\"15280\",1755963839999,\"25181.6578200\",279,\"8314\",\"13700.7483200\",\"0\"],[1755963840000,\"1.6490200\",\"1.6499500\",\"1.6475400\",\"1.6490900\",\"18033\",1755963899999,\"29735.4197500\",326,\"12076\",\"19913.1204100\",\"0\"],[1755963900000,\"1.6490900\",\"1.6490900\",\"1.6458400\",\"1.6458400\",\"19760\",1755963959999,\"32549.6205400\",413,\"7889\",\"12994.5923600\",\"0\"],[1755963960000,\"1.6462000\",\"1.6473300\",\"1.6454800\",\"1.6469900\",\"12368\",1755964019999,\"20359.8748200\",230,\"4286\",\"7057.1831900\",\"0\"],[1755964020000,\"1.6469900\",\"1.6484100\",\"1.6445300\",\"1.6461500\",\"18733\",1755964079999,\"30838.2835400\",362,\"9458\",\"15570.7677300\",\"0\"],[1755964080000,\"1.6457500\",\"1.6470300\",\"1.6448500\",\"1.6468700\",\"9203\",1755964139999,\"15144.8908700\",194,\"6293\",\"10357.1026800\",\"0\"],[1755964140000,\"1.6465200\",\"1.6475800\",\"1.6456000\",\"1.6468900\",\"16032\",1755964199999,\"26401.9679700\",183,\"5813\",\"9571.1672100\",\"0\"],[1755964200000,\"1.6466300\",\"1.6466400\",\"1.6452000\",\"1.6462000\",\"6654\",1755964259999,\"10951.8110200\",157,\"4046\",\"6660.0278600\",\"0\"]]},{\"symbol\":\"PUMPUSDT\",\"priceChange\":\"0.0001710\",\"priceChangePercent\":\"5.638\",\"weightedAvgPrice\":\"0.0031682\",\"lastPrice\":\"0.0032040\",\"openPrice\":\"0.0030330\",\"highPrice\":\"0.0032700\",\"lowPrice\":\"0.0030070\",\"volume\":\"91727090039\",\"quoteVolume\":\"290609834.3167470\",\"openTime\":1755877800000,\"count\":751893,\"range_position_percent\":74.9,\"price_spread_percent\":8.21,\"openPrice_spread_percent\":8.67,\"klines_1m\":[[1755962820000,\"0.0032100\",\"0.0032170\",\"0.0032080\",\"0.0032170\",\"48828925\",1755962879999,\"156825.1822370\",426,\"19013364\",\"61084.7435260\",\"0\"],[1755962880000,\"0.0032160\",\"0.0032190\",\"0.0032120\",\"0.0032190\",\"24829154\",1755962939999,\"79842.4602780\",361,\"11311273\",\"36373.9249870\",\"0\"],[1755962940000,\"0.0032190\",\"0.0032340\",\"0.0032170\",\"0.0032290\",\"64524078\",1755962999999,\"208319.0333330\",623,\"47800574\",\"154318.8344350\",\"0\"],[1755963000000,\"0.0032290\",\"0.0032380\",\"0.0032240\",\"0.0032270\",\"48006096\",1755963059999,\"155065.6186130\",478,\"16163606\",\"52210.9606940\",\"0\"],[1755963060000,\"0.0032280\",\"0.0032280\",\"0.0032100\",\"0.0032200\",\"75241872\",1755963119999,\"242172.1087970\",785,\"25630116\",\"82486.1747550\",\"0\"],[1755963120000,\"0.0032210\",\"0.0032220\",\"0.0032180\",\"0.0032210\",\"22912941\",1755963179999,\"73772.2917720\",271,\"10627478\",\"34219.4355930\",\"0\"],[1755963180000,\"0.0032210\",\"0.0032230\",\"0.0032140\",\"0.0032140\",\"49937113\",1755963239999,\"160693.5748040\",506,\"13832774\",\"44517.0633910\",\"0\"],[1755963240000,\"0.0032140\",\"0.0032260\",\"0.0031930\",\"0.0031960\",\"284937488\",1755963299999,\"913677.0061090\",1787,\"68241667\",\"218790.8661620\",\"0\"],[1755963300000,\"0.0031950\",\"0.0032040\",\"0.0031950\",\"0.0032010\",\"81172209\",1755963359999,\"259671.9600500\",564,\"52790169\",\"168862.5716520\",\"0\"],[1755963360000,\"0.0032010\",\"0.0032020\",\"0.0031960\",\"0.0031980\",\"23460001\",1755963419999,\"75049.4458990\",315,\"8760317\",\"28025.3899190\",\"0\"],[1755963420000,\"0.0031990\",\"0.0032120\",\"0.0031980\",\"0.0032110\",\"25011832\",1755963479999,\"80188.0347070\",328,\"14160280\",\"45390.9418770\",\"0\"],[1755963480000,\"0.0032110\",\"0.0032140\",\"0.0032050\",\"0.0032070\",\"24607183\",1755963539999,\"78967.7619990\",307,\"12331190\",\"39565.7789790\",\"0\"],[1755963540000,\"0.0032070\",\"0.0032110\",\"0.0032030\",\"0.0032050\",\"20343176\",1755963599999,\"65232.0988820\",290,\"10702146\",\"34314.8508330\",\"0\"],[1755963600000,\"0.0032060\",\"0.0032060\",\"0.0031960\",\"0.0032000\",\"38659245\",1755963659999,\"123754.9246340\",372,\"12401302\",\"39683.8221980\",\"0\"],[1755963660000,\"0.0031990\",\"0.0032110\",\"0.0031990\",\"0.0032080\",\"28452622\",1755963719999,\"91220.3164310\",319,\"19831690\",\"63574.2975400\",\"0\"],[1755963720000,\"0.0032080\",\"0.0032120\",\"0.0032050\",\"0.0032070\",\"49162586\",1755963779999,\"157785.7254620\",307,\"10938622\",\"35103.8383020\",\"0\"],[1755963780000,\"0.0032070\",\"0.0032100\",\"0.0032030\",\"0.0032080\",\"19292357\",1755963839999,\"61876.2625950\",250,\"12251331\",\"39287.7065880\",\"0\"],[1755963840000,\"0.0032080\",\"0.0032100\",\"0.0032070\",\"0.0032080\",\"9813120\",1755963899999,\"31482.4973640\",165,\"4209172\",\"13505.9591070\",\"0\"],[1755963900000,\"0.0032070\",\"0.0032150\",\"0.0032070\",\"0.0032090\",\"24937474\",1755963959999,\"80061.1766100\",360,\"12012945\",\"38568.7090150\",\"0\"],[1755963960000,\"0.0032090\",\"0.0032110\",\"0.0032030\",\"0.0032040\",\"22329129\",1755964019999,\"71622.9540600\",355,\"9962200\",\"31950.6576750\",\"0\"],[1755964020000,\"0.0032040\",\"0.0032110\",\"0.0032040\",\"0.0032070\",\"20169744\",1755964079999,\"64694.9780410\",279,\"5336229\",\"17113.2610630\",\"0\"],[1755964080000,\"0.0032080\",\"0.0032110\",\"0.0032060\",\"0.0032060\",\"24778140\",1755964139999,\"79530.3712300\",311,\"17981022\",\"57718.2099070\",\"0\"],[1755964140000,\"0.0032050\",\"0.0032110\",\"0.0032050\",\"0.0032080\",\"34691451\",1755964199999,\"111324.2601410\",364,\"22694826\",\"72832.7894830\",\"0\"],[1755964200000,\"0.0032080\",\"0.0032090\",\"0.0032030\",\"0.0032040\",\"16686785\",1755964259999,\"53500.6685140\",264,\"6568998\",\"21061.9798940\",\"0\"]]},{\"symbol\":\"ETHUSDT\",\"priceChange\":\"102.47\",\"priceChangePercent\":\"2.202\",\"weightedAvgPrice\":\"4766.58\",\"lastPrice\":\"4756.45\",\"openPrice\":\"4653.98\",\"highPrice\":\"4888.49\",\"lowPrice\":\"4612.40\",\"volume\":\"8046194.524\",\"quoteVolume\":\"38352807333.02\",\"openTime\":1755877800000,\"count\":8615833,\"range_position_percent\":52.18,\"price_spread_percent\":5.8,\"openPrice_spread_percent\":5.93,\"klines_1m\":[[1755962820000,\"4736.93\",\"4740.31\",\"4736.25\",\"4740.30\",\"1536.482\",1755962879999,\"7279791.40745\",2777,\"642.682\",\"3044894.82471\",\"0\"],[1755962880000,\"4740.30\",\"4743.77\",\"4739.27\",\"4740.26\",\"2552.558\",1755962939999,\"12102749.12801\",3695,\"601.709\",\"2852860.95746\",\"0\"],[1755962940000,\"4740.26\",\"4743.32\",\"4740.00\",\"4742.60\",\"893.006\",1755962999999,\"4234838.97794\",1607,\"377.797\",\"1791482.65202\",\"0\"],[1755963000000,\"4742.60\",\"4744.64\",\"4737.66\",\"4737.66\",\"2312.645\",1755963059999,\"10965108.74334\",3196,\"619.595\",\"2937506.93202\",\"0\"],[1755963060000,\"4737.67\",\"4743.00\",\"4736.68\",\"4739.97\",\"2052.245\",1755963119999,\"9727114.32156\",3722,\"731.825\",\"3468752.90474\",\"0\"],[1755963120000,\"4739.98\",\"4741.20\",\"4736.09\",\"4736.09\",\"2516.651\",1755963179999,\"11926027.04884\",3142,\"689.772\",\"3269157.69712\",\"0\"],[1755963180000,\"4736.10\",\"4740.32\",\"4736.10\",\"4739.03\",\"786.689\",1755963239999,\"3727472.53125\",2061,\"389.680\",\"1846239.10651\",\"0\"],[1755963240000,\"4739.03\",\"4741.00\",\"4738.15\",\"4738.23\",\"536.534\",1755963299999,\"2542924.31612\",1731,\"253.032\",\"1199251.71918\",\"0\"],[1755963300000,\"4738.24\",\"4741.05\",\"4737.19\",\"4739.32\",\"1184.850\",1755963359999,\"5614980.56758\",2153,\"762.397\",\"3612724.03692\",\"0\"],[1755963360000,\"4739.33\",\"4742.92\",\"4738.46\",\"4741.88\",\"1037.423\",1755963419999,\"4917694.88594\",2424,\"510.461\",\"2419672.00502\",\"0\"],[1755963420000,\"4741.89\",\"4746.00\",\"4741.37\",\"4745.99\",\"3096.593\",1755963479999,\"14688436.61650\",4050,\"1633.184\",\"7747753.50622\",\"0\"],[1755963480000,\"4746.00\",\"4747.10\",\"4743.55\",\"4744.07\",\"1738.550\",1755963539999,\"8249988.43179\",2847,\"735.205\",\"3488895.48496\",\"0\"],[1755963540000,\"4744.07\",\"4746.69\",\"4743.94\",\"4744.85\",\"1278.523\",1755963599999,\"6066973.84190\",2330,\"588.448\",\"2792317.22488\",\"0\"],[1755963600000,\"4744.86\",\"4745.65\",\"4744.23\",\"4744.80\",\"633.559\",1755963659999,\"3006191.20363\",1611,\"323.458\",\"1534791.77015\",\"0\"],[1755963660000,\"4744.80\",\"4751.93\",\"4744.10\",\"4750.92\",\"4534.756\",1755963719999,\"21534592.13303\",4696,\"3462.571\",\"16443935.23579\",\"0\"],[1755963720000,\"4750.93\",\"4754.00\",\"4749.56\",\"4749.86\",\"5874.831\",1755963779999,\"27915768.18318\",6007,\"3579.509\",\"17009727.03824\",\"0\"],[1755963780000,\"4749.86\",\"4751.73\",\"4746.11\",\"4751.22\",\"3597.210\",1755963839999,\"17082754.84024\",3900,\"1723.068\",\"8183631.41695\",\"0\"],[1755963840000,\"4751.23\",\"4751.44\",\"4750.00\",\"4750.19\",\"2012.853\",1755963899999,\"9562346.95164\",2392,\"1066.719\",\"5067675.47169\",\"0\"],[1755963900000,\"4750.19\",\"4754.98\",\"4750.14\",\"4752.11\",\"4691.484\",1755963959999,\"22296517.54822\",4683,\"3503.296\",\"16649852.08778\",\"0\"],[1755963960000,\"4752.11\",\"4752.99\",\"4750.38\",\"4750.51\",\"1506.500\",1755964019999,\"7157789.57043\",2623,\"482.736\",\"2293599.79597\",\"0\"],[1755964020000,\"4750.51\",\"4753.50\",\"4750.12\",\"4753.08\",\"1503.579\",1755964079999,\"7144021.85232\",2322,\"796.271\",\"3783502.42899\",\"0\"],[1755964080000,\"4753.08\",\"4754.40\",\"4751.02\",\"4751.76\",\"1975.298\",1755964139999,\"9388689.36762\",2667,\"722.001\",\"3432060.73911\",\"0\"],[1755964140000,\"4751.76\",\"4754.98\",\"4751.07\",\"4754.01\",\"1477.065\",1755964199999,\"7021358.26688\",2438,\"833.575\",\"3962519.55160\",\"0\"],[1755964200000,\"4754.01\",\"4756.46\",\"4753.58\",\"4756.00\",\"2454.924\",1755964259999,\"11674045.04525\",3894,\"1756.295\",\"8351805.00493\",\"0\"]]}]", "response": "```json\n{\n  \"analysisTimestamp\": \"2025-08-23T19:42:20.728Z\",\n  \"disclaimer\": \"<PERSON>u analiz, yalnızca bilgi amaçlıdır ve yatırım tavsiyesi niteliği taşımaz. Kripto para piyasaları yüksek volatiliteye sahiptir ve tüm yatırımlar risk içerir. Herhangi bir işlem yapmadan önce kendi araştırmanızı yapmanız ve finansal danışmanınıza başvurmanız önerilir.\",\n  \"marketAnalysis\": {\n    \"overallCondition\": \"Mevcut piyasa koşulları, genel olarak işlem hacminin yüksek olduğu ve belirli altcoinlerde dikkat çekici fiyat hareketlerinin yaşandığı bir ortamı göstermektedir. BTC ve ETH gibi majör varlıklarda belirli bir trend hakimken, bazı altcoinler bu trendden ayrışarak daha agresif fiyat hareketleri sergilemektedir. Bu durum, risk iştahı yüksek olan scalper'lar için fırsatlar sunmaktadır.\",\n    \"observation\": \"BTC pariteleri ile altcoinler arasındaki ayrışma, özellikle yüksek volatilite ve hacim gösteren altcoinlerde belirgindir. Bazı altcoinler, majör piyasa hareketlerinden bağımsız olarak kendi momentumlarını yaratabilmektedir. Bu durum, volatiliteyi ve fiyat hareketliliğini kullanarak kısa vadeli karlar elde etmek isteyen scalper'lar için önemli bir inceleme alanıdır. Önerilen pariteler, bu ayrışmayı ve kendi iç dinamiklerindeki anormallikleri kullanarak hızlı işlem fırsatları sunmaktadır.\"\n  },\n  \"selectionCriteria\": [\n    {\n      \"criterion\": \"Yüksek Günlük Fiyat Değişimi (priceChangePercent)\",\n      \"description\": \"Paritenin son 24 saat içinde gösterdiği yüzdesel fiyat değişimi. Scalp trading için yüksek değişim, kısa sürede daha fazla potansiyel kar/zarar fırsatı sunar.\"\n    },\n    {\n      \"criterion\": \"Yüksek Hacim (volume ve quoteVolume)\",\n      \"description\": \"Paritenin son 24 saatteki işlem hacmi. Yüksek hacim, piyasa likiditesini ve işlemleri daha kolay gerçekleştirme yeteneğini gösterir. Scalp trading için likidite esastır.\"\n    },\n    {\n      \"criterion\": \"Yüksek Fiyat Volatilitesi (price_spread_percent ve openPrice_spread_percent)\",\n      \"description\": \"Paritenin 24 saatlik fiyat aralığının, mevcut veya açılış fiyatına oranı. Yüksek spread, kısa sürede büyük fiyat hareketleri potansiyelini işaret eder.\"\n    },\n    {\n      \"criterion\": \"Range Pozisyonu (range_position_percent)\",\n      \"description\": \"Paritenin güncel fiyatının, 24 saatlik fiyat aralığı içindeki konumunu gösterir. Aşırı uçlarda (üst veya alt) olan pariteler, potansiyel geri çekilme veya devam sinyalleri verebilir.\"\n    },\n    {\n      \"criterion\": \"Yüksek Ticaret Sayısı (count)\",\n      \"description\": \"24 saatlik periyotta gerçekleşen işlem sayısı. Yüksek işlem sayısı, paritenin aktif olarak işlem gördüğünü ve ilgi çektiğini gösterir.\"\n    }\n  ],\n  \"recommendedPairs\": [\n    {\n      \"symbol\": \"SAPIENUSDT\",\n      \"confidenceScore\": 85,\n      \"analysis\": {\n        \"marketAnomaly\": \"Piyasa genelindeki ortalama volatilite ve hacmin üzerinde, yüksek bir günlük fiyat değişimi (%33.297) sergilemektedir. Bu durum, piyasadan ayrışan güçlü bir momentumu işaret eder.\",\n        \"internalAnomaly\": \"Yüksek range_position_percent (%76.41) ve yüksek price_spread_percent (%32.76) ile kendi iç dinamiklerinde de yüksek volatilite ve fiyat hareketliliği göstermektedir. Yüksek hacim ve ticaret sayısı, bu hareketliliğin likidite ile desteklendiğini gösterir.\"\n      }\n    },\n    {\n      \"symbol\": \"MEMEUSDT\",\n      \"confidenceScore\": 82,\n      \"analysis\": {\n        \"marketAnomaly\": \"Piyasa genelindeki diğer çiftlere kıyasla olağanüstü yüksek bir günlük fiyat değişimi (%36.440) ve çok yüksek quoteVolume sergilemektedir. Bu, yoğun bir ilgi ve işlem aktivitesi olduğunu gösterir.\",\n        \"internalAnomaly\": \"Meme coinler arasında tipik olan yüksek volatiliteyi gösteren yüksek openPrice_spread_percent (%67.89) ile dikkat çekmektedir. range_position_percent'in (%56.72) ortada olması, potansiyel devam eden bir hareketlilik için alan bırakmaktadır. Yüksek ticaret sayısı, likiditenin yeterli olduğunu teyit eder.\"\n      }\n    },\n    {\n      \"symbol\": \"PUMPUSDT\",\n      \"confidenceScore\": 78,\n      \"analysis\": {\n        \"marketAnomaly\": \"Belirgin bir günlük fiyat değişimi (%5.638) ve astronomik derecede yüksek hacim (91 milyar üzeri quoteVolume) sergilemektedir. Bu, piyasa genelinde dikkate değer bir aktiviteye işaret eder.\",\n        \"internalAnomaly\": \"Yüksek range_position_percent (%74.9) ve nispeten düşük price_spread_percent (%8.21) ile, fiyatın 24 saatlik aralığın üst yarısında agresif bir şekilde hareket ettiğini göstermektedir. Bu durum, yukarı yönlü momentumun güçlü olabileceğini düşündürmektedir.\"\n      }\n    },\n    {\n      \"symbol\": \"ETHUSDT\",\n      \"confidenceScore\": 75,\n      \"analysis\": {\n        \"marketAnomaly\": \"Majör bir varlık olarak, %2.202'lik sağlıklı bir günlük fiyat değişimi ve yüksek hacim ile piyasanın ana eğilimini takip etmektedir. Yüksek ticaret sayısı, likiditenin derinliğini gösterir.\",\n        \"internalAnomaly\": \"Nispeten daha düşük fiyat spreadleri (%5.8) ile daha stabil bir volatilite sergilerken, range_position_percent'in (%52.18) ortada olması, orta vadeli trend takip stratejileri için de uygun bir zemin oluşturmaktadır. Kısa vadeli hareketler için de işlem hacmi yeterlidir.\"\n      }\n    }\n  ],\n  \"strategies\": [\n    {\n      \"symbol\": \"SAPIENUSDT\",\n      \"direction\": \"Long\",\n      \"logic\": \"Yüksek günlük fiyat değişimi, yüksek volatilite ve kendi 24 saatlik aralığının üst yarısındaki konumu (range_position_percent: %76.41), paritenin yükseliş momentumunun devam edebileceğine işaret ediyor. Ayrıca, yüksek işlem hacmi, bu hareketin likidite ile desteklendiğini gösteriyor.\",\n      \"entryLevels\": [\n        {\n          \"type\": \"Primary\",\n          \"level\": \"0.26750\",\n          \"logic\": \"Mevcut son fiyatın hemen üzeri ve 1 dakikalık kline verilerindeki son hareketliliğin teyidi.\"\n        },\n        {\n          \"type\": \"Secondary\",\n          \"level\": \"0.26400\",\n          \"logic\": \"Eğer fiyat kısa bir geri çekilme yaşarsa, daha önceki bir destek seviyesinden alım fırsatı.\"\n        }\n      ],\n      \"timeframe\": {\n        \"tradeDuration\": 5,\n        \"recommendationValidity\": 15\n      },\n      \"riskManagement\": {\n        \"stopLoss\": {\n          \"level\": \"0.26200\",\n          \"logic\": \"Yakın bir önceki önemli düşük fiyat seviyesinin biraz altı, yükseliş momentumunun kırılması durumunda hızlı çıkış için.\"\n        },\n        \"riskCalculationLogic\": \"Pozisyon büyüklüğü, stop-loss seviyesine göre hesaplanan maksimum kabul edilebilir kayıp miktarı (örneğin, sermayenin %1-2'si) üzerinden belirlenmelidir. Mevcut fiyat ve giriş seviyelerine göre potansiyel kazanç/kayıp oranı (Risk/Ödül) dikkate alınmalıdır.\"\n      },\n      \"takeProfitLevels\": [\n        {\n          \"level\": \"0.27500\",\n          \"riskRewardRatio\": \"1:1.5\",\n          \"logic\": \"Mevcut giriş seviyesinden yaklaşık %3'lük bir artış. Risk/ödül oranını sağlamak için ilk hedef.\"\n        },\n        {\n          \"level\": \"0.28500\",\n          \"riskRewardRatio\": \"1:2.5\",\n          \"logic\": \"24 saatlik yüksek fiyatın (highPrice: 0.28701) yakınlarına bir hedef. Yükseliş momentumunun güçlü olması durumunda ulaşılabilir bir seviye.\"\n        }\n      ]\n    },\n    {\n      \"symbol\": \"MEMEUSDT\",\n      \"direction\": \"Long\",\n      \"logic\": \"Olağanüstü yüksek günlük fiyat değişimi (%36.440) ve yüksek hacim, bu paritenin güçlü bir momentumu olduğunu gösteriyor. Yüksek openPrice_spread_percent, potansiyel bir yukarı yönlü hareket için alan olduğunu işaret ediyor. Meme coinlerin doğası gereği, hızlı ve büyük hareketler beklenebilir.\",\n      \"entryLevels\": [\n        {\n          \"type\": \"Primary\",\n          \"level\": \"0.0035700\",\n          \"logic\": \"Son dakikalık kline verilerindeki mevcut işlem fiyatına yakın bir giriş.\"\n        },\n        {\n          \"type\": \"Secondary\",\n          \"level\": \"0.0035200\",\n          \"logic\": \"Eğer kısa bir geri çekilme olursa, bu seviyeden ek alım yapma imkanı.\"\n        }\n      ],\n      \"timeframe\": {\n        \"tradeDuration\": 3,\n        \"recommendationValidity\": 10\n      },\n      \"riskManagement\": {\n        \"stopLoss\": {\n          \"level\": \"0.0034700\",\n          \"logic\": \"Yakın bir önceki önemli düşük fiyat seviyesinin biraz altında, hızlı çıkış stratejisi.\"\n        },\n        \"riskCalculationLogic\": \"Meme coinlerin aşırı volatilitesi göz önüne alındığında, pozisyon büyüklüğü daha da dikkatli yönetilmelidir. Sermayenin küçük bir yüzdesi (örneğin %0.5-1) ile işlem yapmak önerilir.\"\n      },\n      \"takeProfitLevels\": [\n        {\n          \"level\": \"0.0036800\",\n          \"riskRewardRatio\": \"1:1.5\",\n          \"logic\": \"Mevcut giriş seviyesinden yaklaşık %3'lük bir artış. İlk hedef.\"\n        },\n        {\n          \"level\": \"0.0038500\",\n          \"riskRewardRatio\": \"1:2.5\",\n          \"logic\": \"24 saatlik yüksek fiyatın (highPrice: 0.0043230) daha erken bir aşaması için hedef. Yüksek momentumun devamı durumunda potansiyel.\"\n        }\n      ]\n    },\n    {\n      \"symbol\": \"PUMPUSDT\",\n      \"direction\": \"Long\",\n      \"logic\": \"Olağanüstü yüksek hacim ve yüksek range_position_percent, paritenin güçlü bir yukarı yönlü momentumla hareket ettiğini gösteriyor. Fiyatın, 24 saatlik aralığın üst bandına yakın olması, momentumun devam edebileceği beklentisini artırıyor.\",\n      \"entryLevels\": [\n        {\n          \"type\": \"Primary\",\n          \"level\": \"0.0032100\",\n          \"logic\": \"Mevcut son fiyat ve 1 dakikalık kline'lardaki hareketliliğe uygun giriş.\"\n        },\n        {\n          \"type\": \"Secondary\",\n          \"level\": \"0.0031800\",\n          \"logic\": \"Kısa bir geri çekilme durumunda değerlendirilecek destek seviyesi.\"\n        }\n      ],\n      \"timeframe\": {\n        \"tradeDuration\": 4,\n        \"recommendationValidity\": 12\n      },\n      \"riskManagement\": {\n        \"stopLoss\": {\n          \"level\": \"0.0031600\",\n          \"logic\": \"Yakın bir önceki önemli düşük fiyat seviyesinin biraz altı.\"\n        },\n        \"riskCalculationLogic\": \"Yüksek hacim ve volatiliteye sahip bu tür paritelerde, pozisyon büyüklüğü sermayenin küçük bir yüzdesiyle sınırlanmalıdır. Risk/ödül oranı sürekli gözden geçirilmelidir.\"\n      },\n      \"takeProfitLevels\": [\n        {\n          \"level\": \"0.0032800\",\n          \"riskRewardRatio\": \"1:1.5\",\n          \"logic\": \"Mevcut giriş seviyesinden yaklaşık %2.5'lik bir artış. İlk hedef.\"\n        },\n        {\n          \"level\": \"0.0033500\",\n          \"riskRewardRatio\": \"1:2.3\",\n          \"logic\": \"24 saatlik yüksek fiyatın (highPrice: 0.0032700) biraz üzerine bir hedef. Yükselişin devamı durumunda potansiyel hedef.\"\n        }\n      ]\n    },\n    {\n      \"symbol\": \"ETHUSDT\",\n      \"direction\": \"Long\",\n      \"logic\": \"Majör bir varlık olarak ETH, piyasa genelinde sağlıklı bir yükseliş momentumu sergiliyor. Yüksek hacim, likidite ve nispeten stabil volatilite, scalping için uygun bir zemin oluşturuyor. range_position_percent'in ortada olması, devam eden bir hareket potansiyeline işaret ediyor.\",\n      \"entryLevels\": [\n        {\n          \"type\": \"Primary\",\n          \"level\": \"4760.00\",\n          \"logic\": \"Mevcut son fiyat civarında, kısa vadeli direnç seviyesinin kırılması beklenerek giriş.\"\n        },\n        {\n          \"type\": \"Secondary\",\n          \"level\": \"4740.00\",\n          \"logic\": \"Eğer kısa bir geri çekilme yaşanırsa, bir önceki 1 dakikalık mumdaki kapanış fiyatı civarında giriş.\"\n        }\n      ],\n      \"timeframe\": {\n        \"tradeDuration\": 10,\n        \"recommendationValidity\": 20\n      },\n      \"riskManagement\": {\n        \"stopLoss\": {\n          \"level\": \"4725.00\",\n          \"logic\": \"Yakın bir önceki önemli destek seviyesinin veya önemli bir 1 dakikalık mumun dibinin biraz altı.\"\n        },\n        \"riskCalculationLogic\": \"Sermayenin %1-1.5'i ile işlem yapılabilir. Giriş seviyesi ve stop-loss arasındaki yüzde farkı, pozisyon büyüklüğünü belirlemek için kullanılacaktır. Risk/ödül oranı hedeflenmelidir.\"\n      },\n      \"takeProfitLevels\": [\n        {\n          \"level\": \"4785.00\",\n          \"riskRewardRatio\": \"1:1.5\",\n          \"logic\": \"Mevcut giriş seviyesinden yaklaşık %0.5'lik bir artış. Kısa vadeli hedef.\"\n        },\n        {\n          \"level\": \"4820.00\",\n          \"riskRewardRatio\": \"1:2.2\",\n          \"logic\": \"24 saatlik yüksek fiyata (highPrice: 4888.49) yaklaşan bir hedef. Orta vadeli momentumun devamı beklentisiyle.\"\n        }\n      ]\n    }\n  ]\n}\n```", "dt": "2025-08-23T16:42:38.465Z", "elapsed": 17727}