{"name": "sv-gauss", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3012", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@debut/indicators": "^1.3.22", "@emotion/cache": "latest", "@emotion/react": "^11.11.3", "@emotion/server": "latest", "@emotion/styled": "^11.11.0", "@google/generative-ai": "^0.24.1", "@mui/icons-material": "latest", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.15.7", "@mui/material-nextjs": "^5.15.7", "@mui/styled-engine-sc": "^6.0.0-alpha.14", "@mui/styles": "^5.15.7", "@mui/x-charts": "^6.19.5", "@mui/x-data-grid": "^6.19.4", "@next-auth/mongodb-adapter": "^1.1.3", "@observablehq/plot": "^0.6.13", "@redis/client": "^1.5.16", "@redis/json": "^1.0.6", "@tabler/icons-react": "^2.46.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "better-sqlite3": "^12.2.0", "binance-api-node": "^0.12.7", "body-parser": "^1.20.2", "child_process": "^1.0.2", "clsx": "^2.1.0", "cors": "^2.8.5", "cron-job-manager": "^2.3.1", "cron-parser": "^4.9.0", "cronstrue": "^2.47.0", "d3": "^7.8.5", "dotenv": "^16.4.1", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.18.2", "fs": "^0.0.1-security", "indicatorts": "^2.2.1", "ioredis": "^5.4.1", "ioredis-rejson": "^1.0.10", "json-2-csv": "^5.5.5", "jsonwebtoken": "^9.0.2", "jstat": "^1.9.6", "lightweight-charts": "^4.1.7", "lodash": "^4.17.21", "mathjs": "^12.4.0", "moment": "^2.30.1", "mongodb": "^6.3.0", "next": "14.1.0", "next-auth": "^4.24.5", "node-binance-api": "^0.13.1", "path": "^0.12.7", "ps-node": "^0.1.6", "react": "^18", "react-dom": "^18", "react-google-charts": "^4.0.1", "react-icons": "^5.0.1", "react-pro-sidebar": "^1.1.0-alpha.2", "shelljs": "^0.8.5", "simple-statistics": "^7.8.3", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "stock-technical-indicators": "^2.0.1", "styled-components": "^6.1.8", "technicalindicators": "^3.1.0", "tree-kill": "^1.2.2", "underscore-node": "^0.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "20.11.15", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "5.3.3"}}