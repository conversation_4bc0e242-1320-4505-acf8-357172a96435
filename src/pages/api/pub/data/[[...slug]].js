/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */
// import Redis from 'ioredis-rejson';
const Redis = require('ioredis');
// import { createClient } from '@redis/client';
// import RedisJsonModule from '@redis/json';
const process = require('process');
var fs = require('fs')
var spawn = require('child_process').spawn;
const path = require('path');
const Binance = require('node-binance-api');
const BinanceBAN = require('binance-api-node');
// import Binance from 'binance-api-node'
// const Binance2 = require('binance-api-node').default

import clientPromise from "../../../../lib/mongodb.js"
import sqlClient from "../../../../lib/sqlite";
import sqlMarketClient from "../../../../lib/sqlmarketlite.js";
import sqlTradesClient from "../../../../lib/sqltradeslite.js";
import sqlBacktestClient from "../../../../lib/sqlbacktestlite.js";
const fnxDb = require('../../../../lib/fnx/fnx.db');
const fnxApis = require('../../../../lib/fnx/fnx.apis.js');

const mongoDBColls = {
    variables: 'app.main.variables',
};
var mainProcessID = 0;
const mongoDBDB = 'algoweb'
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const io = global.io;
//http://localhost:3000/api/app/data/9fcf487017fe351ec3a19c002
export default async function handler(req, res) {
    const { slug } = req.query
    const data = Array.isArray(slug) ? slug[0] : null;
    const dbConnMongo = await clientPromise;
    const redisClient = new Redis({
        host: '127.0.0.1',
        port: 6379,
        connectionName: 'gauss',
        retryStrategy(times) {
            const delay = Math.min(times * 50, 2000);
            return delay;
        },
    });
    //DONE:: redis with JSON Module!

    if (process.pid) {
        // console.log('Main Pid: ' + process.pid);
        mainProcessID = process.pid;
      }

    //mongoDB
    // const dbConn = await clientPromise;
    if (data == 'test') {
        //http://localhost:3011/api/pub/data/test
        console.log(new Date(Date.now()).toISOString(), 'EP.test')
        let dt=Date.now()
        let qq =`  
                    select hero_name, is_xman, was_snapped from hero h
                    inner join hero_power hp on h.hero_id = hp.hero_id
                `;
        let resx = await fnxDb.q.query(sqlClient, qq);
        let sure = Date.now() - dt
        redisClient && redisClient.quit();
        res.status(200).json({ data: resx, sure });

    } 
    // else if (data == 'appvars') {
    //     //http://localhost:3000/api/pub/data/appvars
    //     const dbConn = await clientPromise;
    //     const dbCollection = dbConn.db(mongoDBDB).collection(mongoDBColls.variables); 
    //     const varGroupName = Array.isArray(slug) && slug[1] ? slug[1] : 'main'
    //     var q = { dataName: varGroupName };
    //     console.log(new Date(Date.now()).toISOString(), 'EP03.01 - appvars main', q)
    //     var varz = await dbCollection.findOne(q, {});
    //     res.status(200).json({ data: varz ? varz.values : {} }) // userInitials
        
    // } 
    else if (data == 'battleStart') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('battleStart');
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        // const dexApi = await fnxApis.dex.getDexApi({
        //     dexPack: Binance,
        //     body: req.body,
        // })?.api;
        try {
            var getResp = await fnxApis.battle.start({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                // dbConn,
                sqlClient,
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
                // dexApi
            });
            // console.log('battleStart getResp', getResp)
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battleStart - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'battleStop') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battleStop')
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.battle.stop({
                redisClient,
                // dbConn,
                sqlClient,
                sqlMarketClient,
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battleStop - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'battlenoderestart') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battlenoderestart')
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.battleNode.restart({
                // dbConn,
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battlenoderestart - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'battlenodekill') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battlenodekill')
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.battleNode.kill({
                // dbConn,
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battlenodekill - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'battlehardreset') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battlehardreset')
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.battle.hardReset({
                // dbConn,
                redisClient,
                sqlClient,
                sqlMarketClient,
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
                mainProcessID,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battlehardreset - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'battleList') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battleList')
        try {
            var getResp = await fnxApis.battle.list({
                redisClient,
                sqlClient,
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'rulesetsgroup') {
        //http://localhost:3000/api/pub/data/appvars
        if (req.method === 'POST') {
            // res.status(405).send({ message: 'Only POST requests allowed' })
            // return
            console.log(new Date(Date.now()).toISOString(), 'save rulesetsgroup')
            // const dbConn = await clientPromise;

            try {
                var getResp = await fnxApis.market.saverulesetsgroup({
                    dbConnMongo: dbConnMongo, //false,// 
                    sqlClient,
                    sqlMarketClient,
                    slug,
                    query: req.query,
                    body: req.body,
                })
                redisClient && redisClient.quit();
                res.status(200).json({ ...getResp })
                return
            } catch (e) {
                console.log('err: rulesetsgroup - ', e)
                redisClient && redisClient.quit();
                res.status(405).send({ message: 'err check logs' })
                return
            }

        } else {
            console.log(new Date(Date.now()).toISOString(), 'get rulesetgroups')
            try {
                // getAllPairs
                var getRespStg = await fnxApis.market.getrsgx({
                    db: sqlMarketClient,
                    dbConnMongo: dbConnMongo, //false,// 
                    query: req.query,
                    body: req.body,
                });
                var getResp = {
                    data: getRespStg
                }
                redisClient && redisClient.quit();
                res.status(200).json({ ...getResp });
                return
            } catch (e) {
                console.log('err: battleList - ', e)
                redisClient && redisClient.quit();
                res.status(405).send({ message: 'err check logs' })
                return
            }
        }
    } else if (data == 'createtestruleset') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'createtestruleset')
        try {
            // getAllPairs
            var getRespStg = await fnxApis.market.setrsgTestData({
                db: sqlMarketClient
            });
            var getResp = {
                data: getRespStg
            }
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'deleterulesetsgroup') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'deleterulesetsgroup')
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.market.deleterulesetsgroup({
                dbConnMongo: dbConnMongo, //false,// 
                sqlClient,
                sqlMarketClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: deleterulesetsgroup - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'resetklines') {
        //http://localhost:3000/api/pub/data/resetklines
        console.log(new Date(Date.now()).toISOString(), 'resetklines')
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.market.resetKlines({
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: resetklines - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }

    } else if (data == 'klines') {
        //http://localhost:3000/api/pub/data/appvars
        // console.log(new Date(Date.now()).toISOString(), 'klines')
        try {
            var getResp = await fnxApis.market.klinesWindcx_data({
                redisClient,
                sqlClient,
                sqlMarketClient,
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'trades') {
        //http://localhost:3000/api/pub/data/trades
        // console.log(new Date(Date.now()).toISOString(), 'trades')
        try {
            var getResp = await fnxApis.market.getTradeList({
                redisClient,
                // sqlClient,
                // sqlMarketClient,
                // sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: getTradeList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'getTradeList err check logs' })
            return
        }
    } else if (data == 'trades4symbol') {
        //http://localhost:3000/api/pub/data/trades
        // console.log(new Date(Date.now()).toISOString(), 'trades')
        try {
            var getResp = await fnxApis.market.getTradeList4Symbol({
                redisClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: getTradeList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'getTradeList err check logs' })
            return
        }
    } else if (data == 'tradeslist4chart') {
        //http://localhost:3000/api/pub/data/trades
        // console.log(new Date(Date.now()).toISOString(), 'trades')
        try {
            var getResp = await fnxApis.market.getTradeList4Chart({
                redisClient,
                // sqlClient,
                // sqlMarketClient,
                // sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: getTradeList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'getTradeList err check logs' })
            return
        }
    } else if (data == 'closeallopentrades') {
        //http://localhost:3000/api/pub/data/closeallopentrades
        console.log(new Date(Date.now()).toISOString(), 'EP.closeallopentrades', req.query)
        let dt=Date.now()
        try {
            // console.log('_redisClient', redisClient)
            let resp = await fnxApis.market.closeOpenTrades({redisClient, query: req.query, section: 'userCloseRequest'})
            let sure = Date.now() - dt
            // redisClient && redisClient.quit();
            res.status(200).json({ data: resp, sure });
        }
        catch (e) {
            console.log('killall error', e)
            redisClient && redisClient.quit();
            res.status(501).json({ data: false });
        }
    } else if (data == 'indicatorcalculations') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'indicatorcalculations')
        try {
            var getResp = await fnxApis.market.indicatorCalculations({
                sqlClient,
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'nodelist') {
        //http://localhost:3000/api/pub/data/nodelist
        console.log(new Date(Date.now()).toISOString(), 'nodelist')
        try {
            var getResp = await fnxApis.battleNode.list({
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'nodetasklist') {
        //http://localhost:3000/api/pub/data/nodetasklist
        console.log(new Date(Date.now()).toISOString(), 'nodetasklist')
        try {
            var getResp = await fnxApis.battleNode.tasklist({
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'nodetasklistmonitor') {
        //http://localhost:3000/api/pub/data/nodetasklist
        console.log(new Date(Date.now()).toISOString(), 'nodetasklistmonitor')
        try {
            var getResp = await fnxApis.battleNode.tasklistmonitor({
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'nodeerrors') {
        //http://localhost:3000/api/pub/data/nodetasklist
        console.log(new Date(Date.now()).toISOString(), 'nodeerrors')
        try {
            var getResp = await fnxApis.battleNode.nodeerrors({
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'nodetask') {
        //http://localhost:3000/api/pub/data/nodetask
        console.log(new Date(Date.now()).toISOString(), 'nodetask')
        try {
            var getResp = await fnxApis.battleNode.task({
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'reconnode') {
        //http://localhost:3000/api/pub/data/reconnode
        console.log(new Date(Date.now()).toISOString(), 'reconnect node')
        try {
            var getResp = await fnxApis.battleNode.reconnode({
                redisClient,
                slug,
                query: req.query,
                body: req.body,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'market_futuresdaily') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'market_futuresdailyx')
        try {
            let force = req.query.force && parseFloat(req.query.force) == 1
            // console.log('force', slug, req.query.force, force)
            var getResp = await fnxApis.market.market_futuresdaily({
                redisClient,
                dbConnMongo, //false,//
                sqlClient,
                sqlMarketClient,
                slug,
                binanceApi,
                query: req.query,
                body: req.body,
                force,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'market_futuresdailylog') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'market_futuresdailylog')
        try {
            let force = req.query.force && parseFloat(req.query.force) == 1
            // console.log('force', slug, req.query.force, force)
            var getResp = await fnxApis.market.market_futuresdailylog({
                redisClient, 
                binanceApi,
                query: req.query,
                body: req.body,
                force,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: market_futuresdailylog - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'market_futuresdaily_live') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'getAllPairs')
        try {
            // getAllPairs
            let pairOnly = req.query.paironly ? req.query.paironly : 1
            var getRespStg = await fnxApis.binance.getAllPairs({
                pairOnly: parseFloat(pairOnly) === 1
            })
            var getResp = {
                data: getRespStg
            }
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'market_futuresdaily_data') {
        //http://localhost:3000/api/pub/data/appvars //TODO 29.11.2024...
        console.log(new Date(Date.now()).toISOString(), 'market_futuresdaily_data')
        try {
            let force = req.query.force && parseFloat(req.query.force) == 1
            // console.log('force', slug, req.query.force, force)
            var getResp = await fnxApis.market.market_futuresdailyMongolog({
                redisClient, 
                dbConnMongo: dbConnMongo, //false,//
                binanceApi,
                query: req.query,
                body: req.body,
                force,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: market_futuresdaily_data - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'indicatorsandparams') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'indicatorsandparams')
        try {
            // getAllPairs
            var getRespStg = await fnxApis.market.getIndicatorsAndParams();
            var getResp = {
                data: getRespStg
            }
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'getactivebattleparameters') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'getactivebattleparameters')
        try {
            // getAllPairs
            var getRespStg = await fnxApis.gauss.getBattleParams({ redisClient });
            var getResp = {
                data: getRespStg
            }
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: listsavebattleparameters - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'listsavebattleparameters') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'listsavebattleparameters')
        try {
            // getAllPairs
            var getRespStg = await fnxApis.battle.listsavedparameters({
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,}
            );
            var getResp = {
                data: getRespStg
            }
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: listsavebattleparameters - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'savebattleparameters') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'savebattleparameters')
        // const dbConn = await clientPromise;
        if (req.method !== 'POST') {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var getResp = await fnxApis.battle.saveparameters({
                // dbConn,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();

            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: savebattleparameters - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'loadbattleparameters') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'loadbattleparameters')
        try {
            var getResp = await fnxApis.battle.loadparameters({
                // dbConn,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: loadbattleparameters - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'deletebattleparametersfile') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'deletebattleparametersfile')
        try {
            var getResp = await fnxApis.battle.deleteparametersfile({
                // dbConn,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: deletebattleparametersfile - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else if (data == 'nodelist_') {
        //http://localhost:3000/api/pub/data/nodelist
        console.log(new Date(Date.now()).toISOString(), 'nodelist')
        let checkState = req.query?.checkstate && parseFloat(req.query?.checkstate) === 1 
        let dt=Date.now()
        try {
            await fnxDb.q.checktable.core_nodes({db: sqlClient});
        } catch (eCheck) {
            console.log('error', eCheck);
        }
        try {
            let qq =`  
                SELECT *
                FROM core_nodes
                Where is_deleted = 0;
                `;
        let resx = await fnxDb.q.query(sqlClient, qq);
        if (checkState) {
            await fnxApis.battleNode.checkProcess(resx, sqlClient)
            resx = await fnxDb.q.query(sqlClient, qq);
        }
        let sure = Date.now() - dt
        redisClient && redisClient.quit();
        res.status(200).json({ data: resx, sure });
        }
        catch(e) {
            console.log('nodelist error', e)
            redisClient && redisClient.quit();
            res.status(501).json({ data: false });
        }
        
    } else if (data == 'processkill') {
        //http://localhost:3000/api/pub/data/processkill
        console.log(new Date(Date.now()).toISOString(), 'EP.processkill', req.query)
        let process_id = req.query?.process_id && parseFloat(req.query?.process_id)
        let dt=Date.now()
        try {
            await fnxApis.battleNode.killProcess(process_id, sqlClient)
            let sure = Date.now() - dt
            redisClient && redisClient.quit();
            res.status(200).json({ data: process_id, sure });
        }
        catch (e) {
            console.log('processkill error', e)
            redisClient && redisClient.quit();
            res.status(501).json({ data: false });
        }
    } else if (data == 'resumetask') {
        //http://localhost:3000/api/pub/data/resumetask
        console.log(new Date(Date.now()).toISOString(), 'EP.resumetask', req.query)
        let task_id = req.query?.task_id && parseFloat(req.query?.task_id)
        let dt=Date.now()
        try {
            await fnxApis.starttask({
                task_id, db: sqlClient, resume: true
            })
            let sure = Date.now() - dt
            redisClient && redisClient.quit();
            res.status(200).json({ data: task_id, sure });
        }
        catch (e) {
            console.log('resumetask error', e)
            redisClient && redisClient.quit();
            res.status(501).json({ data: false });
        }
    } else if (data == 'killall') {
        //http://localhost:3000/api/pub/data/killall
        console.log(new Date(Date.now()).toISOString(), 'EP.killall', req.query)
        let dt=Date.now()
        try {
            await fnxApis.battleNode.killAllX({redisClient, db: sqlClient})
            let sure = Date.now() - dt
            redisClient && redisClient.quit();
            res.status(200).json({ data: true, sure });
        }
        catch (e) {
            console.log('killAllX error', e)
            redisClient && redisClient.quit();
            res.status(501).json({ data: false });
        }
    } else if (data == 'backtestStart') {
        //http://localhost:3000/api/pub/data/killall
        console.log(new Date(Date.now()).toISOString(), 'EP.backtestStart-real', req.query)
        let dt=Date.now()
        try {
            // await fnxApis.battleNode.killAllX({redisClient, db: sqlClient})
            
            let sure = Date.now() - dt;
            var getResp = await fnxApis.backtest.start({
                io,
                dbConn: sqlBacktestClient,
                redisClient,
                binanceApi,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp, sure })
            return

        }
        catch (e) {
            console.log('backtestStart error', e)
            redisClient && redisClient.quit();
            res.status(501).json({ data: false });
        }
    } else if (data == 'runbacktest') {
        //http://localhost:3000/api/pub/data/appvars
        try {
            let getResp = {};
            redisClient && redisClient.quit();

            // io && io.emit('serverBCM', {
            //     mType: 'act',
            //     action: 'setActionStatus',
            //     actionValue: 1,
            //     actionDesc: 'Started calculations...',
            //     mAttachment: null,
            // });

            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'Started calculations',
                mDesc: 'Started calculations',
                mAttachment: null,
            });

            fnxApis.backtest.runScript(path.join(process.cwd(), '/nodes/backtest.js'), 'nodes', function (err) {
                if (err == 0) { 
                    console.log('finished running backtest');
                    getResp = {
                        error: false,
                        openReports: true,
                        data: {
                            status: 'backtest success!'
                        }
                    };
                    res.status(200).json({...getResp, });
                    return
                } else if (err == 1) {
                    console.log('backtest task is in progress');
                    getResp = {
                        error: false,
                        openReports: false,
                        data: {
                            status: 'backtest task is in progress'
                        }
                    };
                    res.status(200).json({...getResp, });
                    return
                    // throw err
                } else {
                    console.log('error in run script', err);
                    getResp = {
                        error: true,
                        errCode: err,
                    };
                    res.status(200).json({ ...getResp, });
                    return
                };
            }, io);

        } catch (e) {
            console.log('err: runbacktest - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'backtest') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'backtest')
        try {
            // fnxApis.log('slug', slug)
            let getResp = {}
            if (slug && slug[1]) {
                if(slug[1] == 'results') {
                    getResp = await fnxApis.backtest.results({
                        redisClient,
                        sqlClient,
                        sqlTradesClient,
                        slug,
                        query: req.query,
                        body: req.body,
                    })
                } else if(slug[1] == 'pairresults') {
                    getResp = await fnxApis.backtest.pairresults({
                        redisClient,
                        sqlClient,
                        sqlTradesClient,
                        slug,
                        query: req.query,
                        body: req.body,
                        pair: slug[2],
                    })
                }
            }
            // var getResp = await fnxApis.battle.list({
            //     redisClient,
            //     sqlClient,
            //     sqlTradesClient,
            //     slug,
            //     query: req.query,
            //     body: req.body,
            // })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dextest') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'dextest');


        try {
            // fnxApis.log('slug', slug)
            let getResp = {}
            let body = req.body;
            let form = JSON.parse(body);
            fnxApis.log('form', form);
            const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

            var options = {
                apiKey: apiKey,
                apiSecret: apiSecret,
                APIKEY: apiKey,
                APISECRET: apiSecret,
                useServerTime: true,
                'family': 4,
            };
            if (testDex) {
                options = {
                    ...options,
                    test: testDex,
                    // httpBase : "https://testnet.binance.vision/api",
                    httpFutures: 'https://testnet.binancefuture.com',
                    wsFutures: 'wss://fstream.binancefuture.com',
                }
            }

            const client = BinanceBAN.default(options);
            const dexApi = new Binance().options({
                ...options,
            });

            let servTime = await dexApi.futuresTime();
            console.log('client time', await client.futuresPing(), await client.futuresTime())
            
            var fBalance = await dexApi.futuresBalance();
            fBalance = Array.isArray(fBalance) && fBalance.filter(b => parseFloat(b.balance) !== 0);
            getResp = fBalance;

            redisClient && redisClient.quit();
            res.status(200).json({ error: false, data: {servTime, balance: getResp}, });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dexaccount') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('dexAccount1');
        try {
            let getResp = {}
            let body = req.body;
            let form = JSON.parse(body);
            const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

            var options = {
                apiKey: apiKey, apiSecret: apiSecret,
                APIKEY: apiKey, APISECRET: apiSecret,
                useServerTime: true, 'family': 4,
            };
            if (testDex) {
                options = {
                    ...options, test: testDex,
                    httpFutures: 'https://testnet.binancefuture.com',
                    wsFutures: 'wss://fstream.binancefuture.com',
                }
            }
            const dexApi = new Binance().options({
                ...options,
            });  
            // dexApi.time().then(time => fnxApis.log('dexApiTime:', time));
            // dexApi.websockets.userFutureData(console.log,console.log,console.log)
            
            getResp = await fnxApis.dex.faccount({
                redisClient, 
                sqlTradesClient,
                clientPromise,
                slug,
                query: req.query,
                body: req.body,
                dexApi,
                ...form, 
            });
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp, });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dexaccount2') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('dexAccount2');
        try {
            let getResp = {}

            let isBattleInDex = await fnxApis.battle.isBattleInExchange({redisClient});
            getResp = isBattleInDex ? await fnxApis.dex.faccount2({
                redisClient,
                slug,
                query: req.query,
                body: req.body,
            }): {data: false};
            redisClient && redisClient.quit();
            res.status(200).json({...getResp});
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dexpositions') {
        fnxApis.log('dexpositions');
        try {
            let getResp = {}
            let body = req.body;
            let form = JSON.parse(body);
            const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

            var options = {
                apiKey: apiKey, apiSecret: apiSecret,
                APIKEY: apiKey, APISECRET: apiSecret,
                useServerTime: true, 'family': 4,
            };
            if (testDex) {
                options = {
                    ...options, test: testDex,
                    httpFutures: 'https://testnet.binancefuture.com',
                    wsFutures: 'wss://fstream.binancefuture.com',
                }
            }
            const dexApi = new Binance().options({
                ...options,
            });  
            // dexApi.time().then(time => fnxApis.log('dexApiTime:', time));
            // dexApi.websockets.userFutureData(console.log,console.log,console.log)
            
            getResp = await fnxApis.dex.fpositionsoo({ // fnxApis.dex.fpositionsoo fnxApis.gauss.getDexPositions
                redisClient, 
                sqlTradesClient,
                clientPromise,
                slug,
                query: req.query,
                body: req.body,
                dexApi,
                ...form, 
            });
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp, });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dexbalance') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('dexbalance');
        try { 
            let getResp = {}
            let body = req.body;
            let form = JSON.parse(body); 
            const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

            var options = {
                apiKey: apiKey, apiSecret: apiSecret,
                APIKEY: apiKey, APISECRET: apiSecret,
                useServerTime: true, 'family': 4,
            };
            if (testDex) {
                options = {
                    ...options, test: testDex,
                }
            }
            const dexApi = new Binance().options({
                ...options,
            });  
            dexApi.time().then(time => fnxApis.log('dexApi fbalance Time:', time));
            
            getResp = await fnxApis.dex.fbalance({
                redisClient, 
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
                dexApi,
                ...form, 
            })
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp, });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dexallorders') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('futuresAllOrders');
        try { 
            let getResp = {}
            let body = req.body;
            let form = JSON.parse(body); 
            const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

            var options = {
                apiKey: apiKey, apiSecret: apiSecret,
                APIKEY: apiKey, APISECRET: apiSecret,
                useServerTime: true, 'family': 4,
            };
            if (testDex) {
                options = {
                    ...options, test: testDex,
                }
            }
            const dexApi = new Binance().options({
                ...options,
            });  
            dexApi.time().then(time => fnxApis.log('dexApi futuresAllOrders Time:', time));
            
            getResp = await fnxApis.dex.futuresAllOrders({
                redisClient, 
                sqlTradesClient,
                slug,
                query: req.query,
                body: req.body,
                dexApi,
                ...form, 
            })
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp, });
            return
        } catch (e) {
            console.log('err: battleList - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    }  else if (data == 'dexsymbolorders') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('dexsymbolorders');
        try { 
            let getResp = {}
            let body = req.body;
            let form = body ? JSON.parse(body) : {}; 
            const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

            let symbol = slug[1];
            console.log('symbol', symbol, slug);

            getResp = symbol ? await fnxApis.dex.getorders({
                redisClient, 
                slug,
                query: req.query,
                body: req.body, symbol,
            }) : false;
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp, });
            return
        } catch (e) {
            console.log('err: dexsymbolorders - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dexinfo') {
        //http://localhost:3000/api/pub/data/appvars
        fnxApis.log('dexinfo');
        try { 
            let getResp = {}
            
            if (req.method !== 'POST') {
                var options = {
                    useServerTime: true, 'family': 4,
                };
                const dexApi = new Binance().options({
                    ...options,
                });
                dexApi.time().then(time => fnxApis.log('dexApi fdexInfo Time:', time));

                getResp = await fnxApis.dex.fdexInfo({
                    redisClient,
                    sqlTradesClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    dexApi,
                })
            } else {

                let body = req.body;
                let form = JSON.parse(body);
                // const { apiKey, apiSecret, dexCode, dexTitle, testDex} = form;

                var options = {
                    apiKey: apiKey, apiSecret: apiSecret,
                    APIKEY: apiKey, APISECRET: apiSecret,
                    useServerTime: true, 'family': 4,
                };
                if (testDex) {
                    options = {
                        ...options, test: testDex,
                    }
                }
                const dexApi = new Binance().options({
                    ...options,
                });
                dexApi.time().then(time => fnxApis.log('dexApi fdexInfo Time:', time));

                getResp = await fnxApis.dex.fbalance({
                    redisClient,
                    sqlTradesClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    dexApi,
                    ...form,
                });
                console.log('fBalance', getResp)
            }
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp, });
            return
        } catch (e) {
            console.log('err:fdexInfo - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'battlelogs') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battlelogs')
        try {
            var getResp = await fnxApis.battle.getlogs({
                redisClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battlelogs - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    }  else if (data == 'battledexlogs') {
        //http://localhost:3000/api/pub/data/appvars
        console.log(new Date(Date.now()).toISOString(), 'battledexlogs')
        try {
            var getResp = await fnxApis.battle.getdexlogs({
                redisClient,
                slug,
                query: req.query,
                body: req.body,
                modeDebug: false,
            })
            redisClient && redisClient.quit();
            res.status(200).json({ ...getResp })
            return
        } catch (e) {
            console.log('err: battlelogs - ', e)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
        
    } else {
        console.log(new Date(Date.now()).toISOString(), 'EP02.XX - data', data, )
        console.log(new Date(Date.now()).toISOString(), 'EP02.NN - query', req.query)
        redisClient && redisClient.quit();
     
        // console.log('doc', doc)
        redisClient && redisClient.quit();
        res.status(200).json({ error: true, data: data })
    }
    redisClient && redisClient.quit();

}
