/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */
// import Redis from 'ioredis-rejson';
const Redis = require('ioredis');
// import { createClient } from '@redis/client';
// import RedisJsonModule from '@redis/json';
const process = require('process');
var fs = require('fs')
var spawn = require('child_process').spawn;
const path = require('path');
const Binance = require('node-binance-api');
const BinanceBAN = require('binance-api-node');
// import Binance from 'binance-api-node'
// const Binance2 = require('binance-api-node').default

import clientPromise from "../../../../lib/mongodb.js"
import sqlClient from "../../../../lib/sqlite";
const fnxDb = require('../../../../lib/fnx/fnx.db');
const fnxAi = require('../../../../lib/fnx/fnx.aix.js');

const mongoDBColls = {
    variables: 'app.main.variables',
};
var mainProcessID = 0;
const mongoDBDB = 'algoweb'
const binanceApi = new Binance().options({
    // APIKEY: '<key>',
    // APISECRET: '<secret>'
});
const io = global.io;
//http://localhost:3000/api/app/data/9fcf487017fe351ec3a19c002
export default async function handler(req, res) {
    const { slug } = req.query
    const data = Array.isArray(slug) ? slug[0] : null;
    const dbConnMongo = await clientPromise;
    const redisClient = new Redis({
        host: '127.0.0.1',
        port: 6379,
        connectionName: 'gauss',
        retryStrategy(times) {
            const delay = Math.min(times * 50, 2000);
            return delay;
        },
    });
    //DONE:: redis with JSON Module!

    if (process.pid) {
        // console.log('Main Pid: ' + process.pid);
        mainProcessID = process.pid;
    }

    //mongoDB
    // const dbConn = await clientPromise;
    if (data == 'test') {
        //http://localhost:3011/api/pub/data/test
        console.log(new Date(Date.now()).toISOString(), 'EP.test')
        let dt = Date.now()
        let qq = `  
                    select hero_name, is_xman, was_snapped from hero h
                    inner join hero_power hp on h.hero_id = hp.hero_id
                `;
        let resx = await fnxDb.q.query(sqlClient, qq);
        let sure = Date.now() - dt
        redisClient && redisClient.quit();
        res.status(200).json({ data: resx, sure });

    } else if (data == 'prompts') {
        try {
            var getResp = await fnxAi.mongox.prompts.list({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                // dbConn,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                // dexApi
            });
            redisClient && redisClient.quit();
            res.status(200).json({ prompts: getResp })
            return
        } catch (e) {
            console.log('err: prompts - ', e, fnxAi)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'dataschemas') {
        try {
            var getResp = await fnxAi.mongox.dataschemes.list({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                // dbConn,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                // dexApi
            });
            redisClient && redisClient.quit();
            res.status(200).json({ dataschemes: getResp })
            return
        } catch (e) {
            console.log('err: prompts - ', e, fnxAi)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'pairklinedata') {
        try {
            var getResp = await fnxAi.redix.pairklinedata({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                slug,
                query: req.query,
                body: req.body,
            });
            redisClient && redisClient.quit();
            res.status(200).json({ data: getResp })
            return
        } catch (e) {
            console.log('err: prompts - ', e, fnxAi)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'update') {
        if (req.method === 'POST') {
            try {
                var updateResp = await fnxAi.mongox.prompts.update({
                    dbConnMongo: dbConnMongo,
                    redisClient,
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                });
                redisClient && redisClient.quit();
                res.status(200).json({ success: true, data: updateResp })
                return
            } catch (e) {
                console.log('err: update prompt - ', e)
                redisClient && redisClient.quit();
                res.status(405).send({ message: 'err check logs' })
                return
            }
        } else if (req.method === 'DELETE') {
            try {
                var deleteResp = await fnxAi.mongox.prompts.delete({
                    dbConnMongo: dbConnMongo,
                    redisClient,
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                });
                redisClient && redisClient.quit();
                res.status(200).json({ success: true, data: deleteResp })
                return
            } catch (e) {
                console.log('err: delete prompt - ', e)
                redisClient && redisClient.quit();
                res.status(405).send({ message: 'err check logs' })
                return
            }
        } else {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Method not allowed' })
            return
        }
    } else if (data == 'localllmlist') {
        try {
            var getResp = await fnxAi.ollama.models({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                // dexApi
            });
            redisClient && redisClient.quit();
            res.status(200).json({ models: getResp })
            return
        } catch (e) {
            console.log('err: prompts - ', e, fnxAi)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'localllmquery') {
        try {
            var getResp = await fnxAi.ollama.query({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                // dexApi
            });
            redisClient && redisClient.quit();
            res.status(200).json({ response: getResp })
            return
        } catch (e) {
            console.log('err: prompts - ', e, fnxAi)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'llmquery') {
        try {
            var getResp = await fnxAi.ollama.query_gemini({
                dbConnMongo: dbConnMongo, //false,// 
                redisClient,
                sqlClient,
                slug,
                query: req.query,
                body: req.body,
                // dexApi
            });
            redisClient && redisClient.quit();
            res.status(200).json({ response: getResp })
            return
        } catch (e) {
            console.log('err: prompts - ', e, fnxAi)
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (data == 'sandbox_savedata') {
        if (req.method === 'POST') {
            try {
                console.log('sandbox_savedata', new Date().toISOString())
                var getResp = await fnxAi.sqlite.savedata({ 
                    redisClient, 
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    // dexApi
                });
                redisClient && redisClient.quit();
                res.status(200).json({ result: getResp })
                return
            } catch (e) {
                console.log('err: prompts - ', e, fnxAi)
                redisClient && redisClient.quit();
                res.status(500).send({ message: 'err check logs' })
                return
            }
        } else {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Method not allowed' })
            return
        }
    } else if (data == 'prepare_anomaly_data') {
        if (req.method === 'POST') {
            try {
                console.log('sandbox_savedata', new Date().toISOString())
                var getResp = await fnxAi.anomali.prepare_anomaly_data({ 
                    redisClient, 
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    // dexApi
                });
                redisClient && redisClient.quit();
                res.status(200).json({ result: getResp })
                return
            } catch (e) {
                console.log('err: prompts - ', e, fnxAi)
                redisClient && redisClient.quit();
                res.status(500).send({ message: 'err check logs' })
                return
            }
        } else {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Method not allowed' })
            return
        }
    } else if (data == 'get_anomaly_list') {
        if (req.method === 'POST') {
            try {
                console.log('get_anomaly_list', new Date().toISOString())
                var getResp = await fnxAi.anomali.get_anomaly_list({ 
                    redisClient, 
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    // dexApi
                });
                redisClient && redisClient.quit();
                res.status(200).json({ data: getResp })
                return
            } catch (e) {
                console.log('err: prompts - ', e, fnxAi)
                redisClient && redisClient.quit();
                res.status(500).send({ message: 'err check logs' })
                return
            }
        } else {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Method not allowed' })
            return
        }
    } else if (data == 'get_anomaly_pairs') {
        if (req.method === 'POST') {
            try {
                console.log('get_anomaly_pairs', new Date().toISOString())
                var getResp = await fnxAi.anomali.get_anomaly_pairs({ 
                    redisClient, 
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    // dexApi
                });
                redisClient && redisClient.quit();
                res.status(200).json({ data: getResp })
                return
            } catch (e) {
                console.log('err: prompts - ', e, fnxAi)
                redisClient && redisClient.quit();
                res.status(500).send({ message: 'err check logs' })
                return
            }
        } else {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Method not allowed' })
            return
        }
    } else if (data == 'get_klines') {
        if (req.method === 'POST') {
            try {
                console.log('get_klines', new Date().toISOString())
                var getResp = await fnxAi.anomali.get_klines({ 
                    redisClient, 
                    sqlClient,
                    slug,
                    query: req.query,
                    body: req.body,
                    // dexApi
                });
                redisClient && redisClient.quit();
                res.status(200).json({ data: getResp })
                return
            } catch (e) {
                console.log('err: get_klines - ', e)
                redisClient && redisClient.quit();
                res.status(500).send({ message: 'err check logs' })
                return
            }
        } else {
            redisClient && redisClient.quit();
            res.status(405).send({ message: 'Method not allowed' })
            return
        }
    } else {
        console.log(new Date(Date.now()).toISOString(), 'EP02.XX - data', data,)
        console.log(new Date(Date.now()).toISOString(), 'EP02.NN - query', req.query)
        redisClient && redisClient.quit();

        // console.log('doc', doc)
        redisClient && redisClient.quit();
        res.status(200).json({ error: true, data: data })
    }
    redisClient && redisClient.quit();

}
