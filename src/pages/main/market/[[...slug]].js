/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import { signIn, signOut, useSession } from 'next-auth/react'
import Head from 'next/head'
import { useRouter } from 'next/router'

import { appvars } from '../../../lib/constants.js'
import * as React from 'react';
import Market from '../../../_components/pages/main.market.js';

export default function Home(props) {
    const router = useRouter();
    const { slug } = router.query

    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    });
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    const [updateMe, setUpdateMe] = React.useState(0)
    React.useEffect(() => {
        setUpdateMe(Date.now())
    }, [])
    return (
        <>
            <Head>
                <title>Gauss Algo - Market</title>
            </Head>
            <Market {...props} updateMe={updateMe} />
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

