/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import { signIn, signOut, useSession } from 'next-auth/react'
import Head from 'next/head'

import { appvars } from '../../lib/constants'
import * as React from 'react';
import UserAdvNodeTasks from '../../_components/pages/user.adv.nodeTasks';

export default function Home(props) {
    const { data: session } = useSession();
    return (
        <>
            <Head>
                <title>Gauss Algo - Node Tasks</title>
            </Head>
            <UserAdvNodeTasks {...props} />
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

