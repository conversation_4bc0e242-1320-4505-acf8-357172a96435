/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, { useEffect, useState, useContext } from 'react'
import '../lib/styles/globals.css'
import { Socket, io } from "socket.io-client"
import { SessionProvider, useSession } from "next-auth/react"
import PropTypes from 'prop-types';
import Head from 'next/head';
import { AppCacheProvider } from '@mui/material-nextjs/v14-pagesRouter';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import theme from '../lib/styles/theme';
import DarkModeProvider from "../lib/context/Provider.themeDarkMode";

const PORT = 3013;
export default function App(props) {
  const {
    Component,
    pageProps: { session, ...pageProps },
  } = props;


  const [socket, setSocket] = useState(null);
  const initSocket_io = () => {
    const socketz = io(`:${PORT + 1}`, { path: "/api/socket", addTrailingSlash: false });
    setSocket(socketz)
    global.socket = socketz;
    socketz.on('connect', () => {
      console.log('_app on connect', socketz?.id)
      socketz.emit('hello', 'hi to server!: ' + socketz?.id)
    })

    socketz?.on('disconnect', function (reason) {
      console.log('disconnected!', new Date(Date.now()).toLocaleString(), reason);
    });

    socketz.on('hello', data => {
      console.log('serverMsg: ', data)
    })

  }

  useEffect(() => { initSocket_io(); }, []) //          initSocket_ws();

  socket && socket.on('disconnect', function (reason) {
    console.log('disconnected!', new Date(Date.now()).toLocaleString(), reason);
  });


  socket && socket.on("connect_error", async err => {
    console.log(`connect_error due to ${err.message}`)
    await fetch("/api/socket")
  })

  return (
    <SessionProvider session={session}
      refetchInterval={5 * 60}
      refetchOnWindowFocus={true}>
      {Component.auth ? (
        <Auth>
          <AppCacheProvider {...props}>
            <Head>
              <meta name="author" content="Taner Subasi" />
              <meta name="description" content="GaussAlgo" />
              <meta name="viewport" content="initial-scale=1, width=device-width" />
            </Head>

            <DarkModeProvider>
              <ThemeProvider theme={theme}>
                {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
                <CssBaseline />
                <Component {...pageProps} socket={socket} />
              </ThemeProvider>
            </DarkModeProvider>
          </AppCacheProvider>
        </Auth>
      ) : (
          <AppCacheProvider {...props}>
            <Head>
              <meta name="author" content="Taner Subasi" />
              <meta name="description" content="GaussAlgo" />
              <meta name="viewport" content="initial-scale=1, width=device-width" />
            </Head>
            <DarkModeProvider>
              <ThemeProvider theme={theme}>
                {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
                <CssBaseline />
                <Component {...pageProps} />
              </ThemeProvider>
            </DarkModeProvider>
          </AppCacheProvider>
      )}
    </SessionProvider>
  )
}

function Auth({ children }) {
  const { status } = useSession({ required: true })
  if (status === "loading") {
    return <div>Loading...</div>
  }
  return children
}
