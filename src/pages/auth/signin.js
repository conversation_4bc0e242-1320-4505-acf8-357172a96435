/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import {
  Flex,
  Heading,
  Stack,
  Image,
} from '@chakra-ui/react';
import PgHeader from '../../_components/_tmp/generic/pg.header'
// import PgHeader from '@/components/pg.generic/pg.header'
import { Formik, Field, ErrorMessage } from 'formik'
import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/router'
import * as Yup from 'yup'
 
export default function SignIn({ csrfToken }) {
  const router = useRouter()
  const [error, setError] = useState(null)
  return (
    <>
      <PgHeader />
      <Stack minH={'100vh'} direction={{ base: 'column', md: 'row' }}>
        <Flex p={8} flex={1} align={'center'} justify={'center'}>
          <Stack spacing={4} w={'full'} maxW={'md'}>
            <Heading fontSize={'2xl'}>Sign in to your account</Heading>

            <Formik
              initialValues={{ username: '', password: '', tenantKey: '' }}
              validationSchema={Yup.object({
                username: Yup.string()
                  .max(60, 'Must be 30 characters or less')
                  .email('Invalid username name')
                  .required('Please enter your username'),
                password: Yup.string().required('Please enter your password'),
              })}
              onSubmit={async (values, { setSubmitting }) => {
                // console.log('${window.location.origin}', window.location.origin)
                const res = await signIn('credentials', {
                  redirect: false,
                  username: values.username,
                  password: values.password,
                  callbackUrl: `${window.location.origin}/app`,
                })
                if (res?.error) {
                  // console.log('res', res)
                  let errX = res.error === 'CredentialsSignin' ? 'Unauthorized !' : res.error;

                  setError(errX)
                } else {
                  setError(null)
                }
                if (res.url) router.push(res.url)
                setSubmitting(false)
              }}
            >
              {(formik) => (
                <form onSubmit={formik.handleSubmit}>
                  <div className="w-full items-center justify-center min-h-screen py-2">
                    <div className="bg-white rounded px-2 pt-6 pb-8 mb-4">
                      <input
                        name="csrfToken"
                        type="hidden"
                        defaultValue={csrfToken}
                      />

                      <div className="text-red-400 text-md text-center rounded p-2">
                        {error}
                      </div>
                      <div className="mb-4">
                        <label
                          htmlFor="username"
                          className="uppercase text-sm text-gray-600 font-bold"
                        >
                          Username
                          <Field
                            name="username"
                            aria-label="enter your username"
                            aria-required="true"
                            type="text"
                            className="w-full bg-gray-300 text-gray-900 mt-2 p-3 rounded-lg focus:outline-none focus:shadow-outline"
                          />
                        </label>

                        <div className="text-red-600 text-sm">
                          <ErrorMessage name="username" />
                        </div>
                      </div>
                      <div className="mb-6">
                        <label
                          htmlFor="password"
                          className="uppercase text-sm text-gray-600 font-bold"
                        >
                          password
                          <Field
                            name="password"
                            aria-label="enter your password"
                            aria-required="true"
                            type="password"
                            className="w-full bg-gray-300 text-gray-900 mt-2 p-3 rounded-lg focus:outline-none focus:shadow-outline"
                          />
                        </label>

                        <div className="text-red-600 text-sm">
                          <ErrorMessage name="password" />
                        </div>
                      </div>
                      <div className="flex items-center justify-center">
                        <button
                          type="submit"
                          className="uppercase text-sm font-bold tracking-wide bg-green-400 text-gray-100 p-3 rounded-lg w-full focus:outline-none focus:shadow-outline hover:shadow-xl active:scale-90 transition duration-150"
                        >
                          {formik.isSubmitting ? 'Please wait...' : 'Sign In'}
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              )}
            </Formik>
          </Stack>
        </Flex>
        <Flex flex={1}>
          <Image
            alt={'Login Image'}
            objectFit={'cover'}
            src={
              'https://source.unsplash.com/random/1600x900/?grocery'
            }
          />
        </Flex>
      </Stack>
    </>
  );
}