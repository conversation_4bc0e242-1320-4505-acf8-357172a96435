/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, {useEffect, useRef, useState} from "react";
import * as d3 from "d3";
import * as Plot from "@observablehq/plot";
import PlotFigure from "./PlotFigure.js";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import { Chart } from "react-google-charts";
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import { Histogram } from "./chart.histogram.js";
import { Button } from "@mui/material";
import { IconProgress } from "@tabler/icons-react";
import { LoadingButton } from "@mui/lab";

const FuturesEx = props => {
    const { token, indParam = 'priceChangePercent', 
        binq = false, rang = [-100, 100],
        mode,
    } = props;
    const [loading, setloading] = useState(false)
    const [data, setData] = useState(false);
    const [dataAct, setDataAct] = useState(false);
    const [dataHistogram, setdataHistogram] = useState(false);
    const [dataHistogramBins, setdataHistogramBins] = useState(false);
    const [dataTime, setdataTime] = useState(false);

    var histGenerator = d3.bin()
        .domain(rang)    // Set the domain to cover the entire intervall [0,1]
        .thresholds(binq);  // number of thresholds; this will create 19+1 bins

    const options = {
        title: indParam + " Histogram",
        legend: { position: "none" },
        // colors: ["green"],
        // colors: ["#e7711c"],
        // histogram: { lastBucketPercentile: binq },
        // vAxis: { scaleType: "mirrorLog" },
    };

    const optionsDistributions = {
        title: indParam + " Distribution",
        legend: { position: "none" },
        colors: ["#4285F4"],
        // chartArea: { width: 401 },
        hAxis: {
          ticks: [-1, -0.75, -0.5, -0.25, 0, 0.25, 0.5, 0.75, 1],
        },
        bar: { gap: 0 },
        histogram: {
          bucketSize: 5,
          maxNumBuckets: 300,
          minValue: -100,
          maxValue: 100,
        },
      };

    if (binq) options.histogram = { lastBucketPercentile: binq }

    const fetchMarketStats = async forceUpdate => {
        setloading(true)
        let res;
        forceUpdate && setData(false);
        try {
            let uri = '/api/pub/data/market_futuresdaily/' + (forceUpdate ? '?force=1' : '');
            console.log('uri', uri)
            res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            const datax = await res.json();
            setdataTime(new Date(Date.now()).toLocaleTimeString())

            Array.isArray(datax.data) && datax.data.map((d, ix) => {
                d.id = ix + 1
                d.volumeK = d.quoteVolume / 1000000
            });
            setData(datax.data);
            // console.log('datax.data', new Date(Date.now()).toLocaleTimeString(), datax.data)

            let dataActStg = []
            let dataHistogramStg = [["symbol", indParam, { role: "style" }]];
            Array.isArray(datax.data) && datax.data.map(d => {
                dataActStg.push(d[indParam]);
                dataHistogramStg.push([d['symbol'], d[indParam], "color: 'linear-gradient(to top, #4d4fb1, #3c97a8)'"])
            });
            setDataAct(dataActStg);
            setdataHistogram(dataHistogramStg);
            var bins = histGenerator(dataActStg);
            setdataHistogramBins(bins);
            // console.log('dataPrep', indParam,)
            // console.log('dataPrep bins', indParam, bins)
            // console.log('dataPrep dataHistogramStg', indParam, dataHistogramStg)
            setloading(false);

        } catch (e) {
            console.log('fetch error', e)
            alert('Hata', e)
            setloading(false)
        }
    }
    useEffect(() => {
        fetchMarketStats();
    }, []);
    return (
        <>
            <Box>
                <Card variant="outlined" sx={{ mx: 2, minWidth: 300 }}>
                    {dataHistogram && <Chart
                        chartType="Histogram"
                        width="300px"
                        height="300px"
                        data={dataHistogram}
                        options={mode === 'distribution' ? optionsDistributions : options}
                    />}
                </Card>

                &nbsp;
                {(props.indParam == 'priceChangePercent' || props.indParam == 'delta' || props.indParam == 'count') && data && (
                    <Card variant="outlined" sx={{ mx: 2, minWidth: 300 }}>
                        <MarketSummary data={data} indParam={props.indParam} />
                    </Card>
                )}
                {props.indParam == 'priceChangePercent' && (
                    <Card variant="outlined" sx={{ mx: 2, minWidth: 300 }}>
                        <LoadingButton loading={loading} onClick={() => fetchMarketStats(true)}>refresh data!</LoadingButton>
                    </Card>
                )}

            {/* {props.showTable && (
                <>
                    <Card variant="outlined" sx={{ mx: 2, my: 1, minWidth: 300 }}>
                        show table!
                        <div>
                            {JSON.stringify(dataAct, " ", 4)}
                        </div>
                    </Card>
                </>
            )} */}
            </Box>
        </>
    )
};

export default FuturesEx;


const MarketSummary = props => {
    const {data, indParam} = props;

    const [dataRaw, setdataRaw] = useState(false);
    const [dataSummary, setdataSummary] = useState(false);
    const [dataHist, setdataHist] = useState(false);
    const computeHistogram = (arr, nBins, domain, clamp = false) => {
        if (domain && clamp) {
            const [domainMin, domainMax] = domain
            arr.forEach((val, i, arr) => {
                arr[i] = Math.min(Math.max(val, domainMin), domainMax)
            })
        }
        let bins = d3.bin()
        if (domain) bins = bins.domain(domain)
        if (nBins) bins = bins.thresholds(nBins)
        const d3Hist = bins(arr)
        const hist = d3Hist.map(item => item.length)
        const binEdges = d3Hist.map(item => item.x0)
        if (d3Hist.length) binEdges.push(d3Hist.at(-1).x1)

        return { hist, binEdges }
    }

    const stat = {
        calcAverage: arr => {
            var a = arr.slice();
            if (a.length) {
                let sum = stat.sumArr(a);
                let avg = sum / a.length;
                return avg;
            }
            return false;
        },
        calcMax: (arr) => {
            return Math.max(...arr);
        },
        calcMin: (arr) => {
            return Math.min(...arr);
        },
        calcMedian: (arr) => {
            var a = arr.slice();
            let hf = Math.floor(a.length / 2);
            arr = stat.sortArr(a);
            if (a.length % 2) {
                return arr[hf];
            } else {
                return (parseFloat(arr[hf - 1]) + parseFloat(arr[hf])) / 2.0;
            }
        },
        calcMode: (arr) => {
            var ary = arr.slice();
            let t = ary.sort(function (a, b) {
                ary.filter(function (val) {
                    val === a
                }).length - ary.filter(function (val) {
                    val === b
                }).length
            });
            return t.pop();
        },
        calcQuartile: (arr, q) => {
            var a = arr.slice();
            // Turn q into a decimal (e.g. 95 becomes 0.95)
            q = q / 100;

            // Sort the array into ascending order
            let Qdata = stat.sortArr(a);

            // Work out the position in the array of the percentile point
            var p = ((Qdata.length) - 1) * q;
            var b = Math.floor(p);

            // Work out what we rounded off (if anything)
            var remainder = p - b;

            // See whether that data exists directly
            if (Qdata[b + 1] !== undefined) {
                return parseFloat(Qdata[b]) + remainder * (parseFloat(Qdata[b + 1]) - parseFloat(Qdata[b]));
            } else {
                return parseFloat(Qdata[b]);
            }
        },
        calcRange: (arr) => {
            mx = stat.calcMax(arr);
            mn = stat.calcMin(arr);
            return mx - mn;
        },
        sumArr: (arr) => {
            var a = arr.slice();
            return a.reduce(function (a, b) { return parseFloat(a) + parseFloat(b); });
        },
        sortArr: (arr) => {
            var ary = arr.slice();
            ary.sort(function (a, b) { return parseFloat(a) - parseFloat(b); });
            return ary;
        }

    }

    let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
    let fractionOption = { minimumFractionDigits: 2 }
    fractionOption = {
        ...fractionOption,
        maximumFractionDigits: fractionDigit
    };

    function nFormatter(num, digits) {
        const lookup = [
            { value: 1, symbol: "" },
            { value: 1e3, symbol: "k" },
            { value: 1e6, symbol: "M" },
            { value: 1e9, symbol: "B" },
            { value: 1e12, symbol: "T" },
            { value: 1e15, symbol: "P" },
            { value: 1e18, symbol: "E" }
        ];
        const regexp = /\.0+$|(?<=\.[0-9]*[1-9])0+$/;
        const item = lookup.findLast(item => num >= item.value);
        return item ? (num / item.value).toFixed(digits).replace(regexp, "").concat(" ", item.symbol) : "0";
        // return item ? Intl.NumberFormat("en-US", { minimumFractionDigits: 3 }).format(num / item.value).toString().replace(regexp, "").concat(item.symbol) : "0";
    
    }

    useEffect(() => {
        if (props.data && Array.isArray(props.data)) {
            let target = props.data.map(d => {
                return parseFloat(d[props.indParam])
            })
            let maxV = Math.ceil(d3.max(target));
            let minV = Math.ceil(d3.min(target));
            let histo = computeHistogram(target, 10, [minV, maxV])
            setdataHist(histo)
            let summ = {}
            // summ.mean1 = d3.mean(target);
            summ.mean = stat.calcAverage(target);
            // summ.median1 = d3.median(target);
            summ.median = stat.calcMedian(target);
            // summ.mode1 = d3.mode(target);
            summ.mode = stat.calcMode(target);
            setdataSummary(summ)
            // props.indParam == 'priceChangePercent' && console.log('data', props.data)
            // props.indParam == 'priceChangePercent' && console.log('target', target)
            // props.indParam == 'priceChangePercent' && console.log('summary', summ)
            // console.log('summ', summ);
            // console.log('histo', histo, minV, maxV);
            setdataRaw(target)
        }
    }, [props.data]);

    return (
        <>
            {dataHist && Array.isArray(dataHist?.binEdges) && (
                <Stack sx={{ borderWidth: '0px', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ fontSize: '8px', width: '20px', minWidth: '20px' }}>&nbsp;</Box>
                    <Box sx={{ fontSize: '12px', width: '50px', borderWidth: '1px', textAlign: 'right', minWidth: '20px' }}>bin&nbsp;</Box>
                    <Box sx={{ fontSize: '12px', width: '50px', borderWidth: '1px', textAlign: 'right', minWidth: '20px' }}>hist&nbsp;</Box>
                </Stack>
            )
            }

            {dataHist && Array.isArray(dataHist?.binEdges) && dataHist?.binEdges.map((d, ix) => {
                return (
                    <Stack key={ix.toString()} sx={{ borderWidth: '0px', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                        <Box sx={{ fontSize: '8px', width: '20px', borderWidth: '1px', minWidth: '20px', textAlign: 'right' }}>{ix + 1}&nbsp;</Box>
                        <Box sx={{ fontSize: '12px', width: '50px', borderWidth: '1px', textAlign: 'right', minWidth: '20px' }}>{dataHist?.binEdges[ix]}&nbsp;</Box>
                        <Box sx={{ fontSize: '12px', width: '50px', borderWidth: '1px', textAlign: 'right', minWidth: '20px' }}>{dataHist?.hist[ix]}&nbsp;</Box>
                    </Stack>
                )
            })}

            {dataHist && Array.isArray(dataHist?.binEdges) && (
                <Stack sx={{ borderWidth: '0px', flexDirection: 'column', my: 2, alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ fontSize: '12px',  borderWidth: '1px', textAlign: 'right', minWidth: '20px' }}>
                        mean: {Intl.NumberFormat("en-US", fractionOption).format((parseFloat(dataSummary?.mean).toFixed(2))) } &nbsp; &nbsp; &nbsp;<br />
                        median: {parseFloat(dataSummary?.median).toFixed(2)}  &nbsp; &nbsp; &nbsp;<br />
                        mode: {parseFloat(dataSummary?.mode).toFixed(2)} &nbsp; &nbsp; &nbsp;<br />
                    </Box>
                </Stack>
            )
            }
        </>
    )
}


// function LinePlot({
//     data,
//     width = 1040,
//     height = 400,
//     marginTop = 20,
//     marginRight = 20,
//     marginBottom = 20,
//     marginLeft = 20
// }) {
//     // const x = d3.scaleLinear([0, data.length - 1], [marginLeft, width - marginRight]);
//     // const y = d3.scaleLinear(d3.extent(data), [height - marginBottom, marginTop]);


//     // Bin the data.

//     const scale = d3
//         .scaleLinear()
//         .domain(d3.extent(data))
//         .nice();

//     const bins = d3.bin()
//         .thresholds(10)
//         .value((d) => d)
//         (data);


//     const x = d3.scaleLinear()
//         .domain([bins[0].x0, bins[bins.length - 1].x1])
//         .range([marginLeft, width - marginRight]);

//     // Declare the y (vertical position) scale.
//     const y = d3.scaleLinear()
//         .domain([0, d3.max(bins, (d) => d.length)])
//         .range([height - marginBottom, marginTop]);

//     const line = d3.line((d, i) => x(i), y);
//     console.log('bins', bins);

//     return (
//         <svg width={width} height={height}>
//             <path fill="none" stroke="currentColor" strokeWidth="1.5" d={line(data)} />
//             <g fill="white" stroke="currentColor" strokeWidth="1.5">
//                 {data.map((d, i) => (<circle key={i} cx={x(i)} cy={y(d)} r="0.5" />))}
//             </g>
//         </svg>
//     );
// }


// const Plott = props => {
//     const containerRef = useRef();
//     const [data, setData] = useState(props.data);
  
//     useEffect(() => {
//       if (data === undefined) return;
//       console.log('data', data)
//       const plot = Plot.plot({
//         y: {grid: true},
//         color: {scheme: "burd"},
//         marks: [
//           Plot.ruleY([0]),
//           Plot.dot(data, {y: "volumeK", x: "priceChangePercent", stroke: "volumeK"})
//         ]
//       });
//       containerRef.current.append(plot);
//       return () => plot.remove();
//     }, [data]);
  
//     return <div ref={containerRef} />;
//   }