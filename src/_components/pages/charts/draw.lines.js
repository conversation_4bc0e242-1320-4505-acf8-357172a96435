/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, {useEffect, useRef, useState} from "react";
import * as d3 from "d3";
import * as Plot from "@observablehq/plot";
import PlotFigure from "./PlotFigure.js";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import { Chart } from "react-google-charts";
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import { Histogram } from "./chart.histogram.js";
import { Button } from "@mui/material";
import { IconProgress } from "@tabler/icons-react";
import { LoadingButton } from "@mui/lab";

const FuturesEx = props => {
    const { token, indParam = 'priceChangePercent', 
        binq = false, rang = [-100, 100],
        mode,
    } = props;
    const [loading, setloading] = useState(false)
    const [data, setData] = useState(false);
    const [dataAct, setDataAct] = useState(false);
    const [dataHistogram, setdataHistogram] = useState(false);
    const [dataHistogramBins, setdataHistogramBins] = useState(false);
    const [dataTime, setdataTime] = useState(false);

    const datax = [
        ["x", "dogs"],
        [0, 0],
        [1, 10],
        [2, 23],
        [3, 17],
        [4, 18],
        [5, 9],
        [6, 11],
        [7, 27],
        [8, 33],
        [9, 40],
        [10, 32],
        [11, 35],
    ];

    const options = {
        title: props.title || "Line Chart Example",
        hAxis: props.hAxis || { title: "Time" },
        vAxis: props.vAxis || { title: "Popularity" },
        legend: "none",
    };
 
    useEffect(() => {
        props.data && setData(props.data);
        // fetchMarketStats();
    }, [props.data]);
    return (
        <>
            <Box>
                <Card variant="outlined" sx={{ mx: 2, minWidth: 300 }}>
                    {data && <Chart
                        chartType="LineChart"
                        width="100%"
                        height="400px"
                        data={data}
                        options={options}
                    />}
                </Card>
            </Box>
        </>
    )
};

export default FuturesEx;


const MarketSummary = props => {
    const {data, indParam} = props;

    const [dataRaw, setdataRaw] = useState(false);
    const [dataSummary, setdataSummary] = useState(false);
    const [dataHist, setdataHist] = useState(false);
    const computeHistogram = (arr, nBins, domain, clamp = false) => {
        if (domain && clamp) {
            const [domainMin, domainMax] = domain
            arr.forEach((val, i, arr) => {
                arr[i] = Math.min(Math.max(val, domainMin), domainMax)
            })
        }
        let bins = d3.bin()
        if (domain) bins = bins.domain(domain)
        if (nBins) bins = bins.thresholds(nBins)
        const d3Hist = bins(arr)
        const hist = d3Hist.map(item => item.length)
        const binEdges = d3Hist.map(item => item.x0)
        if (d3Hist.length) binEdges.push(d3Hist.at(-1).x1)

        return { hist, binEdges }
    }


    useEffect(() => {
        if (props.data && Array.isArray(props.data)) {
            let target = props.data.map(d => {
                return parseFloat(d[props.indParam])
            })
            let maxV = Math.ceil(d3.max(target));
            let minV = Math.ceil(d3.min(target));
            let histo = computeHistogram(target, 10, [minV, maxV])
            setdataHist(histo)
            let summ = {}
            // summ.mean1 = d3.mean(target);
            summ.mean = stat.calcAverage(target);
            // summ.median1 = d3.median(target);
            summ.median = stat.calcMedian(target);
            // summ.mode1 = d3.mode(target);
            summ.mode = stat.calcMode(target);
            setdataSummary(summ)
            // console.log('target', target)
            // console.log('summ', summ);
            // console.log('histo', histo, minV, maxV);
            setdataRaw(target)
        }
    }, [props.data]);

    return (
        <>
            {dataHist && Array.isArray(dataHist?.binEdges) && (
                <Stack sx={{ borderWidth: '0px', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <Box sx={{ fontSize: '8px', width: '20px', minWidth: '20px' }}>&nbsp;</Box>
                </Stack>
            )
            }

        </>
    )
}

