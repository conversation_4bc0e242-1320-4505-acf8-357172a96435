import React, { useState, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import { Alert, createTheme, Divider } from '@mui/material';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Card from '@mui/material/Card';
import TextField from '@mui/material/TextField';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Paper from '@mui/material/Paper';
import DeleteIcon from '@mui/icons-material/Delete';
import Avatar from '@mui/material/Avatar';
import Select from '@mui/material/Select';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Typography from '@mui/material/Typography';
import { red } from '@mui/material/colors';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ArrowForwardRoundedIcon from '@mui/icons-material/ArrowForwardRounded';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import NotificationImportantRounded from '@mui/icons-material/NotificationImportantRounded';
import _ from 'lodash';

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

const CardIndicators = props => {
  const { user = {} } = props;
  const { login } = user;
  const [selected, setSelected] = useState([]);
  const [updateMe, setupdateMe] = useState(0);
  const [battleIndicators, setbattleIndicators] = useState([]);

    // console.log('stg2', JSON.stringify(stg));
  const addIndicator = inx => {
    let stg = JSON.parse(JSON.stringify(selected));
    inx.id = Date.now() + Math.floor(Math.random() * 10);
    stg = [...stg, inx]; 
    setSelected(stg);
    let battleData = BattleParamConverter(stg)
    props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: battleData } );
    setupdateMe(Date.now());
  };

  const deleteIndicator = (inx, sselected = selected) => {
    let stg = JSON.parse(JSON.stringify(sselected));
    let uTaskIndex = stg.findIndex(t => t.id == inx.id);
    stg.splice(uTaskIndex, 1);
    setSelected([...stg]);
    let battleData = BattleParamConverter(stg)
    props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: battleData } );
  };

  const deleteAll = () => {
    setSelected([]);
    props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: [] } );
  };

  function BattleParamConverter(vx) {
    let resp = []
    vx.map(v => {
      let params = {};
      for (let p in v.params) {
        params[p] = v.params[p].default
      }

      let a = {
        id: v.id,
        indicator: v.indicator,
        refName: params.refName,
        battleParams: params
      }
      resp.push(a)
    })
    return resp
  };

  const updatedSelecteds = (ix2Update, tbselected = null) => {
    let currbattleIndicators = !tbselected ? JSON.parse(JSON.stringify(selected)) : JSON.parse(JSON.stringify(tbselected));
    let ixx = currbattleIndicators.findIndex(cbi => cbi.id === ix2Update.id)
    currbattleIndicators[ixx] = ix2Update;
    setSelected(currbattleIndicators);
    let battleData = BattleParamConverter(currbattleIndicators)
    props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: battleData } );
  }


  useEffect(() => {
    if (props.battleParams) {
      let parami = props.battleParams.indicatorsWParams ? JSON.parse(JSON.stringify(props.battleParams.indicatorsWParams)) : {}
      let battleIndicators = BattleParamConverter(selected);
      function fnFetch() {
        return new Promise(async (resolve) => {
          try {
            var uri = '/api/pub/data/indicatorsandparams'
            const data = await fetcher(uri)
            resolve(data.data)
          } catch (e) {
            console.log('fetch err', e)
          }
        });
      }
      const InitBoxes = async () => {
        let ixx = await fnFetch();
        let selectedStg = [];
        Array.isArray(parami) && parami.map(p => {
          let pax = [...ixx].findIndex(ix => ix.indicator == p.indicator)
          let idx = {
            id: p.id,
            refName: p.refName,
          }

          let idxAddon = {...ixx[pax]};
          let idxAddonParams = idxAddon.params

          let addOn = {}
          addOn['refName'] = {
            type: 'string',
            default: p.refName
          }

          for (let pa in idxAddonParams) {
            addOn[pa] = {
              ...idxAddonParams[pa],
              default: p?.battleParams?.[pa]
            }
          }
          idxAddon.params = addOn;
          idx = {
            ...idx,
            ...idxAddon,
          }

          selectedStg.push(idx);
        });

        setSelected(selectedStg)
      };
      parami && !_.isEqual(parami, battleIndicators) && InitBoxes();
    }
  }, [props.battleParams])
  return (
    <>

      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 250, width: 250 }}>
          <SelectIndicators {...props} addIndicator={addIndicator} />
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
          <Typography variant="button" display="block" gutterBottom onClick={() => console.log('selected', selected)}>
            Selected Indicators {selected && Array.isArray(selected) && selected.length !== 0 && selected.length}
            {selected && Array.isArray(selected) && selected.length !== 0 && 
              <IconButton onClick={() => deleteAll()} sx={{fontSize: '12px'}}>Remove All</IconButton>}
          </Typography>
          <Divider />
          <SelectedIx updateMe={updateMe} updatedSelecteds={updatedSelecteds} selectedIx={selected} deleteIx={deleteIndicator} {...props} />
        </Box>
      </Stack>

    </>
  );
}
export default CardIndicators;

const SelectedIx = props => {
  const [sselected, setsselected] = useState(false)
  useEffect(() => {
    // console.log('SelectedIx useeffect updateMe', props.updateMe, )
    Array.isArray(props.selectedIx) && setsselected(props.selectedIx)
  }, [props.updateMe, props.selectedIx])

  const updateSelectedIX = ixx => {
    props.updatedSelecteds && props.updatedSelecteds(ixx)
  };
  return (
    <Stack sx={{
      flex: 1, flexDirection: 'row', flexWrap: 'wrap',
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
    }}>
      {sselected && Array.isArray(sselected) && [...sselected].map((ix, i) => {
        if (!ix.params?.refName) {
          ix.params.refName = {
            default: ix?.titleShort + '_' + ix?.params?.length?.default, 
            type: 'string'
          }
        }
        return (
            <Paper sx={{ minWidth: 160, maxWidth: 350, overflow: 'auto', minHeight: 120, borderWidth: 0.5, margin: 2 }} 
            key={i.toString() + ix.id.toString()}>
              <Stack sx={{
                flex: 1, flexDirection: 'row', alignItems: 'center',
                justifyContent: 'space-between', backgroundColor: '#cfcfcf',
              }}>
                <Typography sx={{ paddingLeft: 1 }}>
                  {i + 1}.{ix.titleShort}.{ix.id}
                </Typography>

                <IconButton color="danger" aria-label="delete" size={'12'} onClick={() => props?.deleteIx(ix)}>
                  <DeleteIcon />
                </IconButton>

              </Stack>
              <Divider />
              <SelectedIxParams ix={ix} updateSelectedIX={updateSelectedIX} {...props}/>
            </Paper>
        )
      })}
    </Stack>
  )
}
const SelectedIxParams = props => {
  const [fields, setfields] = useState([]);
  const [battleIXVal, setBattleIXVal] = useState({});
  const [IX, setIX] = useState({});
  const [t, sett] = useState(0);
  const setFieldsFN = (ixxxParams = {}) => {
    let {params} = ixxxParams;
    // console.log('setFieldsFN for', ixxxParams);
    let stg_battleIXParams = {}
    if (params) {
      let x = [];
      for (let p in params) {
        x.push({
          title: p,
          para: params[p]
        })
        stg_battleIXParams[p] = params[p]?.default;
      }
      // console.log('stg_battleIXParams', stg_battleIXParams);
      return x
    }
  }
  useEffect(() => {
    if (props.ix) {
      setIX(props.ix)
      let x = setFieldsFN(props.ix);
      setfields(x);
      sett(Date.now())
      }
  }, [props.ix])

  const handleChange = (para, event) => {
    let currVals = JSON.parse(JSON.stringify(IX));
    // console.log('handleChange', currVals, para, event.target.value)
    if(currVals?.params) {
      currVals.params[para.title].default = event.target.value;
    }
    setIX(currVals);
    let x = setFieldsFN(currVals);
    setfields(x);
    sett(Date.now())
    props.updateSelectedIX && props.updateSelectedIX(currVals)
  };

  return (
    <>
      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', flexWrap: 'wrap' }}>

        {Array.isArray(fields) && fields.map((f, i) => {
          return (
            <Box key={i.toString()} sx={{ m: 1, flexFlow: 'row' }}>
              <>
                {(f.para.type == 'string' || f.para.type == 'numeric') && (
                  <CssTextField id="outlined-basic"
                    placeholder='xxx'
                    size="small"
                    onChange={(e) => handleChange(f, e)}
                    value={f.para.default}
                    type={f.para.type == 'numeric' ? "number" : undefined}
                    InputLabelProps={{ sx: { fontSize: "0.8rem" } }}
                    label={f.title} variant="standard" />
                )}
              </>
              <>
                {(f.para.type == 'list') && (
                  <FormControl variant="standard" sx={{ minWidth: 67 }}>
                    <InputLabel id="demo-simple-select-standard-label">{f.title}</InputLabel>
                    <Select
                      labelId="demo-simple-select-standard-label"
                      id="demo-simple-select-standard"
                      // defaultValue={f.para.default}
                      value={f.para.default}
                      // onChange={handleChange}
                      onChange={(e) => handleChange(f, e)}
                      label={f.title}
                      size="small"
                      sx={{ width: 65 }}
                    >
                      {Array.isArray(f.para.values) && f.para.values.map((o, ii) => {
                        return (
                          <MenuItem key={ii.toString()} value={o}>{o}</MenuItem>
                        )
                      })}
                    </Select>
                  </FormControl>
                )}
              </>
            </Box>
          )
        })}
      </Stack>
    </>
  )
}
const CssTextField = styled(TextField)({
  '& label.Mui-focused': {
    color: '#A0AAB4',
    fontSize: '14px',
  },
  '&.MuiInputLabel-shrink': {
    fontSize: '14px',
  },
  '& .MuiInputBase-input': {
    borderRadius: 8,
    position: 'relative',
    maxWidth: 60,
    // backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
    // border: '1px solid',
    // borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
    // fontSize: 14,
    // width: 'auto',
    // marginLeft: '5px',
    padding: '4px 6px',
    fontSize: '14px',
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:focus': {
      // boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      // borderColor: theme.palette.primary.main,
    },
  },
});
const SelectIndicators = props => {
  const [expanded, setExpanded] = React.useState(false);
  const [indicators, setindicators] = React.useState([]);

  useEffect(() => {
    const getX = async () => {
      let ixx = await fnFetch();
      setindicators(ixx);
    };
    getX();
  }, [])

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };

  function fnFetch() {
    return new Promise(async (resolve) => {
      try {
        var uri = '/api/pub/data/indicatorsandparams'
        // console.log(uri)
        const data = await fetcher(uri)
        resolve(data.data)
      } catch (e) {
        console.log('fetch err', e)
      }
    });
  }

  return (
    <>
      <Card sx={{ maxWidth: 345 }}>
        <CardHeader
          sx={{ background: '#ffddaa' }}
          avatar={
            <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
              <NotificationImportantRounded />
            </Avatar>
          }
          action={
            <IconButton aria-label="settings">
              <MoreVertIcon />
            </IconButton>
          }
          title="Indicators"
          subheader="Select Indicators"
        />
        <Divider />
        <CardContent sx={{ padding: 0 }}>
          <SelectIndicatorList {...props} indicators={indicators} addIndicator={props.addIndicator} />
        </CardContent>
      </Card>
    </>
  )
}
const SelectIndicatorList = props => {
  const [open, setOpen] = React.useState(true);
  const [indicators, setindicators] = React.useState(props.indicators);
  const handleClick = () => {
    setOpen(!open);
  };
  return (
    <List
      sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}
      component="nav"
      aria-labelledby="nested-list-subheader"
    >
      {Array.isArray(props.indicators) && props.indicators.map((i, ix) => {
        return (
          <ListItemButton key={ix.toString()} onClick={() => props.addIndicator(i)}>
            <ListItemIcon sx={{ padding: 0, minWidth: 32 }}>
              <ArrowForwardRoundedIcon />
            </ListItemIcon>
            <ListItemText primary={i.titleShort} secondary={i.type} />
            <Divider />
          </ListItemButton>
        )
      })}
    </List>
  );
}
const theme = createTheme({
  components: {
    MuiInputBase: {
      defaultProps: {
        disableInjectingGlobalStyles: true,
      },
    },
  },
});
const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));



// useEffect(() => {
//   if (props.battleParams) {
//     let parami = props.battleParams.indicatorsWParams
//     const InitBoxes = async () => {
//       let ixx = await fnFetch();
//       console.log('parami', parami)
//       let psh = []
//       let pshPost = []
//       Array.isArray(parami) && parami.map(p => {
//         let pax = ixx.find(ix => ix.indicator == p.indicator)
//         if (pax) {
//           console.log('pax', pax)
//           pax.id = p.id;
//           pax.refName = p.refName;
//           console.log('pax-post', pax)
//           for (let pa in pax?.params) {
//             if(p?.battleParams?.[pa]){
//               pax.params[pa].default = p?.battleParams?.[pa];
//               pax.params['refName'] = {
//                 type: 'string',
//                 default: p.refName
//               }
//             } 
//           }
//           console.log('pax-post2', pax)
//           psh.push(pax);
//           // pshPost.push(setPostData(pax));
//         } else {
//           alert('hata, indicator yok!')
//         }
//       });

//       setSelected(psh);
//       setbattleIndicators(pshPost);
//     }
//     parami && !_.isEqual(parami, battleIndicators) && InitBoxes();
//   }
// }, [])

// function setPostData(ix, defValues = false) {
//   let stg_battleIXVal = {
//     id: ix?.id,
//     indicator: ix?.indicator,
//   };
//   let stg_battleIXParams = {}
//   if (ix?.params) {
//     let x = [];
//     for (let p in ix?.params) {
//       x.push({
//         title: p,
//         para: ix?.params[p]
//       })
//       stg_battleIXParams[p] = ix?.params[p]?.default;
//     }
//     x.push({
//       title: 'refName',
//       para: {
//         type: 'string',
//         default: ix?.titleShort + '_' + ix?.params?.length?.default
//       }
//     })

//     stg_battleIXParams["refName"] = ix?.titleShort + '_' + ix?.params?.length?.default
//     stg_battleIXVal["battleParams"] = stg_battleIXParams;
//     stg_battleIXVal['refName'] = stg_battleIXParams.refName;
//     return stg_battleIXVal
//   }

// }

// const updateBattleParams = vx => {
//   let currbattleIndicators = JSON.parse(JSON.stringify(selected));
//   let ixx = currbattleIndicators.findIndex(cbi => cbi.id === vx.id)
//   if (ixx < 0) {
//     // currbattleIndicators.push(vx);
//   } else {
//     let pax = currbattleIndicators[ixx]
//     for (let pa in vx?.battleParams) {
//       let pal1 = pax.params[pa] ? pax.params[pa] : {};
//       pal1.default = vx?.battleParams[pa]
//       pax.params[pa] = pal1;
//       if (pa == 'refName') {
//         pax['refName'] = vx?.battleParams[pa]
//       }; 
//     }
//     currbattleIndicators[ixx] = pax;
//     console.log('vx >0', vx, currbattleIndicators);
//     setSelected(currbattleIndicators);
    
//   }

// }


// const updateBattleParams_ = vx => {
//   let currbattleIndicators = [...battleIndicators];
//   let ixx = currbattleIndicators.findIndex(cbi => cbi.id === vx.id)
//   if (ixx < 0) {
//     currbattleIndicators.push(vx);
//   } else {
//     currbattleIndicators[ixx] = vx;
//   }
//   setbattleIndicators(currbattleIndicators)
//   props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: currbattleIndicators } );
// }

// const addIndicator_ = inx => {
//   let stg = JSON.parse(JSON.stringify(selected));
//   console.log('stg', JSON.stringify(stg));
//   inx.id = Date.now() + Math.floor(Math.random() * 10);
//   stg = [...stg, inx]; 
//   console.log('stg2', JSON.stringify(stg));
//   setSelected(stg);
//   setupdateMe(Date.now());
//   let aIX = setPostData(inx);
//   let currBIX = Array.isArray(props.battleParams.indicatorsWParams) ? [...props.battleParams.indicatorsWParams] : [];
//   currBIX.push(aIX);
//   props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: currBIX } );
// }
