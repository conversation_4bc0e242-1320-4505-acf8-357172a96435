/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, Container, Stack, Typography } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import Hero from '../components/generic/home.hero';
import FuturesHistogram from './charts/futures.histogram';
import DrawLines from './charts/draw.lines';
export default function Home(props) {
  const { ...rest } = props;
  const router = useRouter();
  const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
  const { slug } = router.query
  const [header, setHeader] = useState(false)
  const { status, data: session } = useSession({
      required: true,
      onUnauthenticated() {
        signIn();
          // router.push('/auth/SignInSide');
      },
  })
  useEffect( () => {
    colorMode.setCurrPage('/');
    drawerCollapsed && colorMode.collapseDrawer(true);
  }, []);

  if (status === "loading") {
      return "Loading..."
  }
  const { user } = session ? session : {};
  const { token, refreshToken } = user ? user : {};

  return (
    <>
    <Head>
      <title>Gauss Algo</title>
    </Head>
      <AppLayout session={session} {...props}
        pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
        pgBread={<span>x</span>}
      >
        <Stack className="mt-24">
          {/* <Input /> */}

          <Typography variant="h5" sx={{ px: 6, pt: 2, }}>Market Status</Typography>
          <Stack sx={{m: 4, 
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'flex-start',
              justifyContent: 'space-around',
              }} >
            <FuturesHistogram indParam={'priceChangePercent'} xforceUpdate={true} xshowTable={true} />
            <FuturesHistogram indParam={'delta'} />
            <FuturesHistogram indParam={'volumeK'} />
            <FuturesHistogram indParam={'count'} xshowTable={true}  />
          </Stack>
          
          <hr color="blue" style={{height: 2, marginBottom: '5px'}} />
          <PCPTrend {...props} />

          <Box sx={{m: 4}} >
            <Hero />
          </Box>

        </Stack>
      </AppLayout>
    </>
  )
}

export async function getServerSideProps() {
  return { props: { appvars } }
}


const InputTitle = () => {
  const { mode } = useContext(ColorModeContext);
  return <div>the context in Provider is : {mode}</div>;
};

const PCPTrend = props => {

  const [pcp, setpcp] = useState(false)
  const [delta, setdelta] = useState(false)

  const [pcpMean, setpcpMean] = useState(false)
  const [pcpMedian, setpcpMedian] = useState(false)
  const [deltaMean, setdeltaMean] = useState(false)
  const [deltaMedian, setdeltaMedian] = useState(false)


  const fetchData = async (forceUpdate) => {
    return new Promise(async (resolve, reject) => {
      let uri = '/api/pub/data/market_futuresdaily_data/' + (forceUpdate ? '?force=1' : '');
      try {
        let res = await fetch(uri, {
          method: 'GET',
        });
        const datax = await res.json();
        console.log('uri', uri, datax)
      
        resolve(datax)
      } catch (e) {
        console.log('error', e)
        resolve({pcp: [], delta: []})
      }
    });
  }

  const preData = (arr) => {
    let resp = {}
    if (Array.isArray(arr)) {
      var pcpArr = arr.map((x) => ({ dt: new Date(new Date(x.dtCreated).setSeconds(0, 0)), dtx: new Date(x.dtCreated).getHours() + ':' + new Date(x.dtCreated).getMinutes(), mean: x.pcp?.mean, median: x.pcp?.median }))
      var deltaArr = arr.map((x) => ({ dt: new Date(new Date(x.dtCreated).setSeconds(0, 0)), dtx: new Date(x.dtCreated).getHours() + ':' + new Date(x.dtCreated).getMinutes(), mean: x.delta?.mean, median: x.delta?.median }))

      let pcpMeanStg = [['time', 'mean']];
      let pcpMedianStg = [['time', 'median']];
      const arrOfDates = pcpArr.map(x => new Date(x.dt));
      let BOP = new Date(Math.min(...arrOfDates));
      let EOP = new Date(Math.max(...arrOfDates));

      var diffMs = new Date(EOP) - new Date(BOP);
      var diffMins = Math.round(diffMs / 60000);

      for (let x = 0; x <= diffMins; x++) {
        let nBOP = new Date(new Date(BOP).getTime() + x * 60000);
        let nVal = pcpArr.find(a => new Date(a.dt).toISOString() == nBOP.toISOString());

        pcpMeanStg.push([
          // nBOP, 
          new Date(nBOP).getHours() + ':' + new Date(nBOP).getMinutes(),
          nVal?.mean || undefined
        ]);
        pcpMedianStg.push([
          new Date(nBOP).getHours() + ':' + new Date(nBOP).getMinutes(),
          // nBOP, 
          nVal?.median || undefined
        ]);
      }
      resp.pcpMean = pcpMeanStg;
      resp.pcpMedian = pcpMedianStg;
      // setpcpMean(pcpMeanStg);
      // setpcpMedian(pcpMedianStg);

      let deltaMeanStg = [['time', 'mean']];
      let deltaMedianStg = [['time', 'median']];

      const arrOfDatesDelta = deltaArr.map(x => new Date(x.dt));
      let BOPDelta = new Date(Math.min(...arrOfDatesDelta));
      let EOPDelta = new Date(Math.max(...arrOfDatesDelta));

      var diffMsDelta = new Date(EOPDelta) - new Date(BOPDelta);
      var diffMinsDelta = Math.round(diffMsDelta / 60000);

      for (let x = 0; x <= diffMinsDelta; x++) {
        let nBOP = new Date(new Date(BOPDelta).getTime() + x * 60000);
        let nVal = deltaArr.find(a => new Date(a.dt).toISOString() == nBOP.toISOString());

        deltaMeanStg.push([
          new Date(nBOP).getHours() + ':' + new Date(nBOP).getMinutes(),
          // nBOP, 
          nVal?.mean || undefined
        ]);
        deltaMedianStg.push([
          new Date(nBOP).getHours() + ':' + new Date(nBOP).getMinutes(),
          // nBOP, 
          nVal?.median || undefined
        ]);

      }

      resp.deltaMean = deltaMeanStg;
      resp.deltaMedian = deltaMedianStg;

      resp.delta = deltaArr;
      resp.pcp = pcpArr;
      // console.log('pcpArr', pcpArr.length, pcpArr)
      return resp;
    } else {
      return false;
    }
  }

  const refreshMe = async () => {
    let data = await fetchData();
    let arr = preData(data.data);
    if (arr) {
      setpcpMean(arr?.pcpMean);
      setpcpMedian(arr?.pcpMedian);
      setdeltaMean(arr?.deltaMean);
      setdeltaMedian(arr?.deltaMedian);
      setdelta(arr?.delta);
      setpcp(arr?.pcp);
      console.log('refreshed')
    }
  }

  useEffect( () => {
    const xx = async() => {
      let data = await fetchData();
      let arr = preData(data.data);
      if (arr) {
        setpcpMean(arr?.pcpMean);
        setpcpMedian(arr?.pcpMedian);
        setdeltaMean(arr?.deltaMean);
        setdeltaMedian(arr?.deltaMedian);
        setdelta(arr?.delta);
        setpcp(arr?.pcp);
      } 
    };
    xx();
  }, []);

  return (
    <>
      <Stack sx={{
        borderWidth: 0, minHeight: 40, m: 4,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
      }}>
        <Typography variant="h5" sx={{ px: 6, }}>Market Trend</Typography>
        <Button onClick={refreshMe} title="Tazele" variant="contained" sx={{ color: 'black' }}>Refresh</Button>

      </Stack>
      <Stack sx={{
        borderWidth: 0, minHeight: 40, m: 4,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-around',
      }}>
        <Box sx={{flex: 1, my: 1}}>
          <DrawLines title='pcp / Mean' data={pcpMean} hAxis={{title: 'Time'}} vAxis={{title: 'Pcp-Mean'}} />
          <br />
          <DrawLines title='pcp / Median' data={pcpMedian} hAxis={{title: 'Time'}} vAxis={{title: 'Pcp-Median'}}  />

        </Box>
        <Box sx={{flex: 1, my: 1}}>
          <DrawLines title='delta / Mean' data={deltaMean} hAxis={{title: 'Time'}} vAxis={{title: 'Delta-Mean'}} />
          <br />
          <DrawLines title='delta / Median' data={deltaMedian} hAxis={{title: 'Time'}} vAxis={{title: 'Delta-Median'}}  />

        </Box>
      </Stack>
    </>
  )
}

// const Input = () => {
//   const { name, updateName, aCallback } = useContext(Context);

//   return (
//     <div>
      
//       <InputTitle />
//       <div>
//         <InputTitle />
//         <div>
//           <input
//             type="text"
//             value={name}
//             onChange={e => updateName(e.target.value)}
//           />
//         </div>
//         <button onClick={aCallback}>Click Callback</button>
//       </div>
//     </div>
//   );
// };

