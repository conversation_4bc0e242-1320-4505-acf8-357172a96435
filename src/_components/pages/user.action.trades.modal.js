import React, { useState, useEffect, useContext, useRef } from "react";
// import moment from "moment";
// import localforage from 'localforage';
import { create<PERSON><PERSON>, CrosshairMode, PriceScaleMode, ColorType } from 'lightweight-charts';
import moment from 'moment'
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { styled, useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid';
import FormControl from "@mui/material/FormControl"
import FormLabel from "@mui/material/FormLabel"
import TextField from "@mui/material/TextField"
import Typography from "@mui/material/Typography"
import Stack from "@mui/material/Stack"
import MuiCard from "@mui/material/Card"
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import AddBoxIcon from '@mui/icons-material/AddBox';
import LoadingButton from "@mui/lab/LoadingButton"
import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Chip,
    Tooltip,
    LinearProgress,
    Card
} from '@mui/material';

// import { titlecase, camalize } from '@/lib/fnx/fnx.cli'
import { CardContent, CardHeader } from "@mui/material";
import { useRouter } from 'next/router'
import InsertChartIcon from '@mui/icons-material/InsertChart';
import Slide from '@mui/material/Slide';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});
export default function ScrollDialog(props) {
    const router = useRouter();
    // console.log('props dialog', props)
    const [error, setError] = useState(null)
    const [open, setOpen] = React.useState(false);
    const [crosshair, setcrosshair] = useState(false);
    const [scroll, setScroll] = React.useState('paper');
    const [maxWidth, setMaxWidth] = React.useState('lg');

    const [data, setdata] = React.useState(false);
    const [loading, setloading] = React.useState(false);
    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/tradeslist4chart'
                let res = await fetch(uri, {
                    method: 'GET',
                  });
                  const data = await res.json();
                resolve(data)
            } catch (e) {
                console.log('fetch err', e)
            }
        });
    }

    const refreshChart = async () => {
        try {
            setloading(true);
            let dataStg = await fnFetch();
            setloading(false);
            // console.log('dataStg', dataStg);
            setdata(dataStg?.data)
            setloading(false);
        } catch (e) {
            console.log('hata fetch ederken...', e);
            setloading(false);
        }
    } 
    const theme = useTheme();
    const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

    const handleClickOpen = (scrollType) => () => {
        setOpen(true);
        setScroll(scrollType);
    };

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick") {
            setOpen(false);
            return;
        } else {
            // setinvoiceDataInitial(false);
            // setinvoiceDataR(prepInvoiceData(props.data));
            // setinvoiceDataR(false);
            // window.localStorage.setItem('invPrepData', null);
            setOpen(false);
        }
    };
    const descriptionElementRef = React.useRef(null);
    React.useEffect(() => {
        const fm = async () => {
            try {
                setloading(true);
                let dataStg = await fnFetch();
                setloading(false);
                // console.log('dataStg', dataStg);
                setdata(dataStg?.data)
                setloading(false);
            } catch (e) {
                console.log('hata fetch ederken...', e);
                setloading(false);
            }
        }
        if (open) {
            fm();
            const { current: descriptionElement } = descriptionElementRef;
            if (descriptionElement !== null) {
                descriptionElement.focus();
            }
            if (props.data) {}

        }
    }, [open]);

    return (
        <React.Fragment>
            <Button onClick={handleClickOpen('paper')} sx={{mx: 4}}
                // variant="contained"
                startIcon={<InsertChartIcon />}
            >Trades Trend</Button>
            <Dialog
                open={open}
                onClose={handleClose}
                scroll={'paper'}
                aria-labelledby="scroll-dialog-title"
                aria-describedby="scroll-dialog-descri
                TransitionComponent={Transition}ption"
                fullWidth={true}
                maxWidth={fullScreen ? 'xl' : maxWidth}
            >
                <DialogTitle id="scroll-dialog-title">
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                        {/* <Typography variant="h6">
                            Trades Trend
                        </Typography> */}

                        <Button title='Refresh Chart Data' onClick={refreshChart}>Refresh </Button>
                        
                    </Stack>
                </DialogTitle>
                <>

                <div style={{position: 'relative', left: 10, width: '400px', overflow: 'scroll'}}>
                            
                        </div>
                    <DialogContent dividers={scroll === 'paper'}>
                        {loading && <Box sx={{ position: 'absolute', top: 0, left: 0, right: 20 }}><LinearProgress /></Box>}
                        
                        <ChartPNLs data={data} />
                        <ChartTradeVolumes data={data} />
                        <ChartTradeCommissions data={data} />
                        
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleClose}>Close</Button>
                    </DialogActions>
                </>
            </Dialog>
        </React.Fragment>
    );
}

export const ChartComponent = props => {

    const initialData = [
        { time: '2018-12-22', value: 32.51 },
        { time: '2018-12-23', value: 31.11 },
        { time: '2018-12-24', value: 27.02 },
        { time: '2018-12-25', value: 27.32 },
        { time: '2018-12-26', value: 25.17 },
        { time: '2018-12-27', value: 28.89 },
        { time: '2018-12-28', value: 25.46 },
        { time: '2018-12-29', value: 23.92 },
        { time: '2018-12-30', value: 22.68 },
        { time: '2018-12-31', value: 22.67 },
    ];
    const {
        data = initialData,
        colors: {
            backgroundColor = 'white',
            lineColor = '#2962FF',
            textColor = 'black',
            areaTopColor = '#2962FF',
            areaBottomColor = 'rgba(41, 98, 255, 0.28)',
        } = {},
    } = props;

    const chartContainerRef = useRef();

    useEffect(
        () => {
            const handleResize = () => {
                chart.applyOptions({ width: chartContainerRef.current.clientWidth });
            };

            const chart = createChart(chartContainerRef.current, {
                layout: {
                    background: { type: ColorType.Solid, color: backgroundColor },
                    textColor,
                },
                width: chartContainerRef.current.clientWidth,
                height: 300,
                timeScale: {
                    rightOffset: 12,
                    barSpacing: 4,
                    fixLeftEdge: true,
                    fixRightEdge: true,
                    lockVisibleTimeRangeOnResize: true,
                    rightBarStaysOnScroll: true,
                    borderVisible: true,
                    borderColor: '#000',
                    visible: true,
                    timeVisible: true,
                    secondsVisible: false,
                    tickMarkFormatter: (time, tickMarkType, locale) => {
                        //console.log(time, tickMarkType, locale);
                        var txt = moment(time * 1000).format('hh:mm A');
                        return String(txt);
                    },
                },
            });
            chart.timeScale().fitContent();

            const newSeries = chart.addAreaSeries({ lineColor, topColor: areaTopColor, bottomColor: areaBottomColor });
            newSeries.setData(data);

            window.addEventListener('resize', handleResize);

            return () => {
                window.removeEventListener('resize', handleResize);

                chart.remove();
            };
        },
        [data, backgroundColor, lineColor, textColor, areaTopColor, areaBottomColor]
    );

    return (
        <div
            ref={chartContainerRef}
        />
    );
};

export const ChartPNLs = props => {
    const chartRefX = useRef(null);
    const [crosshair, setcrosshair] = useState(false);
    const [data, setdata] = React.useState(false); 
    const [loading, setloading] = React.useState(false);
 
    const {
        colors: {
            backgroundColor = 'white',
            lineColor = '#2962FF',
            textColor = 'black',
            areaTopColor = '#2962FF',
            areaBottomColor = 'rgba(41, 98, 255, 0.28)',
        } = {},
    } = props;

    useEffect(() => {
        props.data && setdata(props.data)
    }, [props.data]);

    const chartContainerRef = useRef();

    const resetChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale.resetTimeScale();
    };

    const fitChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale && timeScale.fitContent();
    };
    useEffect(
        () => {
            if (data) {
                // console.log('Data Updated', Date.now())
                const handleResize = () => {
                    chart.applyOptions({ width: chartContainerRef.current.clientWidth });
                };

                const chart = createChart(chartContainerRef.current, {
                    layout: {
                        background: { type: ColorType.Solid, color: backgroundColor },
                        textColor,
                    },
                    width: chartContainerRef.current.clientWidth,
                    height: 300,
                    timeScale: {
                        rightOffset: 12,
                        barSpacing: 4,
                        fixLeftEdge: true,
                        fixRightEdge: true,
                        lockVisibleTimeRangeOnResize: true,
                        rightBarStaysOnScroll: true,
                        borderVisible: true,
                        borderColor: '#000',
                        visible: true,
                        timeVisible: true,
                        secondsVisible: false,
                        tickMarkFormatter: (time, tickMarkType, locale) => {
                            //console.log(time, tickMarkType, locale);
                            var txt = moment(time * 1000).format('hh:mm A');
                            return String(txt);
                        },
                    },

                });
                chart.timeScale().fitContent();

                let realizedpnlData = data.map(d => {
                    return ({
                        time: new Date(d.dt).getTime() / 1000, //d.dt.substring(0, 10), 
                        value: d.realizedPnl,
                        color: d.realizedPnl >= 0 ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
                    })
                });

                realizedpnlData = realizedpnlData.filter((arr, index, self) =>
                    index === self.findIndex((t) => (t.time === arr.time)))

                // console.log('realizedpnlData', realizedpnlData)
                const pnlBarSeries = chart.addHistogramSeries({
                    color: "#000",
                    lineWidth: 2,
                    priceFormat: {
                        type: 'volume',
                        precision: 3,
                    },
                    priceScaleId: 'right',
                    overlay: false,
                    scaleMargins: {
                        top: 0.1, // highest point of the series will be 70% away from the top
                        bottom: 0,
                    },
                });
                pnlBarSeries.setData(realizedpnlData);

                let pnlData = data.map(d => {
                    return ({
                        time: new Date(d.dt).getTime() / 1000, //d.dt.substring(0, 10), 
                        value: d.netpnl,
                        color: d.netpnl >= 0 ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
                    })
                })

                pnlData = pnlData.filter((arr, index, self) =>
                    index === self.findIndex((t) => (t.time === arr.time)))


                const lineSeries = chart.addLineSeries({
                    color: '#2962FF',
                    lineWidth: 4,
                    priceFormat: {
                        type: 'volume',
                        precision: 3,
                    },
                    // priceScaleId: '',
                    priceScaleId: "right",
                    overlay: true,
                    scaleMargins: {
                        top: 0.1, // highest point of the series will be 70% away from the top
                        bottom: 0,
                    },
                });

                lineSeries.setData(pnlData);


                chart.subscribeCrosshairMove((param) => {
                    if (param.time) {
                        var time = parseFloat(param.time) * 1000;
                        const dateStr = param.time;
                        var ix = Array.isArray(data) && data.findIndex(d => new Date(d.dt).getTime() / 1000 == param.time);
                        var txt = moment(param.time * 1000).format('MM-DD h:mm A');
                        // firstRow.innerHTML = '' + txt + ' - last:' + JSON.stringify(param.seriesData.get(candleSeries)) + ' ' + JSON.stringify(ix);
                        setcrosshair('' + dateStr + '/' + txt + ' - last:' + JSON.stringify(data[ix]) + ' ' + JSON.stringify(ix))
                        
                    } else {
                        // firstRow.innerText = ' ';
                        setcrosshair(false)
                    }
                });

                chartRefX.current = chart;
                window.addEventListener('resize', handleResize);

                return () => {
                    window.removeEventListener('resize', handleResize);

                    chart.remove();
                };
            }

            // const newSeries = chart.addAreaSeries({ lineColor, topColor: areaTopColor, bottomColor: areaBottomColor });
            // newSeries.setData(data);

        },
        [data, backgroundColor, lineColor, textColor, areaTopColor, areaBottomColor]
    );

    return (
        <>

<Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>

                <Typography variant="h5">PNL Trend: Bars: Realized, Lines: NetPnl</Typography>
                <Button title='Reset Chart View' sx={{ml: 2}} onClick={resetChart}>Reset </Button>
                <Button title='Fit Chart' onClick={fitChart}>Fit </Button>


    </Stack>
            <Box sx={{ width: '1100px', ml: '0px', height: '60px', borderWidth: 0, borderColor: 'red', overflow: 'auto', wordBreak: 'break-word' }}>
                <Typography sx={{ fontSize: 11, ml: '10px', borderBottomWidth: '1px', paddingVertical: '2px' }}>
                    {crosshair}&nbsp;
                </Typography>
            </Box>
        <div
            ref={chartContainerRef}
        />
        </>
    );
};


export const ChartTradeVolumes = props => {
    const chartRefX = useRef(null);
    const [crosshair, setcrosshair] = useState(false);
    const [data, setdata] = React.useState(false); 
    const [loading, setloading] = React.useState(false);
 
    const {
        colors: {
            backgroundColor = 'white',
            lineColor = '#2962FF',
            textColor = 'black',
            areaTopColor = '#2962FF',
            areaBottomColor = 'rgba(41, 98, 255, 0.28)',
        } = {},
    } = props;

    useEffect(() => {
        props.data && setdata(props.data)
    }, [props.data]);

    const chartContainerRef = useRef();


    const resetChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale.resetTimeScale();
    };

    const fitChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale && timeScale.fitContent();
    };
    useEffect(
        () => {
            if (data) {
                // console.log('Data Updated', Date.now())
                const handleResize = () => {
                    chart.applyOptions({ width: chartContainerRef.current.clientWidth });
                };

                const chart = createChart(chartContainerRef.current, {
                    layout: {
                        background: { type: ColorType.Solid, color: backgroundColor },
                        textColor,
                    },
                    width: chartContainerRef.current.clientWidth,
                    height: 300,
                    timeScale: {
                        rightOffset: 12,
                        barSpacing: 4,
                        fixLeftEdge: true,
                        fixRightEdge: true,
                        lockVisibleTimeRangeOnResize: true,
                        rightBarStaysOnScroll: true,
                        borderVisible: true,
                        borderColor: '#000',
                        visible: true,
                        timeVisible: true,
                        secondsVisible: false,
                        tickMarkFormatter: (time, tickMarkType, locale) => {
                            //console.log(time, tickMarkType, locale);
                            var txt = moment(time * 1000).format('hh:mm A');
                            return String(txt);
                        },
                    },
                });
                chart.timeScale().fitContent();

                let realizedpnlData = data.map(d => {
                    return ({
                        time: new Date(d.dt).getTime() / 1000, //d.dt.substring(0, 10), 
                        value: d.openTradesVol,
                        color: d.openTradesVol >= 0 ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
                    })
                });

                realizedpnlData = realizedpnlData.filter((arr, index, self) =>
                    index === self.findIndex((t) => (t.time === arr.time)))

                const pnlBarSeries = chart.addHistogramSeries({
                    color: "#000",
                    lineWidth: 2,
                    priceFormat: {
                        type: 'volume',
                        precision: 3,
                    },
                    priceScaleId: 'right',
                    overlay: false,
                    scaleMargins: {
                        top: 0.1, // highest point of the series will be 70% away from the top
                        bottom: 0,
                    },
                });
                pnlBarSeries.setData(realizedpnlData);

                let pnlData = data.map(d => {
                    return ({
                        time: new Date(d.dt).getTime() / 1000, //d.dt.substring(0, 10), 
                        value: d.closedTradesVol,
                        color: d.closedTradesVol >= 0 ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
                    })
                })
                pnlData = pnlData.filter((arr, index, self) =>
                    index === self.findIndex((t) => (t.time === arr.time)))

                const lineSeries = chart.addLineSeries({
                    color: '#2962FF',
                    lineWidth: 4,
                    priceFormat: {
                        type: 'volume',
                        precision: 3,
                    },
                    // priceScaleId: '',
                    priceScaleId: "right",
                    overlay: true,
                    scaleMargins: {
                        top: 0.1, // highest point of the series will be 70% away from the top
                        bottom: 0,
                    },
                });

                lineSeries.setData(pnlData);


                chart.subscribeCrosshairMove((param) => {
                    if (param.time) {
                        var time = parseFloat(param.time) * 1000;
                        const dateStr = param.time;
                        var ix = Array.isArray(data) && data.findIndex(d => new Date(d.dt).getTime() / 1000 == param.time);
                        var txt = moment(param.time * 1000).format('MM-DD h:mm A');
                        // firstRow.innerHTML = '' + txt + ' - last:' + JSON.stringify(param.seriesData.get(candleSeries)) + ' ' + JSON.stringify(ix);
                        setcrosshair('' + dateStr + '/' + txt + ' - last:' + JSON.stringify(data[ix]) + ' ' + JSON.stringify(ix))
                        
                    } else {
                        // firstRow.innerText = ' ';
                        setcrosshair(false)
                    }
                });

                chartRefX.current = chart;
                window.addEventListener('resize', handleResize);

                return () => {
                    window.removeEventListener('resize', handleResize);

                    chart.remove();
                };
            }

            // const newSeries = chart.addAreaSeries({ lineColor, topColor: areaTopColor, bottomColor: areaBottomColor });
            // newSeries.setData(data);

        },
        [data, backgroundColor, lineColor, textColor, areaTopColor, areaBottomColor]
    );

    return (
        <>

            <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>

                <Typography variant="h5">Volume Trend: Bars: Open, Lines: Closed</Typography>
                <Button title='Reset Chart View' sx={{ ml: 2 }} onClick={resetChart}>Reset </Button>
                <Button title='Fit Chart' onClick={fitChart}>Fit </Button>


            </Stack>
            <Box sx={{ width: '1100px', ml: '0px', height: '60px', borderWidth: 0, borderColor: 'red', overflow: 'auto', wordBreak: 'break-word' }}>
                <Typography sx={{ fontSize: 11, ml: '10px', borderBottomWidth: '1px', paddingVertical: '2px' }}>
                    {crosshair}&nbsp;
                </Typography>
            </Box>
            <div
                ref={chartContainerRef}
            />
        </>
    );
};


export const ChartTradeCommissions = props => {
    const chartRefX = useRef(null);
    const [crosshair, setcrosshair] = useState(false);
    const [data, setdata] = React.useState(false); 
    const [loading, setloading] = React.useState(false);
 
    const {
        colors: {
            backgroundColor = 'white',
            lineColor = '#2962FF',
            textColor = 'black',
            areaTopColor = '#2962FF',
            areaBottomColor = 'rgba(41, 98, 255, 0.28)',
        } = {},
    } = props;

    useEffect(() => {
        props.data && setdata(props.data)
    }, [props.data]);

    const chartContainerRef = useRef();


    const resetChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale.resetTimeScale();
    };

    const fitChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale && timeScale.fitContent();
    };
    useEffect(
        () => {
            if (data) {
                // console.log('Data Updated', Date.now())
                const handleResize = () => {
                    chart.applyOptions({ width: chartContainerRef.current.clientWidth });
                };

                const chart = createChart(chartContainerRef.current, {
                    layout: {
                        background: { type: ColorType.Solid, color: backgroundColor },
                        textColor,
                    },
                    width: chartContainerRef.current.clientWidth,
                    height: 300,
                    timeScale: {
                        rightOffset: 12,
                        barSpacing: 4,
                        fixLeftEdge: true,
                        fixRightEdge: true,
                        lockVisibleTimeRangeOnResize: true,
                        rightBarStaysOnScroll: true,
                        borderVisible: true,
                        borderColor: '#000',
                        visible: true,
                        timeVisible: true,
                        secondsVisible: false,
                        tickMarkFormatter: (time, tickMarkType, locale) => {
                            //console.log(time, tickMarkType, locale);
                            var txt = moment(time * 1000).format('hh:mm A');
                            return String(txt);
                        },
                    },
                });
                chart.timeScale().fitContent();

                let realizedpnlData = data.map(d => {
                    return ({
                        time: new Date(d.dt).getTime() / 1000, //d.dt.substring(0, 10), 
                        value: d.realizedCommision,
                        color: d.realizedCommision >= 0 ? 'rgba(150, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
                    })
                });
                realizedpnlData = realizedpnlData.filter((arr, index, self) =>
                    index === self.findIndex((t) => (t.time === arr.time)))


                const pnlBarSeries = chart.addHistogramSeries({
                    color: "#000",
                    lineWidth: 2,
                    priceFormat: {
                        type: 'volume',
                        precision: 3,
                    },
                    priceScaleId: 'right',
                    overlay: false,
                    scaleMargins: {
                        top: 0.1, // highest point of the series will be 70% away from the top
                        bottom: 0,
                    },
                });
                pnlBarSeries.setData(realizedpnlData);

                // let pnlData = data.map(d => {
                //     return ({
                //         time: new Date(d.dt).getTime() / 1000, //d.dt.substring(0, 10), 
                //         value: d.closedTradesVol,
                //         color: d.closedTradesVol >= 0 ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
                //     })
                // })
                // const lineSeries = chart.addLineSeries({
                //     color: '#2962FF',
                //     lineWidth: 4,
                //     priceFormat: {
                //         type: 'volume',
                //         precision: 3,
                //     },
                //     // priceScaleId: '',
                //     priceScaleId: "right",
                //     overlay: true,
                //     scaleMargins: {
                //         top: 0.1, // highest point of the series will be 70% away from the top
                //         bottom: 0,
                //     },
                // });

                // lineSeries.setData(pnlData);


                chart.subscribeCrosshairMove((param) => {
                    if (param.time) {
                        var time = parseFloat(param.time) * 1000;
                        const dateStr = param.time;
                        var ix = Array.isArray(data) && data.findIndex(d => new Date(d.dt).getTime() / 1000 == param.time);
                        var txt = moment(param.time * 1000).format('MM-DD h:mm A');
                        // firstRow.innerHTML = '' + txt + ' - last:' + JSON.stringify(param.seriesData.get(candleSeries)) + ' ' + JSON.stringify(ix);
                        setcrosshair('' + dateStr + '/' + txt + ' - last:' + JSON.stringify(data[ix]) + ' ' + JSON.stringify(ix))
                        
                    } else {
                        // firstRow.innerText = ' ';
                        setcrosshair(false)
                    }
                });

                chartRefX.current = chart;
                window.addEventListener('resize', handleResize);

                return () => {
                    window.removeEventListener('resize', handleResize);

                    chart.remove();
                };
            }

            // const newSeries = chart.addAreaSeries({ lineColor, topColor: areaTopColor, bottomColor: areaBottomColor });
            // newSeries.setData(data);

        },
        [data, backgroundColor, lineColor, textColor, areaTopColor, areaBottomColor]
    );

    return (
        <>

            <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>

                <Typography variant="h5">Commission Trend</Typography>
                <Button title='Reset Chart View' sx={{ ml: 2 }} onClick={resetChart}>Reset </Button>
                <Button title='Fit Chart' onClick={fitChart}>Fit </Button>


            </Stack>
            <Box sx={{ width: '1100px', ml: '0px', height: '60px', borderWidth: 0, borderColor: 'red', overflow: 'auto', wordBreak: 'break-word' }}>
                <Typography sx={{ fontSize: 11, ml: '10px', borderBottomWidth: '1px', paddingVertical: '2px' }}>
                    {crosshair}&nbsp;
                </Typography>
            </Box>
            <div
                ref={chartContainerRef}
            />
        </>
    );
};
