/* eslint-disable react/display-name */
/* eslint-disable react/jsx-key */
/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the
 * property of Subanet Limited. Unauthorized use, reproduction
 * or distribution of this software, or any portion thereof,
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, {
    useState, useEffect, useContext,
    useRef,
    forwardRef,
    useImperativeHandle,
} from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, Container, Divider, Stack } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import Dialog from '@mui/material/Dialog';
import AddchartIcon from '@mui/icons-material/Addchart';
import Slide from '@mui/material/Slide';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import CircularProgress from '@mui/material/CircularProgress';
import Select from '@mui/material/Select';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { styled } from "@mui/material/styles";
import Collapse from '@mui/material/Collapse';
import CancelIcon from '@mui/icons-material/Cancel';

import _ from 'lodash';
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

export const ButtonS = styled(Button)((props) => ({
    // Custom CSS

    '&:hover': {
        backgroundColor: '#ffcc00',
        boxShadow: 'none',
    },
    '&:active': {
        boxShadow: 'none',
        backgroundColor: '#3c52b2',
    },

    fontSize: '14px',
    borderRadius: props.pill ? 20 : 4,
    // border: '2px solid #000',
    color: '#3c52b2',
    marginBottom: 2,
}));

const style = {
    ml: 100,
    mt: 40,
    // position: 'absolute',
    // top: '120px',
    // left: '50%',
    transform: 'translate(-50%, -50%)',
    minWidth: 400,
    bgcolor: 'background.paper',
    border: '2px solid #000',
    boxShadow: 24,
    pt: 2,
    px: 4,
    pb: 3,
};



export default function Home(props) {
    const { ...rest } = props;
    const descriptionElementRef = React.useRef(null);
    const refListofBSG = useRef();
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false)
    const [open, setOpen] = useState(false);
    const [indicators, setindicators] = React.useState([]);
    const [rulesets, setrulesets] = React.useState([]);
    const [updateMe, setupdateMe] = useState(0);
    const [scroll, setScroll] = React.useState('paper');
    const [saveLoader, setsaveLoader] = useState(false)
    const [collapsed, setcollapsed] = useState(false)
    const [rulesetGroupData, setrulesetGroupData] = useState({})
    const [list, setlist] = useState([]);
    const [RSGDir, setRSGDir] = useState(false);
    const [CopyRuleSetsVisible, setCopyRuleSetsVisible] = useState(false);
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
            // router.push('/auth/SignInSide');
        },
    });
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    useEffect(() => {
        let d2B = {
            ...rulesetGroupData,
            ruleSet: rulesets,
        };
        console.log('[rulesets, updateMe]', rulesets, updateMe, d2B)
        setrulesetGroupData(d2B)
    }, [rulesets, updateMe]);
    React.useEffect(() => {
        // console.log('rulesetGroupData updated', rulesetGroupData)
    }, [rulesetGroupData]);
    useEffect(() => {
        colorMode.setCurrPage('/advanced/battlerulesets');
        drawerCollapsed && colorMode.collapseDrawer(true);
        const getX = async () => {
            let ixx = await fnFetch();
            setindicators(ixx);
        };
        getX();
    }, []);
    useEffect(() => {
        if (open) {
            const { current: descriptionElement } = descriptionElementRef;
            if (descriptionElement !== null) {
                descriptionElement.focus();
            }
        }
    }, [open]);
    if (status === "loading") {
        return "Loading..."
    };
    const toggleForm = (val) => {
        setcollapsed(val !== undefined ? val : !collapsed);
    };

    const copyRuleset = (nset) => {

        let currSet = rulesets;
        let idx = nset.name + '_' + (+new Date * Math.random()).toString(36).substring(0,6)
        nset.id = idx
        nset.rulesetId = idx;
        delete nset.rulesetGroupName;

        currSet.push(nset)
        setrulesets(currSet);
        setupdateMe(Date.now());

    };

    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/indicatorsandparams'
                // console.log(uri)
                const data = await fetcher(uri)
                resolve(data.data)
            } catch (e) {
                console.log('fetch err', e)
            }
        });
    }
    const handleClose = () => {
        setOpen(false);
    };
    const handleOpen = () => {
        setOpen(true);
        toggleForm(true);
    };
    const updateCondValue = (event) => {
        setrulesetGroupData({
            ...rulesetGroupData,
            cond: event.target.value == 'all' ? 1 : 0,
        });
    };
    const updatDirectionValue = (event) => {
        setrulesetGroupData({
            ...rulesetGroupData,
            direction: event.target.value,
        });
        setRSGDir(event.target.value)
    };
    const removeRuleset = inx => {
        let stg = [...rulesets];
        let uTaskIndex = stg.findIndex(t => t.id == inx.id);
        stg.splice(uTaskIndex, 1);
        setrulesets([...stg]);
    }

    const updateRSGName = (event) => {
        setrulesetGroupData({
            ...rulesetGroupData,
            rulesetName: event.target.value,
        });
    }
    const updateRulesetGroup = (inx, criterias) => {
        let stg = rulesets;
        let uTaskIndex = stg.findIndex(t => t.id == inx.id);
        let uTask = stg.find(t => t.id == inx.id);
        stg[uTaskIndex] = {
            ...uTask,
            ...criterias
        }
        setrulesets(stg);
        setupdateMe(Date.now())
    }
    const cancelEntry = async () => {
        setrulesetGroupData({});
        setrulesets([]);
    }
    const createTestRecords = async () => {
        let uri = '/api/pub/data/createtestruleset';
        try {
            const res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json();

            if (!datax.error) {
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }
    const saveRuleSetGroup = async () => {
        let data2Post = rulesetGroupData
        setsaveLoader(true);
        let uri = '/api/pub/data/rulesetsgroup'
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                setsaveLoader(false)
                return
            }

            const datax = await res.json()
            setsaveLoader(false)
            // await battleList();

            if (!datax.error) {
                alert('Saved!');
                setOpen(false);
                setrulesetGroupData({});
                setrulesets([]);
                setsaveLoader(false);
                refListofBSG && refListofBSG.current && refListofBSG.current?.refresh();
            } else {
                console.log('err desc', datax);
                setsaveLoader(false)
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setsaveLoader(false)
        };
    }
    return (
        <>
            <Head>
                <title>Gauss Algo - Nodes</title>
            </Head>
            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >
                <Stack className="mt-24 p-2">
                    {/* <Box sx={{ minHeight: 100 }} /> */}
                    <Box>
                        <ButtonS variant="contained" onClick={handleOpen} title="Create RuleSet">
                            <Typography>Add New RuleSet Group</Typography>
                        </ButtonS>&nbsp;&nbsp;
                        <ButtonS variant="contained" onClick={() => refListofBSG && refListofBSG.current && refListofBSG.current?.refresh()}>Refresh List</ButtonS>&nbsp;&nbsp;
                        {open && <ButtonS variant="contained" onClick={() => toggleForm(!collapsed)}>Toggle Create Form</ButtonS>}
                        <ButtonS variant="contained" onClick={() => createTestRecords()}>Create Test Records</ButtonS>&nbsp;&nbsp;
                    </Box>
                </Stack>

                {open && (
                    <Collapse in={collapsed}>
                        <Stack sx={{borderWidth: 2, m: 2, boxShadow: 24, }}>
                            <Box sx={{ minWidth: '1024px', m: 2 }}>
                                <Stack sx={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between', pr: 2 }}>
                                    <h2 id="parent-modal-title" style={{ margin: 1, color: 'black' }}>Create new ruleset group</h2>
                                    <Box>
                                        <ButtonS onClick={() => {
                                            cancelEntry();
                                            setOpen(false);
                                        }} disabled={saveLoader} variant="contained" pill={' '} color="success">Cancel</ButtonS>
                                        &nbsp;&nbsp;
                                        <ButtonS onClick={saveRuleSetGroup} disabled={saveLoader} variant="contained" pill={' '} color="success">Save</ButtonS>
                                    </Box>
                                </Stack>
                                <Divider />
                                <Stack sx={{ flexDirection: 'row' }}>
                                    <Box sx={{ borderWidth: 1, margin: 1, padding: 1, minWidth: '200px' }}>
                                        <FormControl>
                                            <TextField id="standard-basic" onChange={updateRSGName} label="Ruleset group name" variant="standard" />
                                            <br />
                                            <FormLabel id="demo-row-radio-buttons-group-label">Directon</FormLabel>
                                            <RadioGroup
                                                row
                                                aria-labelledby="demo-row-radio-buttons-group-label"
                                                name="row-radio-buttons-group"
                                                onChange={updatDirectionValue}
                                            >
                                                <FormControlLabel value="long" control={<Radio />} label="long" style={{ color: 'black' }} />
                                                <FormControlLabel value="short" control={<Radio />} label="short" style={{ color: 'black' }} />
                                                {/* <FormControlLabel value="both" control={<Radio />} label="both" style={{ color: 'black' }} /> */}
                                            </RadioGroup>

                                            <br />
                                            <FormLabel id="demo-row-radio-buttons-group-label">Condition</FormLabel>
                                            <RadioGroup
                                                row
                                                aria-labelledby="demo-row-radio-buttons-group-label"
                                                name="row-radio-buttons-group"
                                                onChange={updateCondValue}
                                            >
                                                <FormControlLabel value="all" control={<Radio />} style={{ color: 'black' }} label="All" />
                                                <FormControlLabel value="any" control={<Radio />} style={{ color: 'black' }} label="Any" />

                                            </RadioGroup>

                                            <Button onClick={() => {
                                                let currSet = rulesets;
                                                currSet.push({
                                                    id: 'RuleSet' + Date.now().toString(),
                                                })
                                                setrulesets(currSet);
                                                setupdateMe(Date.now());
                                                // console.log('currSet', currSet)
                                            }}>add rule set</Button>
                                        </FormControl>
                                        {RSGDir && (
                                            <>
                                                <Divider sx={{ my: 2 }} />
                                                <Typography sx={{
                                                    fontSize: '12px',
                                                    textAlign: 'center',
                                                    backgroundColor: '#cecece',
                                                    borderRadius: '5px',
                                                    p: '3px',
                                                    cursor: 'pointer',
                                                    borderWidth: '1px'
                                                }} 
                                                    onClick={() => setCopyRuleSetsVisible(!CopyRuleSetsVisible)}
                                                >Copy rulesets from other strategies</Typography>
                                            </>
                                        )}
                                    </Box>
                                    <Stack sx={{ flexDirection: 'row', flex: 1, flexWrap: 'wrap' }}>
                                        {Array.isArray(rulesets) && rulesets.map((r, rix) => {
                                            return (
                                                <AddRuleSet
                                                    key={rix.toString()}
                                                    ruleset={r}
                                                    updateRulesetGroup={updateRulesetGroup}
                                                    removeRuleset={removeRuleset}
                                                    indicators={indicators} />
                                            )
                                        })}
                                    </Stack>
                                </Stack>

                                {CopyRuleSetsVisible && <CopyRuleSets rulesets={list} direction={RSGDir} copyRuleset={copyRuleset} />}
                                {/* <ChildModal /> */}
                            </Box>
                        </Stack>
                    </Collapse>
                )}

                <ListofBSG {...props} setlist={setlist} list={list} ref={refListofBSG} token={token} handleOpen={handleOpen} />
            </AppLayout>


        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

const AddRuleSet = forwardRef((props, ref) => {
    const [rulesetData, setrulesetData] = useState({})
    const [rules, setrules] = React.useState([]);
    const [updateMe, setupdateMe] = useState(0);
    useEffect(() => {

        let obj = JSON.parse(JSON.stringify(rulesetData));
        let indicators = Array.isArray(obj.criteria) ? [...new Set((obj.criteria).map(a => a.indicator))] : [];
        obj.indicators = Array.isArray(indicators) && indicators[0] !== undefined ? indicators : [];
        console.log('AddRuleSet rules updated! || updateMe: ', rules)
        setrulesetData({
            ...obj,
            criteria: rules,
        })
    }, [rules, updateMe]);

    const removeRule = inx => {
        let stg = [...rules];
        let uTaskIndex = stg.findIndex(t => t.id == inx.id);
        stg.splice(uTaskIndex, 1);
        setrules([...stg]);
    }

    React.useEffect(() => {
        console.log('Addruleset mounted!', props.ruleset);
        if (props.ruleset && props.ruleset.criteria && Array.isArray(props.ruleset.criteria)) {
            setrules([...props.ruleset.criteria]);
            setrulesetData({
                ...rulesetData,
                name: props.ruleset.name,
                key: props.ruleset.id + '_key',
                cond: props.ruleset.cond,
            });
        }
    }, []);

    React.useEffect(() => {
        // console.log('rulesetData updated', rulesetData)
        // let obj = JSON.parse(JSON.stringify(rulesetData));
        // let indicators = Array.isArray(obj.criteria) ? [...new Set((obj.criteria).map(a => a.indicator))] : [];
        // obj.indicators = indicators;
        props.updateRulesetGroup && props.updateRulesetGroup(props.ruleset, rulesetData);
    }, [rulesetData]);

    const updateRSName = (event) => {
        // console.log('updateRSName - rulesetData', rulesetData)
        setrulesetData({
            ...rulesetData,
            name: event.target.value,
            key: event.target.value.toString() + '_key',
        });
    };

    const updateCondValue = (event) => {
        setrulesetData({
            ...rulesetData,
            cond: event.target.value == 'all' ? 1 : 0,
        });
    };

    const updateRuleset = (inx, criterias) => {
        let stg = rules;
        let uTaskIndex = stg.findIndex(t => t.id == inx.id);
        let uTask = stg.find(t => t.id == inx.id);

        // console.log('stg', stg)
        // console.log('uTasks', uTask)
        // console.log('inx, criterias', inx, criterias)
        stg[uTaskIndex] = {
            ...uTask,
            ...criterias
        }
        setrules(stg);
        setupdateMe(Date.now())
    }

    useImperativeHandle(ref, () => ({
        async openBottomSheet() {
            console.log('xx')
        },
    }));


    return (
        <Box sx={{ borderWidth: 1, m: 1, flex: 1, minWidth: '300px' }}>
            <Stack sx={{ backgroundColor: '#ffcc00', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography sx={{ px: 1 }}>{props.ruleset?.id}</Typography>
                <Button onClick={() => props.removeRuleset(props.ruleset)}>x</Button>
            </Stack>

            <Stack sx={{ flexDirection: 'row' }}>

                <Box sx={{ p: 1, }}>
                    <TextField id="standard-basic" 
                        onChange={updateRSName} 
                        value={rulesetData.name}
                        label="Ruleset name" variant="standard"
                         />
                    <br /><br />
                    <FormLabel id="demo-row-radio-buttons-group-label">Match Condition</FormLabel>
                    <RadioGroup
                        row
                        aria-labelledby="demo-row-radio-buttons-group-label"
                        name="row-radio-buttons-group"
                        onChange={updateCondValue}
                    >
                        <FormControlLabel value="all" checked={rulesetData.cond == 1} control={<Radio />} style={{ color: 'black' }} label="All" />
                        <FormControlLabel value="any" checked={rulesetData.cond == 0} control={<Radio />} style={{ color: 'black' }} label="Any" />
                    </RadioGroup>

                    <Button onClick={() => {
                        let currSet = rules;
                        currSet.push({
                            id: 'Rule' + Date.now().toString(),
                        })
                        // console.log('curr', currSet)
                        setrules(currSet);
                        setupdateMe(Date.now());
                    }}>Add Rule</Button>

                </Box>


                <Stack sx={{ flexDirection: 'row', flex: 1, flexWrap: 'wrap' }}>
                    {Array.isArray(rules) && rules.map((r, rix) => {
                        return (
                            <SetRuleSetBox key={rix.toString()}
                                rule={r}
                                removeRule={removeRule}
                                updateRuleset={updateRuleset}
                                indicators={props.indicators} />
                        )
                    })}
                </Stack>

            </Stack>

        </Box>
    )
});

const SetRuleSetBox = props => {
    const [criteria, setcriteria] = React.useState({});
    const [age, setAge] = React.useState('');
    const [ageSub, setAgeSub] = React.useState('');
    const [ageKeys, setAgeKeys] = React.useState('');
    const handleChange = (event) => {
        setAge(event.target.value);
        let currIndicator = criteria?.indicator;
        if (currIndicator !== event.target.value) {
            setcriteria({
                ...criteria,
                indicator: event.target.value,
                indicatorRef: event.target.value.toString() + 'Ref',
                item: '',
            });
            setAgeSub('');
        }

        if (event.target.value && event.target.value !== '') {
            let ixx = props.indicators.find(i => i.indicator === event.target.value);
            setAgeKeys(ixx?.resultKeys)
        } else {
            setAgeKeys('');
        }
    };
    const handleChangeSub = (event) => {
        setAgeSub(event.target.value);
        setcriteria({
            ...criteria,
            item: event.target.value,
        })
    };
    const updateRefName = (event) => {
        setcriteria({
            ...criteria,
            indicatorRef: event.target.value,
        })
    };
    const updateRule = (event) => {
        setcriteria({
            ...criteria,
            rule: event.target.value,
        })
    }
    const updateCriteriaName = (event) => {
        setcriteria({
            ...criteria,
            name: event.target.value,
        })
    }

    React.useEffect(() => {
        // console.log('criteria updated', criteria)
        props.updateRuleset && props.updateRuleset(props.rule, criteria);
    }, [criteria]);

    React.useEffect(() => {
        console.log('rule box mounted!', props.rule);
        setAge(props.rule.indicator);
        setcriteria({
            ...criteria,
            id: props.rule.id,
            name: props.rule.name,
            indicator: props.rule.indicator,
            indicatorRef: props.rule.indicatorRef,
            item: props.rule.item,
            rule: props.rule.rule,
            // key: props.ruleset.id + '_key',
            // cond: props.ruleset.cond,
        });

        if (props.rule.indicator && props.rule.indicator !== '') {
            let ixx = props.indicators.find(i => i.indicator === props.rule.indicator);
            setAgeKeys(ixx?.resultKeys)
        } else {
            setAgeKeys('');
        } 

        // if (props.rule && props.rule.criteria && Array.isArray(props.ruleset.criteria)) {
        //     setrules([...props.ruleset.criteria]);
        //     setrulesetData({
        //         ...rulesetData,
        //         name: props.ruleset.name,
        //         key: props.ruleset.id + '_key',
        //         cond: props.ruleset.cond,
        //     });
        // }
    }, []);

    return (
        <Box sx={{ borderWidth: 1, m: 1, minWidth: '300px' }}>
            <Stack sx={{ backgroundColor: '#ffcc00', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography sx={{ px: 1 }}>{props.rule?.id}</Typography>
                <Button onClick={() => props.removeRule(props.rule)}>x</Button>
            </Stack>
            <Box sx={{
                color: 'GrayText',
                flex: 1, fontSize: 12,
                borderWidth: 1,
            }}>
                <TextField id="ruleCriteriaName" onChange={updateCriteriaName} label="Criteria name" variant="standard"
                    sx={{ m: 1, }} />
                <FormControl variant="standard" sx={{ minWidth: 120, m: 1 }}>
                    <InputLabel id="resultkeyID">Indicator</InputLabel>
                    <Select
                        labelId="demo-select-small-label"
                        id="demo-select-small"
                        value={age}
                        label="Age"
                        onChange={handleChange}
                    >
                        <MenuItem value="">
                            <em>{'none - ' + criteria?.indicator}</em>
                        </MenuItem>
                        {Array.isArray(props.indicators) && props.indicators.map((ixx, i) => {
                            return (
                                <MenuItem key={i.toString()} 
                                    defaultChecked={ixx.indicator == criteria.indicator} 
                                    selected={ixx.indicator == criteria.indicator} 
                                    value={ixx.indicator}>{ixx.indicator}
                                </MenuItem>
                            )
                        })}
                    </Select>
                </FormControl>
                {ageKeys !== '' && (
                    <>
                        <Box sx={{ my: 1 }}>
                            <TextField id="indicatorRef"
                                label="İndicator ref"
                                onChange={updateRefName}
                                value={props.rule.indicatorRef || (age.toString() + 'Ref') }
                                defaultValue={props.rule.indicatorRef || (age.toString() + 'Ref')}
                                // defaultValue={(age.toString() + 'Ref')}
                                variant="standard" sx={{ m: 1 }} />
                                
                            <FormControl variant="standard" sx={{ minWidth: 120, mx: 1 }}>
                                <InputLabel id="resultkeyID">Result Key</InputLabel>
                                <Select
                                    labelId="resultkeyID"
                                    id="resultkeyID_Select"
                                    value={props.rule.item || ageSub}
                                    // label="Key"
                                    onChange={handleChangeSub}
                                >
                                    <MenuItem value="">
                                        <em>None</em>
                                    </MenuItem>
                                    {Array.isArray(ageKeys) && ageKeys.map((ixxS, is) => {
                                        return (
                                            <MenuItem key={is.toString()} 
                                                defaultChecked={ixxS.name == props.rule.item}
                                                selected={ixxS.name == props.rule.item}
                                                value={ixxS.name}>{ixxS.name}</MenuItem>
                                        )
                                    })}
                                </Select>
                            </FormControl>
                            <TextField id="standard-basic" sx={{ mx: 1 }}
                                onChange={updateRule} label="rule"
                                value={props.rule.rule}
                                defaultValue={props.rule.rule}
                                variant="standard" />
                        </Box>
                    </>
                )}
                <Divider />
            </Box>
        </Box>
    )
}


const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
  });

const ListofBSG = forwardRef((props, ref) => {

    const btnRef = React.useRef()
    const [loading, setloading] = useState(false)
    const [data, setdata] = React.useState([]);
    const [modaldata, setmodaldata] = useState(false)
    // const [modaldataHTML, setmodaldataHTML] = useState(false);
    useEffect(() => {
        const getX = async () => {
            let ixx = await fnFetch();
            setdata(ixx?.data);
            props.setlist && props.setlist(ixx?.data);
        };
        getX();
    }, []);

    const [selectedValue, setSelectedValue] = React.useState();
    const [open, setOpen] = React.useState(false);
  
    const handleClickOpen = modaldataStg => {
        let resx = {...modaldataStg}
        resx.ruleSet = JSON.parse(resx.ruleSet)
        setmodaldata(resx)
        setTimeout(() => {
            setOpen(true);
            // setmodaldataHTML(JSON.stringify(modaldataStg, null, 4));
        }, 100);
    };
  
    const handleClose = (value) => {
      setOpen(false);
      setSelectedValue(value);
    };
  
    const handleDelete = async (value) => {
      let data2Post = value
      if (confirm('Are you sure?')) {

        let uri = '/api/pub/data/deleterulesetsgroup'
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }
  
            const datax = await res.json()
            // await battleList();
  
            if (!datax.error) {
                alert('deleted!');
                setOpen(false);
                refresh();
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        };
      }

    };

    const refresh = async () => {
        let ixx = await fnFetch();
        setdata(ixx?.data);
    };

    useImperativeHandle(ref, () => ({
        async refresh() {
            let ixx = await fnFetch();
            setdata(ixx?.data);
        },
    }));

    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                setloading(true)
                var uri = '/api/pub/data/rulesetsgroup'
                // console.log(uri)
                const datax = await fetcher(uri)
                setloading(false)
                resolve(datax.data)
            } catch (e) {
                console.log('fetch err', e)
                setloading(false)
            }
        });
    }

    return (
        <Box sx={{ padding: 2, borderWidth: 2, m: 2, boxShadow: 24, }}>

            <Box
                sx={{
                    mx: 1,
                }}
            >
                <TableContainer component={Paper}>
                    {!loading && <Table sx={{ minWidth: 650 }} aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell>#</TableCell>
                                <TableCell>id</TableCell>
                                <TableCell>rulesetName</TableCell>
                                <TableCell>direction</TableCell>
                                <TableCell>cond</TableCell>
                                <TableCell>ruleset</TableCell>
                                {/* <TableCell>dt created</TableCell> */}
                                <TableCell>&nbsp;</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {data && Array.isArray(data) && data.map((d, i) => {
                                // let LastLogData = Array.isArray(LogData) && LogData.find(l => l.script === d.key);
                                let textClass = 'text-xs ' + (!d.is_active ? 'p-2' : 'bg-yellow-300 p-2');
                                return (
                                    <TableRow key={i.toString()}>
                                        <TableCell>{i + 1}</TableCell>
                                        <TableCell title={d.node_type}>
                                            <span className='text-xs' >{d.rsgID}</span></TableCell>
                                        <TableCell >
                                            <span className='text-xs' >{d.rulesetName}</span></TableCell>
                                        <TableCell >
                                            <span className='text-xs' >{d.direction}</span></TableCell>
                                        <TableCell >
                                            <span className='text-xs' >{d.cond}</span></TableCell>
                                        {/* <TableCell ><span className='text-xs' >{d.dtcreated}</span></TableCell> */}
                                        <TableCell title={'created: ' + d.dtupdated} sx={{ maxWidth: '560px', overflow: 'auto' }}>
                                            <Box sx={{ minWidth: '400px', maxHeight: '50px', m: 0, wordBreak: 'break-all' }} onClick={() => {
                                                navigator.clipboard.writeText(JSON.stringify(d));

                                            }}>
                                                <pre sx={{ maxWidth: '400px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                                                    <code className='text-xs'>{JSON.stringify(JSON.parse(d.ruleSet), null, 0)}</code>
                                                </pre>
                                            </Box></TableCell>
                                        <TableCell>
                                            <Button ref={btnRef} variant="outlined" onClick={() => handleClickOpen(d)} size={'xs'}>
                                                View
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                )
                            })}
                            {data && Array.isArray(data) && data.length == 0 && (
                                <>
                                <Typography variant="h5" sx={{p: 1, px: 3}}>No record found.</Typography>

                                    <ButtonS variant="contained" sx={{m:1 }} onClick={props.handleOpen} title="Create RuleSet">
                                        <Typography>Add New RuleSet Group</Typography>
                                    </ButtonS>
                                </>
                            )}
                        </TableBody>
                    </Table>
                    }
                    {loading && <div align='center' className='py-4'><CircularProgress /></div>}
                </TableContainer>
            </Box>

            <Dialog onClose={handleClose} open={open} fullScreen={false} maxWidth={'lg'}
                TransitionComponent={Transition}
                keepMounted
            >
                <DialogTitle>
                    <Stack sx={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
                        <Typography variant="h5">{modaldata?.rulesetName ? modaldata?.rulesetName : modaldata?.rsgID}</Typography>
                        <ButtonS onClick={handleClose}>x</ButtonS>
                    </Stack>
                </DialogTitle>
                <Divider />

                <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(modaldata));

                }}>
                    <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                        <code>{JSON.stringify(modaldata, null , 4)}</code>
                    </pre>
                </Box>

                <Divider />
                <Stack sx={{ flex: 1, flexDirection: 'row', justifyContent: 'space-between' }}>
                    <ButtonS style={{ backgroundColor: 'red', color: 'white' }} sx={{ m: 1 }} onClick={() => handleDelete(modaldata)}>Delete</ButtonS>
                    <ButtonS sx={{ m: 1, flex: 1 }} onClick={handleClose}>Close</ButtonS>
                </Stack>
            </Dialog>
        </Box>
    )
})

const CopyRuleSets = props => {
    const {rulesets, } = props;
    const [ruleSetlist, setruleSetlist] = useState([]);
    const [ruleset, setruleset] = useState(false);
    useEffect(() => {
        if (props.rulesets) {
            let arr = props.rulesets;
            let refArr = []
            let refL = Array.isArray(arr) && arr.map(a => {
                let rules = JSON.parse(a.ruleSet);
                Array.isArray(rules) && rules.map(r => {
                    r.rulesetId = r.id;
                    r.direction = a.direction;
                    r.rulesetGroupName = a.rulesetName;
                    delete r.id;
                    // console.log('rules', r)
                    refArr.push(r)
                });
            });
            if (props.direction) {
                let nx = refArr.filter(r => r.direction == props.direction)
                setruleSetlist(nx);
            } else {
                setruleSetlist(false);
            }
        }
    }, [props.rulesets, props.direction]);
    const rfilter = fc => {
        let x = ruleSetlist.find(r => r.rulesetId == fc)
        setruleset(x);
    };

    const copyRuleset = rset => {
        props.copyRuleset && props.copyRuleset(rset)
    }
    return (
        <Stack sx={{ flexDirection: 'row' }}>
            <Box sx={{minWidth: '220px', mx: '5px'}}>
                {Array.isArray(ruleSetlist) && ruleSetlist.map((r,ixx) => {
                    return (
                        <Typography key={ixx.toString()} onClick={() => rfilter(r.rulesetId)} sx={{fontSize: '12px', 
                            textAlign: 'center', 
                            backgroundColor: r.direction == 'long' ? '#D7BDE2' : '#E67E22',
                            borderRadius: '5px',
                            p: '3px',
                            cursor: 'pointer',
                            borderWidth: '1px'}}><span title={r.rulesetGroupName}>{r.rulesetGroupName} / {r.name}</span></Typography>
                    )
                })}
            </Box>
            {ruleset && (
                <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(ruleset));

                }}>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', my: '3px' }}>
                    <Typography>{ruleset?.direction} / {ruleset?.rulesetGroupName} - {ruleset?.name}</Typography>
                        <Stack  sx={{ flexDirection: 'row', alignItems: 'center'}}>
                        <Typography onClick={()=> setruleset(false)} sx={{
                            fontSize: '12px',
                            textAlign: 'center',
                            borderRadius: '5px',
                            p: '3px',
                            cursor: 'pointer',
                            borderWidth: '1px',
                            mr: '10px',
                        }}><CancelIcon />&nbsp;Close</Typography>
                        <Typography onClick={() => copyRuleset(ruleset)} sx={{
                            fontSize: '12px',
                            textAlign: 'center',
                            backgroundColor: '#cecece',
                            borderRadius: '5px',
                            p: '3px',
                            cursor: 'pointer',
                            borderWidth: '1px'
                        }}><AddchartIcon />&nbsp;Add</Typography>
                        </Stack>
                    </Stack>
                    <Divider sx={{my: '5px'}} />
                    <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap", fontSize: '11px' }}>
                        <code>{JSON.stringify(ruleset, null, 4)}</code>
                    </pre>
                </Box>
            )}

        </Stack>
    )
}