/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, { useState, useEffect, useContext, Suspense } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'

import Link from 'next/link'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, CircularProgress, Container, Checkbox, Stack, Typography, Chip } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import Dialog from '@mui/material/Dialog';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { BiKnife, BiWindowClose } from "react-icons/bi";
import Drawer from '@mui/material/Drawer';
import moment from "moment";
import LinearProgress from '@mui/material/LinearProgress';
import { convertUTCDateToLocalDate } from "../../lib/fnx/fnx.fe";
import clsx from 'clsx';
import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import CloseIcon from '@mui/icons-material/Close';
import FunctionsIcon from '@mui/icons-material/Functions';
import AddIcon from '@mui/icons-material/Add';
import CandlestickChartIcon from '@mui/icons-material/CandlestickChart';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Collapse from '@mui/material/Collapse';
import Slide from '@mui/material/Slide';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

const ODD_OPACITY = 0.2;

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function LinearProgressWithLabel(props) {
    return (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress variant="determinate" {...props} />
            </Box>
            <Box sx={{ minWidth: 35 }}>
                <Typography variant="body2" color="text.secondary">{`${Math.round(
                    props.value,
                )}%`}</Typography>
            </Box>
        </Box>
    );
}

export default function Home(props) {
    const { ...rest } = props;
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false);
    const [battleLoader, setbattleLoader] = useState(false);
    const [battleLoader2, setbattleLoader2] = useState(false);
    const [battleListLoader, setbattleListLoader] = useState(false);
    const [activeBattleData, setactiveBattleData] = useState(false);
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    })
    useEffect(() => {
        colorMode.setCurrPage('/action/watch');
        drawerCollapsed && colorMode.collapseDrawer(true);
    }, []);
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    const battleList = async () => {
        setbattleListLoader(true);
        let uri = '/api/pub/data/battleList/all'
        try {
            const res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                setbattleListLoader(false)
                return
            }

            const datax = await res.json();
            setactiveBattleData(datax);
            setbattleListLoader(false)

            if (!datax.error) {
                setbattleListLoader(false)
            } else {
                console.log('err desc', datax);
                setbattleListLoader(false)
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setbattleListLoader(false)
        }

    }
    useEffect(() => {
        const getList = async () => {
            let list = await battleList();
        }
        getList();
    }, []);

    return (
        <>
            <Head>
                <title>Gauss Algo - Battle Watch</title>
            </Head>

            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >
                <Stack className="mt-24">
                    {/* <Input /> */}
                    <Box sx={{
                        m: 2,
                    }}>
                        <Typography variant="h5" component="div">
                            {bull}Battle {bull} Watch &nbsp;{bull}&nbsp;
                            {/* <Chip variant="filled" color={'primary'} label={'%'} /> */}
                        </Typography>
                        <Box>
                            <BTabs {...props} activeBattleData={activeBattleData} user={user} />
                        </Box>
                        <br />

                    </Box>
                </Stack>
            </AppLayout>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

const bull = (
    <Box
        component="span"
        sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
        •
    </Box>
);
const BTabs = props => {
    const { activeBattleData, user } = props;
    const { token, refreshToken } = user ? user : {};
    const [value, setValue] = React.useState('1');

    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    return (
        <>
            <Box sx={{ width: '100%', typography: 'body1' }}>
                <TabContext value={value}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <TabList onChange={handleChange} aria-label="lab API tabs example">
                            <Tab label="Battle Pairs" value="1" />
                            {/* <Tab label="Trades" value="2" /> */}
                            <Tab label="Logs" value="2" />
                        </TabList>
                    </Box>
                    <TabPanel value="1"><TabBattlePairs {...props} /></TabPanel>
                    {/* <TabPanel value="2"><TabBattleTrades {...props} /></TabPanel> */}
                    <TabPanel value="2">
                        <Suspense fallback={<div>loading...</div>}>
                            <TabLogs />
                        </Suspense>
                    </TabPanel>
                </TabContext>
            </Box>
        </>
    )
}
const TabLogs = props => {
    useEffect(() => {
        console.log('lazy!', Date.now())
    }, [])
    return (
        <>
            test... {Date.now()}
        </>
    )
};
const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));

const TabBattlePairs = props => {
    const { activeBattleData, user } = props;
    const { token, refreshToken } = user ? user : {};
    const [klineData, setklineData] = useState(false);
    const [watchData, setwatchData] = useState(false);
    const [battleInitData, setbattleInitData] = useState(props.activeBattleData ? props.activeBattleData[0] : false);
    const [parameters, setparameters] = useState(false);
    const [pairs, setpairs] = useState(false);
    const [loading, setloading] = useState(false);
    const [opencloseTime, setopencloseTime] = useState('');

    const [dataTime, setdataTime] = useState(false);
    const fKlineData = async () => {
        setloading(true);
        let uri = '/api/pub/data/klines'
        try {
            const res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                setloading(false)
                return
            }

            const datax = await res.json();
            // console.log('fKlineData', datax);
            setklineData(datax?.data);
            setdataTime(new Date(Date.now()).toLocaleTimeString())

            setloading(false)

            if (!datax.error) {
                setloading(false)
            } else {
                console.log('err desc', datax);
                setloading(false)
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
            return datax?.data;
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setloading(false)
        }

    }

    useEffect(() => {
        if (props.activeBattleData) {
            let btlParams = props.activeBattleData[0] ? JSON.parse(props.activeBattleData[0]?.battle_params) : {};
            setpairs(btlParams?.parameters?.pairs)
            setparameters(btlParams)
        }
    }, [props.activeBattleData])

    const prepWatchData = stgData => {
        let resp = [];
        let pairs = stgData?.battle_params?.parameters?.pairs;
        let battleInterval = stgData?.battle_params?.parameters?.battleInterval;
        let stgArr = []
        let klineData = stgData?.klines; //?.klineData
        Array.isArray(klineData) && klineData.map((p, rowID) => {
            stgArr.push({
                id: rowID + 1,
                ...p,
                takerRatio: parseFloat(p?.quoteVolumeTaker) / parseFloat(p?.quoteVolume) * 100,
                rulesets: Array.isArray(p.strategies) && p.strategies.filter(s => s.result == true),
            })
        }); 
        resp = stgArr;
        return resp;
    }

    const [state, setState] = React.useState({
        top: false,
        left: false,
        bottom: false,
        right: false,
    });
    const [drawerData, setdrawerData] = useState(false)
    const [drawerDataURL, setdrawerDataURL] = useState(false)
    const [drawerDataT, setdrawerDataT] = useState(false)

    const toggleDrawer = (anchor, open, pairData) => (event) => {
        // console.log('toggle', anchor, open, pairData)
        if (event?.type === 'keydown' && (event?.key === 'Tab' || event?.key === 'Shift')) {
            return;
        }
        if (open) {
            setdrawerDataURL(`https://www.tradingview.com/chart?symbol=BINANCE%3A${pairData?.symbol}PERP`);
            setdrawerData(pairData);
        } else {
            setdrawerDataT(false)
            setdrawerData(false)
        }
        setState({ ...state, [anchor]: open });
    };

    const restartNode = async pair => {
        let uri = '/api/pub/data/battlenoderestart'
        let data2Post = {
            node_key_tag: pair,
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json()

            if (!datax.error) {
                alert('restarted node: ' + pair)
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }

    useEffect(() => {
        const getList = async () => {
            let list = await fKlineData();
            // console.log('fKlineData!list', Date.now(), list, list?.battle_params);
            setbattleInitData(list?.battle_params)
            let watchStg = prepWatchData(list);
            // console.log('watch', watchStg);
            // console.log('watchStg!list-refres', watchStg);
            setwatchData(watchStg);
            //watchData
        }
        getList();
    }, []);

    const refreshData = async () => {
        try {
            let list = await fKlineData();
            // console.log('fKlineData!refreshData', Date.now(), list);
            let watchStg = prepWatchData(list);
            let ocTxt = `${watchStg[0].kline?.openTimeHRF} - ${watchStg[0].kline?.closeTimeHRF}`
            // console.log('watchStg!list', watchStg, 'xx', Array.isArray(watchStg) && watchStg[0]);
            setopencloseTime(ocTxt)
            setwatchData(watchStg);
        } catch (e) {
            console.log('refresh', e)
        }
    }
    useEffect(() => {
        const interval = setInterval(() => {
            refreshData();
        }, 5000); //set your time here. repeat every 5 seconds
        return () => clearInterval(interval);
    }, []);

    const columns = watchData ? [
        { field: 'symbol', headerName: 'Symbol', width: 120, },
        {
            field: 'close', headerName: 'Close', width: 100, type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: fractionDigit
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            }
        },
        {
            field: 'volume', headerName: 'QVolume', width: 85, type: 'number',
            valueGetter: (params) => params.row?.quoteVolume,
            valueFormatter: ({ value }) => {
                let fractionDigit = 2; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: fractionDigit
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            },

            renderCell: (params) => {
                let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.row?.volume}>{val}</span>;
            }
        },
        {
            field: 'takerRatio', headerName: 'Taker', width: 60, type: 'number',
            // valueGetter: (params) => parseFloat(params.row?.quoteVolumeTaker) / parseFloat(params.row?.quoteVolume) * 100,
            // valueGetter: (params) => params.row.kline,
            valueFormatter: ({ value }) => {
                // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
                return parseFloat(value).toFixed(0) + '%';
            }
        },
        {
            field: 'trades', headerName: 'Trades', width: 70, type: 'number',
            valueGetter: (params) => params.row?.trades,
            valueFormatter: ({ value }) => {
                // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
                return Number(value).toFixed(0);
            }
        },
        {
            field: 'delta24h', headerName: 'Delta', width: 70, type: 'number',
            // valueGetter: (params) => {
            //     let value = params.row?.kline;
            //     let Ratio = value ? (parseFloat(value.close) - parseFloat(value.low24h)) / (parseFloat(value.high24h) - parseFloat(value.low24h)) * 100 : 0
            //     return Ratio;
            // },
            // valueFormatter: ({ value }) => {
            //     let Ratio = (parseFloat(value.close) - parseFloat(value.low24h)) / (parseFloat(value.high24h) - parseFloat(value.low24h)) * 100
            //     return Ratio;
            // },
            renderCell: ({ value }) => {
                // console.log(p)
                return (
                    <ProgressBar value={(parseFloat(value) / 100)} />

                )
            }
        },
        {
            field: 'olh', headerName: 'HLO', width: 180,
            valueGetter: (params) => params.row,
            renderCell: (p) => {
                let value = p.value;
                setopencloseTime(`${value?.openTimeHRF} - ${value?.closeTimeHRF}`)
                return (
                    <Typography title={JSON.stringify(value)} variant="caption">{`O: ${value?.open}, L: ${value?.low}, H: ${value?.high}, H24: ${value?.high24h}, L24: ${value?.low24h}, O24: ${value?.open24h}`}</Typography>
                )
            },
            valueFormatter: ({ value }) => {
                // console.log(value)
                setopencloseTime(`${value?.openTimeHRF} - ${value?.closeTimeHRF}`)
                return `O: ${value?.open}, L: ${value?.low}, H: ${value?.high}, H24: ${value?.high24h}, L24: ${value?.low24h}, O24: ${value?.open24h}`;
            }
        },
        //TODO Add active position state...
        {
            field: 'indixLu', headerName: 'LUI', width: 45,
            valueGetter: (params) => {
                const { row = {} } = params;
                const { indicators, kline = {} } = row;
                let resp = null;
                // console.log('params indicatorOpenTime', indicators)
                let indicatorOpenTime = indicators && 
                    Array.isArray(indicators) && 
                    indicators.filter(ifx => ifx.interval == ifx.battleInterval)[0]?.openTime
                let indicatorOpenTimeHRF = indicators && Array.isArray(indicators) && 
                    indicators.filter(ifx => ifx.interval == ifx.battleInterval)[0]?.openTimeHRF
                let klineOpenTimeDiff = kline?.openTime !== indicatorOpenTime;
                return (klineOpenTimeDiff ? 'XXX...  ' : ``) + `${indicatorOpenTimeHRF ? indicatorOpenTimeHRF.substr(11, 5) : ''}` + '  ' + kline?.openTime + ' - ' + indicatorOpenTime;
            },
            renderCell: ({ value }) => {
                return (
                    <Typography title={value} variant="caption">{value}</Typography>
                )
            }
        },
        {
            field: 'action', headerName: 'action', minWidth: 120,
            renderCell: (params) => {
                // console.log('paramx', params);
                return (
                    <>
                    <Stack sx={{flexDirection: 'row',  overflowX: 'auto'}}>
                    <Typography
                        onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + params.row.symbol + 'PERP', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        tvw
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => window.open('https://www.binance.com/en/futures/' + params.row.symbol + '', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        bi
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => restartNode(params.row.symbol)}
                        title={'reset nodes'}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        rstNode
                    </Typography> 
                    </Stack>
                    </>
                );
            }
        },
        {
            field: 'indix', headerName: 'Indicators', width: 200,
            valueGetter: (params) => params.row,
            renderCell: (p) => {
                // console.log('p', p)
                let value = p.value;
                let resp = Array.isArray(value?.indicators) && value?.indicators.map(o => {
                    let r = {};
                    r[o.indicator] = o.indicatorValue
                    return r;
                });
                resp && resp.push({ lU: value?.kline?.dtupdated });
                let indicatorOpenTime = value.indicators && Array.isArray(value.indicators) && value.indicators[0]?.openTime
                let indicatorOpenTimeHRF = value.indicators && Array.isArray(value.indicators) && value.indicators[0]?.openTimeHRF
                let klineOpenTimeDiff = value.kline?.openTime !== indicatorOpenTime;
                return (
                    <Typography variant="caption" style={{overflow: 'auto'}}>{JSON.stringify(resp)}</Typography>
                )
            }
        },
        {
            field: 'rulesx', headerName: 'Rulesets', width: 200,
            valueGetter: (params) => params.row,
            renderCell: (p) => {
                // console.log('p', p)
                let value = p.value;
                let resp = Array.isArray(value?.rulesets) && value?.rulesets.map(o => {
                    let r = {};
                    r[o.sName] = o.direction
                    return r;
                });
                resp && resp.push({ lU: value?.kline?.dtupdated });
                let rsOpenTime = value.rulesets && Array.isArray(value.rulesets) && value.rulesets[0]?.openTime
                let rsTimeHRF = value.rulesets && Array.isArray(value.rulesets) && value.rulesets[0]?.openTimeHRF
                let klineOpenTimeDiff = value.kline?.openTime !== rsOpenTime;
                return (
                    <Typography title={JSON.stringify(resp)} style={{overflow: 'auto'}} variant="caption">{`${rsTimeHRF ? rsTimeHRF.substr(11, 5) : ''} ${rsTimeHRF ? ' | ' + JSON.stringify(resp) : ''}`}</Typography>
                )
            },
        },
    ] : [];

    const handleRowDoubleClick = v => {
        console.log('handleRowDoubleClick', v)
        toggleDrawer('right', true, v.row)(event);
    }
    const handleRowClick = async v => {
        console.log('handleRowClick', v)
        toggleDrawer('right', true, v.row)(event);
        // toggleDrawer('right', true, v.row)(event);
    };

    const [aiModalOpen, setAiModalOpen] = useState(false);
    const [aiPrompts, setAiPrompts] = useState([]);
    const [selectedPrompt, setSelectedPrompt] = useState(null);
    const [loadingPrompts, setLoadingPrompts] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editedPrompt, setEditedPrompt] = useState('');
    const [promptTitle, setPromptTitle] = useState('');


    const [aiDataSchemas, setAiDataSchemas] = useState([]);
    const [selectedDataSchema, setSelectedDataSchema] = useState(null);

    const [sliceLastNRows, setSliceLastNRows] = useState(20)
    const [includeIndicators, setIncludeIndicators] = useState(false);
    
    const [aiModels, setAiModels] = useState([]);
    const [selectedModel, setSelectedModel] = useState(null);
    const [localLLMLoading, setLocalLLMLoading] = useState(false);
    const [LLMLoading, setLLMLoading] = useState(false);

    const [aiData, setAiData] = useState('');

    const AIData = async () => {
        const tag = drawerData?.tag || 'adausdt_1m';
        const response3 = await fetch(`http://localhost:3012/api/pub/ai/pairklinedata/${tag}?json=1&limit=${sliceLastNRows}`);
        const data3 = await response3.json();
        if (data3.data) {
            // console.log('selected Schema', selectedDataSchema)
            if (selectedDataSchema) {
            let fields = selectedDataSchema?.sema;
            let dataStg = [];
            data3.data.klines.map(d => {
                let row = {};
                fields.map(f => {
                    row[f] = d[f];
                });
                dataStg.push(row);
            });
            setAiData(dataStg);
            } else {
                setAiData(data3.data.klines);
            }
        }

    }
    const fetchAIModels = async () => {
        try {
            const response = await fetch('http://localhost:3012/api/pub/ai/localllmlist');
            const data = await response.json();
            if (data.models && Array.isArray(data.models)) {
                setAiModels(data.models);
                // Set the first model as default if none is selected
                if (!selectedModel && data.models.length > 0) {
                    setSelectedModel(data.models[0]);
                }
            }
        } catch (e) {
            console.log('Error fetching AI models:', e);
            alert('Error fetching AI models');
        }
    };

    const AIModal = async ({ node_key_tag, pair, redisKey }) => {
        try {
            // Fetch prompts from the API
            setLLMLoading(false)
            setLoadingPrompts(true);
            setAiModalOpen(true); // Open modal immediately to show loading state
            setSelectedDataSchema(null);
            setSelectedPrompt(null);
            
            // Fetch AI models
            await fetchAIModels();
            const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
            const data = await response.json();
            
            if (data.prompts) {
                setAiPrompts(data.prompts);
            }

            const response2 = await fetch('http://localhost:3012/api/pub/ai/dataschemas');
            const data2 = await response2.json();
            if (data2.dataschemes && Array.isArray(data2.dataschemes)) {
                setAiDataSchemas(data2.dataschemes);
                data2.dataschemes.length > 0 && setSelectedDataSchema(data2.dataschemes[0]);
            }
            await AIData();

            setLoadingPrompts(false);
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setLoadingPrompts(false);
            setAiModalOpen(false); // Close modal on error
        }
    };

    const handleEditPrompt = () => {
        setIsEditing(true);
        setEditedPrompt(selectedPrompt.prompt);
        setPromptTitle(selectedPrompt.title);
    };

    const handleSavePrompt = async () => {
        try {
            // Show popup for title input
            const newTitle = prompt('Enter prompt title:', promptTitle);
            if (newTitle === null) return; // User cancelled
            
            const promptData = {
                promptID: selectedPrompt.promptID,
                title: newTitle,
                prompt: editedPrompt,
                isUpdate: selectedPrompt.title === newTitle // If same title, update existing, otherwise create new
            };
            
            const response = await fetch('/api/pub/ai/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(promptData),
            });
            
            if (response.ok) {
                // Refresh prompts
                const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
                const data = await response.json();
                
                if (data.prompts) {
                    setAiPrompts(data.prompts);
                }
                
                setIsEditing(false);
                alert('Prompt saved successfully!');
            } else {
                alert('Failed to save prompt');
            }
        } catch (e) {
            console.log('Error saving prompt:', e);
            alert('Error saving prompt');
        }
    };

    const handleDeletePrompt = async () => {
        if (!window.confirm('Are you sure you want to delete this prompt?')) {
            return;
        }
        
        try {
            const response = await fetch('/api/pub/ai/update', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ promptID: selectedPrompt.promptID }),
            });
            
            if (response.ok) {
                // Refresh prompts
                const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
                const data = await response.json();
                
                if (data.prompts) {
                    setAiPrompts(data.prompts);
                }
                
                setSelectedPrompt(null);
                alert('Prompt deleted successfully!');
            } else {
                alert('Failed to delete prompt');
            }
        } catch (e) {
            console.log('Error deleting prompt:', e);
            alert('Error deleting prompt');
        }
    };

    const handleCancelEdit = () => {
        setIsEditing(false);
        setEditedPrompt(selectedPrompt.prompt);
    };

    const reconn = async ({ node_key_tag, pair, redisKey }) => {
        try {
            // console.log('drawerData', drawerData)
            let uri = `/api/pub/data/reconnode/${redisKey}`;
            let res = await fetch(uri, {
                // mode: 'no-cors',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            });

            console.log('uri', uri);
            if (!res.ok) {
                var message = `An error has occured: ${pair} ${res.status} - ${res.statusText}`;
                alert(message);
                pair && restartNode(pair);
                return
            } else {
                console.log('res', res);
                toggleDrawer('right', false)(event)
                alert('reconn ok')
            }

            // alert('ok');
        } catch (e) {
            console.log('error', e)
            alert('error');
        }
    };

    const buildPrompt = () => {
        if (selectedPrompt && selectedDataSchema && aiData) {
            console.log('battleInitData?.parameters', drawerData)
            console.log('battleInitData?.parameters', drawerData.indicators, drawerData.delta24h);
            let prompt = selectedPrompt.prompt;
            let sembol = drawerData?.symbol;
            let interval = battleInitData?.parameters ? battleInitData?.parameters?.battleInterval.toString() : ' ';
            let data = aiData;
            let schema = selectedDataSchema.sema;
            let promptData = prompt
                .replace('<<referans veri >>', JSON.stringify(data))
                .replace('<<referans sembol>>', sembol)
                .replace('<<referans interval>>', interval)
                .replace('<<referans delta24h>>', drawerData.delta24h)
                .replace('<<referans sema>>', JSON.stringify(schema));
            if (includeIndicators) {
                let addIdxText = '';

                let tar = [];
                Array.isArray(drawerData.indicators) && drawerData.indicators.map(a => {
                    tar.push({
                        openTime: new Date(new Date(a.openTime) - (new Date().getTimezoneOffset() * 60000)).toISOString(),
                        indicator: a.indicator,
                        values: a.indicatorAdditionalData,
                    })
                })

                promptData = promptData.replace('<<referans indikatorler>>', "\n##### a. ***Referans Indikator Verileri**\n" + JSON.stringify(tar));
            } else {
                promptData = promptData.replace('<<referans indikatorler>>', '');
            }
            return promptData;
        }
    }

    const getLocalLLMResponse = async () => {
        try {
            setLocalLLMLoading(true);
            const promptData = buildPrompt();
            const response = await fetch('/api/pub/ai/localllmquery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: promptData, model: selectedModel }),
            });
            const data = await response.json();
            setLocalLLMLoading(false);
            if (data.response) {
                alert(data.response?.aiResponse);
            } else {
                alert('No response');
            }
        } catch (e) {
            console.log('Error getting response:', e);
            setLocalLLMLoading(false);
            alert('Error getting response');
        }
    }

    const getGeminiResponse = async () => {
        try {
            setLLMLoading(true);
            const promptData = buildPrompt();
            const response = await fetch('/api/pub/ai/llmquery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: promptData, model: selectedModel }),
            });
            const data = await response.json();
            setLLMLoading(false);
            if (data.response) {
                alert(data.response?.aiResponse);
            } else {
                alert('No response');
            }
        } catch (e) {
            console.log('Error getting response:', e);
            setLLMLoading(false);
            alert('Error getting response');
        }
    }
    


    const CopyPrompt2Clipboard = () => {
        navigator.clipboard.writeText(fPrompt);
        alert('Prompt copied to clipboard');
    }

    const [fPrompt, setFPrompt] = useState('');
    useEffect(() => {
        // console.log('AIData', aiData);
        // console.log('selectedDataSchema', selectedDataSchema);
        // console.log('selectedPrompt', selectedPrompt);
        let pstr = buildPrompt();
        setFPrompt(pstr);
        // console.log('buildPrompt', pstr);
        // console.log('drawerData?.symbol', drawerData?.symbol);
        // console.log('interval', battleInitData?.parameters ? battleInitData?.parameters?.battleInterval : ' ')
    }, [aiData, selectedDataSchema, selectedPrompt, includeIndicators ]);

    return (
        <>

            <Box sx={{ my: 1 }} >
                <Button size="small"
                    // variant="outlined"
                    style={{ backgroundColor: 'yellow', borderWidth: 1 }}
                    onClick={refreshData}>Refresh</Button> &nbsp;
                <Typography variant="caption" color={'GrayText'}>
                    {dataTime}&nbsp;&nbsp;
                    <Chip label={battleInitData?.parameters ? battleInitData?.parameters?.battleInterval : ' '} />&nbsp;&nbsp;
                    O / C
                </Typography>
            </Box>
            {/* <br /><br /> */}


            {watchData && <StripedDataGrid
                rows={watchData}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}

                slots={{
                    toolbar: GridToolbar,
                    // loadingOverlay: LinearProgress,
                }}
                slotProps={{
                    toolbar: {
                        showQuickFilter: true,
                        printOptions: { disableToolbarButton: true },
                        csvOptions: {
                            fileName: 'battle',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                    },
                }}

                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }

                // onRowDoubleClick={(row, event) => {
                //     handleRowDoubleClick(row.row);
                // }}
                onRowDoubleClick={handleRowClick}
                // onRowClick={handleRowClick}
                sx={{
                    // m: 2,
                    boxShadow: 2,
                }} />
            }
            <Drawer
                anchor={'right'}
                open={state['right']}
                onClose={toggleDrawer('right', false)}
            >

                <Box sx={{
                    minHeight: 300,
                    minWidth: 300,
                    // marginLeft: 300,
                    // backgroundColor: '#ffccaa', 
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Card sx={{ minWidth: 255, m: 2 }}>
                        <CardContent>
                            <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
                                Symbol
                            </Typography>
                            <Typography variant="h4" component="div">
                                {drawerData?.symbol}&nbsp;{bull}{bull}&nbsp;<Chip variant="filled" color={parseFloat(drawerData?.klineClose) < 0 ? "error" : 'primary'} label={parseFloat(drawerData?.klineClose) + ''} />
                            </Typography>
                            <Typography sx={{ mb: 1.5 }} color="text.secondary">
                                adjective
                            </Typography>
                            <Typography variant="body2">
                                well meaning and kindly.!
                                <br />
                                {'"a benevolent smile"'}
                            </Typography>
                        </CardContent>
                        <CardActions>
                            <Link href={`/main/market/${drawerData?.symbol}`}>
                                <Button size="small"
                                    variant="outlined"
                                    onClick={toggleDrawer('right', false)}>Details</Button>
                            </Link>
                            &nbsp;&nbsp;
                            <Link href={`${drawerDataURL}`} target="_blank">
                                <Button size="small"
                                    variant="outlined"
                                    onClick={toggleDrawer('right', false)}>Tradingview</Button>
                            </Link>
                            &nbsp;&nbsp;
                            <Button size="small"
                                variant="outlined"
                                onClick={refreshData}>Refresh Table</Button>


                        </CardActions>
                        <CardActions>
                            <Button size="small"
                                variant="outlined"
                                onClick={refreshData}>Refresh Table</Button>
                            <Button size="small"
                                variant="outlined" onClick={() => reconn({ redisKey: drawerData?.symbol.toLowerCase(), node_key_tag: drawerData?.symbol + '_' + battleInitData?.parameters?.battleInterval, pair: drawerData?.symbol })}>
                                Reconn
                            </Button>
                            <Button size="small"
                                variant="outlined" onClick={() => AIModal({ redisKey: drawerData?.symbol.toLowerCase(), node_key_tag: drawerData?.symbol + '_' + battleInitData?.parameters?.battleInterval, pair: drawerData?.symbol })}>
                                AI Prompt
                            </Button>
                        </CardActions>

                    </Card>

                    <Box sx={{
                        width: '100%',
                        minWidth: 300, overflow: 'auto', alignItems: 'center', justifyContent: 'center'
                    }}>
                        {drawerData && <Indicators {...props} drawerData={drawerData} />}
                    </Box>
                </Box>
            </Drawer>
            <Dialog 
                open={aiModalOpen} 
                onClose={() => setAiModalOpen(false)} 
                sx={{ margin: 'auto' }}
                PaperProps={{
                    sx: {
                        width: 'unset',
                        maxWidth: '1200px',
                        minWidth: '1200px',
                        margin: '16px'
                    }
                }}
            >
                <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                            <Typography variant="h6">AI Prompt</Typography>
                            <Typography variant="caption" color={'GrayText'} sx={{ ml: 2 }}>
                                {drawerData?.symbol}
                            </Typography>
                            <Button onClick={() => window.open(`https://www.tradingview.com/chart?symbol=BINANCE%3A${drawerData?.symbol}PERP`, "_blank")} sx={{ ml: 2 }}>tradingview</Button>
                            <Button onClick={() => window.open('https://chat.qwen.ai/', "_blank")} sx={{ ml: 2 }}>Qwen</Button>
                            <Button onClick={() => window.open('https://aistudio.google.com/prompts/new_chat', "_blank")} sx={{ ml: 2 }}>Google AI Studio</Button>
                            <Button onClick={() => window.open('https://chatgpt.com/', "_blank")} sx={{ ml: 2 }}>Chat GPT</Button>
                            

                        </Box>
                        <IconButton onClick={() => setAiModalOpen(false)}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    {loadingPrompts ? (
                        <Box sx={{ 
                            display: 'flex', 
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            minHeight: '200px',
                            gap: 2
                        }}>
                            <Box sx={{ 
                                position: 'relative',
                                display: 'inline-flex'
                            }}>
                                <CircularProgress size={60} thickness={4} />
                                <Box
                                    sx={{
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        position: 'absolute',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <Typography
                                        variant="caption"
                                        component="div"
                                        color="text.secondary"
                                    >
                                        AI
                                    </Typography>
                                </Box>
                            </Box>
                            <Typography variant="h6" color="text.secondary">
                                Loading AI Prompts
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                Please wait while we fetch the latest data...
                            </Typography>
                            <LinearProgress 
                                sx={{ 
                                    width: '100%',
                                    borderRadius: 10
                                }} 
                            />
                        </Box>
                    ) : (
                        <>
                            <Autocomplete
                                options={aiPrompts}
                                getOptionLabel={(option) => option.title}
                                onChange={(event, newValue) => {
                                    setSelectedPrompt(newValue);
                                }}
                                renderInput={(params) => (
                                    <TextField 
                                        {...params} 
                                        label="Select a prompt" 
                                        variant="outlined" 
                                        fullWidth 
                                    />
                                )}
                                sx={{ width: '100%', marginBottom: '20px' }}
                            />
                                {selectedPrompt && (
                                    <>

                                        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Box sx={{ width: '40%', display: 'flex', flexDirection: 'row', }}>
                                                <Autocomplete
                                                    options={aiDataSchemas}
                                                    getOptionLabel={(option) => option.title}
                                                    value={selectedDataSchema}
                                                    onChange={async (event, newValue) => {
                                                        setSelectedDataSchema(newValue);
                                                        // Refresh AI data when schema changes
                                                        await AIData();
                                                    }}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            label="Select a data schema"
                                                            variant="outlined"
                                                            fullWidth
                                                        />
                                                    )}
                                                    sx={{ width: '50%' }}
                                                />
                                                <TextField
                                                    sx={{ width: '100px', ml: 1 }}
                                                    label="Rows"
                                                    variant="outlined"
                                                    fullWidth
                                                    value={sliceLastNRows}
                                                    onChange={(e) => setSliceLastNRows(e.target.value)}
                                                    onBlur={(e) => {
                                                        e.preventDefault();
                                                        AIData();
                                                    }}
                                                    onKeyDown={(e) => {
                                                        if (e.key === 'Enter') {
                                                            e.preventDefault();
                                                            AIData();
                                                        }
                                                    }}
                                                />
                                                {/* --add a checkbox titled "include indicators";
                                                 */}

                                                <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                                                    <Checkbox
                                                        checked={includeIndicators}
                                                        onChange={(e) => setIncludeIndicators(e.target.checked)}
                                                        id="include-indicators-checkbox"
                                                    />
                                                    <label 
                                                        htmlFor="include-indicators-checkbox"
                                                        style={{ cursor: 'pointer', userSelect: 'none', marginLeft: '8px', fontSize: '12px' }}
                                                    >
                                                        Include Indicators
                                                    </label>
                                                </Box>

                                            </Box>
                                            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'center' }}>
                                                
                                                <Autocomplete
                                                    options={aiModels}
                                                    getOptionLabel={(option) => option.name}
                                                    value={selectedModel}
                                                    onChange={(event, newValue) => {
                                                        setSelectedModel(newValue);
                                                    }}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            label="Select a model"
                                                            variant="outlined"
                                                            fullWidth
                                                        />
                                                    )}
                                                    sx={{ width: 200 }}
                                                />
                                                <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', ml: 1, gap: 1 }}>

                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={getLocalLLMResponse}
                                                        disabled={localLLMLoading}
                                                        sx={{ mr: 1, minWidth: '120px' }}
                                                    >
                                                        {localLLMLoading ? (
                                                            <CircularProgress size={20} />
                                                        ) : (
                                                            'Local LLM'
                                                        )}
                                                    </Button>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={getGeminiResponse}
                                                        disabled={LLMLoading}
                                                        sx={{ mr: 1, minWidth: '120px' }}
                                                    >
                                                        {LLMLoading ? (
                                                            <CircularProgress size={20} />
                                                        ) : (
                                                            'Cloud AI'
                                                        )}
                                                        
                                                    </Button>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={CopyPrompt2Clipboard}
                                                        sx={{ mr: 1 }}
                                                    >
                                                        Clipboard
                                                    </Button>
                                                </Box>
                                            </Box>
                                            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end' }}>
                                                {isEditing ? (
                                                    <>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={handleSavePrompt}
                                                            sx={{ mr: 1 }}
                                                        >
                                                            Save
                                                        </Button>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={handleCancelEdit}
                                                        >
                                                            Cancel
                                                        </Button>
                                                    </>
                                                ) : (
                                                    <>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={handleEditPrompt}
                                                            sx={{ mr: 1 }}
                                                        >
                                                            Edit
                                                        </Button>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            color="error"
                                                            onClick={handleDeletePrompt}
                                                        >
                                                            Delete
                                                        </Button>
                                                    </>
                                                )}
                                            </Box>
                                        </Box>
                                        <Box sx={{ mt: 2, p: 2, border: '1px solid #ccc', borderRadius: '4px', backgroundColor: '#f9f9f9', position: 'relative' }}>
                                            {isEditing ? (
                                                <TextField
                                                    fullWidth
                                                    multiline
                                                    rows={10}
                                                    value={editedPrompt}
                                                    onChange={(e) => setEditedPrompt(e.target.value)}
                                                    variant="outlined"
                                                />
                                            ) : (
                                                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                                                    {fPrompt}
                                                    {/* {selectedPrompt.prompt}
                                                    <code>
                                                        {JSON.stringify(aiData, null, 4)}
                                                    </code> */}
                                                </Typography>
                                            )}
                                        </Box>
                                    </>
                                )}
                        </>
                    )}
                </Box>
            </Dialog>

        </>
    )
};

function convertToValidJSON(text) {
    // Adım 1: Metni temizle ve düzelt
    let cleaned = text.trim();

    // Adım 2: Tek tırnakları (tekil) çift tırnağa çevir, ama dikkatli ol (örneğin, python kodu içindekiler dahil)
    // Basit yaklaşım: Tüm ' karakterlerini " yap, ama bu riskli olabilir.
    // Daha güvenli: Sadece JSON anahtarlarında ' kullanılmışsa onları " yap.
    // Ama metin aslında bir JSON değil, bir string içeriği, bu yüzden TAMAMI bir string haline getirilecek.

    // Burada asıl amaç: bu metni bir JSON string değeri yapmak.
    // Yani sonuç: { "prompt": "..." } gibi bir şey olacak.

    // Adım 3: İçerideki kaçış karakterlerini ve tırnakları handle et
    cleaned = cleaned.replace(/\\/g, '\\\\'); // varsa ters slaşları escape et
    cleaned = cleaned.replace(/"/g, '\\"');   // çift tırnakları escape et
    cleaned = cleaned.replace(/\n/g, '\\n');  // yeni satırları \n yap
    cleaned = cleaned.replace(/\r/g, '');     // satır dönüşlerini temizle

    // Adım 4: Tam bir JSON nesnesi oluştur
    const jsonObject = {
        userID: null,
        prompt: cleaned
    };

    return JSON.stringify(jsonObject, null, 2);
}

function nFormatter(num, digits) {
    const lookup = [
        { value: 1, symbol: "" },
        { value: 1e3, symbol: "k" },
        { value: 1e6, symbol: "M" },
        { value: 1e9, symbol: "B" },
        { value: 1e12, symbol: "T" },
        { value: 1e15, symbol: "P" },
        { value: 1e18, symbol: "E" }
    ];
    const regexp = /\.0+$|(?<=\.[0-9]*[1-9])0+$/;
    const item = lookup.findLast(item => num >= item.value);
    return item ? (num / item.value).toFixed(digits).replace(regexp, "").concat(" ", item.symbol) : "0";
    // return item ? Intl.NumberFormat("en-US", { minimumFractionDigits: 3 }).format(num / item.value).toString().replace(regexp, "").concat(item.symbol) : "0";

}
const Indicators = props => {
    const [data, setData] = useState(false);
    useEffect(() => {
        props.drawerData?.indicators && setData[props.drawerData?.indicators];
        try {
            let indicators = props.drawerData?.indicators;
            Array.isArray(indicators) && indicators.map (ix => {
                let iAdd = ix && JSON.parse(ix.indicatorAdditionalData);
                ix.indicatorAdditionalData = iAdd ? iAdd : ix.indicatorAdditionalData;
                let iParams = ix && JSON.parse(ix.indicatorParams);
                ix.indicatorParams = iParams;
            })
            props.drawerData?.indicators && console.log('drawerData', indicators)
        } catch (e) {
            console.log('indicators e', e, props.drawerData?.indicators)
        }
    }, [props.drawerData]);

    const [expanded, setExpanded] = useState(false);

    return (
        <>
            {props.drawerData?.indicators && <Card sx={{
                width: '%100',
                minWidth: 300, mx: 2
            }}>
                <CardContent>

                    <ListItemButton onClick={() => setExpanded(!expanded)}>
                        <ListItemIcon>
                            <CandlestickChartIcon />
                        </ListItemIcon>
                        <ListItemText primary="Indicators" />
                        {!expanded ? <ExpandLess /> : <ExpandMore />}
                    </ListItemButton>

                    {/* <Typography onClick={() => setExpanded(!expanded)} sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
                        Indicators {JSON.stringify(expanded)}
                    </Typography> */}
                    <Collapse in={!expanded} timeout={50} unmountOnExit >
                        {Array.isArray(props.drawerData?.indicators) && props.drawerData?.indicators.map((d, idx) => {
                            // console.log('d', d, data)
                            return (
                                <Card key={idx.toString()} sx={{ my: 1, px: 1 }}>
                                    <Typography variant="caption" sx={{ fontWeight: 'bold' }} component="div" title={JSON.stringify(d.indicatorParams)}>
                                        {d.indicator}&nbsp;{bull}{bull}&nbsp;{d.interval}&nbsp;{bull}{bull}&nbsp;{d.dtcreated}
                                    </Typography>

                                    <Box sx={{ maxWidth: 300, overflow: 'auto', fontSize: '10px' }}>
                                        <Typography variant="caption">
                                            {JSON.stringify(d.indicatorValue)}
                                        </Typography>
                                    </Box>
                                    <hr />
                                    <Box sx={{ width: '100%', maxWidth: 300, maxHeight: 100, overflow: 'auto' }}>
                                        <Typography variant="caption">
                                            {JSON.stringify(d.indicatorAdditionalData)}
                                        </Typography>
                                    </Box>
                                </Card>
                            )
                        })}
                    </Collapse>
                </CardContent>
            </Card>}
        </>
    )
}
const Center = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
});
const Element = styled('div')(({ theme }) => ({
    border: `1px solid ${(theme.vars || theme).palette.divider}`,
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
    height: 26,
    borderRadius: 2,
}));

const Value = styled('div')({
    position: 'absolute',
    lineHeight: '24px',
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
});
const Bar = styled('div')({
    height: '100%',
    '&.low': {
        backgroundColor: '#f44336',
    },
    '&.medium': {
        backgroundColor: '#efbb5aa3',
    },
    '&.high': {
        backgroundColor: '#088208a3',
    },
});
const ProgressBar = React.memo(function ProgressBar(props) {
    const { value } = props;
    const valueInPercent = value * 100;

    return (
        <Element>
            <Value>{`${valueInPercent.toFixed(0)}`}</Value>
            <Bar
                className={clsx({
                    low: valueInPercent < 30,
                    medium: valueInPercent >= 30 && valueInPercent <= 70,
                    high: valueInPercent > 70,
                })}
                style={{ maxWidth: `${valueInPercent}%` }}
            />
        </Element>
    );
});

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));
