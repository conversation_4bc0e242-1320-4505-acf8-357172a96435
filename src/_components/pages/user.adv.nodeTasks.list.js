
import React, { useState, useEffect, useContext } from "react";
import { <PERSON>, Button, Container, Stack, Typography } from '@mui/material';

import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import CircularProgress from '@mui/material/CircularProgress';
import Drawer from '@mui/material/Drawer';
import Snackbar from '@mui/material/Snackbar';
import { LoadingButton } from '@mui/lab';

import moment from "moment";
import { convertUTCDateToLocalDate } from "../../lib/fnx/fnx.fe";

import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import { <PERSON><PERSON><PERSON><PERSON>, BiWindowClose } from "react-icons/bi";

import { alpha, styled } from '@mui/material/styles';
import Slide from '@mui/material/Slide';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';

const ODD_OPACITY = 0.2;

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

// import cronstrue from 'cronstrue';
const ListNodeTasks = props => {
    const { token } = props;
    const btnRef = React.useRef()
    const modalRef = React.useRef(null)
    const [data, setData] = useState(false)
    const [dataGrid, setdataGrid] = useState(false)
    const [loading, setloading] = useState(false)
    const [loadingUpdate, setloadingUpdate] = useState(false)
    const [errorMsg, setErrorMsg] = useState(false)
    const [loadingKillAll, setloadingKillAll] = useState(false)
    const [lastU, setlastU] = useState(new Date(Date.now()))

    const fetchNodes = async checkStatus => {
        setloading(true)
        let res
        try {
            let uri = '/api/pub/data/nodetasklist/';
            uri += checkStatus ? '?checkstate=1' : '';
            res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            const datax = await res.json()
            setlastU(new Date(Date.now()));
            // console.log(datax);
            setData(datax.data)
            let dGrid = [];
            Array.isArray(datax.data) && datax.data.map((d, ix) => {
                if (Array.isArray(d.processMon)) {
                    d.processMon.map(sd => {
                        d['stat_' + (sd.statType && sd.statType == 'market' ? 'kline' : sd.statType == 'marketticker' ? 'klineticker' : sd.statType)] = sd.stat.dtupdatedEn
                    });
                }
                dGrid.push(d);
            });
            console.log('dGrid', dGrid)
            setdataGrid(dGrid);
            setloading(false)
        } catch (e) {
            console.log('fetch error', e)
            alert('Hata', e)
            setloading(false)
        }
    }
    const fetchTask = async gtaskid => {
        setloading(true)
        let res;
        return new Promise(async (resolve, reject) => {
            try {
                let uri = '/api/pub/data/nodetask/';
                uri += gtaskid ? gtaskid : '';
                res = await fetch(uri, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                })
                const datax = await res.json()
                setloading(false)
                resolve(datax)
            } catch (e) {
                setloading(false)
                reject(e)
            }
        });
    }

    const fetchNodesList = async () => {
        let res;
        return new Promise(async (resolve, reject) => {
            try {
                let uri = '/api/pub/data/nodelist/';
                res = await fetch(uri, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                })
                const datax = await res.json()
                resolve(datax)
            } catch (e) {
                reject(e)
            }
        });
    }


    const refresh = (ac = true) => {
        setloadingUpdate(false)
        fetchNodes(ac);
    }
    const [drawerData, setdrawerData] = useState(false)
    const [drawerDataT, setdrawerDataT] = useState(false)
    const [state, setState] = React.useState({
        top: false,
        left: false,
        bottom: false,
        right: false,
    });

    const restartNode = async redisKey => {
        let uri = '/api/pub/data/battlenoderestart'
        let data2Post = {
            redisKey: redisKey,
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json()

            if (!datax.error) {
                alert('restarted node: ' + redisKey)
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }

    const toggleDrawer = (anchor, open, node_key) => async (event) => {
        if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
            return;
        }
        if (open) {
            let nData = [...data].filter(d => d.taskID === node_key);
            let taskData = await fetchTask(node_key)
            let dpost = Array.isArray(nData) ? nData[0] : {};
            let logData = []
            setdrawerDataT(taskData.data?.logs);
            setdrawerData(taskData.data);
        } else {
            setdrawerDataT(false)
            setdrawerData(false)
        }
        setState({ ...state, [anchor]: open });
    };
    const killNode = (node_id) => {
        alert('kill node task: ' + node_id)
    }
    const killAllNodes = () => {
        alert('kill all node tasks')
    }

    useEffect(() => {
        fetchNodes(false);
    }, []);

    useEffect(() => {
        const interval = setInterval(() => {
            fetchNodes(false);
        }, 30000); //set your time here. repeat every 5 seconds
        return () => clearInterval(interval);
    }, []);

    const handleRowClick = async v => {
        console.log('handleRowClick', v)
        toggleDrawer('bottom', true, v.row)(event);
        // toggleDrawer('right', true, v.row)(event);
    };

    const columns = dataGrid ? [
        { field: 'id', headerName: 'id', width: 90,  
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val}</span>;
            }
        },
        // { field: 'redisKey', headerName: 'Node', width: 120, },
        { field: 'taskDesc', headerName: 'Task', width: 90,  
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val}</span>;
            }
        },
        { field: 'pair', headerName: 'Pair', width: 90,  
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val}</span>;
            }},
        { field: 'nodeInterval', headerName: 'TimeFram', width: 90,  
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val}</span>;
            }},
        { field: 'batch', headerName: 'Batch', width: 60,  
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val}</span>;
            }},

        { field: 'period', headerName: 'Period', width: 80, 
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val}</span>;
            }
        },
        { field: 'nextRun', headerName: 'nextRun', width: 60, 
            valueGetter: (params) => params.row?.nextRun?.sec,
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.row?.nextRun.dt}>{val}s</span>;
            }
        },
        { field: 'is_active', headerName: 'Active', width: 60, 
            renderCell: (params) => {
                let val = (params.value); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span style={{ fontSize: 11,}} title={params.value}>{val ? 'yes' : '-'}</span>;
            }
        },
        { field: 'dtcreatedEn', headerName: 'Created', width: 80, 
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = (params.value) && (params.value).substring(11, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{ fontSize: 10, backgroundColor: null }}>{tim} {delta && delta < -5000 && (delta / 1000).toFixed(0)}</span>;
            } 

        },
        { field: 'stat_klineinit', headerName: 'klineInt', width: 80, 
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = (params.value) && (params.value).substring(11, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{ fontSize: 11, backgroundColor: null }}>{tim} {delta && delta < -5000 && (delta / 1000).toFixed(0)}</span>;
            } },
        { field: 'stat_kline', headerName: 'kline', width: 80, 
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = (params.value) && (params.value).substring(11, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{ fontSize: 11, backgroundColor: delta < -5000  ? 'yellow' : null }}>{tim} {delta && delta < -5000 && (delta / 1000).toFixed(0)}</span>;
            } },
        {
            field: 'stat_klineticker', headerName: 'ticker', width: 80,
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = (params.value) && (params.value).substring(11, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{ fontSize: 11, backgroundColor: delta < -5000 ? 'yellow' : null }}>{tim} {delta && delta < -5000 && (delta / 1000).toFixed(0)}</span>;
            }
        },
        {
            field: 'stat_indicators', headerName: 'indicators', width: 80,
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = (params.value) && (params.value).substring(11, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{ fontSize: 11, backgroundColor: delta < -5000 ? 'yellow' : null }}>{tim} {delta && delta < -5000 && (delta / 1000).toFixed(0)}</span>;
            }
        },
        {
            field: '', headerName: 'action', minWidth: 120,
            renderCell: (params) => {
                // console.log('paramx', params);
                return (
                    <>
                        {params.row.taskDesc === 'klineLoader' && (
                            <Stack sx={{ flexDirection: 'row', overflowX: 'auto' }}>
                                <Typography
                                    onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + params.row.pair.toUpperCase() + 'PERP', "_blank")}
                                    size={'xs'}
                                    style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                                    sx={{ fontSize: 9, p: 0, px: 1 }}
                                >
                                    tvw
                                </Typography>
                                &nbsp;
                                &nbsp;
                                <Typography
                                    onClick={() => window.open('https://www.binance.com/en/futures/' + params.row.pair.toUpperCase() + '', "_blank")}
                                    size={'xs'}
                                    style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                                    sx={{ fontSize: 9, p: 0, px: 1 }}
                                >
                                    bi
                                </Typography>
                                &nbsp;
                                &nbsp;
                                <Typography
                                    onClick={() => restartNode(params.row.redisKey)}
                                    size={'xs'}
                                    style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                                    sx={{ fontSize: 9, p: 0, px: 1 }}
                                >
                                    rstNode
                                </Typography>
                            </Stack>
                        )}
                    </>
                );
            }
        },
    ] : [];

    const stopconns = async ({ nodeKey }) => {
        try {
            let nodesListStg = await fetchNodesList();
            let nodesList = nodesListStg ? nodesListStg.data : []
            let nData = [...nodesList].filter(d => d.node_key === nodeKey);
            let uri = `http://localhost:${nData[0].port}/stopconns`;
            console.log('uri', uri);
            let res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            });
            const datax = await res.json();
            alert('ok');
        } catch (e) {
            console.log('error', e)
            alert('error');
        }
    };

    const reconn = async ({ battleID, nodeKey, taskID }) => {
        try {
            let nodesListStg = await fetchNodesList();
            let nodesList = nodesListStg ? nodesListStg.data : []
            let nData = [...nodesList].filter(d => d.node_key === nodeKey);
            // console.log('nodekey', nodeKey, nodesList, nData[0]);
            let uri = `http://localhost:${nData[0].port}/socketreconn`;
            console.log('uri', uri);
            let res = await fetch(uri, {
                // mode: 'no-cors',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            });
            // console.log('res', res);
            const datax = await res.json();
            alert('ok');
        } catch (e) {
            console.log('error', e)
            alert('error');
        }
    };
    return (
        <>
            <Box
                sx={{
                    my: 1,
                    mx: 4,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
                    List of Node Tasks
                </Typography>
                <div>
                    {/* <LoadingButton loading={loadingKillAll} onClick={killAllNodes} size={'xs'} 
                    
                    color="warning"
                    variant="outlined"
                    className='mx-4 outline-red-600'>
                    <BiKnife size={22} />&nbsp;Kill All
                    </LoadingButton>&nbsp; */}
                    <Button ref={btnRef} onClick={refresh} size={'xs'} className='ml-4 outline-red-600'>
                        Refresh
                    </Button>&nbsp;
                    {/* <Button ref={btnRef}  onClick={() => refresh(false)} size={'xs'} className='outline-red-600'>
                        R
                    </Button> */}

                </div>
            </Box>


            <Box
                sx={{
                    mx: 4,
                }}
            >
                {dataGrid && <StripedDataGrid
                    rows={dataGrid}
                    columns={columns}
                    rowHeight={25}
                    loading={loading}
                    disableColumnMenu={true}

                    slots={{
                        toolbar: GridToolbar,
                        // loadingOverlay: LinearProgress,
                    }}
                    slotProps={{
                        toolbar: {
                            showQuickFilter: true,
                            printOptions: { disableToolbarButton: true },
                            csvOptions: {
                                fileName: 'battle',
                                delimiter: ';',
                                utf8WithBom: true,
                            }
                        }
                    }}
                    initialState={{
                        sorting: {
                            sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                        },
                    }}

                    getRowClassName={(params) =>
                        params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                    }

                    // onRowDoubleClick={(row, event) => {
                    //     handleRowDoubleClick(row.row);
                    // }}
                    onRowDoubleClick={handleRowClick}
                    // onRowClick={handleRowClick}
                    sx={{
                        // m: 2,
                        boxShadow: 2,
                    }} />
                }
            </Box>

            <Drawer
                anchor={'bottom'}
                open={state['bottom']}
                onClose={toggleDrawer('bottom', false)}
            >
                <Box sx={{
                    minHeight: 300, flex: 1,
                    // marginLeft: 300,
                    // backgroundColor: '#ffccaa', 
                    paddingLeft: 35, paddingRight: 1,
                }}>
                    <Stack sx={{ borderColor: '#ccc', p: 3 }}>
                        <Card variant="outlined" sx={{ borderWidth: 0 }}>
                            <Box sx={{ p: 2 }}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                    <Stack sx={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>

                                        <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                            <Typography gutterBottom variant="h5" component="div">
                                                {drawerData?.taskTag}
                                            </Typography>
                                            <Chip color="primary" label="Running" size="small" sx={{ mx: 2 }} />
                                        </Stack>
                                        <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                            <Typography color="#ccc" variant="body2">
                                                node:{drawerData.nodeKey} task:{drawerData.taskID}
                                            </Typography>&nbsp;<Typography color="#ccc" variant="body2">
                                                c:{drawerData.dtcreated}
                                            </Typography>&nbsp;
                                            <Typography color="#ccc" variant="body2">
                                                u:{drawerData.dtupdated}
                                            </Typography>
                                        </Stack>

                                    </Stack>

                                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                        <Button
                                            loading={loadingKillAll}
                                            onClick={toggleDrawer('bottom', false)}
                                        >
                                            <BiWindowClose size={28} />
                                        </Button>
                                    </Stack>

                                </Stack>
                                <Box>

                                    <TableContainer component={Paper} sx={{ maxHeight: 300, overflow: 'auto' }}>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>#</TableCell>
                                                <TableCell>status</TableCell>
                                                <TableCell>status desc</TableCell>
                                                <TableCell>task timer</TableCell>
                                                <TableCell>dt created</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {drawerDataT && Array.isArray(drawerDataT) && drawerDataT.map((dc, i) => {
                                                // let LastLogData = Array.isArray(LogData) && LogData.find(l => l.script === d.key);
                                                let textClass = 'text-xs ' + (!dc.is_active ? 'p-2' : 'bg-yellow-300 p-2');
                                                return (
                                                    <TableRow key={i.toString()}>
                                                        <TableCell>{i + 1}</TableCell>
                                                        <TableCell title={dc.nodeKey} sx={{ maxWidth: 400, overflow: 'auto' }}>
                                                            <span className='text-xs' >{dc.status_code}</span>
                                                        </TableCell>
                                                        <TableCell >
                                                            <span className='text-xs' >{dc.status_desc}</span></TableCell>
                                                        <TableCell >
                                                            <span className='text-xs' >{dc.task_timer}</span></TableCell>
                                                        <TableCell >
                                                            <span className='text-xs' >{dc.dtcreated}</span></TableCell>
                                                    </TableRow>
                                                )
                                            })}
                                        </TableBody>

                                    </TableContainer>
                                    {/* drawerDataT */}
                                </Box>
                            </Box>
                            <Divider />

                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                                <Stack direction="row" spacing={1}>
                                    <Box sx={{ p: 2 }}>
                                        <Typography gutterBottom variant="body2">
                                            Battle ID
                                        </Typography>
                                        <Stack direction="row" spacing={1}>
                                            <Chip label={drawerData.battleID} size="small" />
                                        </Stack>
                                    </Box>

                                    <Box sx={{ p: 2 }}>
                                        <Typography gutterBottom variant="body2">
                                            last websocket
                                        </Typography>
                                        <Chip color="primary" label={drawerData.node_type} size="small" />&nbsp;
                                        <Chip label={drawerData.dtLastWebSocketConn} size="small" />

                                        <Button ref={btnRef}
                                            size="small"
                                            sx={{ padding: 0, margin: 0, marginLeft: 1 }}
                                            onClick={() => reconn({
                                                battleID: drawerData.battleID,
                                                nodeKey: drawerData.nodeKey,
                                                taskID: drawerData.taskID,
                                            })}>
                                            reconn
                                        </Button>
                                        <Button ref={btnRef}
                                            size="small"
                                            sx={{ padding: 0, margin: 0, marginLeft: 1 }}
                                            onClick={() => stopconns({
                                                battleID: drawerData.battleID,
                                                nodeKey: drawerData.nodeKey,
                                                taskID: drawerData.taskID,
                                            })}>
                                            stop
                                        </Button>

                                    </Box>
                                    {/*                                 
                                <Box sx={{ p: 2 }}>
                                    <Typography gutterBottom variant="body2">
                                        Node Key
                                    </Typography>
                                    <Stack direction="row" spacing={1}>
                                        <Chip color="primary" label={drawerData.node_type} size="small" />
                                        <Chip label={drawerData.nodeKey} size="small" />
                                    </Stack>
                                </Box> */}
                                    <Box sx={{ p: 2 }}>
                                        <Typography gutterBottom variant="body2">
                                            Period
                                        </Typography>
                                        <Stack direction="row" spacing={1}>
                                            <Chip label={'' + drawerData.period} size="small" />
                                            <Chip label={'' + drawerData.periodDesc} size="small" />
                                        </Stack>
                                    </Box>

                                </Stack>
                                <Box sx={{ m: 2 }}>
                                    <Typography gutterBottom variant="h6" component="div">
                                        &nbsp;
                                    </Typography>
                                </Box>
                            </Stack>
                        </Card>

                    </Stack>
                </Box>
            </Drawer>
            {!loading && errorMsg}

            <Box
                sx={{
                    mx: 4,
                }}
            >
                <Typography color="#ccc" variant="body2" sx={{ mb: 2 }}>
                    last refresh: {lastU.toISOString()} - {moment(lastU).fromNow()}
                </Typography>
            </Box>
        </>
    )
}
export default ListNodeTasks;



const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));
