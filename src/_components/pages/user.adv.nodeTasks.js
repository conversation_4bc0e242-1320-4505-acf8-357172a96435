/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, Container, Stack } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import ListNodeTasks from './user.adv.nodeTasks.list'

export default function Home(props) {
  const { ...rest } = props;
  const router = useRouter();
  const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
  const { slug } = router.query
  const [header, setHeader] = useState(false)
  const { status, data: session } = useSession({
      required: true,
      onUnauthenticated() {
        signIn();
          // router.push('/auth/SignInSide');
      },
  })
  useEffect( () => {
    colorMode.setCurrPage('/advanced/nodetasks');
    drawerCollapsed && colorMode.collapseDrawer(true);
  }, [])

  if (status === "loading") {
      return "Loading..."
  }
  const { user } = session ? session : {};
  const { token, refreshToken } = user ? user : {};
  return (
    <> 
      <AppLayout session={session} {...props}
        pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
        pgBread={<span>x</span>}
      >
        <Stack className="mt-24">
          <ListNodeTasks />
          <Box sx={{minHeight: 100}} />
        </Stack>
      </AppLayout>
    </>
  )
}

export async function getServerSideProps() {
  return { props: { appvars } }
}

