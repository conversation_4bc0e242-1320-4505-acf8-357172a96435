/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, { useState, useEffect, useContext, Suspense } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'

import Link from 'next/link'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, Container, Tooltip, Stack, Typography, Chip } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import BattleTradesTrend from "./user.action.trades.modal";
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Backdrop from '@mui/material/Backdrop';
import Divider from '@mui/material/Divider';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import { BiKnife, BiLoader, BiWindowClose } from "react-icons/bi";
import Drawer from '@mui/material/Drawer';
import moment from "moment";
import LinearProgress from '@mui/material/LinearProgress';
import { convertUTCDateToLocalDate } from "../../lib/fnx/fnx.fe";
import clsx from 'clsx';
import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';

import InboxIcon from '@mui/icons-material/MoveToInbox';
import DownloadIcon from '@mui/icons-material/Download';
import CloseIcon from '@mui/icons-material/Close';
import FunctionsIcon from '@mui/icons-material/Functions';
import AddIcon from '@mui/icons-material/Add';
import CandlestickChartIcon from '@mui/icons-material/CandlestickChart';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Collapse from '@mui/material/Collapse';
import Slide from '@mui/material/Slide';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';

const ODD_OPACITY = 0.2;
const keyHead = 'dex:';
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
export default function Home(props) {
    const { ...rest } = props;
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false);
    const [battleLoader, setbattleLoader] = useState(false);
    const [battleLoader2, setbattleLoader2] = useState(false);
    const [battleListLoader, setbattleListLoader] = useState(false);
    const [activeBattleData, setactiveBattleData] = useState(false);
    const [battleParams, setbattleParams] = useState(false);
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    })
    useEffect(() => {
        colorMode.setCurrPage('/action/trades');
        drawerCollapsed && colorMode.collapseDrawer(true);
    }, []);
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    const battleList = async () => {
        setbattleListLoader(true);
        let uri = '/api/pub/data/battleList/all'
        try {
            const res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                setbattleListLoader(false)
                return
            }

            const datax = await res.json();
            setactiveBattleData(datax);
            setbattleListLoader(false)

            if (!datax.error) {
                setbattleListLoader(false)
                return datax
            } else {
                console.log('err desc', datax);
                setbattleListLoader(false)
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            // alert('Error Code: 981', e)
            setbattleListLoader(false)
        }

    }
    useEffect(() => {
        const getList = async () => {
            let list = await battleList();
            // console.log('getList list', list, list[0])
            let battleParameters = window.localStorage.getItem('battleParameters');
            // console.log('getList battleParameters', battleParameters);
            if (list && !list[0]) {
                let rx = {
                    type: 'battle',
                    dtCreated: new Date(Date.now()).toISOString(),
                    parameters: (battleParameters),

                };
                setactiveBattleData([
                    {
                        battleID: Date.now(),
                        battle_type: 'battle',
                        battle_params: rx,
                        is_active: false,
                    }
                ])
            }
            
        }
        getList();
    }, []);

    return (
        <>
            <Head>
                <title>Gauss Algo - Battle Watch</title>
            </Head>

            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >
                <Stack className="mt-24">
                    {/* <Input /> */}
                    <Box sx={{
                        m: 2,
                    }}>
                        <Typography variant="h5" component="div">
                            {bull}Battle {bull} Trades &nbsp;{bull}&nbsp;
                            {/* <Chip variant="filled" color={'primary'} label={'%'} /> */}
                        </Typography>
                        <Box>
                            <BTabs {...props} activeBattleData={activeBattleData} user={user} token={token} />
                        </Box>
                        <br />

                    </Box>
                </Stack>
            </AppLayout>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

const bull = (
    <Box
        component="span"
        sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
        •
    </Box>
);
const BTabs = props => {
    // console.log('btaps', props)
    const { activeBattleData, user } = props;
    const { token, refreshToken } = user ? user : {};
    const [value, setValue] = React.useState('1');
    const [battleParams, setbattleParams] = React.useState(false);
    const [dex, setdexType] = React.useState('simulator');

    useEffect(() => {
        if (props.activeBattleData && Array.isArray(props.activeBattleData)) {
            let paramz = props.activeBattleData[0];
            let parameters = JSON.parse(((paramz?.battle_params.parameters)));
            setbattleParams(parameters);
            setdexType(parameters?.battleType?.dex);
            // console.log('props.activeBattleDataXX', parameters.battleType?.dex)
        } else if (props.activeBattleData && props.activeBattleData[0]) {
            let paramz = props.activeBattleData[0];
            let battle_params = JSON.parse(((paramz?.battle_params)));
            let parameters = battle_params.parameters;
            setbattleParams(parameters);
            setdexType(parameters?.battleType?.dex);
            // console.log('props.activeBattleDataXX', parameters.battleType?.dex)
        }
    }, [props.activeBattleData])
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    return (
        <>
            <Box sx={{ width: '100%', typography: 'body1' }}>
                <TabContext value={value}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <TabList onChange={handleChange} aria-label="lab API">
                            <Tab label="Trades" value="1" />
                            {/* {dex !== 'simulator' && (
                                <Tab label="Trade Account" value="2" />
                            )} */}
                            {dex !== 'simulator' && (
                                <Tab label="dEX Logs" value="3" />
                            )}
                            <Tab label="Logs" value="99" />
                        </TabList>
                    </Box>
                    <TabPanel value="1"><TabBattleTrades {...props} activeBattleData={props.activeBattleData} dex={dex} battleParams={battleParams} token={token} /></TabPanel>
                    <TabPanel value="2"><TabDEXAccount {...props} activeBattleData={props.activeBattleData} battleParams={battleParams} token={token} /></TabPanel>
                    <TabPanel value="3"><TabDEXTrades {...props} activeBattleData={props.activeBattleData} battleParams={battleParams} token={token} /></TabPanel>
                    <TabPanel value="99">
                        <Suspense fallback={<div>loading...</div>}>
                            <TabLogs />
                        </Suspense>
                    </TabPanel>
                </TabContext>
            </Box>
        </>
    )
}
const TabLogs = props => {

    const [loading, setloading] = useState(false)
    const [logdata, setlogdata] = useState(false);

    const [modalxData, setxmodalData] = useState(false);
    const [open, setOpen] = React.useState(false);
    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/battlelogs'
                setloading(true);
                const data = await fetcher(uri);
                Array.isArray(data?.data) && data.data.map(d => {
                    d.id = d.lID;
                });
                setloading(false);
                resolve(data?.data)
            } catch (e) {
                console.log('fetch err', e)
                setloading(false);
            }
        });
    }

    const handleClickOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setxmodalData(false);
        setOpen(false);
    };
    useEffect(() => {
        let ixx;
        const getX = async () => {
            ixx = await fnFetch();
            setlogdata(ixx);
        };
        getX();
        console.log('lazy!', Date.now())
    }, []);

    const handleRowDoubleClick = v => {
        console.log('handleRowDoubleClick', v);
        setxmodalData(v);
        handleClickOpen();
    };
    const handleRowClick = async v => {
        console.log('right', true, v.row)(event);
    };

    const refreshData = async () => {
        try {
            // await fetchMarketStats(true);
            let ixx = await fnFetch();
            // console.log('data', ixx);
            setlogdata(ixx);
        } catch (e) { }
    }

    const columns = [
        { field: 'lID', headerName: 'ID', width: 70, },
        { field: 'lTimeISO', headerName: 'Time', width: 120, renderCell: (params) => {
            return <span title={params.row?.lTimeISO}>{moment(params.row.lTimeISO).format('DD/ hh.mm.ss')}</span>;
        } },
        { field: 'symbol', headerName: 'Symbol', width: 160, 
            valueGetter: (params) => params.row.value?.symbol,
         },
         { field: 'section', headerName: 'Section', width: 290, 
             valueGetter: (params) => params.row.value?.section,
          },
          { field: 'note', headerName: 'Note', width: 690, 
              valueGetter: (params) => params.row.value?.note,
           },
           { field: 'issuccess', headerName: 'Success', width: 90, 
               valueGetter: (params) => params.row.value?.isSuccess,
            },
    ];
    return (
        <>
            <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center' }} >
                    <Button size="small"
                        // variant="outlined"
                        style={{ backgroundColor: 'yellow', borderWidth: 1 }}
                        onClick={refreshData}>Refresh</Button> &nbsp;

                    &nbsp;
                </Stack>
            </Stack>
            <StripedDataGrid
                rows={logdata}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}
                slots={{ toolbar: GridToolbar }}
                slotProps={{
                    toolbar: {
                        printOptions: { disableToolbarButton: true },
                        showQuickFilter: true,
                        csvOptions: {
                            fileName: 'logs',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'lTime', sort: 'desc' }],
                    },
                }}

                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }

                onRowDoubleClick={(row, event) => {
                    handleRowDoubleClick(row.row);
                }}
                // onRowClick={handleRowClick}
                sx={{
                    m: 2,
                    boxShadow: 2,
                }} />
            
            <Dialog
                fullScreen
                open={open}
                onClose={handleClose}
                TransitionComponent={Transition}
                sx={{ mx: 30, my: 10 }}
            >

                <Toolbar sx={{ borderBottomWidth: 1 }}>
                    <IconButton
                        edge="start"
                        color="inherit"
                        onClick={handleClose}
                        aria-label="close"
                    >
                        <CloseIcon />
                    </IconButton>
                    <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                       Log Details
                    </Typography>
                    <Button autoFocus color="inherit" onClick={handleClose}>
                        close
                    </Button>
                </Toolbar>

                <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(modalxData));

                }}>
                    <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                        <code style={{ fontSize: '12px' }}>{JSON.stringify(modalxData, null, 4)}</code>
                    </pre>
                </Box>
            </Dialog>
        </>
    )
};
const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));

function nFormatter(num, digits) {
    const lookup = [
        { value: 1, symbol: "" },
        { value: 1e3, symbol: "k" },
        { value: 1e6, symbol: "M" },
        { value: 1e9, symbol: "B" },
        { value: 1e12, symbol: "T" },
        { value: 1e15, symbol: "P" },
        { value: 1e18, symbol: "E" }
    ];
    const regexp = /\.0+$|(?<=\.[0-9]*[1-9])0+$/;
    const item = lookup.findLast(item => num >= item.value);
    return item ? (num / item.value).toFixed(digits).replace(regexp, "").concat(" ", item.symbol) : "0";
    // return item ? Intl.NumberFormat("en-US", { minimumFractionDigits: 3 }).format(num / item.value).toString().replace(regexp, "").concat(item.symbol) : "0";

}

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

const TabBattleTrades = props => {
    const {token} = props;

    const [open, setOpen] = React.useState(false);
    const [modalxData, setxmodalData] = useState(false);
    const [activeBattleData, setactiveBattleData] = useState(false);
    const [battle_parameters, setbattle_parameters] = useState(false);
    const [tradesData, settradesData] = useState(false);
    const [tradesRawData, settradesRawData] = useState(false);
    const [dataTime, setdataTime] = useState(false);
    const [loading, setloading] = useState(true);


    const fBattleParams = async () => {
        try {
            let uri = '/api/pub/data/getactivebattleparameters';
            const datax = await fetcher(uri);
            let dataStg = datax?.data;
            setbattle_parameters(dataStg);
            return dataStg;
        }
        catch (e) {
            console.log('fBattleParams', e)
        };
    };

    const fTradesData = async ({modeRefresh = false}) => {
        try {
            !modeRefresh && setloading(true);
            let uri = '/api/pub/data/trades';
            const datax = await fetcher(uri)
            setdataTime(new Date(Date.now()).toLocaleTimeString())
            !modeRefresh && setloading(false);
            // console.log('datax', datax)

            settradesRawData(datax.data);
            let dataStg = await prepTableData(datax.data);
            settradesData(dataStg);
            return dataStg;
        }
        catch (e) {
            console.log('ex', e)
            // alert('Error Code: 981x', e)
            !modeRefresh && setloading(false)
        };
    };
    const fTradesData4Symbol = async ({modeRefresh = false, symbol = false}) => {
        try {
            !modeRefresh && setloading(true);
            let uri = '/api/pub/data/trades4symbol';
            uri += symbol ? '/' + symbol : ''; 
            const datax = await fetcher(uri)
            setdataTime(new Date(Date.now()).toLocaleTimeString())
            !modeRefresh && setloading(false);
            return datax.data;
        }
        catch (e) {
            console.log('ex', e)
            // alert('Error Code: 981x', e)
            !modeRefresh && setloading(false)
            return false;
        };
    };
    const closeAllTrades = async () => {
        let uri = '/api/pub/data/closeallopentrades?pair=all';
        try {
            const data = await fetcher(uri)
            alert('closed All!', uri)
            refreshData();
        } catch (e) {
            alert('Error conn');
            console.log('e', e, uri)
        }
    };
    const closeOpenTrades = async (pair) => {
        let uri = '/api/pub/data/closeallopentrades?pair=' + pair;
        console.log('uri', uri)
        try {
            setbOpen(true);
            const data = await fetcher(uri)
            setbOpen(false);
            console.log('closed !', uri, data);
            Array.isArray(data?.data) && data?.data.every(s => s == true) ? 
                alert(pair + ' position closed!') : alert(pair + ' position close failed!')
            refreshData();
        } catch (e) {
            alert('Error conn');
            setbOpen(false);
            console.log('e', e, uri)
        }
    };
    useEffect(() => {
        const getList = async () => {
            await fTradesData({});
            await fBattleParams();
        }
        // console.log('tradesData', tradesData)
        getList();
    }, []);

    useEffect(() => {
        const getList = async () => {
            await fTradesData({modeRefresh: true});
        }
        const interval = setInterval(() => {
            getList();
        }, 5000); //set your time here. repeat every 5 seconds
        return () => clearInterval(interval);
    }, []);

    const refreshData = async () => {
        try {
            let list = await fTradesData({modeRefresh: true});
            console.log('fTradesData!', Date.now(), list);// console.log('watchStg!list', watchStg, 'xx', Array.isArray(watchStg) && watchStg[0]);
        } catch (e) {
            console.log('refresh error', e)
        }
    };
    const prepTableData = async rawData => {
        return new Promise(async (resolve, reject) => {
            try {
                // rawData.map(r => {
                //     r.id = r.tradeID
                // })

                let symbolsStg = rawData.map(a => a.pair)

                const symbols = [...new Set(symbolsStg)]
                let data = []

                symbols.map((s, ix) => {
                    let sStg = rawData.filter(a => a.pair === s)
                    let sStgClosed = sStg.filter(a => a.tradeClosed == 1);
                    let sStgOpen = sStg.filter(a => a.tradeClosed == 0);
                    let sData = {}
                    // let cAvgPrice = sStgOpen.reduce((acc, x) => acc + parseFloat(x.entryPrice), 0) / sStgOpen.length;
                    let cunRlzdPNL = sStgOpen.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                    let cTradeAmount = sStgOpen.reduce((acc, x) => acc + parseFloat(x.entryBudget), 0);
                    let cTradeEntryAmount = sStgOpen.reduce((acc, x) => acc + (parseFloat(x.entryPrice) * parseFloat(x.entryAmount)), 0); //sStgOpen.reduce((acc, x) => acc + (parseFloat(sStg[0].closePrice) * x.amountEntry), 0);
                    
                    let cTradeVol = sStgOpen.reduce((acc, x) => acc + parseFloat(x.entryAmount), 0);
                    let cAvgPrice = cTradeEntryAmount / cTradeVol;

                    let winningTradesArr = sStgClosed.filter(t => t.closeNote == 'takeProfit');
                    let losingTradesArr = sStgClosed.filter(t => t.closeNote == 'stopLoss');
                    
                    let closedTrades = sStgClosed.length;

                    let winningTrades = winningTradesArr.length;
                    let losingTrades = losingTradesArr.length;
                    let percentProfitable = closedTrades !== 0 ? winningTrades / closedTrades : 0;
        

                    // console.log('date', sStg[0].pair + '_' + ix.toString(), new Date(Math.max(...sStg.map(x => new Date(x["dtupdated"])))).toISOString())
                    //console.log('entryTime', s, sStg, (Math.max(...sStg.map(x => new Date(x["dtupdated"])))) )
                    sData = {
                        id: sStg[0].pair + '_' + ix.toString(),
                        pair: sStg[0].pair,
                        direction: sStgOpen.length !== 0 ? sStgOpen[0].direction : '',
                        openTrades: sStgOpen.length,
                        tradeAmount: sStgOpen.reduce((acc, x) => acc + parseFloat(x.entryAmount), 0),
                        tradeVolume: sStgOpen.reduce((acc, x) => acc + parseFloat(x.entryBudget), 0),
                        avgPrice: cAvgPrice,
                        closePrice: sStg[0].closePrice,
                        unRlzdPNL: cunRlzdPNL,
                        percentProfitable: percentProfitable,
                        unRlzdPNLperc: sStgOpen.length !== 0 && (
                            // (sStg[0].closePrice - cAvgPrice)*(sStgOpen[0].direction == 'long' ? 1 : -1) * 100) ,
                           (cunRlzdPNL) / cTradeEntryAmount * 100) ,
                        closedTrades: sStgClosed.length,
                        rPNL: sStgClosed.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0),
                        firstTrade: new Date(Math.min(...sStg.map(x => x["entryTime"] ? x["entryTime"] : 0))).toISOString(),
                        lastTrade: new Date(Math.max(...sStg.map(x => x["entryTime"] ? x["entryTime"] : 0))).toISOString(),
                        dtUpdated: new Date(Math.max(...sStg.map(x => new Date(x["dtupdated"] ? x["dtupdated"] : 0)))).toISOString(),
                    }
                    data.push(sData)
                })
                // console.log('conv. data', data)

                resolve(data)
                // console.log('watchStg!list', watchStg, 'xx', Array.isArray(watchStg) && watchStg[0]);
            } catch (e) {
                console.log('prepTableData error', e)
            }
        });
    };
    const handleClickOpen = () => {
        setOpen(true);
    };
    const listAllTrades = async (excel = false) => {

        let uriN = '/api/pub/data/nodelist/?1=1';
        uriN += '&node=Market_Trader';
        let portStg;
        const NodeInf = await fetcher(uriN);
        if (NodeInf && Array.isArray(NodeInf.data) && NodeInf.data.length !== 0) {
            let AportStg = NodeInf.data.filter(n => 
                n.is_active == 1 && n.is_deleted == 0);
                console.log('Ap', AportStg)
            portStg = AportStg[0]?.port
        }

        let port = portStg || 130;
        let urlX = `http://localhost:${port}/trades`;
        urlX += excel ? '?excel=1' : '';
        // var urlX = 'https://www.tradingview.com/chart?symbol=BINANCE%3A';
        console.log('', new Date().toISOString(), 'listAllTrades', urlX);
        window.open(urlX, "_blank");
    };
    const handleClose = () => {
        setxmodalData(false);
        setOpen(false);
    };
    const handleRowDoubleClick = async v => {
        // console.log('handleRowDoubleClick', v, tradesRawData)
        let pairData = await fTradesData4Symbol({symbol: v.pair});

        // let trx = Array.isArray(tradesRawData) && tradesRawData.filter(t => t.pair === v.pair)
        v.trades = pairData; //trx
        // console.log('trades', trx, pairData)
        setxmodalData(v);
        handleClickOpen();
    };
    const handleRowDoubleClick_ = async v => {
        // console.log('handleRowDoubleClick', v, tradesRawData)
        let trx = Array.isArray(tradesRawData) && tradesRawData.filter(t => t.pair === v.pair)
        v.trades = trx;
        console.log('trades', trx)
        setxmodalData(v);
        handleClickOpen();
    };
    const restartNode = async pair => {
        let uri = '/api/pub/data/battlenoderestart'
        let data2Post = {
            node_key_tag: pair,
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json()

            if (!datax.error) {
                alert('restarted node: ' + pair)
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    };
    const columns = tradesData ? [
        { field: 'pair', headerName: 'Pair', width: 140,},
        { field: 'direction', headerName: 'Dir', width: 55, },
        { field: 'openTrades', headerName: 'OpenT# Q#', width: 99, type: 'number', 
            // valueGetter: (params) => (params.row?.openTrades) + ' / ' + parseFloat(params.row?.tradeVolume).toFixed(2),
            renderCell: (params) => {
                // console.log('params', params)
                let tim = moment((new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{fontSize: 12}}>{(params.row?.openTrades) + ' / ' + parseFloat(params.row?.tradeVolume).toFixed(2)}</span>;
            }

        },
        // { field: 'sumQuote', headerName: 'Quote', width: 120, },
        {
            field: 'tradeAmount', headerName: 'Size', width: 99, type: 'number',
            valueFormatter: ({ value }) => {
                // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
                return parseFloat(value).toFixed(3) + '';
            },
        },
        { field: 'avgPrice', headerName: 'Avg P', width: 88, type: 'number', },
        { field: 'closePrice', headerName: 'Close P', width: 88, type: 'number', },
        { field: 'unRlzdPNL', headerName: 'unRlzdPNL', width: 99, type: 'number', 
            valueFormatter: ({value}) => {
                return parseFloat(value).toFixed(4) + '';
            }
        }, 
        {
            field: 'unRlzdPNLperc', headerName: '%', width: 59, type: 'number',
            valueFormatter: ({ value }) => {
                return isNaN(parseFloat(value).toFixed(2)) ? '-' : parseFloat(value).toFixed(2) + '';
            }
        },
        {
            field: 'unRlzdPNLpercR', headerName: '% P', width: 59, 
            valueFormatter: ({ value }) => {
                return isNaN(parseFloat(value).toFixed(2)) ? '-' : parseFloat(value).toFixed(2) + '';
            },
            valueGetter: (params) => { 
                const { trading = {} } = battle_parameters || {};
                const { entry, exit = {}, wallet } = trading;
                const { takeProfitRatio, stopLoss, stopLossUsePercentage, stopLossPercentage } = exit;
                let value = params.row;
                // console.log('unRlzdPNLpercR ROW', value)
                let Ratio = (parseFloat(value.unRlzdPNLperc) * 100) / (parseFloat(value.unRlzdPNLperc) > 0 ? takeProfitRatio : (stopLossPercentage) )
                return Ratio;
            },
            renderCell: ({ value }) => {
                return (
                    <>
                        {!isNaN(value) && <ProgressBar value={(parseFloat(value) / 100)} />}
                    </>
                )
            }
        },
        { field: 'percentProfitable', headerName: 'profitable', width: 75, type: 'number', 
            valueFormatter: ({ value }) => {
                return isNaN(parseFloat(value).toFixed(2)) ? '-' : (parseFloat(value) * 100).toFixed() + ' %' + '';
            }},
        { field: 'closedTrades', headerName: 'ClsdT#', width: 75, type: 'number', },
        { field: 'rPNL', headerName: 'r PNL', width: 99, type: 'number', },
        {
            field: 'lastTrade', headerName: 'Last Entry', width: 120,
            valueFormatter: ({ value }) => {
                // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
                let tim = moment(convertUTCDateToLocalDate(new Date(value))).fromNow();
                return (tim);
            },
            renderCell: (params) => {
                let tim = moment((new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{fontSize: 10}}>{tim}</span>;
            }
        },
        { field: 'dtUpdated', headerName: 'dtUpdated', width: 120, 
        // valueFormatter: ({ value }) => {
        //     // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
        //     return (value).substring(8, 19) + '';
        // },
        renderCell: (params) => {
            let delta = moment().diff((new Date(params.value)))
            let tim = (params.value).substring(8, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
            // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
            return <span title={params.value} style={{fontSize: 10, backgroundColor: delta > 10000 && params.row?.openTrades > 0 ? 'yellow' : null}}>{tim} {delta && (delta / 1000).toFixed(0)}s</span>;
        }},
        {
            field: 'action', headerName: 'action', minWidth: 200,
            renderCell: (params) => {
                // console.log('paramx', params);
                return (
                    <>
                    <Stack sx={{flexDirection: 'row',  overflowX: 'auto'}}>
                    <Typography
                        onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + params.row.pair + 'PERP', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        tvw
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => window.open('https://www.binance.com/en/futures/' + params.row.pair + '', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        bi
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => restartNode(params.row.pair)}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        rstNode
                    </Typography> 
                        &nbsp;
                        &nbsp;
                        {params.row.openTrades !== 0 && (
                            <Typography
                                onClick={() => closeOpenTrades(params.row.pair)}
                                size={'xs'}
                                style={{ borderWidth: 1, backgroundColor: 'yellow', m: 1, p: 0, cursor: 'pointer'  }}
                                sx={{ fontSize: 9, p: 0, px: 1 }}
                            >
                                CloseTrd
                            </Typography>
                        )}
                        &nbsp;
                        &nbsp;
                    </Stack>
                    </>
                );
            }
        },
    ] : []; 
    useEffect(() => {
        // console.log('props.activeBattleData', props.activeBattleData)
        props.activeBattleData && setactiveBattleData(props.activeBattleData)
    }, [props.activeBattleData]); 

    const [bopen, setbOpen] = React.useState(false);
    const handlebClose = () => {
        setbOpen(false);
    };
    const handlebToggle = () => {
        setbOpen(!bopen);
    };

    return (
        <>
            <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>

                <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center' }} >
                    <Button size="small"
                        // variant="outlined"
                        style={{ backgroundColor: 'yellow', borderWidth: 1 }}
                        onClick={refreshData}>Refresh</Button> &nbsp;
                    <Typography variant="caption" color={'GrayText'}>
                        {dataTime}&nbsp;&nbsp;
                        {activeBattleData && activeBattleData[0].dtUpdated && 
                            <Typography variant="caption" title={activeBattleData ? activeBattleData[0].dtUpdated : ''} color={'GrayText'}>
                                battle Duration: {activeBattleData ? parseFloat((new Date((Date.now())) - new Date(activeBattleData[0].dtUpdated)) / 1000 / 60).toFixed(0) + ' min': ''}
                            </Typography>
                        }
                    </Typography>
                    &nbsp;
                    {tradesData && Array.isArray(tradesData) && tradesData.length !== 0 && (
                        <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center' }}>
                            <Button size="small"
                                // variant="outlined"
                                style={{ backgroundColor: '#cecece', color: '#333', borderWidth: 1 }}
                                sx={{ mx: 2 }}
                                onClick={() => listAllTrades(false)}>List All Trades</Button>
                            <Typography onClick={() => listAllTrades(true)}>
                                <DownloadIcon />
                            </Typography>
                            <BattleTradesTrend {...props} />
                        </Stack>
                    )}
                    &nbsp;
                </Stack>
                <Box sx={{ my: 1 }} >

                    {tradesData && Array.isArray(tradesData) && tradesData.length !== 0 && (
                        <Button size="small"
                            // variant="outlined"
                            style={{ backgroundColor: 'red', color: 'white', borderWidth: 1 }}
                            sx={{ mx: 2 }}
                            onClick={closeAllTrades}>Close All Trades</Button>)}
                </Box>
            </Stack>

            <Backdrop
                sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
                open={bopen}
                onClick={handlebClose}
            >
                <CircularProgress color="inherit" />
            </Backdrop>

            {tradesRawData && <TradeHead tradesData={tradesData} tradesRawData={tradesRawData} />}
            {!loading && tradesData && <StripedDataGrid
                rows={tradesData}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}

                slots={{
                    toolbar: GridToolbar,
                    // loadingOverlay: LinearProgress,
                }}
                slotProps={{
                    toolbar: {
                        showQuickFilter: true,
                        printOptions: { disableToolbarButton: true },
                        csvOptions: {
                            fileName: 'trades',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                    },
                }}

                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }

                onRowDoubleClick={(row, event) => {
                    handleRowDoubleClick(row.row);
                }}
                // onRowClick={handleRowClick}
                sx={{
                    // m: 2,
                    boxShadow: 2,
                }} />
            }
            <Dialog
                fullScreen
                open={open}
                onClose={handleClose}
                TransitionComponent={Transition}
                sx={{ mx: 30, my: 10 }}
            >

                <Toolbar sx={{ borderBottomWidth: 1 }}>
                    <IconButton
                        edge="start"
                        color="inherit"
                        onClick={handleClose}
                        aria-label="close"
                    >
                        <CloseIcon />
                    </IconButton>
                    <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                        {modalxData?.pair} Trades
                    </Typography>
                    <Button autoFocus color="inherit" onClick={handleClose}>
                        close
                    </Button>
                </Toolbar>
                <Box sx={{ p: 2 }}>
                    {modalxData && <SymbolTradeList data={modalxData} />}
                    {/* <TableContainer component={Paper}>
                        <Table sx={{ minWidth: 650 }} size="small" aria-label="a dense table">
                            <TableHead>
                                <TableRow>
                                    <TableCell>Trade</TableCell>
                                    <TableCell align="right">direction</TableCell>
                                    <TableCell align="right">entryTime</TableCell>
                                    <TableCell align="right">entryAmount</TableCell>
                                    <TableCell align="right">entryPrice</TableCell>
                                    <TableCell align="right">closePrice</TableCell>
                                    <TableCell align="right">realPnl</TableCell>
                                    <TableCell align="right">unRealP</TableCell>
                                    <TableCell align="right">%</TableCell>
                                    <TableCell align="right">closed</TableCell>
                                    <TableCell align="right">trdLifeT</TableCell>
                                    <TableCell align="right">closeType</TableCell>
                                    <TableCell align="right">closeNote</TableCell>
                                    <TableCell align="right">updateNote</TableCell>
                                    <TableCell align="right">dtupdated</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {Array.isArray(modalxData.trades) && modalxData.trades.map((row) => {
                                    // console.log('row', row);
                                    return (
                                        <TableRow
                                            key={row.tradeID}
                                            sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                                        >
                                            <TableCell component="th" scope="row">
                                                {row.tradeID}_{row.tradeNo}/{row.tradeSubNo}
                                            </TableCell>
                                            <TableCell align="right">{row.direction}</TableCell>
                                            <TableCell align="right" title={row.entryBarBOP}>{row.entryTimeEn && row.entryTimeEn.substring(8, 19)}</TableCell>
                                            <TableCell align="right">{row.entryAmount && parseFloat(row.entryAmount).toFixed(5)}</TableCell>
                                            <TableCell align="right">{row.entryPrice && parseFloat(row.entryPrice).toFixed(6)}</TableCell>
                                            <TableCell align="right">{row.closePrice}</TableCell>
                                            <TableCell align="right">{row.realizedPnl && parseFloat(row.realizedPnl).toFixed(3)}</TableCell>
                                            <TableCell align="right">{row.unRealizedPnl && parseFloat(row.unRealizedPnl).toFixed(3)}</TableCell>
                                            <TableCell align="right">{
                                                row.realizedPnl
                                                    ? (parseFloat(row.realizedPnl) / (parseFloat(row.entryAmount) * parseFloat(row.entryPrice)) * 100).toFixed(2)
                                                    : (parseFloat(row.unRealizedPnl) / (parseFloat(row.entryAmount) * parseFloat(row.entryPrice)) * 100).toFixed(2)
                                            }</TableCell>
                                            <TableCell align="right" title={row.closeTimeEn} >{row.closeTimeEn && row.closeTimeEn.substring(8, 19)}</TableCell>
                                            <TableCell align="right">{row.tradeLifeTime}</TableCell>
                                            <TableCell align="right">{row.closeType}</TableCell>
                                            <TableCell align="right">{row.closeNote}</TableCell>
                                            <TableCell align="right">{row.updateNote}</TableCell>
                                            <TableCell align="right" title={row?.entryTimeEn}>{row.dtupdatedEn && row.dtupdatedEn.substring(8, 19)}</TableCell>
                                        </TableRow>
                                    )
                                }
                                )}
                            </TableBody>
                        </Table>
                    </TableContainer> */}

                </Box>
            </Dialog>

        </>
    )
}


const SymbolTradeList = props => {
    
    const [loading, setloading] = useState(false);
    const [data, setdata] = useState(false);
    const [trades, settrades] = useState(false);
    useEffect(() => {
        let tr = props.data?.trades;
        // console.log('tradez', tr);
        Array.isArray(tr) && tr.map((i, ix) => i.id = (ix + 1));
        settrades(tr)
        setdata(props.data)
    }, [props.data]);


    const columns = [
        {
            field: 'tradeID', headerName: 'ID', width: 120, renderCell: (params) => {
                return <span title={params.row?.tradeID}>{(params.row.tradeNo) + '_' + (params.row.tradeSubNo)}</span>;
            }
        },
        {
            field: 'direction', headerName: 'direction', width: 90,
            valueGetter: (params) => params.row?.direction,
        },
        {
            field: 'id', headerName: 'entryTime', width: 120, renderCell: (params) => {
                return <span title={params.row?.entryTimeEn}>{moment(params.row.entryTimeEn).format('DD/ h.mm.ss')}</span>;
            }
        }, 
        {
            field: 'entryVolume', headerName: 'Volume', width: 110, type: 'number',
            valueGetter: (params) => (parseFloat(params.row.entryAmount) * parseFloat(params.row.entryPrice)).toFixed(2),
        },
        {
            field: 'entryAmount', headerName: 'eAmount', width: 110, type: 'number',
            valueGetter: (params) => parseFloat(params.row.entryAmount).toFixed(5),
        },
        {
            field: 'entryPrice', headerName: 'ePrice', width: 110, type: 'number',
            valueGetter: (params) => parseFloat(params.row.entryPrice).toFixed(5),
        },
        {
            field: 'closePrice', headerName: 'closePrice', width: 120, type: 'number',
            valueGetter: (params) => parseFloat(params.row.closePrice).toFixed(5),
        },
        {
            field: 'realizedPnl', headerName: 'realizedPnl', width: 120, type: 'number',
            valueGetter: (params) => params.row.realizedPnl && parseFloat(params.row.realizedPnl).toFixed(3),
        },
        {
            field: 'unRealizedPnl', headerName: 'unRealizedPnl', width: 120, type: 'number',
            valueGetter: (params) => params.row.unRealizedPnl && params.row.unRealizedPnl !== 0 ? parseFloat(params.row.unRealizedPnl).toFixed(3) : ' ',
        },
        {
            field: 'rlzRatio', headerName: '%', width: 50, type: 'number', renderCell: (params) => {
                return <span title={params.row?.entryTimeEn}>{
                    params.row.realizedPnl
                        ? (parseFloat(params.row.realizedPnl) / (parseFloat(params.row.entryAmount) * parseFloat(params.row.entryPrice)) * 100).toFixed(2)
                        : (parseFloat(params.row.unRealizedPnl) / (parseFloat(params.row.entryAmount) * parseFloat(params.row.entryPrice)) * 100).toFixed(2)
                }</span>;
            }
        }, 

        {
            field: 'closeNote', headerName: 'closeNote', width: 160,
            valueGetter: (params) => params.row.closeNote,
        },
        //delta Info
        //delta Info
        {
            field: 'deltaValue', headerName: 'delta', width: 80, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.deltaValue).toFixed(2)},
        },
        {
            field: 'deltaPercentile', headerName: 'deltaPrc', width: 110, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.deltaPercentile).toFixed(0)},
        },
        {
            field: 'deltaMean', headerName: 'deltaMn', width: 90, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.deltaMean).toFixed(2)},
        },
        {
            field: 'deltaMedian', headerName: 'deltaMd', width: 90, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.deltaMedian).toFixed(2)},
        },
        {
            field: 'PCPValue', headerName: 'pcp', width: 80, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.PCPValue).toFixed(2)},
        },

        {
            field: 'PCPpercentile', headerName: 'pcpPrc', width: 110, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.PCPpercentile).toFixed(0)},
        },
        {
            field: 'PCPmean', headerName: 'pcpMn', width: 90, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.PCPmean).toFixed(2)},
        },
        {
            field: 'PCPmedian', headerName: 'pcpMd', width: 90, type: 'number',
            valueGetter: (params) => {
                return parseFloat(params.row?.orderInfo?.orderSrc_candleMore.PCPmedian).toFixed(2)},
        },
            
        //delta Info
        //delta Info
        //delta Info
        //delta Info

        {
            field: 'closeTimeEn', headerName: 'closeTimeEn', width: 120, renderCell: (params) => {
                return <span title={params.row?.closeTimeEn}>{moment(params.row.closeTimeEn).format('DD/ h.mm.ss')}</span>;
            }
        }, 
        {
            field: 'tradeLifeTime', headerName: 'lifetime', width: 70, type: 'number',
            valueGetter: (params) => parseFloat(params.row.tradeLifeTime),
        },
        {
            field: 'closeType', headerName: 'closeType', width: 90, type: 'number',
            valueGetter: (params) => parseFloat(params.row.closeType),
        },
        {
            field: 'updateNote', headerName: 'updateNote', width: 160,
            valueGetter: (params) => params.row.updateNote,
        },
        {
            field: 'dtupdatedEn', headerName: 'dtupdatedEn', width: 120, renderCell: (params) => {
                return <span title={params.row?.dtupdatedEn}>{moment(params.row.dtupdatedEn).format('DD/ h.mm.ss')}</span>;
            }
        }, 
    ];

    return (
        <>
        
        <StripedDataGrid
                rows={trades}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}
                slots={{ toolbar: GridToolbar }}
                slotProps={{
                    toolbar: {
                        printOptions: { disableToolbarButton: true },
                        showQuickFilter: true,
                        csvOptions: {
                            fileName: 'trades',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'lTime', sort: 'desc' }],
                    },
                }}

                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }

                onRowDoubleClick={(row, event) => {
                    handleRowDoubleClick(row.row);
                }}
                // onRowClick={handleRowClick}
                sx={{
                    m: 2,
                    boxShadow: 2,
                }} />
            

        </>
    )
}
const TradeHead = props => {
    const { tradesRawData, tradesData } = props;
    const [collapsed, setcollapsedR] = useState(false);
    const [data_stat, setdata_stat] = useState(false);
    const [UpdateMe, setUpdateMe] = useState(0);
    useEffect(() => {
        // console.log((props));
        let trades = tradesRawData;
        let closedTradesArr = trades.filter(t => t.tradeClosed == true);
        let openTradesArr = trades.filter(t => t.tradeClosed !== true);
        let winningTradesArr = closedTradesArr.filter(t => t.closeNote == 'takeProfit');
        let losingTradesArr = closedTradesArr.filter(t => t.closeNote == 'stopLoss');
        let grossLoss = losingTradesArr.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
        let grossProfit = winningTradesArr.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);

        let data_stat_stg = {};
        let openTrades = openTradesArr.length;
        let unRealizedPnl = trades.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
        let openTradesVol = openTradesArr.reduce((acc, x) => acc + (parseFloat(x.closePrice) * parseFloat(x.entryAmount)), 0);
        let winningTrades = winningTradesArr.length;
        let losingTrades = losingTradesArr.length;
        let closedTrades = closedTradesArr.length;
        let realizedPnl = trades.reduce((acc, x) => acc + (x.realizedPnl ? parseFloat(x.realizedPnl) : 0), 0);
        let closedTradesVol = closedTradesArr.reduce((acc, x) => acc + (parseFloat(x.closePrice) * parseFloat(x.entryAmount)), 0);
        let realizedCommision = closedTradesArr.reduce((acc, x) => acc + parseFloat(x.commission), 0);

        let closedPairPositions = [...new Set(closedTradesArr.map(a => a.pair))].length;
        let openPairPositions = [...new Set(openTradesArr.map(a => a.pair))].length;

        closedTradesArr.map(sg => {
            sg.tradeDuration = sg.closeTime ? Math.ceil((sg.closeTime - sg.entryTime) / 1000 / 60) : -1;
        });

        let tradedDuration = closedTradesArr.reduce((acc, x) => acc + parseFloat(x.tradeDuration), 0);
        let netpnl = unRealizedPnl + realizedPnl;
        let percentProfitable = closedTrades !== 0 ? winningTrades / closedTrades : 0;
        let tradedDurationAvg = tradedDuration / closedTrades;
        // console.log('tradedDuration / closedTrades', tradedDuration, closedTrades, closedTradesArr)
        let avgRealizedPnl = realizedPnl / closedTrades;

        data_stat_stg = {
            openTrades, unRealizedPnl, openTradesVol, grossProfit,
            grossLoss, winningTrades, losingTrades, closedTrades,
            realizedPnl, closedTradesVol, realizedCommision, netpnl,
            percentProfitable, tradedDurationAvg, avgRealizedPnl,
            closedPairPositions, openPairPositions
        };
        setdata_stat(data_stat_stg);

    }, [props.tradesRawData]);

    const setcollapsed = v => {
        setcollapsedR(v);
        setUpdateMe(Date.now());
    };

    return (
        <>
            <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center' }}>

                <IconButton
                    edge="start"
                    color="inherit"
                    onClick={() => setcollapsed(!collapsed)}
                    aria-label="close"
                >
                    {collapsed && UpdateMe !== 0 ? <ExpandLess /> : <UnfoldMoreIcon />}
                </IconButton>

                <StatCard title={'Net Profit:'} desc={'unRealizedPnl + realizedPnl'} subText={` $ ${parseFloat(data_stat?.netpnl).toFixed(2)} `} />
                {/* <StatCard title={'Total Open Trades:'} desc={`Pairs: ${data_stat?.openPairPositions} `} subText={` ${parseFloat(data_stat?.openTrades).toFixed(0)} `} /> */}
                <StatCard title={'Total Open Trades:'} desc={`Pairs: ${data_stat?.openPairPositions} / ${parseFloat(data_stat?.openTrades).toFixed(0)} `} subText={` ${data_stat?.openPairPositions}p / ${parseFloat(data_stat?.openTrades).toFixed(0)}t `} />
                <StatCard title={'Open Trades Vol $:'} subText={` $ ${parseFloat(data_stat?.openTradesVol).toFixed(2)} `} />
                <StatCard title={'UnRealized Profit:'} subText={` $ ${parseFloat(data_stat?.unRealizedPnl).toFixed(2)} `} />
                <StatCard title={'Total Closed Trades:'} desc={`Pairs: ${data_stat?.closedPairPositions} `} subText={` ${parseFloat(data_stat?.closedTrades).toFixed(0)} `} />
                <StatCard title={'Closed Trades Vol $:'} subText={` $ ${parseFloat(data_stat?.closedTradesVol).toFixed(2)} `} />
                <StatCard title={'Net Realized Profit:'} subText={` $ ${parseFloat(data_stat?.realizedPnl).toFixed(2)} `} />
                <StatCard title={'Percent Profitable:'} style={{ backgroundColor: '#ffcc00' }} subText={` ${(parseFloat(data_stat?.percentProfitable) * 100).toFixed(0)} % `} />
                <StatCard title={'Profit Factor:'} desc={'gross profit / gross loss'} subText={` ${(parseFloat(data_stat?.grossProfit) / parseFloat(data_stat?.grossLoss) * (-1)).toFixed(2)} `} />
                <StatCard title={'Avg Trade:'} desc={'net profit / closed trades'} subText={` $ ${(parseFloat(data_stat?.realizedPnl) / parseFloat(data_stat?.closedTrades)).toFixed(2)} `} />
                <StatCard title={'Avg Trade Dur:'} subText={` ${(parseFloat(data_stat?.tradedDurationAvg)).toFixed(2) + 'min'} `} />
            </Stack>

            <Collapse in={collapsed} >
                <TradeHeader {...props} />
            </Collapse>

        </>

    )
}

const StatCard = ({ title, subText, desc, style }) => {
    let ViewComp = desc ? Tooltip : Box
    return (
        <Card sx={{ p: 1, minWidth: '100px', mr: 2, mb: 1, ...style }}>
            <ViewComp title={desc} variant="solid">
                <Typography sx={{ fontSize: '10px', color: '#00000080' }}>
                    {title}
                </Typography>
                <div>
                    <Box>
                        <Typography title={desc} sx={{ fontSize: '18px', color: '#000000' }}>
                            {subText}
                        </Typography>
                    </Box>
                </div>
            </ViewComp>
        </Card>
    )
};
const TradeHeader = props => {
    const {tradesRawData, tradesData} = props
    const [cOpen, setcOpen] = useState(false);
    const [loading, setloading] = useState(false)
    const [logdata, setlogdata] = useState(false);
    const [accountData, setaccountData] = useState(false);
    
    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/dexaccount2'
                setloading(true);
                const data = await fetcher(uri);
                setloading(false);
                resolve(data?.data)
            } catch (e) {
                console.log('fetch err', e)
                setloading(false);
            }
        });
    }

    useEffect(() => {
        // console.log('', tradesRawData)
        if (tradesRawData) {
            let r = {};
            let arr = tradesRawData;

            //open trades
            let sStgClosed = arr.filter(a => a.tradeClosed === true);
            let cPnl = sStgClosed.reduce((acc,x) => acc + parseFloat(x.realizedPnl), 0).toFixed(2);
            r.closedPositions = [...new Set(sStgClosed.map(a => a.pair))].length;
            r.Pnl = cPnl;
            r.closedTrades = sStgClosed.length;
            r.closedQuote = sStgClosed.reduce((acc,x) => acc + parseFloat(x.entryBudget), 0);

            let sStgOpen = arr.filter(a => a.tradeClosed === false);
            let sStgOpenPstv = sStgOpen.filter(a => a.unRealizedPnl > 0);
            // let sStgOpenNega = sStgOpen.filter(a => a.unRealizedPnl > 0);
            let uPnl = sStgOpen.reduce((acc,x) => acc + parseFloat(x.unRealizedPnl), 0).toFixed(2);
            let qVolume = sStgOpen.reduce((acc,x) => acc + parseFloat(x.entryBudget), 0);
            r.trades = sStgOpen.length;
            r.positons = [...new Set(sStgOpen.map(a => a.pair))].length;
            r.qVolume = qVolume;
            r.uPnl = uPnl;
            r.uPnlPrc = (uPnl / qVolume * 100)
            r.tradesPstv = sStgOpenPstv.length;
            r.posPstv = [...new Set(sStgOpenPstv.map(a => a.pair))].length;
            r.uPnlPstv = sStgOpenPstv.reduce((acc,x) => acc + parseFloat(x.unRealizedPnl), 0).toFixed(2)
            r.openQuote = qVolume.toFixed(2)
            setcOpen(r);
        }
    }, [tradesRawData])

    useEffect(() => {
        let ixx;
        const getX = async () => {
            ixx = await fnFetch();
            setlogdata(ixx);
            setaccountData(ixx)
        };
        getX();
    }, []);

    const refreshData = async () => {
        try {
            // await fetchMarketStats(true);
            let ixx = await fnFetch();
            console.log('setaccountData', ixx);
            setlogdata(ixx);
            setaccountData(ixx)
        } catch (e) { }
    }
    return (
        <>
            <Box sx={{ borderWidth: 0.5, mb: 2 }}>
                <Typography color={'GrayText'} sx={{ borderBottomWidth: 0.5, px: 0.5 }}>Account Data:</Typography>
                <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', px: 0.5 }}>
                    <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center' }} >
                        <Button size="small"
                            // variant="outlined"
                            style={{ backgroundColor: 'yellow', borderWidth: 1, fontSize: '12px' }}
                            onClick={refreshData}>Refresh Account Data</Button> &nbsp;
                        &nbsp;
                        {loading && <Box sx={{ height: 20, width: 20, overflow: 'hidden' }}>
                            <CircularProgress sx={{ height: 20, width: 20 }} />
                        </Box>}
                    </Stack>
                </Stack>
                <Box sx={{ px: 0.5 }}>
                    {!loading && accountData && (
                        <>
                            <Divider />
                            <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-evenly' }} >
                                <Box>
                                    Wallet Balance
                                    <Typography>
                                        $ {parseFloat(accountData?.fAccount?.data?.totalWalletBalance).toFixed(2)}
                                    </Typography>
                                </Box>
                                <Box>
                                    Unrealized Profit
                                    <Typography>
                                        $ {parseFloat(accountData?.fAccount?.data?.totalUnrealizedProfit).toFixed(2)}
                                    </Typography>
                                </Box>
                                <Box>
                                    Margin Balance
                                    <Typography>
                                        $ {parseFloat(accountData?.fAccount?.data?.totalMarginBalance).toFixed(2)}
                                    </Typography>
                                </Box>
                                <Box>
                                    Available Balance
                                    <Typography>
                                        $ {parseFloat(accountData?.fAccount?.data?.availableBalance).toFixed(2)}
                                    </Typography>
                                </Box>
                            </Stack>
                            <Divider />
                        </>
                    )}
                </Box>
                {/* 
                <Box sx={{ my: 1, px: 1 }} >
                    <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', borderTopWidth: 1, borderBottomWidth: 1, my: 1, py: 1 }}>
                        <Box>
                            <Typography color={'GrayText'} sx={{ borderBottomWidth: 0.5 }}>Realized Balance</Typography>
                            <FunctionsIcon sx={{ backgroundColor: cOpen?.Pnl > 0 ? 'blue' : 'yellow', color: cOpen?.Pnl > 0 ? 'white' : 'black' }} />&nbsp;$ {cOpen.Pnl ? parseFloat(cOpen?.Pnl, 2)?.toFixed(2) : ''}   Q: $ {cOpen.closedQuote && cOpen.closedQuote.toFixed(2)}
                            &nbsp;&nbsp;net B: $ {((cOpen.Pnl ? parseFloat(cOpen?.Pnl, 2) : 0) + (cOpen.uPnl ? parseFloat(cOpen?.uPnl, 2) : 0)).toFixed(2)}
                            <Box>
                                {''} {cOpen?.closedPositions} pairs, {cOpen?.closedTrades} trades
                            </Box>
                        </Box>
                        <Box>
                            <Typography color={'GrayText'} sx={{ borderBottomWidth: 0.5 }}>UnRealized Balance</Typography>
                            <FunctionsIcon sx={{ backgroundColor: cOpen?.uPnl > 0 ? 'blue' : 'yellow', color: cOpen?.uPnl > 0 ? 'white' : 'black' }} />{''} $ {cOpen?.uPnl} %{cOpen.uPnlPrc && cOpen.uPnlPrc.toFixed(2)}
                            <Box>
                                <AddIcon sx={{ backgroundColor: cOpen?.uPnl > 0 ? 'blue' : 'blue', color: 'white' }} /> {''} $ {cOpen?.uPnlPstv}, {cOpen.posPstv}#
                            </Box>
                        </Box>
                        <Box>
                            <Typography color={'GrayText'} sx={{ borderBottomWidth: 0.5 }}>Pairs / Open Position</Typography>
                            <FunctionsIcon sx={{ backgroundColor: cOpen?.uPnl > 0 ? 'blue' : 'yellow', color: cOpen?.uPnl > 0 ? 'white' : 'black' }} />{''} {cOpen?.positons} pos, {cOpen.trades} trades
                            <Box>
                                <AddIcon sx={{ backgroundColor: cOpen?.uPnl > 0 ? 'blue' : 'blue', color: 'white' }} /> {cOpen?.posPstv} pos, {cOpen.tradesPstv} trades
                            </Box>
                        </Box>
                        <Box>
                            <Typography color={'GrayText'} sx={{ borderBottomWidth: 0.5 }}>Total Open Quote</Typography>
                            <Box>
                                <FunctionsIcon sx={{ backgroundColor: cOpen?.uPnl > 0 ? 'blue' : 'yellow', color: cOpen?.uPnl > 0 ? 'white' : 'black' }} />{''}$ {cOpen?.openQuote}
                            </Box>
                        </Box>
                    </Stack>
                </Box> */}
            </Box>
        </>
    )
}
const TabDEXAccount = props => {
    const { token, battleParams } = props;
    const [loader, setLoader] = useState(false);
    const [accountData, setaccountdata] = useState(false);

    const [loaderBalance, setLoaderBalance] = useState(false);
    const [balanceData, setbalanceData] = useState(false);
    const [positions, setpositions] = useState([]);

    const [dexvalues, setdexvalues] = useState(false);
    const [mounted, setmounted] = useState(false);

    let keyx = keyHead + 'faccount'; 
    const calcPozData = refD => {
        let resp = {};
        const {fOpenOrders, fAccount, fPRisk} = refD;
        // console.log('refD', refD)
        let pozs = fAccount?.data?.positions;
        let oo = fOpenOrders?.data;
        let pr = fPRisk?.data;
        pozs.map(p => {
            let prx = [...pr].find(pz => pz.symbol == p.symbol);
            // console.log('prx', prx);
            p.markPrice = prx.markPrice;
            p.liquidationPrice =  prx.liquidationPrice;
            p.direction =  prx.direction;

            let oos = Array.isArray(oo) && oo.filter(o => o.symbol == p.symbol);
            p.oos = oos;
             
        })
        return pozs;
    }
    const fetchAccountData = async (dval = dexvalues, force = false) => {
        setLoader(true);
        const dtBop = Date.now();
        let uri = '/api/pub/data/dexaccount2';
        const currValue = window.localStorage.getItem(keyx);
        // let fetchNeeded = false;
        if (!force && currValue) {
            let jsonCurrValue = JSON.parse(currValue)
            // console.log('currValue', keyx, force, Date.now(), jsonCurrValue.updatetime, Date.now() - jsonCurrValue.updatetime, Date.now() - jsonCurrValue.updatetime < 20000)
            if (Date.now() - jsonCurrValue?.updatetime < (60000 * 10)) {
                console.log('fetch not needed', jsonCurrValue);
                setaccountdata(jsonCurrValue);
                let pozData = calcPozData(jsonCurrValue);
                setpositions(pozData);
                setLoader(false);
                return currValue
            } else {
                console.log('fetch new data', jsonCurrValue);
                // fetchNeeded = true;
                window.localStorage.setItem(keyx, null);
            }
        } else {
            // console.log('currValue', currValue, force);
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(dval),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                setLoader(false)
                return
            } 
            const datax = await res.json();
            setLoader(false) 
            if (!datax.error) {
                // console.log('resp:', datax)
                let resp = {
                    ...datax.data,
                };
                // delete resp.assets;
                // delete resp.positions;
                if (datax.data) {
                    // let assets = Array.isArray(datax.data.assets) && datax.data.assets.filter(a => a.updateTime !== 0);
                    // let positions = Array.isArray(datax.data.positions) && datax.data.positions.filter(a => parseFloat(a.initialMargin) !== 0);
                    // resp.assets = assets;
                    // resp.positions = positions;
                    resp.loadTime = Date.now() - dtBop;
                    resp.updatetime = Date.now();
                    resp.updatetimeISO = new Date(Date.now()).toISOString();
                }


                window.localStorage.setItem(keyx, JSON.stringify(resp));
                setaccountdata(resp);

                let pozData = calcPozData(resp);
                setpositions(pozData);

                setLoader(false);
                return resp;
            } else {
                console.log('err desc', datax);
                setLoader(false)
                alert('test failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setLoader(false)
            return false;
        };
    };
    const refreshAccountData = () => {
        console.log('refresh data!')
        fetchAccountData(props.battleParams?.battleType.config, true);
    };

    let keyBalance = keyHead + 'fbalance';
    const fetchBalanceData = async (dval = dexvalues, force = false) => {
        setLoaderBalance(true);
        let uri = '/api/pub/data/dexbalance';
        const currValue = window.localStorage.getItem(keyBalance);
        // let fetchNeeded = false;
        if (!force && currValue) {
            let jsonCurrValue = JSON.parse(currValue)
            if (Date.now() - jsonCurrValue?.updatetime < (60000 * 5)) {
                console.log('dexbalance fetch not needed', jsonCurrValue);
                setbalanceData(jsonCurrValue);
                setLoaderBalance(false);
                return currValue
            } else {
                console.log('fetch new data', jsonCurrValue); 
                window.localStorage.setItem(keyBalance, null);
            }
        } else {
            // console.log('currValue', currValue, force);
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(dval),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                setLoaderBalance(false)
                return
            } 
            const datax = await res.json();
            setLoaderBalance(false) 
            if (!datax.error) {
                console.log('resp:', datax)
                let resp = {
                    ...datax.data,
                };
                // delete resp.assets;
                // delete resp.positions;
                // if (datax.data) {
                //     let assets = Array.isArray(datax.data.assets) && datax.data.assets.filter(a => a.updateTime !== 0);
                //     let positions = Array.isArray(datax.data.positions) && datax.data.positions.filter(a => parseFloat(a.initialMargin) !== 0);
                //     resp.assets = assets;
                //     resp.positions = positions;
                //     resp.updatetime = Date.now();
                //     resp.updatetimeISO = new Date(Date.now()).toISOString();
                // }
                window.localStorage.setItem(keyBalance, JSON.stringify(resp));
                setLoaderBalance(resp);
                setLoader(false);
                return resp;
            } else {
                console.log('err desc', datax);
                setLoaderBalance(false)
                alert('test failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setLoaderBalance(false)
            return false;
        };

    };
    const refreshBalanceData = () => {
        console.log('refresh data!!')
        fetchBalanceData(props.battleParams.battleType.config, true);
    };

    useEffect(() => {
        if (props.battleParams && props.battleParams?.battleType?.config) {
            setdexvalues(props.battleParams.battleType.config);
            !mounted && fetchAccountData(props.battleParams.battleType.config);
            !mounted && setmounted(true);
            // !mounted && fetchBalanceData(props.battleParams.battleType.config);
        };
    }, [props.battleParams])
    const [value, setValue] = React.useState('1');
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    //DONE fPRisk ten markPrice ve liquidationPrice ı al.
    //DONE gel all orders... (binance + localDB..)
    return (
        <>
            <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center' }} >
                <Button size="small"
                    // variant="outlined"
                    style={{ backgroundColor: 'yellow', borderWidth: 1 }}
                    onClick={refreshAccountData}>Refresh</Button> &nbsp;
                <Typography variant="caption" color={'GrayText'}>
                    {accountData?.updatetimeISO}&nbsp;&nbsp;
                </Typography>
                &nbsp; 
                &nbsp;
                {loader && <Box sx={{ height: 20, width: 20, overflow: 'hidden' }}>
                    <CircularProgress sx={{height: 20, width: 20}} />
                </Box>}
                {loader && <Box sx={{ height: 20, mx: 2 }}>
                    <Typography sx={{fontSize: '12px'}} color={'GrayText'}>Please wait, loading...</Typography>
                </Box>}
            </Stack>

            {!loader && accountData && (
                <>
                <Divider />
                <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-evenly' }} >
                    <Box>
                        Wallet Balance
                        <Typography>
                            $ {parseFloat(accountData?.fAccount?.data?.totalWalletBalance).toFixed(2)}
                        </Typography>
                    </Box>
                    <Box>
                        Unrealized Profit
                        <Typography>
                            $ {parseFloat(accountData?.fAccount?.data?.totalUnrealizedProfit).toFixed(2)}
                        </Typography>
                    </Box>
                    <Box>
                        Margin Balance
                        <Typography>
                            $ {parseFloat(accountData?.fAccount?.data?.totalMarginBalance).toFixed(2)}
                        </Typography>
                    </Box>
                    <Box>
                        Available Balance
                        <Typography>
                            $ {parseFloat(accountData?.fAccount?.data?.availableBalance).toFixed(2)}
                        </Typography>
                    </Box>
                </Stack>
                <Divider />
                </>
            )}
            {!loader && accountData && (
                <>
                    <Box sx={{ width: '100%', typography: 'body1' }}>
                        <TabContext value={value}>
                            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                <TabList onChange={handleChange} aria-label="lab API">
                                    <Tab label="Positions" value="1" />
                                    <Tab label="Open Orders" value="2" />
                                </TabList>
                            </Box>
                            <TabPanel value="1">
                                <TablePositions data={positions || []} />

                                {/* {JSON.stringify(accountData?.fAccount?.data?.positions, " ", 4)} */}
                            </TabPanel>
                            <TabPanel value="2">
                                <Suspense fallback={<div>loading...</div>}>
                                    <TableOOrders data={accountData?.fOpenOrders?.data || []} />
                                </Suspense>
                            </TabPanel>
                        </TabContext>
                    </Box>
                </>
            )}
            {
                /* {!loader && <Button onClick={refreshAccountData}>Refresh</Button>} */}
            {/* {!loader && accountData && (
                <p>
                    {JSON.stringify(accountData, " ", 4)}
                </p>
            )} */}
        </>
    )
}

const TabDEXTrades = props => {
    
    const [loading, setloading] = useState(false)
    const [logdata, setlogdata] = useState(false);

    const [modalxData, setxmodalData] = useState(false);
    const [open, setOpen] = React.useState(false);
    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/battledexlogs'
                setloading(true);
                const data = await fetcher(uri);
                Array.isArray(data?.data) && data.data.map(d => {
                    d.id = d.lID;
                });
                setloading(false);
                resolve(data?.data)
            } catch (e) {
                console.log('fetch err', e)
                setloading(false);
            }
        });
    }

    const handleClickOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setxmodalData(false);
        setOpen(false);
    };
    useEffect(() => {
        let ixx;
        const getX = async () => {
            ixx = await fnFetch();
            setlogdata(ixx);
        };
        getX();
        console.log('lazy!', Date.now())
    }, []);

    const handleRowDoubleClick = v => {
        console.log('handleRowDoubleClick', v);
        setxmodalData(v);
        handleClickOpen();
    };
    const handleRowClick = async v => {
        console.log('right', true, v.row)(event);
    };

    const refreshData = async () => {
        try {
            // await fetchMarketStats(true);
            let ixx = await fnFetch();
            // console.log('data', ixx);
            setlogdata(ixx);
        } catch (e) { }
    }

    const columns = [
        { field: 'lID', headerName: 'ID', width: 70, },
        {
            field: 'lTimeISO', headerName: 'Time', width: 120, renderCell: (params) => {
                return <span title={params.row?.lTimeISO}>{moment(params.row.lTimeISO).format('DD/ hh.mm.ss')}</span>;
            }
        },
        // {
        //     field: 'symbol', headerName: 'Symbol', width: 160,
        //     valueGetter: (params) => params.row.value?.symbol,
        // },
        {
            field: 'section', headerName: 'Section', width: 290,
            valueGetter: (params) => params.row.value?.section,
        },
        {
            field: 'note', headerName: 'Note', width: 690,
            valueGetter: (params) => params.row.value?.note,
        },
        {
            field: 'refCall', headerName: 'refCall', width: 160,
            valueGetter: (params) => params.row.value?.refCall,
        },
    ];
    return (
        <>
            <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Stack sx={{ my: 1, flexDirection: 'row', alignItems: 'center' }} >
                    <Button size="small"
                        // variant="outlined"
                        style={{ backgroundColor: 'yellow', borderWidth: 1 }}
                        onClick={refreshData}>Refresh</Button> &nbsp;

                    &nbsp;
                </Stack>
            </Stack>
            <StripedDataGrid
                rows={logdata}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}
                slots={{ toolbar: GridToolbar }}
                slotProps={{
                    toolbar: {
                        printOptions: { disableToolbarButton: true },
                        showQuickFilter: true,
                        csvOptions: {
                            fileName: 'logs',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'lTime', sort: 'desc' }],
                    },
                }}

                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }

                onRowDoubleClick={(row, event) => {
                    handleRowDoubleClick(row.row);
                }}
                // onRowClick={handleRowClick}
                sx={{
                    m: 2,
                    boxShadow: 2,
                }} />
            
            <Dialog
                fullScreen
                open={open}
                onClose={handleClose}
                TransitionComponent={Transition}
                sx={{ mx: 30, my: 10 }}
            >

                <Toolbar sx={{ borderBottomWidth: 1 }}>
                    <IconButton
                        edge="start"
                        color="inherit"
                        onClick={handleClose}
                        aria-label="close"
                    >
                        <CloseIcon />
                    </IconButton>
                    <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                       Log Details
                    </Typography>
                    <Button autoFocus color="inherit" onClick={handleClose}>
                        close
                    </Button>
                </Toolbar>

                <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(modalxData));

                }}>
                    <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                        <code style={{ fontSize: '12px' }}>{JSON.stringify(modalxData, null, 4)}</code>
                    </pre>
                </Box>
            </Dialog>
        </>
    )
}

const TablePositions = props => {

    const [loading, setloading] = useState(false);
    const [data, setdata] = React.useState([]);
    useEffect(() => {
        if (props.data) {
            let arr = props.data;
            arr.map((d, di) => d.id = di.toString() );
            setdata(arr);
        }
    }, [props.data])

    const columns = data ? [
        { field: 'symbol', headerName: 'Pair', width: 100, },
        {
            field: 'direction', headerName: 'Dir', width: 55,
        },
        {
            field: 'initialMargin', headerName: 'intMarg', type: 'number', width: 100,
            // valueFormatter: ({ value }) => {
            //     return parseFloat(value).toFixed(4) + '';
            // },
            renderCell: (params) => {

                return <span title={JSON.stringify(params.row)} 
                    style={{fontSize: 14, }}>$ {parseFloat(params.value).toFixed(2)} | {params.row.leverage}</span>;
            },
        },
        {
            field: 'notional', headerName: 'Notional', width: 85, type: 'number',
            // valueFormatter: ({ value }) => {
            //     return parseFloat(value).toFixed(2) + '';
            // },

            renderCell: (params) => {

                let initNotiion = parseFloat(params.row.positionAmt) * parseFloat(params.row.entryPrice);
                return <span title={'initial notition: $' + initNotiion.toFixed(2)} style={{fontSize: 14, }}>
                    $ {parseFloat(params.value).toFixed(2)}</span>;
            },
        },
        {
            field: 'positionAmt', headerName: 'PosAmnt', width: 85, type: 'number',
            valueFormatter: ({ value }) => {
                return parseFloat(value).toFixed(4) + '';
            }
        },
        {
            field: 'unrealizedProfit', headerName: 'UnRlz', type: 'number', width: 110,
            // valueFormatter: ({ value }) => {
            //     return parseFloat(value).toFixed(4) + '';
            // },
            renderCell: (params) => {
                let tim = parseFloat(params.row.unrealizedProfit); 
                let notion = params.row.notional;
                let initNotiion = parseFloat(params.row.positionAmt) * parseFloat(params.row.entryPrice);
                let ratio = tim / initNotiion * 100;
                return (<>
                    $ {parseFloat(params.row.unrealizedProfit).toFixed(2)}
                    <span title={'liquidationPrice: ' + parseFloat(params.row.liquidationPrice).toFixed(6)}
                        style={{
                            fontSize: 10, marginLeft: '5px', padding: '2px',
                            backgroundColor: ratio < 0 ? 'yellow' : null
                        }}>
                        {ratio && ('%' + (ratio).toFixed(2))}
                    </span></>);
            }
        },
        { field: 'entryPrice', headerName: 'entryP', width: 105, type: 'number',
            valueFormatter: ({ value }) => {
                return parseFloat(value).toFixed(6) + '';
            },

            renderCell: (params) => {
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return (
                    <span title={'breakEven: ' + parseFloat(params.row.breakEvenPrice).toFixed(6)}
                        style={{ fontSize: 14, }}>
                        $ {parseFloat(params.value).toFixed(6) + ''}
                    </span>
                );
            }

        },
        {
            field: 'markPrice', headerName: 'markP', width: 105, type: 'number',
            // valueFormatter: ({ value }) => {
            //     return parseFloat(value).toFixed(6) + '';
            // },

            renderCell: (params) => {
                return <span title={'pnl marj %' + parseFloat(parseFloat(params.row.unrealizedProfit) / parseFloat(params.row.initialMargin) * 100).toFixed(2)} style={{fontSize: 14, }}>
                    $ {parseFloat(params.value).toFixed(6)}</span>;
            }
        },
        { field: 'updateTime', headerName: 'Updat', width: 95, type: 'number',
            
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = convertUTCDateToLocalDate(new Date(params.value)).toISOString().substring(5, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{fontSize: 11, backgroundColor: delta > 10000 && params.row?.openTrades > 0 ? 'yellow' : null}}>{tim}</span>;
            },
        },
        { field: 'oos', headerName: 'Open Orders', width: 325,
            renderCell: (params) => {
                return (
                    <Stack sx={{overflow: 'auto', height: '15px'}}>
                        <Typography sx={{fontSize: '14px'}}>{Array.isArray(params.row.oos) && params.row.oos.length } orders. <br/></Typography>
                    {Array.isArray(params.row.oos) && params.row.oos.map((o, oi) => {
                        let closerOrder = o.side !== params.row.direction && (o.reduceOnly || o.closePosition); 
                        let closerOrderPNL = closerOrder ? (parseFloat(o.price) - parseFloat(params.row.entryPrice)) * parseFloat(o.origQty) * (params.row.direction == 'LONG' ? -1 : 1) : 0;
                        let closerOrderType = closerOrderPNL > 0 ? 'TP' : closerOrderPNL < 0 ? 'SL' : 'AO';
                        let closeAmt = parseFloat(o.origQty) / parseFloat(params.row.positionAmt)
                        return (
                            <span key={oi} 
                                title={params.value} 
                                style={{fontSize: 14}}>
                                    {closerOrderType }.{o.reduceOnly && '-'} 
                                    {closerOrder && '%' + parseFloat(closeAmt * 100).toFixed(0)} - {o.price}  - {o.origQty} qty, ${parseFloat(parseFloat(o.origQty) * parseFloat(o.price)).toFixed(2)}
                                    {closerOrder && ' / $' + parseFloat(closerOrderPNL).toFixed(2)}
                                    
                            </span>
                        )
                    })}
                    </Stack>
                );
            },}, 
        {
            field: 'action', headerName: 'action', minWidth: 200,
            renderCell: (params) => {
                // console.log('paramx', params);
                return (
                    <>
                    <Stack sx={{flexDirection: 'row',  overflowX: 'auto'}}>
                    <Typography
                        onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + params.row.symbol + 'PERP', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        tvw
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => window.open('https://www.binance.com/en/futures/' + params.row.symbol + '', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        bi
                    </Typography>
                    </Stack>
                    </>
                );
            }
        }, 
    ] : [];
    return (
        <>
        {!loading && data && <StripedDataGrid
                rows={data}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}

                slots={{
                    toolbar: GridToolbar,
                    // loadingOverlay: LinearProgress,
                }}
                slotProps={{
                    toolbar: {
                        showQuickFilter: true,
                        printOptions: { disableToolbarButton: true },
                        csvOptions: {
                            fileName: 'trades',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                    },
                }}
                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }
                // onRowDoubleClick={(row, event) => {
                //     handleRowDoubleClick(row.row);
                // }}
                // onRowClick={handleRowClick}
                sx={{
                    // m: 2,
                    boxShadow: 2,
                }} />
            }

        </>
    )
}
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import { number } from "mathjs";

const TableOOrders = props => {

    const [loading, setloading] = useState(false);
    const [data, setdata] = React.useState([]);
    useEffect(() => {
        if (props.data) {
            let arr = props.data;
            arr.map((d, di) => d.id = di.toString() );
            setdata(arr);
        }
    }, [props.data])

    const columns = data ? [
        { field: 'symbol', headerName: 'Pair', width: 100, },
        { field: 'side', headerName: 'Side', width: 100, },
        { field: 'type', headerName: 'Type', width: 100, },
        { field: 'price', headerName: 'Price', width: 100, type: 'number', 
            renderCell: (params) => {
                return <span title={params.row?.volume}>$ {parseFloat(params.value).toFixed(3)}</span>;
            }
        },
        { field: 'origQty', headerName: 'Qty', width: 100, type: 'number',
            renderCell: (params) => {
                return <span title={params.row?.volume}>{parseFloat(params.value).toFixed(3)}</span>;
            } },
        { field: 'notional', headerName: 'Notion', type: 'number', width: 100,
            valueGetter: (params) => parseFloat(params.row?.price) * parseFloat(params.row?.origQty),
            renderCell: (params) => {
                return <span title={params.row?.volume}>$ {parseFloat(params.value).toFixed(2)}</span>;
            }
         },
            
        { field: 'reduceOnly', headerName: 'Reduce', width: 100,
            renderCell: (params) => {
                return <span title={params.value}>{params.value ? <CheckBoxIcon/> : <CheckBoxOutlineBlankIcon />}</span>;
            }, },
        { field: 'closePosition', headerName: 'Close', width: 100, 
            renderCell: (params) => {
                return <span title={params.value}>{params.value ? <CheckBoxIcon/> : <CheckBoxOutlineBlankIcon />}</span>;
            }, },
        { field: 'time', headerName: 'Time', width: 100, 
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = convertUTCDateToLocalDate(new Date(params.value)).toISOString().substring(5, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{fontSize: 12, }}>{tim}</span>;
            }, 
        },
        { field: 'updateTime', headerName: 'uTime', width: 100,
            renderCell: (params) => {
                let delta = moment().diff((new Date(params.value)))
                let tim = convertUTCDateToLocalDate(new Date(params.value)).toISOString().substring(5, 19) + ''; //moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow();
                // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value} style={{fontSize: 12, }}>{tim}</span>;
            }, 
        },
          
        {
            field: 'action', headerName: 'action', minWidth: 200,
            renderCell: (params) => {
                // console.log('paramx', params);
                return (
                    <>
                    <Stack sx={{flexDirection: 'row',  overflowX: 'auto'}}>
                    <Typography
                        onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + params.row.symbol + 'PERP', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        tvw
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => window.open('https://www.binance.com/en/futures/' + params.row.symbol + '', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 1, px: 1, cursor: 'pointer' }}
                        sx={{ fontSize: 9, p: 0, px: 1}}
                    >
                        bi
                    </Typography>
                    </Stack>
                    </>
                );
            }
        }
    ] : [];
    return (
        <>
        {!loading && data && <StripedDataGrid
                rows={data}
                columns={columns}
                rowHeight={25}
                loading={loading}
                disableColumnMenu={true}

                slots={{
                    toolbar: GridToolbar,
                    // loadingOverlay: LinearProgress,
                }}
                slotProps={{
                    toolbar: {
                        showQuickFilter: true,
                        printOptions: { disableToolbarButton: true },
                        csvOptions: {
                            fileName: 'trades',
                            delimiter: ';',
                            utf8WithBom: true,
                        }
                    }
                }}
                initialState={{
                    sorting: {
                        sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                    },
                }}
                getRowClassName={(params) =>
                    params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                }
                sx={{
                    // m: 2,
                    boxShadow: 2,
                }} />
            }

        </>
    )
}





const Center = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
});

const Element = styled('div')(({ theme }) => ({
    border: `1px solid ${(theme.vars || theme).palette.divider}`,
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
    height: 26,
    borderRadius: 2,
}));

const Value = styled('div')({
    position: 'absolute',
    lineHeight: '24px',
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
});

const Bar = styled('div')({
    height: '100%',
    '&.low': {
        backgroundColor: '#3498db50',
    },
    '&.medium': {
        backgroundColor: '#3498db80',
    },
    '&.high': {
        backgroundColor: '#3498db',
    },
    '&.lowX': {
        backgroundColor: '#f4433650',
    },
    '&.mediumX': {
        backgroundColor: '#f4433690',
    },
    '&.highX': {
        backgroundColor: '#f44336',
    },
});


const ProgressBar = React.memo(function ProgressBar(props) {
    const { value } = props;
    const valueInPercent = Math.abs(value) * 100;
    const isProfit = value > 0;

    return (
        <Element>
            <Value>{`% ${valueInPercent.toFixed(0)}`}</Value>
            <Bar
                className={isProfit ? clsx({
                    low: valueInPercent < 30,
                    medium: valueInPercent >= 30 && valueInPercent <= 70,
                    high: valueInPercent > 70,
                }) : clsx({
                    lowX: valueInPercent < 30,
                    mediumX: valueInPercent >= 30 && valueInPercent <= 70,
                    highX: valueInPercent > 70,
                })}
                style={{ maxWidth: `${valueInPercent}%` }}
            />
        </Element>
    );
});

