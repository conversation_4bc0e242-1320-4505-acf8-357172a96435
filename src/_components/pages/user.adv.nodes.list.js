
import React, { useState, useEffect, useContext } from "react";
import { Box, Button, Container, Stack, Typography } from '@mui/material';

import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import CircularProgress from '@mui/material/CircularProgress';
import Drawer from '@mui/material/Drawer';
import Snackbar from '@mui/material/Snackbar';
import { LoadingButton } from '@mui/lab';

import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import { BiKnife, BiWindowClose } from "react-icons/bi";
import Link from 'next/link'

import cronstrue from 'cronstrue';
const ListNodes = props => {
    const { token } = props;
    const btnRef = React.useRef()
    const modalRef = React.useRef(null)
    const [data, setData] = useState(false)
    const [loading, setloading] = useState(false)
    const [loadingUpdate, setloadingUpdate] = useState(false)
    const [errorMsg, setErrorMsg] = useState(false)
    const [loadingKillAll, setloadingKillAll] = useState(false)
    const killall = async () => {
        setloadingKillAll(true)
        let res
        try {
            let uri = '/api/pub/data/killall';
            res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })

            // toast({
            //     title: 'All nodes stopped.',
            //     description: "We've stopped all active nodes for you.",
            //     status: 'success',
            //     duration: 4000,
            //     isClosable: true,
            // })
            // alert('All nodes stopped')
            setloadingKillAll(false)
            fetchNodes(false);
        } catch (e) {
            console.log('fetch error', e)
            setloadingKillAll(false)
        }
    }
    const fetchNodes = async checkStatus => {
        setloading(true)
        let res
        try {
            let uri = '/api/pub/data/nodelist/';
            uri += checkStatus ? '?checkstate=1' : '';
            res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            const datax = await res.json();
            // console.log('datax', datax.data);
            setData(datax.data)
            setloading(false)
        } catch (e) {
            console.log('fetch error', e)
            alert('Hata', e)
            setloading(false)
        }
    }
    const refresh = (ac = true) => {
        setloadingUpdate(false)
        fetchNodes(ac);
    }
    const [drawerData, setdrawerData] = useState(false)
    const [drawerDataT, setdrawerDataT] = useState(false)
    const [state, setState] = React.useState({
        top: false,
        left: false,
        bottom: false,
        right: false,
      });
    const toggleDrawer = (anchor, open, node_key) => (event) => {
        if (event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
            return;
        }
        if (open) {
            let nData = [...data].filter(d => d.node_key === node_key);
            let dpost = Array.isArray(nData) ? nData[0] : {};
            if(dpost && dpost.node_tasks) {
                let sx1 = JSON.parse(dpost.node_tasks);
                setdrawerDataT(sx1.tasks);
            } else {
            }
            setdrawerData(dpost);
        } else {
            setdrawerDataT(false)
            setdrawerData(false)
        }
        setState({ ...state, [anchor]: open });
    };
    const killNode = async (node_id) => {

        let uri = '/api/pub/data/battlenodekill'
        let data2Post = {
            redisKey: node_id,
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json()

            if (!datax.error) {
                alert('killed node: ' + node_id);
                refresh();
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
        
    }
    const restartNode = async node_id => {
        let uri = '/api/pub/data/battlenoderestart'
        let data2Post = {
            redisKey: node_id,
        }
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(data2Post),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json()

            if (!datax.error) {
                alert('restarted node: ' + node_id)
                refresh();
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }
    const killAllNodes = () => {
        killall();
        alert('kill all nodes')
    }


    const startStopNodeTask = async (v, statu) => {
        const {process_id, task_id} = v;
        let uri = statu == 'stop' ? 
            '/api/pub/data/processkill?1=1&process_id=' + process_id 
            : 
            '/api/pub/data/resumetask?1=1&task_id=' + task_id;
        uri += ''
        console.log('startStopNodeTask uri', uri)
        setloadingUpdate(true)
        let res
        try {
            res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + 'token',
                    'X-Host': 'Subanet.com',
                },
            })
            const datax = await res.json()
            // console.log('resp', statu, key, datax, )
            setloadingUpdate(false);
            setTimeout(() => {
                fetchNodes(false);
            }, 300);
        } catch (e) {
            console.log('fetch error', e)
            setloadingUpdate(false)
        }
    }

    const toggleNodeStatus = vx => {
        const {process_id, tobeState} = vx;
        startStopNodeTask(vx, tobeState);
    };

    useEffect(() => {
        fetchNodes(false);
    }, []);
    return (
        <>

            {/* <div className='flex justify-between py-2 px-2 mb-2 bg-sky-500/50'>
                
            </div> */}
            <Box
            sx={{
              my: 1,
              mx: 4,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
                <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
                    List of Nodes
                </Typography>
                <div>
                    <LoadingButton loading={loadingKillAll} onClick={killAllNodes} size={'xs'} 
                    
                    color="warning"
                    variant="outlined"
                    className='mx-4 outline-red-600'>
                    <BiKnife size={22} />&nbsp;Kill All
                    </LoadingButton>&nbsp;
                    <Button ref={btnRef}  onClick={refresh} size={'xs'} className='ml-4 outline-red-600'>
                        Refresh
                    </Button>&nbsp;
                    {/* <Button ref={btnRef}  onClick={() => refresh(false)} size={'xs'} className='outline-red-600'>
                        R
                    </Button> */}

                </div>
            </Box>

            <Box
            sx={{
              mx: 4,}}
            >
            <TableContainer component={Paper}>
                {!loading && <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell>#</TableCell>
                            <TableCell>node id</TableCell>
                            <TableCell>node name</TableCell>
                            <TableCell>process id</TableCell>
                            <TableCell>port</TableCell>
                            {/* <TableCell>dt created</TableCell> */}
                            <TableCell>Status</TableCell>
                            <TableCell>dt updated</TableCell>
                            <TableCell>&nbsp;</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {data && Array.isArray(data) && data.map((d, i) => {
                            // let LastLogData = Array.isArray(LogData) && LogData.find(l => l.script === d.key);
                            let textClass = 'text-xs ' + (!d.is_active ? 'p-2' : 'bg-yellow-300 p-2');
                            return (
                                <TableRow key={i.toString()}>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}}>{i + 1}</TableCell>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}} title={d.node_type}>
                                        <span className='text-xs' >{d.node_key}</span></TableCell>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}} >
                                        <span className='text-xs' >{d.node_key_tag}</span></TableCell>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}} >
                                        <span className='text-xs' >{d.process_id}</span></TableCell>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}} >
                                        <span className='text-xs' >{d.port}</span></TableCell>
                                    {/* <TableCell ><span className='text-xs' >{d.dtcreated}</span></TableCell> */}
                                    <TableCell sx={{padding: 0, fontSize: '11px'}} >{d.is_active ? 'Running' : 'Stopped'}</TableCell>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}} title={'created: ' + d.dtcreatedEn}><span className='text-xs' >{d.dtupdatedEn}</span></TableCell>
                                    <TableCell sx={{padding: 0, fontSize: '11px'}}>
                                        
                                    {!d.is_active && <LoadingButton
                                            color="warning"
                                            variant="outlined"
                                            className="MuiButton-containedWarning"
                                            loading={loadingKillAll}
                                            onClick={() => restartNode(d.redisKey)} style={{padding: 0, margin: '2px'}}
                                            sx={{ fontSize: 11 }} >

                                            <BiKnife size={18} />&nbsp;Start
                                        </LoadingButton> }
                                        &nbsp;
                                        {d.is_active && (
                                            <LoadingButton
                                                color="warning"
                                                variant="outlined"
                                                className="MuiButton-containedWarning"
                                                loading={loadingKillAll}
                                                onClick={() => killNode(d.redisKey)} style={{padding: 0}}
                                                sx={{ fontSize: 11 }} >
                                                <BiKnife size={18} />&nbsp;Kill
                                            </LoadingButton>
                                        )}

                                    &nbsp;&nbsp;
                                    <Button ref={btnRef} variant="outlined"  onClick={toggleDrawer('bottom', true, d.node_key)} size={'xs'}
                                            sx={{ fontSize: 11 }} style={{padding: 0}} >
                                        View
                                    </Button>
                                    </TableCell>
                                </TableRow>
                            )
                        })}
                    </TableBody>
                </Table>
                }
                {loading && <div align='center' className='py-4'><CircularProgress  /></div>}
            </TableContainer>
            </Box>
            <Drawer
                anchor={'bottom'}
                open={state['bottom']}
                onClose={toggleDrawer('bottom', false)}
            >
                <Box sx={{ minHeight: 300, 
                    // marginLeft: 300,
                    // backgroundColor: '#ffccaa', 
                    justifyContent: 'center', 
                    paddingLeft: 35, paddingRight: 15,
                    alignItems: 'center' }}>
                    <Stack sx={{borderColor: '#ccc', p: 3}}>
                        <Card variant="outlined" sx={{ maxWidth: 960 }}>
                            <Box sx={{ p: 2 }}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                    <Stack sx={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                                    <Typography gutterBottom variant="h5" component="div">
                                        {drawerData?.node_key_tag}
                                    </Typography>
                                    <Chip color="primary" label="Running" size="small" sx={{mx: 2}} />

                                        <Typography color="#ccc" variant="body2">
                                            c:{drawerData.dtcreated}
                                        </Typography>&nbsp;
                                        <Typography color="#ccc" variant="body2">
                                            u:{drawerData.dtupdated}
                                        </Typography>

                                    </Stack>

                                    <Stack sx={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                                        <Button
                                            loading={loadingKillAll}
                                            onClick={toggleDrawer('bottom', false)}  
                                        >
                                            <BiWindowClose size={28} />
                                        </Button>
                                    </Stack>
                    
                                </Stack>
                                <Box>

                                    <TableContainer component={Paper}>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>#</TableCell>
                                                <TableCell>taskID</TableCell>
                                                <TableCell>taskTag</TableCell>
                                                <TableCell>taskDesc</TableCell>
                                                <TableCell>script2Run</TableCell>
                                                <TableCell>period</TableCell>
                                                <TableCell>&nbsp;</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {drawerDataT && Array.isArray(drawerDataT) && drawerDataT.map((dc, i) => {
                                                // let LastLogData = Array.isArray(LogData) && LogData.find(l => l.script === d.key);
                                                let textClass = 'text-xs ' + (!dc.is_active ? 'p-2' : 'bg-yellow-300 p-2');
                                                return (
                                                    <TableRow key={i.toString()}>
                                                        <TableCell>{i + 1}</TableCell>
                                                        <TableCell title={dc.nodeKey} sx={{ maxWidth: 400, overflow: 'auto' }}>
                                                            <span className='text-xs' >{dc.taskID}</span><br />

                                                            <span style={{ fontSize: 9 }} >{dc.taskParams}</span>
                                                        </TableCell>
                                                        <TableCell >
                                                            <span className='text-xs' >{dc.taskTag}</span></TableCell>
                                                        <TableCell >
                                                            <span className='text-xs' >{dc.taskDesc}</span></TableCell>
                                                        <TableCell >
                                                            <span className='text-xs' >{dc.script2Run}</span></TableCell>
                                                        <TableCell title={dc.periodDesc}>
                                                            <span className='text-xs' >{dc.period}</span></TableCell>
                                                    </TableRow>
                                                )
                                            })}
                                        </TableBody>

                                    </TableContainer>
                                {/* drawerDataT */}
                                </Box>
                            </Box>
                            <Divider />

                            <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Stack direction="row" spacing={1}>
                                <Box sx={{ p: 2 }}>
                                    <Typography gutterBottom variant="body2">
                                        Battle ID
                                    </Typography>
                                    <Stack direction="row" spacing={1}>
                                        <Chip label={drawerData.battleID} size="small" />
                                    </Stack>
                                </Box>

                                <Box sx={{ p: 2 }}>
                                    <Typography gutterBottom variant="body2">
                                        Node Key
                                    </Typography>
                                    <Stack direction="row" spacing={1}>
                                        <Chip color="primary" label={drawerData.node_type} size="small" />
                                        <Chip label={drawerData.node_key} size="small" />
                                    </Stack>
                                </Box>
                                <Box sx={{ p: 2 }}>
                                    <Typography gutterBottom variant="body2">
                                        Process
                                    </Typography>
                                    <Stack direction="row" spacing={1}>
                                        <Chip label={'id: ' + drawerData.process_id} size="small" />
                                        <Chip label={'port: ' + drawerData.port} size="small" />
                                        <Link href={`http://localhost:${drawerData.port}/info`} target="_blank">
                                        <Typography sx={{px: '5px', borderWidth: '1px', fontSize: '11px', backgroundColor: '#ffcc00'}}>view node info</Typography></Link>
                                    </Stack>
                                </Box>
                            </Stack>
                                {/* <Box sx={{m: 2}}>
                                    <Typography gutterBottom variant="h6" component="div">
                                    <LoadingButton 
                                            color="warning"
                                            variant="outlined"
                                            className="MuiButton-containedWarning"
                                            loading={loadingKillAll}
                                            onClick={() => restartNode(d.redisKey)}
                                            sx={{fontSize: 11}} >
                                            
                                            <BiKnife size={18} />&nbsp;Restart
                                        </LoadingButton>
                                        &nbsp;
                                        <LoadingButton 
                                            color="warning"
                                            variant="outlined"
                                            className="MuiButton-containedWarning"
                                            loading={loadingKillAll}
                                            onClick={() => killNode(drawerData.redisKey)} 
                                            sx={{fontSize: 11}} >
                                            
                                            <BiKnife size={18} />&nbsp;Kill
                                        </LoadingButton>
                                    </Typography>
                                </Box> */}
                            </Stack>
                        </Card>

                    </Stack>
                </Box>
            </Drawer>
            {!loading && errorMsg }
        </>
    )
}
export default ListNodes;
