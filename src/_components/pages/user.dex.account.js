/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'

import { signIn, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Stack, Typography } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

import Slide from '@mui/material/Slide';

const ODD_OPACITY = 0.2;

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
}); 

export default function Home(props) {
    const { ...rest } = props;
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false);
    const [battleLoader, setbattleLoader] = useState(false); 
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    })
    useEffect(() => {
        colorMode.setCurrPage('/dex/account');
        drawerCollapsed && colorMode.collapseDrawer(true);
    }, []);
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {}; 
    useEffect(() => { }, []);

    return (
        <>
            <Head>
                <title>account - dex - Gauss Algo</title>
            </Head>

            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >
                <Stack className="mt-24">
                    {/* <Input /> */}
                    <Box sx={{
                        m: 2,
                    }}>
                        <Typography variant="h5" component="div">
                            {bull}dEX {bull} Account &nbsp;{bull}&nbsp;
                            {/* <Chip variant="filled" color={'primary'} label={'%'} /> */}
                        </Typography>
                        <Box>
                            xxx
                        </Box>
                        <br />

                    </Box>
                </Stack>
            </AppLayout>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

const bull = (
    <Box
        component="span"
        sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
        •
    </Box>
);