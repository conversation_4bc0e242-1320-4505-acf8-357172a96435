/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

/*eslint-disable*/

// import { Link, Typography, Stack } from '@mui/material';

// ==============================|| FOOTER - AUTHENTICATION 2 & 3 ||============================== //

// const AuthFooter = () => (
//     <Stack direction="row" justifyContent="space-between">
//         <Typography variant="subtitle2" component={Link} href="https://berrydashboard.io" target="_blank" underline="hover">
//             berrydashboard.io
//         </Typography>
//         <Typography variant="subtitle2" component={Link} href="https://codedthemes.com" target="_blank" underline="hover">
//             &copy; codedthemes.com
//         </Typography>
//     </Stack>
// );

// export default AuthFooter;

import React from "react";
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";

const FooterEx = () => (
    <Box
        sx={{
            display: 'flex',
            width: '100%',
            bgcolor: 'background.default',
            color: 'text.primary',
        }}>
    <Typography variant="caption" align={"center"}>
      &nbsp;Svbasi
    </Typography>
    
  </Box>
);

FooterEx.propTypes = {};
FooterEx.defaultProps = {};

export default FooterEx;
