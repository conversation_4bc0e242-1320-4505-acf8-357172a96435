import React, { useState, useEffect } from 'react';

import Chip from '@mui/material/Chip';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import _ from 'lodash';

const Favoriler = require('../../lib/battle.params.favorites');

const CardParamsPairs = props => {
  
  const { user = {} } = props;
  const { login } = user; 
  const [futuresDaily, setFuturesDaily] = useState(false)
  const [userFavorites, setUserFavorites] = useState(false)
  const [selectedPairs, setselectedPairs] = useState([]);
  const [loading, setLoading] = useState(false)
  const [isOpened, setisOpened] = useState(true);

  const [lastpair, setlastpair] = useState(false);
  const [mode, setMode] = useStickyState(null, 'bPairs');

  const handleChange = (tag, checked) => {
    if (true) {
      setlastpair(tag);
      const nextSelectedTags = checked ? [...selectedPairs, tag] : selectedPairs.filter(t => t !== tag);
      // setselectedPairs([...new Set(nextSelectedTags)]);
      const newA = [...new Set(nextSelectedTags)];
      setselectedPairs(newA);
      mode && props.callbackFN({ fname: 'pairs', fvar: newA });
      mode && Array.isArray(newA) && setlastpair(newA.slice(-1).pop());
    } else {
      const nextSelectedTags = checked ? [tag] : selectedPairs.filter(t => t !== tag);
      setselectedPairs(nextSelectedTags);
    }
  }
 
  useEffect(() => {
    if (!mode) {
      setLoading(true)
    } else {
      setFuturesDaily(mode?.data);
      setLoading(false)
    }
  }, [mode])

  useEffect(() => {
    if (props.battleParams) {
      let parami = props.battleParams.pairs
      parami && !_.isEqual(parami, selectedPairs) && setselectedPairs(parami)
    } else {
      console.log('no default for pairs');
      // props.callbackFN({ fname: 'pairs', fvar: ["BTCUSDT"] });

    }
  }, [props.battleParams])

  const refresh = async () => {
    setLoading(true)
    try {
      setFuturesDaily([]);
      var dt = await BinanceFuturesDaily();
      // console.log('dt', dt)
      var arr = []
      setFuturesDaily(dt?.data);
      setMode(dt);
      setLoading(false)
    } catch (e) {
      setLoading(false)
    }
  }

  const selectA = () => {
    setselectedPairs(Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [])

    mode && props.callbackFN({ fname: 'pairs', fvar: Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [] });
    mode && Array.isArray(futuresDaily) && setlastpair((Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : []).slice(-1).pop());

  }
  const setRandomPairs = (n) => {
    var array = (Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [])
    const shuffled = array.sort(() => 0.5 - Math.random());
    let selected = shuffled.slice(0, n);
    setselectedPairs(selected);
    mode && props.callbackFN({ fname: 'pairs', fvar: selected });
    mode && Array.isArray(selected) && setlastpair(selected.slice(-1).pop());
  }
  const [minVolume, setMinVolume] = useState('')
  const fnSelectMinVolume = (vals) => {
    setMinVolume(vals?.target.value ? parseFloat(vals?.target.value) : '')
    // setMinVolume(Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [])
  }

  const fnSetMinVolumeFilter = () => {
    var array = (Array.isArray(futuresDaily) ? futuresDaily.filter(p => p.vn > parseFloat(minVolume)*1000000).map(p => p.s) : [])
    setselectedPairs(array);
    mode && props.callbackFN({ fname: 'pairs', fvar: array });
    mode && Array.isArray(array) && setlastpair(array.slice(-1).pop());
  }

  const openlink = () => {
    var urlX = 'https://www.tradingview.com/chart?symbol=BINANCE%3A' + lastpair + 'PERP';
    console.log('', new Date().toISOString(), lastpair);
    lastpair && window.open(urlX, "_blank");
  }

  const savePairs2Sandbox = async () => { 
    let uri = '/api/pub/ai/sandbox_savedata'
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + props.token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify({ dataName: 'pairs', dataValue: selectedPairs }),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        return
      }

      const datax = await res.json();

      if (!datax.error) {
        alert('pairs saved!')
      } else {
        console.log('err desc', datax);
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
    }
  }
 
  return (
    <>
      <div style={{width: '100%'}}>
      <Box
        sx={{
          mr: 4,
          flex: 1,
          flexGrow: 1,
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          pb: 1,
          width: '100%',
        }}>

        <Chip key={'219151bc'}
          size='small'
          style={{ border: '1px solid #ccc', marginRight: 5 }}
          sx={{ borderRadius: 2, backgroundColor: '#378CE7', color: '#fff' }}
          label={'Tazele'}
          onClick={() => refresh()}
        />
        {loading && <CircularProgress color="inherit" size={16} />}
        {Array.isArray(futuresDaily) && futuresDaily.length !== 0 && (
          <>

            <Chip key={'219151b'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#378CE7', color: '#fff' }}
              label={loading ? 'yükleniyor..' : isOpened ? 'Gizle' : 'Göster'}
              onClick={() => setisOpened(!isOpened)}
            />

            <Chip
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'Favoriler'}
              key={'1'}
              onClick={() => {
                setselectedPairs(Favoriler);

                mode && props.callbackFN({ fname: 'pairs', fvar: Favoriler });
                mode && Array.isArray(Favoriler) && setlastpair(Favoriler.slice(-1).pop());
              }}
            />

            <Chip key={'1992'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'Rx'}
              onClick={() => 
                {
                  let px = ["ADAUSDT", "BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT"]
                  setselectedPairs(px);
                
                  mode && props.callbackFN({ fname: 'pairs', fvar: px });
                  mode && Array.isArray(px) && setlastpair(px.slice(-1).pop());
                }
              }
            />

            <Chip key={'19'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'R2'}
              onClick={() => setRandomPairs(2)}
            />

            <Chip key={'219'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'R5'}
              onClick={() => setRandomPairs(5)}
            />

            <Chip key={'21915'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'R15'}
              onClick={() => setRandomPairs(15)}
            />

            <Tooltip key={'minVolumeX'} title={'Set Minimum Volume For Selection (xMillion)'}>
              <FormControl>
              <CssTextField
                id={'candleQty'}
                size="small"
                // defaultValue={minVolume ? minVolume : '0'}
                value={minVolume}
                onChange={fnSelectMinVolume} 
                style={{
                  width: '70px',
                  marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0
                }}
              /></FormControl>
            </Tooltip>
            <Chip key={'219151bct'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 1, backgroundColor: '#67C6E3', color: '#000' }}
              label={'set'}
              onClick={fnSetMinVolumeFilter}
            />
            <Chip key={'219151'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'Hepsi'}
              onClick={selectA}
            />

            <Chip key={'219151c'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3' }}
              label={'Hiçbirisi'}
              onClick={() => {
                mode && props.callbackFN({ fname: 'pairs', fvar: [] });
                mode && setlastpair(null);
                setselectedPairs([])
              }}
            />

            <Chip key={'219151a'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#67C6E3', minWidth: 45 }}
              label={'Selected: #' + Array.isArray(selectedPairs) && selectedPairs.length}
            />

            <Chip key={'219151bctradingx'}
              size='small'
              style={{ border: '1px solid #ccc', marginRight: 5 }}
              sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
              label={'TV'}
              onClick={openlink}
            />

            <Chip key={'219151bctradingy'}
              size='small'
              sx={{ borderRadius: 2, color: '#000', m: '2px' }}
              // style={{ border: '1px solid #ccc', marginRight: 5 }}
              // sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
              label={'Save Pairs 2 Sandbox'}
              onClick={savePairs2Sandbox}
            />

            {/* <span onClick={savePairs2Sandbox} title={'Save Selected Pairs to Sandbox'} style={{fontSize: 10, color: '#ccc', marginLeft: 5, cursor: 'pointer'}}>{'sv'}</span> */}


          </>
        )}



      </Box>

      {isOpened && (
        <Box sx={{width: '100%', overflow: 'auto', maxHeight: 300}}>
          {/* <div className="flex flex-wrap mt-2 py-2 h-48 overflow-auto border"> */}
          {Array.isArray(futuresDaily) && futuresDaily.map(tag => {
            return (
              <Tooltip title={'Hacim: ' + tag.v} key={tag.s}>
                <Chip
                  style={{ border: '1px solid #ccc', marginRight: 5 }}
                  checked={selectedPairs.indexOf(tag.s) > -1}
                  onClick={() => handleChange(tag.s, !(selectedPairs.indexOf(tag.s) > -1))}
                  label={tag.s}
                  color={selectedPairs.indexOf(tag.s) > -1 ? 'primary' : undefined}
                  variant={'filled'}
                  sx={{ borderRadius: 1, marginTop: 0.5, marginBottom: 0.5, fontSize: 11 }}
                  size='small'
                // variant={selectedTags.indexOf(tag) > -1 ? 'filled' : 'outlined'}
                />
              </Tooltip>

            )
          })}
        {/* </div> */}
        </Box>
      )}
      </div>

    </>
  );
}

export default CardParamsPairs;

import { styled } from '@mui/material/styles';
import { FormControl, createTheme } from '@mui/material';

const theme = createTheme({
  components: {
    MuiInputBase: {
      defaultProps: {
        disableInjectingGlobalStyles: true,
      },
    },
  },
});

const CssTextField = styled(TextField)({
  '& label.Mui-focused': {
    color: '#A0AAB4',
  },
  '& .MuiInputBase-input': {
    borderRadius: 8,
    position: 'relative',
    // backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
    // border: '1px solid',
    // borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
    // fontSize: 14,
    // width: 'auto',
    // marginLeft: '5px',
    padding: '4px 6px',
    fontSize: '11px',
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:focus': {
      // boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      // borderColor: theme.palette.primary.main,
    },
  },
});

function useStickyState(defaultValue, key) {
  const [value, setValue] = React.useState(defaultValue);
  // console.log('useStickyState', defaultValue, key)
  React.useEffect(() => {
    const stickyValue = window.localStorage.getItem(key);
    if (stickyValue && stickyValue !== 'null') {
      // console.log('stickyValue2', JSON.parse(stickyValue))
      try {setValue(JSON.parse(stickyValue));} catch (e) {
        console.log('battle pairs error stickyValue', stickyValue, e)
      }
    } else {
      const fData = async () => {
        // console.log('fData call binance')
        var dt = await BinanceFuturesDaily();
        // console.log('fData JSON.stringify(data)',  JSON.stringify(dt.data))
        window.localStorage.setItem(key, JSON.stringify(dt));
        setValue(JSON.parse(JSON.stringify(dt)));
      }
      fData();
    }
  }, [key]);

  React.useEffect(() => {
    window.localStorage.setItem(key, JSON.stringify(value));
  }, [key, value]);

  return [value, setValue];
}

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

function BinanceFuturesDaily(pair) {
  return new Promise(async (resolve) => {
    try {
      var uri = '/api/pub/data/market_futuresdaily_live?paironly=0'
      uri += pair ? '&pair=' + pair : '' 
      console.log(uri)
      const data = await fetcher(uri)  
      resolve(data)
    } catch(e) {
      console.log('fetch err', e)
    }
  });
}


const numero = (num, dig = 2) => {
  if (num) {
      var resp = parseFloat(num).toLocaleString('tr-TR', {
          minimumFractionDigits: dig,
          maximumFractionDigits: dig
      });
      //console.log('numero', num, resp);
      return resp;
  } else {
      return false;
  }
}

