/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState, useEffect, useRef } from 'react';

import Chip from '@mui/material/Chip';
import Select from '@mui/material/Select';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Card from '@mui/material/Card';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Collapse from '@mui/material/Collapse';

import { alpha, styled } from '@mui/material/styles';
import { GlobalStyles, createTheme, ThemeProvider, Divider } from '@mui/material';

import Switch from '@mui/material/Switch';

import RadioGroup, { useRadioGroup } from '@mui/material/RadioGroup';

import Radio from '@mui/material/Radio';
import FormControlLabel from '@mui/material/FormControlLabel';

import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { red } from '@mui/material/colors';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Avatar from '@mui/material/Avatar';
import NotificationImportantRounded from '@mui/icons-material/NotificationImportantRounded';
import WalletIcon from '@mui/icons-material/Wallet';
import DirectionsRunIcon from '@mui/icons-material/DirectionsRun';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

import Paper from '@mui/material/Paper';
import _ from 'lodash';

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

const defaults = {
  enterPositon: true,
  actOnBarClose: true,
  direction: 'long',
  positionBudget: 100,
  positionMaxBudget: 400,
  addAdditionPosition: true,
  additionPositionPercentage: 1,
  additionPosRefPriceIsAvg: true,
  additionPositionCandleInterval: 3,
  useMartingaleVar: false,
  useMartingaleLevels: [],
}
const defaults_exit = {
  takeProfitRatio: 2,
  stopLoss: '50',
  stopLossUsePercentage: true,
  stopLossPercentage: '4',
  useTrailingStop: true,
  actOnBarClose: true,
}
const defaults_wallet = {
  usePacalMode: false,
  walletBudget: '400',
  walletLeverage: '20',
  pacalTakeProfitUSD: '100',
  pacalStopLossUSD: '400',
  pacalMaxPairs: '20',
}

const CardTrading = props => {
  const [battleIXVal, setBattleIXVal] = useState({});

//   const { status, data: session } = useSession({
//     required: true,
//     onUnauthenticated() {
//         signIn();
//     },
// });

  // useEffect(() => {
  //   // console.log('iterate entry', defaults);
  //   let entryStg = {}
  //   for (let d in defaults) {
  //     // console.log('ddsdas', d, defaults[d])
  //     entryStg[d] = defaults[d];
  //     // handleParams('entry', d, defaults[d])
  //   }
  //   props.setbattleParams && props.setbattleParams({
  //     fname: 'trading', fvar: {
  //       entry: entryStg
  //     },
  //   });
  // }, [])

  useEffect(() => {
    if (props.battleParams && (props.battleParams.trading)) {
      // console.log('props.battleParams', props.battleParams.trading)
      let parami = props.battleParams.trading;
      // console.log('props.battleParams', parami);
      parami && !_.isEqual(parami, battleIXVal) && setBattleIXVal(parami)
    } else {
      let entryStg = {}
      for (let d in defaults) {
        entryStg[d] = isNaN(parseFloat(defaults[d])) ? defaults[d] : parseFloat(defaults[d]);
      }

      let exitStg = {}
      for (let d in defaults_exit) {
        exitStg[d] = isNaN(parseFloat(defaults_exit[d])) ? defaults_exit[d] : parseFloat(defaults_exit[d]); //defaults_exit[d];
      }

      let walletStg = {}
      for (let d in defaults_wallet) {
        walletStg[d] = isNaN(parseFloat(defaults_wallet[d])) ? defaults_wallet[d] : parseFloat(defaults_wallet[d]); // defaults_wallet[d];
      }

      const fvar = {
        entry: entryStg,
        exit: exitStg,
        wallet: walletStg,
      };
      // console.log('fvar', fvar)
      props.setbattleParams && props.setbattleParams({
        fname: 'trading', fvar: fvar,
      });
    }
  }, [props.battleParams])

  const handleParams = (section, key, value) => {
    // console.log('handleParams', section, key, value)
    let currVals = JSON.parse(JSON.stringify(battleIXVal));
    let stg1 = currVals[section] || {};
    let tbVal = isNaN(parseFloat(value)) ? value : parseFloat(value);
    stg1[key] = tbVal;
    currVals[section] = stg1;
    setBattleIXVal(currVals);
    props.setbattleParams && props.setbattleParams({
      fname: 'trading', fvar: currVals
    });

  }
  return (
    <>
    <CardBattleType {...props} handleParams={handleParams} initialvalues={battleIXVal} />
      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
        <Box sx={{ flex: 4 }}>
          <CardEntry {...props} handleParams={handleParams} initialvalues={battleIXVal} />
        </Box>
        <Box sx={{ flex: 2 }}>
          <CardExit {...props} handleParams={handleParams} initialvalues={battleIXVal} />
        </Box>
        <Box sx={{ flex: 2 }}>
        <CardWallet {...props} handleParams={handleParams} initialvalues={battleIXVal}  />

        </Box>
      </Stack>
        {/* <Typography sx={{width: '1200px', overflow: 'auto'}}>{JSON.stringify(battleIXVal)}</Typography> */}
    </>
  );
};
export default CardTrading;

const theme = createTheme({
  components: {
    MuiInputBase: {
      defaultProps: {
        disableInjectingGlobalStyles: true,
      },
    },
  },
});

const CssTextField = styled(TextField)({
  '& label.Mui-focused': {
    color: '#A0AAB4',
  },
  '& .MuiInputBase-input': {
    borderRadius: 8,
    position: 'relative',
    // backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
    // border: '1px solid',
    // borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
    fontSize: 14,
    // width: 'auto',
    // marginLeft: '5px',
    padding: '6px 6px',
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:focus': {
      // boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      // borderColor: theme.palette.primary.main,
    },
  },
});

const CardEntry = props => {
  const [renderme, setrenderme] = useState(false);
  const [mounted, setmounted] = useState(false);
  const [updateMe, setupdateMe] = useState(0);
  const [martinLevels, setmartinLevels] = useState(props.initialvalues.entry?.useMartingaleLevels || []);
  const [mlevels, setmLevels] = useState(5);
  const [mlevelsDefaultMultiplier, setmlevelsDefaultMultiplier] = useState(2);
  const [useMartingaleVar, setuseMartingaleVar] = useState(props.initialvalues.entry?.useMartingaleVar);

  useEffect(() => {
    if (!_.isEmpty(props.initialvalues) && !mounted) {
      setrenderme(true);
      setuseMartingaleVar(props.initialvalues.entry?.useMartingaleVar || false)
      setmartinLevels(props.initialvalues.entry?.useMartingaleLevels || [])
      if (props.initialvalues.entry?.useMartingaleLevels) {
        setmLevels(props.initialvalues.entry?.useMartingaleLevels.length && props.initialvalues.entry?.useMartingaleLevels.length > 0 ? props.initialvalues.entry?.useMartingaleLevels.length: 1 )
        // setmlevelsDefaultMultiplier
      } else {
        setmLevels(2)
        setmlevelsDefaultMultiplier(4)
      };

      if (props.initialvalues.entry?.useMartingaleVar && props.initialvalues.entry?.useMartingaleLevels) {
        Array.isArray(props.initialvalues.entry?.useMartingaleLevels) && setmlevelsDefaultMultiplier(props.initialvalues.entry?.useMartingaleLevels[0]?.multiplier)
      } else {
        setmlevelsDefaultMultiplier(2)
      }

      setmounted(true)
    }
  }, [props.initialvalues])
  
  const handleChange = (section, valueType, value) => {
    let val = valueType == 'checkbox' ? value.target.checked : value?.target?.value 
    props.handleParams && props.handleParams('entry', section, val);
  };

  const setLevel = (l, v) => {
    let arr = martinLevels;
    let arrx = arr.find(a => a.level == l);
    arrx.multiplier = parseFloat(v);
    setmartinLevels(arr);
    setupdateMe(Date.now());
  }

  useEffect(() => {
    if (Array.isArray(martinLevels) && mounted ) {
      props.handleParams && props.handleParams('entry', 'useMartingaleLevels', useMartingaleVar ? martinLevels : []);
    }
  }, [martinLevels, updateMe])

  useEffect(() => {
    if (Array.isArray(martinLevels) && martinLevels.length !== 0) {
      if (martinLevels.length !== parseFloat(mlevels) && !isNaN(parseFloat(mlevels))) {
        if (mlevels !== '' &&
          mlevels.length !== 0 &&
          parseFloat(mlevels) !== 0) {
          let stg = [];
          for (let i = 1; i < parseInt(mlevels) + 1; i++) {
            stg.push({
              level: i,
              multiplier: (mlevelsDefaultMultiplier ? parseFloat(mlevelsDefaultMultiplier) : 2) * Math.pow( 2 , i-1)  
            });
          };
          setmartinLevels(stg);
          setupdateMe(Date.now());
        }
      } else { 
        let arrStg = martinLevels;
        arrStg.map((a, i) => {
          a.multiplier = (mlevelsDefaultMultiplier ? parseFloat(mlevelsDefaultMultiplier) : 2) * Math.pow( 2 , i) //parseFloat(mlevelsDefaultMultiplier);
        });
        setmartinLevels(arrStg);
        setupdateMe(Date.now());
      }
    }
  }, [mlevelsDefaultMultiplier, mlevels])


  const useMartingale = e => {
    setuseMartingaleVar(e.target?.checked);
    if (!e.target?.checked) {
      setmartinLevels([])
    } else {
      let stg = [];
      // console.log('mlevels', mlevels, 'mlevelsDefaultMultiplier', mlevelsDefaultMultiplier)
      for (let i = 1; i < parseInt(mlevels) + 1; i++) {
        stg.push({
          level: i,
          multiplier: (mlevelsDefaultMultiplier ? parseFloat(mlevelsDefaultMultiplier) : 2) * Math.pow( 2 , i - 1)  //mlevelsDefaultMultiplier ? parseFloat(mlevelsDefaultMultiplier) : 2
        });
      }
      setmartinLevels(stg);
    }
    handleChange('useMartingaleVar', 'checkbox', e);
    setupdateMe(Date.now());
    // handleChange('var')
  }

  return (
    <>
      <Paper sx={{ minWidth: 160, overflow: 'auto', padding: 1, m: 4, minHeight: 300, borderWidth: 0.5, margin: 2, flex: 1 }}>
        {renderme && (
          <Card sx={{ minWidth: 345, boxShadow: 0 }}>
          <CardHeader
            sx={{ background: '#ffddaa' }}
            avatar={
              <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                <ShoppingCartIcon />
              </Avatar>
            }
            action={
              <IconButton aria-label="settings">
                <MoreVertIcon />
              </IconButton>
            }
            title="Position Entry"
            subheader="Setup"
          />

            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 110 }} fontSize={12} >
                Enter Position
              </Typography>
              <Tooltip key={'posEntryTool'} placement="top"
                sx={{ padding: 0 }}
                title={'Enter positions'}>
                <FormControl>
                  <Switch
                    checked={props.initialvalues.entry.enterPositon}
                    onChange={(e) => handleChange('enterPositon', 'checkbox', e)} inputProps={{ 'aria-label': 'posEntryTool' }} />
                </FormControl>
              </Tooltip>
            </Box>
            <Divider />

            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 110, }} fontSize={12} >
                Act on bar close
              </Typography>
              <Tooltip key={'tt_ao1'} placement="top"
                sx={{ padding: 0 }}
                title={'Act on bar close'}>
                <FormControl>
                  <Switch checked={props.initialvalues.entry.actOnBarClose || defaults.actOnBarClose}
                    onChange={(e) => handleChange('actOnBarClose', 'checkbox', e)} disabled inputProps={{ 'aria-label': 'tt_ao1' }} />
                </FormControl>
              </Tooltip>
            </Box>
            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 110 }} fontSize={12} >
                Direction
              </Typography>
              <Tooltip key={'tt_dir'} placement="top"
                sx={{ padding: 0 }}
                title={'Direction'}>

                <FormControl>
                  <Select
                    labelId="demo-simple-select-standard-label"
                    id="demo-simple-select-standard"
                    value={props.initialvalues?.entry?.direction || defaults.direction}
                    // onChange={handleChange}
                    onChange={(e) => handleChange('direction', 'listbox', e)}
                    label={'Direction'}
                    size="small"
                    sx={{ width: 95, padding: 0 }}
                  >
                    <MenuItem key={'long'} value={'long'}>{'long'}</MenuItem>
                    <MenuItem key={'short'} value={'short'}>{'short'}</MenuItem>
                    <MenuItem key={'both'} value={'both'}>{'both'}</MenuItem>
                  </Select>
                </FormControl>
              </Tooltip>
            </Box>

            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Position Budget
              </Typography>

              <Tooltip key={'ttPB'} placement="top"
                sx={{ padding: 0 }}
                title={'Position Budget'}>

                <CssTextField id="it_posbudget"
                  size="small"
                  defaultValue={props.initialvalues.entry.positionBudget}
                  // value={props.initialvalues.entry.positionBudget}
                  onChange={(e) => handleChange('positionBudget', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>

            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Max Pos Budget
              </Typography>

              <Tooltip key={'ttPB12'} placement="top"
                sx={{ padding: 0 }}
                title={'Position Budget'}>

                <CssTextField id="it_maxBudget"
                  size="small"
                  defaultValue={props.initialvalues.entry.positionMaxBudget || defaults.positionMaxBudget}
                  onChange={(e) => handleChange('positionMaxBudget', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>
            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 110 }} fontSize={12} >
                Additional Position
              </Typography>
              <Tooltip key={'tt_add1'} placement="top"
                sx={{ padding: 0 }}
                title={'Add Additional Positions'}>

                <FormControl>
                  <Switch checked={props.initialvalues.entry.addAdditionPosition}
                    onChange={(e) => handleChange('addAdditionPosition', 'checkbox', e)} inputProps={{ 'aria-label': 'ant design' }} />
                </FormControl>
              </Tooltip>

              <Tooltip key={'tt_add2'} placement="top"
                sx={{ padding: 0, marginLeft: 5 }}
                title={'Addition Position Percentage'}>
                <CssTextField id="idadditionalPozRatio"
                  size="small"
                  defaultValue={props.initialvalues.entry.additionPositionPercentage || defaults.additionPositionPercentage}
                  onChange={(e) => handleChange('additionPositionPercentage', 'text', e)}
                  style={{ width: '80px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
              % 
              <Tooltip key={'tt_useAvgAsRefPrice'} placement="top"
                sx={{ padding: 0, marginLeft: 5 }}
                title={'Use Avg Price as Ref Price'}>

                <FormControl>
                  <Switch checked={props.initialvalues.entry.additionPosRefPriceIsAvg}
                    onChange={(e) => handleChange('additionPosRefPriceIsAvg', 'checkbox', e)} inputProps={{ 'aria-label': 'tt_useAvgAsRefPrice' }} />
                </FormControl>
              </Tooltip>

            </Box>
            <Divider />

            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Candle Interval
              </Typography>

              <Tooltip key={'tt_key1C'} placement="top"
                sx={{ padding: 0 }}
                title={'Intervals between next orders (Candle)'}>

                <CssTextField id="it_candles"
                  size="small"
                  defaultValue={props.initialvalues.entry.additionPositionCandleInterval}
                  onChange={(e) => handleChange('additionPositionCandleInterval', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>
            <Divider />

            
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start'
              }}>
              <Typography sx={{ minWidth: 105, mt: 1}} fontSize={12} >
                Martingale Mx
              </Typography>
              <Stack sx={{display: 'flex',flexDirection: 'row', alignItems: 'flex-start'}}>
                <Box sx={{ borderWidth: 0.5, p: 0.5 }}>
                  {/* <Stack>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>
                      <Tooltip key={'tt_madd1'} placement="top"
                        sx={{ padding: 0 }}
                        title={'Use martingale multiplier on orders.'}>

                        <FormControl>
                          <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>
                            <Switch checked={useMartingaleVar}
                              onChange={(e) => useMartingale(e)} inputProps={{ 'aria-label': 'ant design' }} />
                            <Typography onClick={() => setuseMartingaleVar(!useMartingaleVar)} sx={{ fontSize: '12px', cursor: 'pointer' }}>stopLoss</Typography>
                          </Stack>
                        </FormControl>
                      </Tooltip> 
                    </Stack>

                    {useMartingaleVar && (
                      <>
                        <Divider sx={{ mb: 1 }} />
                        <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Typography sx={{ fontSize: 9 }}>
                            Multiplier
                          </Typography>
                          <Tooltip key={'tt_madd2'} placement="top"
                            sx={{ padding: 0, marginLeft: 5 }}
                            title={'Multiplier'}>
                            <CssTextField id="iMmultiplier"
                              size="small"
                              defaultValue={mlevelsDefaultMultiplier}
                              onChange={(e) => setmlevelsDefaultMultiplier(e.target.value)}
                              style={{ width: '50px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                            />
                          </Tooltip>

                          <Typography sx={{ fontSize: 9, ml: 2 }}>
                            Depth L.
                          </Typography>
                          <Tooltip key={'ttmadd21'} placement="top"
                            sx={{ padding: 0, marginLeft: 5 }}
                            title={'Level Depth'}>
                            <CssTextField id="iMlevelDepth"
                              size="small"
                              defaultValue={mlevels}
                              onChange={(e) => setmLevels(e.target.value)}
                              style={{ width: '50px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                            />
                          </Tooltip>

                        </Stack>
                        <Divider sx={{ pb: 1 }} />
                        <Stack>
                          {Array.isArray(martinLevels) && martinLevels.length !== 0 && martinLevels.map((m, mix) => {
                            return (
                              <Stack key={mix.toString()} sx={{ flexDirection: 'row', alignItems: 'center', mt: 1 }}>
                                <Typography sx={{ fontSize: 9, mr: 1 }}>
                                  Level {m.level}
                                </Typography>
                                <Tooltip key={'ttmadd21'} placement="top"
                                  sx={{ padding: 0, marginLeft: 5 }}
                                  title={'Level Depth'}>
                                  <CssTextField id="iMlevelDepth"
                                    size="small"
                                    defaultValue={m.multiplier}
                                    value={m.multiplier}
                                    onChange={(e) => setLevel(m.level, e.target.value)}
                                    style={{ width: '60px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                                  />
                                </Tooltip>
                              </Stack>
                            )
                          })}
                        </Stack>
                      </>
                    )}
                  </Stack> */}
                </Box>
                <MartingaleSetup typeM={'stopLoss'} {...props} />
                <MartingaleSetup typeM={'takeProfit'} {...props} />
              </Stack>
            </Box>
          </Card>
        )}
      </Paper>
    </>
  )
}

const CardWallet = props => {
  const [renderme, setrenderme] = useState(false);
  useEffect(() => {
    // !_.isEmpty(props.initialvalues) && console.log('initialvalues', props.initialvalues)
    !_.isEmpty(props.initialvalues) && setrenderme(true)
  }, [props.initialvalues])
  
  const handleChange = (section, valueType, value) => {
    let val = valueType == 'checkbox' ? value.target.checked : value?.target?.value 
    props.handleParams && props.handleParams('wallet', section, val);
    // console.log('seci', section, val);
  }
  return (
    <>
      <Paper sx={{ minWidth: 160, overflow: 'auto', padding: 1, m: 4, minHeight: 300, borderWidth: 0.5, margin: 2, flex: 1 }}>
        {renderme && (
          <Card sx={{ boxShadow: 0 }}>
            <CardHeader
              sx={{ background: '#ffddaa' }}
              avatar={
                <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                  <WalletIcon />
                </Avatar>
              }
              action={
                <IconButton aria-label="settings">
                  <MoreVertIcon />
                </IconButton>
              }
              title="Wallet Management"
              subheader="Setup"
            />

            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 110 }} fontSize={12} >
                Use Pacal Mode
              </Typography>
              <Tooltip key={'tt_usePacalMode'} placement="top"
                sx={{ padding: 0 }}
                title={'Use Pacal Mode'}>
                <FormControl>
                  <Switch
                    checked={props.initialvalues?.wallet?.usePacalMode}
                    onChange={(e) => handleChange('usePacalMode', 'checkbox', e)} inputProps={{ 'aria-label': 'ant design' }} />
                </FormControl>
              </Tooltip>
            </Box>
            <Divider />

            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Max Wallet Budget
              </Typography>

              <Tooltip key={'tt_key1C1'} placement="top"
                sx={{ padding: 0 }}
                title={'Wallet Budget'}>
                <CssTextField id="it_walletBudget"
                  size="small"
                  defaultValue={props.initialvalues?.wallet?.walletBudget}
                  onChange={(e) => handleChange('walletBudget', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>
            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Wallet Leverage
              </Typography>

              <Tooltip key={'tt_walletLeverage'} placement="top"
                sx={{ padding: 0 }}
                title={'Wallet Leverage'}>

                <CssTextField id="it_walletLeverage"
                  size="small"
                  defaultValue={props.initialvalues?.wallet?.walletLeverage}
                  onChange={(e) => handleChange('walletLeverage', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>

            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Pacal TP USD
              </Typography>

              <Tooltip key={'tt_pacalTakeProfitUSD'} placement="top"
                sx={{ padding: 0 }}
                title={'pacal tp stop in usd'}>

                <CssTextField id="it_cpacalTakeProfitUSD"
                  size="small"
                  defaultValue={props.initialvalues?.wallet?.pacalTakeProfitUSD}
                  onChange={(e) => handleChange('pacalTakeProfitUSD', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>
            <Divider />

            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Pacal SL USD
              </Typography>

              <Tooltip key={'tt_pacalStopLossUSD'} placement="top"
                sx={{ padding: 0 }}
                title={'pacal sl stop in usd'}>

                <CssTextField id="it_pacalStopLossUSD"
                  size="small"
                  defaultValue={props.initialvalues?.wallet?.pacalStopLossUSD}
                  onChange={(e) => handleChange('pacalStopLossUSD', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>
            <Divider />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 105 }} fontSize={12} >
                Max Pairs
              </Typography>

              <Tooltip key={'tt_pacalMaxPairs'} placement="top"
                sx={{ padding: 0 }}
                title={'pacal max pairs'}>

                <CssTextField id="it_pacalMaxPairs"
                  size="small"
                  defaultValue={props.initialvalues?.wallet?.pacalMaxPairs}
                  onChange={(e) => handleChange('pacalMaxPairs', 'text', e)}
                  style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Box>
          </Card>
        )}
      </Paper>
    </>
  )
}
 
const CardExit = props => {
  const [renderme, setrenderme] = useState(false);
  useEffect(() => {
    // !_.isEmpty(props.initialvalues) && console.log('initialvalues', props.initialvalues)
    !_.isEmpty(props.initialvalues) && setrenderme(true)
  }, [props.initialvalues])
  
  const handleChange = (section, valueType, value) => {
    let val = valueType == 'checkbox' ? value.target.checked : value?.target?.value 
    props.handleParams && props.handleParams('exit', section, val); 
  }

  return (
    <>
      <Paper sx={{ minWidth: 160, overflow: 'auto', padding: 1, m: 4, minHeight: 300, borderWidth: 0.5, margin: 2, flex: 1 }}>
        {renderme && (<Card sx={{ boxShadow: 0 }}>
          <CardHeader
            sx={{ background: '#ffddaa' }}
            avatar={
              <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                <DirectionsRunIcon />
              </Avatar>
            }
            action={
              <IconButton aria-label="settings">
                <MoreVertIcon />
              </IconButton>
            }
            title="Position Exit"
            subheader="Setup"
          />

          <Box
            sx={{
              p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
            }}>
            <Typography sx={{ minWidth: 105 }} fontSize={12} >
              Take Profit %
            </Typography>

            <Tooltip key={'tt_key1C'} placement="top"
              sx={{ padding: 0 }}
              title={'take profit ratio'}>
              <CssTextField id="it_takeProfitRatio"
                size="small"
                defaultValue={props.initialvalues.exit?.takeProfitRatio}
                // value={props.initialvalues.entry.positionBudget}
                onChange={(e) => handleChange('takeProfitRatio', 'text', e)}
                style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
              />
            </Tooltip>
          </Box>

          <Divider />
          <Box
            sx={{
              p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
            }}>
            <Typography sx={{ minWidth: 105 }} fontSize={12} >
              Stop Loss USD
            </Typography>

            <Tooltip key={'tt_key1DC'} placement="top"
              sx={{ padding: 0 }}
              title={'Stop Loss USD'}>

              <CssTextField id="it_stopLoss"
                size="small"
                defaultValue={props.initialvalues.exit?.stopLoss}
                onChange={(e) => handleChange('stopLoss', 'text', e)}
                style={{ width: '75px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
              />
            </Tooltip>
          </Box>

          <Divider />
          <Box
            sx={{
              p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
            }}>
            <Typography sx={{ minWidth: 110 }} fontSize={12} >
              Stop Loss %
            </Typography>
            <Tooltip key={'tt_stopLossUsePercentage'} placement="top"
              sx={{ padding: 0 }}
              title={'Use Stop Loss with %'}>

              <FormControl>
                <Switch
                  onChange={(e) => handleChange('stopLossUsePercentage', 'checkbox', e)}
                  checked={props.initialvalues.exit?.stopLossUsePercentage} inputProps={{ 'aria-label': 'ant design' }} />
              </FormControl>
            </Tooltip>

            <Tooltip key={'tt_add2'} placement="top"
              sx={{ padding: 0, marginLeft: 5 }}
              title={'Stop Loss %'}>
              <CssTextField id="it_stopLossPercentage"
                size="small"
                defaultValue={props.initialvalues.exit?.stopLossPercentage}
                onChange={(e) => handleChange('stopLossPercentage', 'text', e)}
                style={{ width: '80px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
              />
            </Tooltip>

          </Box>
          <Divider />
          <Box
            sx={{
              p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
            }}>
            <Typography sx={{ minWidth: 110 }} fontSize={12} >
              Trailing Stop
            </Typography>
            <Tooltip key={'useTrailingStop'} placement="top"
              sx={{ padding: 0 }}
              title={'use Trailing Stop'}>
              <FormControl>
                <Switch
                  onChange={(e) => handleChange('useTrailingStop', 'checkbox', e)}
                  checked={props.initialvalues.exit?.useTrailingStop} inputProps={{ 'aria-label': 'ant design' }} />
              </FormControl>
            </Tooltip>
          </Box>
          <Divider />
          <Box
            sx={{
              p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
            }}>
            <Typography sx={{ minWidth: 110, }} fontSize={12} >
              Act on bar close
            </Typography>
            <Tooltip key={'tt_actOnBarClose'} placement="top"
              sx={{ padding: 0 }}
              title={'Act on bar close'}>
              <FormControl>
                <Switch
                  onChange={(e) => handleChange('actOnBarClose', 'checkbox', e)}
                  checked={props.initialvalues.exit?.actOnBarClose} disabled inputProps={{ 'aria-label': 'ant design' }} />
              </FormControl>
            </Tooltip>
          </Box>
        </Card>)}
      </Paper>
    </>
  )
}

const CardBattleType = props => {
  const key = 'dexvar';
  const {token, user} = props;
  const [battleIXVal, setBattleIXVal] = useState({});
  const [renderme, setrenderme] = useState(false);
  const [collapsed, setcollapsed] = useState(false);
  const [collapsedDexSetup, setcollapsedDexSetup] = useState(false);
  const [battleType, setbattleType] = useState('simulator');
  const [testLoader, settestLoader] = useState(false);
  const [dexvalues, setdexvalues] = useState({});
  // const [updateMe, setupdateMe] = useState(0);
  // const [dexCode, setdexCode] = useState(false);
  // const [selectedDex, setselectedDex] = useState(false);
  useEffect(() => {
    // !_.isEmpty(props.initialvalues) && console.log('initialvalues', props.initialvalues)
    !_.isEmpty(props.initialvalues) && setrenderme(true)
  }, [props.initialvalues])
  
  
  const dexes = [
    {
      testDex: true,
      dexTitle: 'Binance - TestNet',
      dexCode: 'binanceTest',
      apiKey: 'a41dedd7ab4ef988097f552a3b82b241a98757b276e1251fec5e33d468aae96a',
      apiSecret: 'd5fdee826f4847635e4a40bfa2cb3f60462a147c102c2e418f9e436c70aeb7c0',
    },
    {
      testDex: true,
      dexTitle: 'Binance - TestNet B',
      dexCode: 'binanceTestB',
      apiKey: 'b146a7dfeac6fd71fd913be4658295ab48ef8400d3af0320ecaa93dd28d68346',
      apiSecret: 'bf3a7245bdf7d04d53652c42f6d4c003841e2d331ccf6682ccc522ece1cee771',
    },
    {
      testDex: false,
      dexTitle: 'Binance',
      dexCode: 'binance',
      apiKey: '',
      apiSecret: '',
    },
  ];

  useEffect(() => {
    if (props.battleParams && (props.battleParams?.battleType)) {
      let parami = props.battleParams?.battleType;
      parami && !_.isEqual(parami, battleIXVal) && setBattleIXVal(parami);
      if (parami && !_.isEqual(parami, battleIXVal)) {
        // console.log('parami', parami)
        setdexvalues(parami?.config || {});
        setbattleType(parami?.dex || 'simulator');
        parami?.dex !== 'simulator' && setcollapsedDexSetup(true);
        // console.log('dexValues', parami?.config);
        // console.log('battleType', parami?.dex);
      }
    } else { 
      props.setbattleParams && props.setbattleParams({
        fname: 'battleType', fvar: { dex: 'simulator' },
      });
    }
  }, [props.battleParams])

  const handleParams = (section, key, value) => {
    let currVals = JSON.parse(JSON.stringify(battleIXVal));
    let stg1 = currVals[key] || {};
    let tbVal = isNaN(parseFloat(value)) ? value : parseFloat(value);
    stg1 = tbVal;
    currVals[key] = tbVal
    props.setbattleParams && props.setbattleParams({
      fname: 'battleType', fvar: currVals
    });
  }

  const handleChange = (section, valueType, value) => {
    let val = valueType == 'checkbox' ? value.target.checked : (valueType !== 'manual' ? value?.target?.value : value);

    let setConfig2Null = false;
    if (section == 'dex') { 
      setbattleType(val)
      setConfig2Null = val !== 'exchange' 
      // setConfig2Null && setdexCode('');
      setConfig2Null && setdexvalues({});
      setConfig2Null && handleParams(null, 'config', {});
      setcollapsedDexSetup(val == 'exchange');
      handleParams(null, section, val);

    } else {
      // console.log('handle details for exchange!', section, val, value?.target, value);
      let dexvaluesStg = JSON.parse(JSON.stringify(dexvalues));
      // console.log('dexvaluesStg pre', dexvaluesStg,)
      dexvaluesStg[section] = val;
      let dStg = {};
      if (section == 'dexCode') {
        dStg = [...dexes].find(d => d.dexCode == val)
        dexvaluesStg = {
          ...dexvaluesStg,
          ...dStg,
        }
        if (dStg?.testDex) {
          dexvaluesStg['apiKey'] = dStg?.apiKey;
          dexvaluesStg['apiSecret'] = dStg?.apiSecret;
        } else {
          dexvaluesStg['apiKey'] = '';
          dexvaluesStg['apiSecret'] = '';
        }
        setdexvalues(dexvaluesStg);
        handleParams(null, 'config', dexvaluesStg);
      } else {
        // console.log('dexvaluesStg post', dexvaluesStg,)
        setdexvalues(dexvaluesStg);
        handleParams(null, 'config', dexvaluesStg);
      }
    }
  };

  const loadkeys = () => {
    let keyx = key + ':' + dexvalues.dexCode
    const stickyValue = window.localStorage.getItem(keyx);
    if (stickyValue) {
      console.log('load keys', keyx, stickyValue);
      let newValsS = JSON.parse(stickyValue);
      let dexvaluesStg = JSON.parse(JSON.stringify(dexvalues));
      let newVals = {
        ...dexvaluesStg,
        ...newValsS,
      };
      setdexvalues(newVals);
      handleParams(null, 'config', newVals);
      // handleChange('dexDatafromlocalcache', 'manual', dexvalues.dexCode);
    } else {
      console.log('no data found', keyx)
    }
  };
  const savekeys = () => {
    let lData = {
      apiKey: dexvalues.apiKey,
      apiSecret: dexvalues.apiSecret,
    }
    let keyx = key + ':' + dexvalues.dexCode
    console.log('savekeys keys', key, lData);
    window.localStorage.setItem(keyx, JSON.stringify(lData));
  };
  const deletekeys = () => {
    let lData = {}
    let keyx = key + ':' + dexvalues.dexCode
    console.log('delete keys', key, lData);
    window.localStorage.setItem(keyx, JSON.stringify(lData));
  };
  const testkeys = async () => {
    console.log('testkeys keys', dexvalues)

    settestLoader(true);
    let uri = '/api/pub/data/dextest'
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify(dexvalues),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        settestLoader(false)
        return
      }

      const datax = await res.json();
      settestLoader(false)
      // await battleList();

      if (!datax.error) {
        console.log('resp:', datax?.data)
        let respTxt = '';
        Array.isArray(datax.data?.balance) && datax?.data?.balance.map(d => {
          respTxt += d.asset + ' balance: ' + parseFloat(d.availableBalance).toFixed(2) + '\n';
        })
        alert('test ok!' + '\n' + respTxt);
        settestLoader(false);
      } else {
        console.log('err desc', datax);
        settestLoader(false)
        alert('test failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
      settestLoader(false)
    };

  };

  const dexInfo = async () => {

    try {
      var uri = '/api/pub/data/dexinfo'
      const data = await fetcher(uri)

      let keyx = key + ':' + dexvalues.dexCode + ':info'
      console.log('dexinfo data saved', keyx, data);
      window.localStorage.setItem(keyx, JSON.stringify(data));

    } catch (e) {
      console.log('fetch err', e)
    }

    // return false;
  }

  return (
    <>
      <Paper sx={{ minWidth: 160, overflow: 'auto', padding: 1, m: 4, borderWidth: 0.5, margin: 2, flex: 1 }}>
        {renderme && (
          <Card sx={{ boxShadow: 0 }}>
            <CardHeader
              sx={{ background: battleType == 'exchange' ? '#ffcc00' : '#ffddaa' }}
              avatar={
                <Avatar sx={{ bgcolor: battleType == 'exchange' ? red[800] : red[300] }} aria-label="recipe">
                  <NotificationImportantRounded />
                </Avatar>
              }
              action={
                <IconButton onClick={() => setcollapsed(!collapsed)} aria-label="settings">
                  <MoreVertIcon />
                </IconButton>
              }
              title="Battle Type Selection:"
              subheader={"" + (battleType == 'exchange' ? '!!! ' : '') + (battleType ? battleType : '')}
            />
            <Box
              sx={{
                p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
              }}>
              <Typography sx={{ minWidth: 110 }} fontSize={12} >
                Battle Type
              </Typography>
              <Tooltip key={'tt_battlePtype'} placement="top"
                sx={{ padding: 0 }}
                title={'select battle type, forward simulation or real trading...'}>
                <FormControl>

                  <RadioGroup
                    aria-labelledby="demo-radio-buttons-group-label"
                    defaultValue={battleType}
                    name="radio-buttons-group"
                    sx={{ flexDirection: 'row' }}
                    onChange={(e) => handleChange('dex', 'radio', e)}
                  // onChange={(e) => console.log('tee', e)}
                  >
                    <FormControlLabel checked={battleType == 'simulator'}
                      value="simulator" control={<Radio />} label="simulator" />
                    <FormControlLabel value="exchange" control={<Radio />} label="exchange" />
                  </RadioGroup>

                </FormControl>
              </Tooltip>
            </Box>
            <Collapse in={collapsedDexSetup}  >

              <Divider />

              <Box
                sx={{
                  p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
                }}>
                <Typography sx={{ minWidth: 110 }} fontSize={12} >
                  Exchange
                </Typography>


                <Tooltip key={'tt_battleExchange'} placement="top"
                  sx={{ padding: 0 }}
                  title={'select exchange...'}>
                  <FormControl>

                    <RadioGroup
                      aria-labelledby="demo-radio-buttons-group-label"
                      defaultValue="female"
                      name="radio-buttons-group"
                      sx={{ flexDirection: 'row' }}
                      onChange={(e) => handleChange('dexCode', 'radio', e)}
                    // onChange={(e) => console.log('tee', e)}
                    >
                      {dexes.map((d, di) => {
                        return (
                          <FormControlLabel
                            key={di.toString()}
                            checked={battleType == 'exchange' && dexvalues['dexCode'] == d.dexCode}
                            value={d.dexCode} control={<Radio />} label={d.dexTitle} />
                        )
                      })}
                    </RadioGroup>

                  </FormControl>
                </Tooltip>

              </Box>
              <Divider />
              <Box
                sx={{
                  p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
                }}>
                <Typography sx={{ minWidth: 105 }} fontSize={12} >
                  Api Key
                </Typography>

                <Tooltip key={'tt_apiKey'} placement="top"
                  sx={{ padding: 0 }}
                  title={'Api Key'}>

                  <CssTextField id="it_apiKey"
                    size="small"
                    defaultValue={dexvalues.apiKey || ''}
                    onChange={(e) => handleChange('apiKey', 'text', e)}
                    value={dexvalues.apiKey || ''}
                    // onChange={(e) => handleChange('walletLeverage', 'text', e)}
                    style={{ width: '100%', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                  />
                </Tooltip>
              </Box>
              <Box
                sx={{
                  p: 1, display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'
                }}>
                <Typography sx={{ minWidth: 105 }} fontSize={12} >
                  Api Secret
                </Typography>

                <Tooltip key={'tt_apiSecret'} placement="top"
                  sx={{ padding: 0 }}
                  title={'Api Secret'}>

                  <CssTextField id="it_apiSecret"
                    size="small"
                    defaultValue={dexvalues.apiSecret || ''}
                    onChange={(e) => handleChange('apiSecret', 'text', e)}
                    value={dexvalues.apiSecret || ''}
                    // onChange={(e) => handleChange('walletLeverage', 'text', e)}
                    style={{ width: '100%', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                  />
                </Tooltip>
              </Box>

              <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-end', mr: 1 }}>
                <Typography sx={{ cursor: 'pointer' }} onClick={loadkeys}>
                  load
                </Typography>
                &nbsp;|&nbsp;
                <Typography sx={{ cursor: 'pointer' }} onClick={savekeys}>
                  save
                </Typography>
                &nbsp;|&nbsp;
                <Typography sx={{ cursor: 'pointer' }} onClick={deletekeys}>
                  delete
                </Typography>
                &nbsp;|&nbsp;
                <Typography sx={{ cursor: 'pointer' }} onClick={testkeys}>
                  test {testLoader ? 'ing' : ''}
                </Typography>
                &nbsp;|&nbsp;
                <Typography sx={{ cursor: 'pointer' }} onClick={dexInfo}>
                  info {testLoader ? 'ing' : ''}
                </Typography>
              </Stack>
              {/* {JSON.stringify(dexvalues, ' ', 4)} */}

            </Collapse>
          </Card>
        )}
      </Paper>
    </>
  )
}

const MartingaleSetup = props => {
  const [typeM, settypeM] = useState(props.typeM || 'takeProfit');
  const [updateMe, setupdateMeR] = useState(0);
  const [useMartingaleVar, setuseMartingaleVar] = useState(false);
  const [martinLevels, setmartinLevels] = useState(props.levels || []);
  const [mlevels, setmLevels] = useState(5);
  const [mlevelsDefaultMultiplier, setmlevelsDefaultMultiplierR] = useState(2);
  
  const setupdateMe = (v, vals2Post, ref) => {
    setupdateMeR(v)
    // console.log('setupdateMe - v, vals2Post, ref', v, vals2Post, ref)
  }
  const useMartingale = e => {
    setuseMartingaleVar(e);
    if (!e) {
      setmartinLevels([]);
      setmLevels(5);
      setmlevelsDefaultMultiplier(2, true);
    } else {
      fnSetmLevels(mlevels)
    }
    setupdateMe(Date.now(), null, 'useMartingale');
  }
  const setmlevelsDefaultMultiplier = (v, reset = false) => {
    let e2b = v && v !== '' ? parseFloat(v) : v;
    if (!isNaN(parseFloat(e2b))) {
      setmlevelsDefaultMultiplierR(e2b);
      !reset && fnSetmLevels(mlevels, e2b, 'baseCarpanUpdated');
      setupdateMe(Date.now(), null, 'setmlevelsDefaultMultiplier');
    }
  }
  useEffect(() => {
    if (Array.isArray(martinLevels)) {
      console.log('updateMe -- post parent', martinLevels, props)
      props.handleParams && props.handleParams('entry', 'useMartingale_' + typeM, useMartingaleVar ? martinLevels : []);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateMe])

  // useEffect(() => {
  //   if (mlevels && mlevels !== '') {
  //     console.log('mlevels', mlevels, martinLevels)

  //   }
  // }, [mlevelsDefaultMultiplier, mlevels])

  const fnSetmLevels = (e, baseCarpan = mlevelsDefaultMultiplier, ref) => {
    // e = e && e !== '' ? e : ''
    let e2b = e && e !== '' ? parseFloat(e) : e;
    // console.log('baseCarpan', baseCarpan)
    if (!isNaN(parseFloat(e2b))) {
      let stg = [];
      for (let i = 1; i < parseInt(e2b) + 1; i++) {
        stg.push({
          level: i,
          multiplier: (baseCarpan ? parseFloat(baseCarpan) : 2) * Math.pow(2, i - 1)
        });
      };
      if (martinLevels.length !== 0 && ref !== 'baseCarpanUpdated') {
        martinLevels.map((m, i) => {
          stg[i] = m;
        });
        stg = stg.slice(0, parseInt(e2b));
      }
      setmartinLevels(stg);
      setupdateMe(Date.now(), stg, 'fnSetmLevels');
    }
    setmLevels(e2b);
  }

  const setLevel = (l, v, force0 = false, forceNum = true) => {
    let arr = martinLevels;
    let arrIdx = Array.isArray(arr) && arr.findIndex(a => a.level == l);
    // let e2b = v && v !== '' ? parseFloat(v.replace(/^(-)|[^0-9.,]+/g, '$1')) : v;
    let e2b = v && v !== '' ? v : ''; // Number(v.replace(/[^0-9.]/g, '')) : v;
    // if (arrIdx >= 0 && !isNaN(parseFloat(e2b))) {
    if (arrIdx >= 0) {
      arr[arrIdx].multiplier = !isNaN(parseFloat(e2b)) ? (forceNum ? parseFloat(e2b) : e2b) : (force0 ? 0 : '');
      // console.log('sasd', !isNaN(parseFloat(e2b)) ? (forceNum ? parseFloat(e2b) : e2b) : (force0 ? 0 : ''));
      // console.log('asdasd', v, e2b, forceNum, (forceNum ? parseFloat(e2b) : e2b))
      setmartinLevels(arr);
      setupdateMe(Date.now(), arr, 'setlevel');
    } else {
    }
  }

  useEffect(() => {
    if (props.initialvalues){
      const {entry} = props.initialvalues || {};
      let initTypeM = 'useMartingale_' + (props.typeM || 'takeProfit')
      let initialValue = entry[initTypeM]
      initialValue && setmartinLevels(initialValue);
      initialValue && setmLevels(initialValue.length);
      initialValue && setuseMartingaleVar(true);
      // console.log('mounted', initTypeM, entry, initialValue);
    }
  }, [])


  return (
    <>
      <Box sx={{ borderWidth: 0.5, p: 0.5, ml: 1, }}>
        <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>
          <Tooltip key={'tt_madd1'} placement="top"
            sx={{ padding: 0 }}
            title={'Use martingale multiplier on orders.'}>

            <FormControl>
              <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>
                <Switch checked={useMartingaleVar}
                  onChange={(e) => useMartingale(e.target?.checked)} inputProps={{ 'aria-label': 'ant design' }} />
                <Typography onClick={() => useMartingale(!useMartingaleVar)} sx={{ fontSize: '12px', cursor: 'pointer' }}>{typeM}</Typography>
              </Stack>
            </FormControl>
          </Tooltip>
        </Stack>
        {useMartingaleVar && (
          <>
            <Divider sx={{ mb: 1 }} />
            <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
              <Typography sx={{ fontSize: 9 }}>
                Multiplier
              </Typography>
              <Tooltip key={'tt_madd2'} placement="top"
                sx={{ padding: 0, marginLeft: 5 }}
                title={'Multiplier'}>
                <CssTextField id="iMmultiplier"
                  size="small"
                  defaultValue={mlevelsDefaultMultiplier}
                  onChange={(e) => setmlevelsDefaultMultiplier(e.target.value)}
                  style={{ width: '50px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>

              <Typography sx={{ fontSize: 9, ml: 2 }}>
                Depth L.
              </Typography>
              <Tooltip key={'ttmadd21'} placement="top"
                sx={{ padding: 0, marginLeft: 5 }}
                title={'Level Depth'}>
                <CssTextField id="iMlevelDepth"
                  size="small"
                  value={mlevels}
                  onChange={(e) => fnSetmLevels(e.target.value)}
                  style={{ width: '50px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                />
              </Tooltip>
            </Stack>
            <Divider sx={{ pb: 1 }} />
            <Stack>
              {Array.isArray(martinLevels) && martinLevels.length !== 0 && martinLevels.map((m, mix) => {
                return (
                  <Stack key={mix.toString()} sx={{ flexDirection: 'row', alignItems: 'center', mt: 1 }}>
                    <Typography sx={{ fontSize: 9, mr: 1 }}>
                      Level {m.level}
                    </Typography>
                    <Tooltip key={'ttmadd21'} placement="top"
                      sx={{ padding: 0, marginLeft: 5 }}
                      title={'Level Depth'}>
                      <CssTextField id="iMlevelDepth"
                        size="small"
                        // defaultValue={m.multiplier}
                        value={m.multiplier}
                        onChange={(e) => setLevel(m.level, e.target.value, false, false)}
                        onBlur={(e) => setLevel(m.level, e.target.value, true)}
                        style={{ width: '60px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0 }}
                      />
                    </Tooltip>
                  </Stack>
                )
              })}
            </Stack>
          </>
        )}
      </Box>
    </>
  )
}