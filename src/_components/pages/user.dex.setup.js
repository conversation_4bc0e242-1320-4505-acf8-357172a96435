/* eslint-disable react/display-name */
/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, {
    useState, useEffect, useContext, Suspense,
    useRef,
    forwardRef,
    useImperativeHandle, } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { styled } from '@mui/material/styles';

import TextField from '@mui/material/TextField';
import { red } from '@mui/material/colors';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import IconButton from '@mui/material/IconButton';
import LoadingButton from '@mui/lab/LoadingButton';

import MoreVertIcon from '@mui/icons-material/MoreVert';
import NotificationImportantRounded from '@mui/icons-material/NotificationImportantRounded';

import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';

import Avatar from '@mui/material/Avatar';

import List from '@mui/material/List';
import Divider from '@mui/material/Divider';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';

import CloseIcon from '@mui/icons-material/Close';
import DisabledByDefaultIcon from '@mui/icons-material/DisabledByDefault';

import { signIn, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Stack, Typography } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

import Slide from '@mui/material/Slide';

const ODD_OPACITY = 0.2;

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

export default function Home(props) {
    const { ...rest } = props;
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    });
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    const [header, setHeader] = useState(false);
    const [battleLoader, setbattleLoader] = useState(false);
    const [value, setValue] = React.useState('1');
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    useEffect(() => {
        colorMode.setCurrPage('/dex/setup');
        drawerCollapsed && colorMode.collapseDrawer(true);
    }, []);
    useEffect(() => { }, []);

    return (
        <>
            <Head>
                <title>setup - dex - Gauss Algo</title>
            </Head>

            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >
                <Stack className="mt-24">
                    {/* <Input /> */}
                    <Box sx={{
                        m: 2,
                    }}>
                        <Typography variant="h5" component="div">
                            {bull}dEX {bull} Setup &nbsp;{bull}&nbsp;
                            {/* <Chip variant="filled" color={'primary'} label={'%'} /> */}
                        </Typography>

                        <Box sx={{ width: '100%', typography: 'body1' }}>
                            <TabContext value={value}>
                                <Stack sx={{ borderBottom: 1, borderColor: 'divider', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <TabList onChange={handleChange} aria-label="lab API tabs example">
                                        <Tab label="Base" value="1" />
                                        <Tab label="Indicators" value="2" />
                                        <Tab label="Rulesets" value="3" />
                                        <Tab label="Trading" value="4" />
                                        <Tab label="Exchange" value="5" />
                                        <Tab label="Summary" value="6" />
                                    </TabList>
                                    <Stack sx={{ mx: 2, flexDirection: 'row', alignItems: 'center' }}>

                                        <Typography sx={{ fontSize: '11px' }}>Parameters:&nbsp;&nbsp; </Typography>
                                        <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => props.listSavedBattleParams()}>
                                            Load
                                        </LoadingButton>
                                        &nbsp;&nbsp;
                                        <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => props.saveBattleParams()}>
                                            Save
                                        </LoadingButton>

                                    </Stack>
                                </Stack>
                                <TabPanel value="1">pairs</TabPanel>
                                <TabPanel value="2">ix</TabPanel>
                                <TabPanel value="3">
                                    <Suspense fallback={<div>loading...</div>}>
                                        x
                                    </Suspense>
                                </TabPanel>
                                <TabPanel value="4">
                                    <Suspense fallback={<div>loading...</div>}>
                                        tradex
                                    </Suspense>
                                </TabPanel>
                                <TabPanel value="5">
                                    <Suspense fallback={<div>loading...</div>}>
                                        <SelectDex {...props} token={token} />
                                    </Suspense>
                                </TabPanel>
                                <TabPanel value="6">
                                    <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                                        navigator.clipboard.writeText(JSON.stringify(props.battleParams));

                                    }}>
                                        <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                                            <code style={{ fontSize: '12px' }}>{JSON.stringify(props.battleParams, null, 4)}</code>
                                        </pre>
                                    </Box>
                                </TabPanel>
                            </TabContext>
                        </Box>
                        <br />

                    </Box>
                </Stack>
            </AppLayout>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

const bull = (
    <Box
        component="span"
        sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
        •
    </Box>
);


import AddToPhotosIcon from '@mui/icons-material/AddToPhotos';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
const SelectDex = props => {
    const {token} = props;
    const key = 'dexvar';
    const [data, setdata] = React.useState([
        {
            testDex: true,
            dexTitle: 'Binance - TestNet',
            dexCode: 'binanceTest',
            apiKey: 'a41dedd7ab4ef988097f552a3b82b241a98757b276e1251fec5e33d468aae96a',
            apiSecret: 'd5fdee826f4847635e4a40bfa2cb3f60462a147c102c2e418f9e436c70aeb7c0',
        },
        {
            testDex: false,
            dexTitle: 'Binance',
            dexCode: 'binance',
            apiKey: '',
            apiSecret: '',
        },
    ]);
    const refShowRuleSet = useRef();
    const [selected, setSelected] = useState(false);

    const [testLoader, settestLoader] = useState(false);

    const [dexvalues, setdexvalues] = useState({});

    const isRulesetSelected = rux => {
        // console.log('selected rules', selected, rux);
        // let tmp = selected.map(s => s.dexCode);
        // let resp = tmp.includes(rux.dexCode);
        return selected?.dexCode == rux?.dexCode;
    }

    const AddRuleSet = inx => {
        let stg = [...selected];
        let uTaskIndex = stg.findIndex(t => t.rsgID == inx.rsgID);
        if (uTaskIndex < 0) {
          inx.id = Date.now() + Math.floor(Math.random() * 10);
          // inx.ruleSet = inx.ruleSet ? JSON.parse(JSON.s) : [];
          stg = [...stg, inx];
          setSelected(stg);
    
          let aIX = inx; // setPostData(inx);
          aIX.ruleSet = aIX.ruleSet && typeof aIX.ruleSet !== 'object' ? JSON.parse(aIX.ruleSet) : aIX.ruleSet
    
          let currBIX = Array.isArray(props.battleParams.rulesets) ? [...props.battleParams.rulesets] : [];
          currBIX.push(aIX);
          props.setbattleParams && props.setbattleParams({ fname: 'rulesets', fvar: currBIX });
    
        }
      };

    const setDEX = inx => {
        let currBIX = inx;
        setSelected(inx);
        setdexvalues(inx)
        //   props.setbattleParams && props.setbattleParams({ fname: 'dex', fvar: currBIX });
    };
    const loadkeys = () => {
        let keyx = key + ':' + dexvalues.dexTitle
        const stickyValue = window.localStorage.getItem(keyx);
        if (stickyValue) {
            console.log('load keys', keyx, stickyValue);
            let newValsS = JSON.parse(stickyValue);
            let dexvaluesStg = JSON.parse(JSON.stringify(dexvalues));
            let newVals = {
                ...dexvaluesStg,
                ...newValsS,
            };
            setdexvalues(newVals)
        }
    };
    const savekeys = () => {
        let lData = {
            apiKey: dexvalues.apiKey,
            apiSecret: dexvalues.apiSecret,
        }
        let keyx = key + ':' + dexvalues.dexTitle
        console.log('savekeys keys', key, lData);
        window.localStorage.setItem(keyx, JSON.stringify(lData));
    };

    const deletekeys = () => {
        let lData = {}
        let keyx = key + ':' + dexvalues.dexTitle
        console.log('delete keys', key, lData);
        window.localStorage.setItem(keyx, JSON.stringify(lData));
    };

    const testkeys = async () => {
        console.log('testkeys keys', selected.dexTitle, dexvalues)

        settestLoader(true);
        let uri = '/api/pub/data/dextest'
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify(dexvalues),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                settestLoader(false)
                return
            }

            const datax = await res.json();
            settestLoader(false)
            // await battleList();

            if (!datax.error) {
                console.log('resp:', datax)
                alert('test ok!'); 
                settestLoader(false); 
            } else {
                console.log('err desc', datax);
                settestLoader(false)
                alert('test failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            settestLoader(false)
        };

    };
    
    const viewRuleset = inx => {
        console.log('viewDEX', inx) 
        // refShowRuleSet.current && refShowRuleSet.current.showRuleset(inx)
    }   

  const handleChange = (para, event) => {
    // let currVals = JSON.parse(JSON.stringify(IX));
    console.log('handleChange', para, event.target.value)
    let dexvaluesStg = JSON.parse(JSON.stringify(dexvalues));
    dexvaluesStg[para] = event.target.value;
    setdexvalues(dexvaluesStg);
  };

    return (
        <>
            <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 250, width: 250 }}>
                    <Card sx={{ maxWidth: 345 }}>
                        <CardHeader
                            sx={{ background: '#ffddaa' }}
                            avatar={
                                <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                                    <NotificationImportantRounded />
                                </Avatar>
                            }
                            action={
                                <IconButton onClick={() => setSelected(false)} aria-label="settings">
                                    <MoreVertIcon />
                                </IconButton>
                            }
                            title="Exchanges"
                            subheader="Select Exchange"
                        />
                        <Divider />
                        <CardContent sx={{ padding: 0 }}>
                            <List
                                sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}
                                component="nav"
                                aria-labelledby="nested-list-subheader"
                                dense={true}
                            >
                                {Array.isArray(data) && data.map((i, ix) => {
                                    // let indx = getIndicatorsInRuleSet(i);
                                    // console.log('isRulesetSelected', isRulesetSelected(i));
                                    return (
                                        <ListItemButton key={ix.toString()} sx={{ backgroundColor: isRulesetSelected(i) ? '#ffcc00' : null }}>

                                            <ListItemIcon style={{ minWidth: '20px' }}>
                                               
                                            </ListItemIcon>
                                            <ListItemText onClick={() => viewRuleset(i)} primary={i.dexTitle} />
                                            <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                                                <RemoveRedEyeIcon sx={{ width: 16 }} onClick={() => viewRuleset(i)} />
                                            </IconButton>
                                            &nbsp;
                                            {true && ( //i?.avail
                                                <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                                                    <AddToPhotosIcon sx={{ width: 18 }} onClick={() => setDEX(i)} />
                                                </IconButton>
                                            )}
                                            <Divider />
                                        </ListItemButton>
                                    )
                                })}
                            </List>
                        </CardContent>
                        <Divider />
                        {/* <Typography variant="caption">unavailable rulesets!</Typography>
            <br />
            <Divider />
            <Typography variant="caption">add new ruleset</Typography> */}
                    </Card>
                </Box>

                <Box sx={{ borderBottom: 1, borderLeftWidth: 1, pl: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
                    <Typography variant="button" bgcolor={red[100]} sx={{ px: 2 }} display="block" gutterBottom>
                        Exchange Settings
                    </Typography>
                    <Divider />
 
                    {selected && (
                        <>
                            <Typography sx={{ fontSize: '24px', mt: 2 }} onClick={() => viewRuleset(selected)} primary={selected.dexTitle} secondary={''}>
                                {selected.dexTitle}
                            </Typography>
                            <Box sx={{ display: 'block', my: 2 }}>

                                <CssTextField id="outlined-basic"
                                    placeholder={selected.dexTitle + ' api key'}
                                    size="small"
                                    onChange={(e) => handleChange('apiKey', e)}
                                    value={dexvalues.apiKey}
                                    InputLabelProps={{ sx: { fontSize: "0.8rem" } }}
                                    label={'Api Key'} variant="standard" />
                            </Box>

                            <Box sx={{ display: 'block', my: 2 }}>
                                <CssTextField id="outlined-basic"
                                    placeholder={selected.dexTitle + ' api secret key'}
                                    size="small"
                                    onChange={(e) => handleChange('apiSecret', e)}
                                    value={dexvalues.apiSecret}
                                    InputLabelProps={{ sx: { fontSize: "0.8rem" } }}
                                    label={'Api Secret'} variant="standard" />
                            </Box>
                            <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                <Box>
                                    <IconButton 
                                        title="Select exchange for trading.."
                                        sx={{borderWidth: '1px', }}
                                        type="button" size="large">
                                        <Typography sx={{borderWidth: 1, p: 1}}>
                                            Select
                                        </Typography>
                                    </IconButton>
                                </Box>
                                <Stack sx={{ flexDirection: 'row', justifyContent: 'space-even' }}>
                                    <Typography sx={{cursor: 'pointer'}} onClick={loadkeys}>
                                        load 
                                    </Typography>
                                    &nbsp;|&nbsp;
                                    <Typography sx={{cursor: 'pointer'}} onClick={savekeys}>
                                        save 
                                    </Typography>
                                    &nbsp;|&nbsp;
                                    <Typography sx={{cursor: 'pointer'}} onClick={deletekeys}>
                                        delete 
                                    </Typography>
                                    &nbsp;|&nbsp;
                                    <Typography sx={{cursor: 'pointer'}} onClick={testkeys}>
                                        test {testLoader ? 'ing' : ''}
                                    </Typography>
                                </Stack>

                            </Stack>
                            <Typography>{JSON.stringify(dexvalues, ' ', 4)}</Typography>
                        </>

                    )
                    }
                </Box>

                <Box sx={{ borderBottom: 1, borderLeftWidth: 1, pl: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2 }}>
                    {/* <ShowStrategyRules ref={refShowRuleSet} /> */}
                    {/* {JSON.stringify(selectedIndicators)} */}
                </Box>
            </Stack>
        </>
    )
}
const CssTextField = styled(TextField)({
  '& label.Mui-focused': {
    color: '#A0AAB4',
    fontSize: '14px',
  },
  '&.MuiInputLabel-shrink': {
    fontSize: '14px',
  },
  '& .MuiInputBase-input': {
    borderRadius: 8,
    position: 'relative',
    // maxWidth: 260,
    // backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
    border: '0px solid #dedede',
    // borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
    // fontSize: 14,
    width: '700px',
    margin: '0px 0px 0px',
    padding: '4px 6px',
    fontSize: '16px',
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:focus': {
      // boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      // borderColor: theme.palette.primary.main,
    },
  },
});

// const ShowStrategyRules = forwardRef((props, ref) => {
//     const [rulesetData, setrulesetData] = useState(false)
//     useImperativeHandle(ref, () => ({
//       async showRuleset(ix) {
//         ix.ruleSet = ix.ruleSet && typeof ix.ruleSet !== 'object' ? JSON.parse(ix.ruleSet) : ix.ruleSet
//         setrulesetData({ ...ix })
//       },
//     }));
//     return (
//       <>
//         <Typography onClick={() => setrulesetData(false)} variant="button" bgcolor={red[100]} sx={{ px: 2 }} display="block" gutterBottom>
//           Exchange Info {rulesetData ? ' ' : ''}
//         </Typography>
//         {rulesetData ? '' + rulesetData?.dexTitle : ''}
//         <Divider />
  
//         <Box sx={{ minWidth: '400px', overflow: 'auto', maxHeight: '450px', m: 0, wordBreak: 'break-all' }} onClick={() => {
//           navigator.clipboard.writeText(JSON.stringify(rulesetData));
//         }}>
//           {rulesetData && <pre sx={{ maxWidth: '400px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
//             <code className='text-xs' style={{ fontSize: '11px' }}>{JSON.stringify(rulesetData, null, 4)}</code>
//           </pre>}
//         </Box>
  
//       </>
//     )
//   });