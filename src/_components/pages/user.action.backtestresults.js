/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';

import { signIn, signOut, useSession } from 'next-auth/react'

import { appvars } from '../../lib/constants'
import React, {
    useState, useEffect, useContext,
    useRef,
    forwardRef,
    useImperativeHandle,
} from "react";
import { useRouter } from 'next/router'

import clsx from 'clsx';
import Container from '@mui/material/Container';
import { Box, Card, Typography, Stack, Tooltip, Collapse } from '@mui/material';

import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';
const ODD_OPACITY = 0.2;
// const StripedDataGrid = DataGrid;
const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

export default function Home(props) {
    const { data: session } = useSession();

    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/backtest/results'
                const data = await fetcher(uri)
                resolve(data)
            } catch (e) {
                console.log('fetch err', e)
            }
        });
    }

    const [backtestData, setbacktestData] = useState(false);
    const [data_stat, setdata_stat] = useState(false);
    const [pOpen, setpOpen] = useState(false)
    const collapseOn = () => {
        setpOpen(!pOpen)
    }
    useEffect(() => {
        const getX = async () => {
            let ixx = await fnFetch();
            setbacktestData(ixx);
            if (Array.isArray(ixx?.stats)) {
                let stt = ixx?.stats;
                let data_stat_stg = {};
                let openTrades = stt.reduce((acc, x) => acc + parseFloat(x.openTrades), 0);
                let unRealizedPnl = stt.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                let openTradesVol = stt.reduce((acc, x) => acc + parseFloat(x.openTradesVol), 0);
                let grossProfit = stt.reduce((acc, x) => acc + parseFloat(x.grossProfit), 0);
                let grossLoss = stt.reduce((acc, x) => acc + parseFloat(x.grossLoss), 0);
                let winningTrades = stt.reduce((acc, x) => acc + parseFloat(x.winningTrades), 0);
                let losingTrades = stt.reduce((acc, x) => acc + parseFloat(x.losingTrades), 0);
                let closedTrades = stt.reduce((acc, x) => acc + parseFloat(x.closedTrades), 0);
                let realizedPnl = stt.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
                let closedTradesVol = stt.reduce((acc, x) => acc + parseFloat(x.closedTradesVol), 0);
                let realizedCommision = stt.reduce((acc, x) => acc + parseFloat(x.realizedCommision), 0);
                let tradedDuration = stt.reduce((acc, x) => acc + parseFloat(x.tradedDuration), 0);
                let netpnl = unRealizedPnl + realizedPnl;
                let percentProfitable = winningTrades / closedTrades;
                let tradedDurationAvg = tradedDuration / closedTrades;
                let avgRealizedPnl = realizedPnl / closedTrades;

                data_stat_stg = {
                    openTrades, unRealizedPnl, openTradesVol, grossProfit,
                    grossLoss, winningTrades, losingTrades, closedTrades,
                    realizedPnl, closedTradesVol, realizedCommision, netpnl, 
                    percentProfitable, tradedDurationAvg, avgRealizedPnl,
                };
                setdata_stat(data_stat_stg)
            }

        };
        getX();
    }, []);

    return (
        <>
            <Container maxWidth="xl">
                <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Typography variant="h4" component="h1" sx={{ mb: 2, color: 'black' }}>
                        Backtest results
                    </Typography>
                    <Box sx={{ mx: 2 }}>
                        <Typography onClick={collapseOn} sx={{ mb: 2, color: 'blue', fontSize: '11px', cursor: 'pointer' }}>
                            View Parameters
                        </Typography>
                    </Box>
                </Stack>
                <Collapse in={pOpen}>
                    <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all', maxHeight: '400px', overflow: 'scroll' }} onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(props.battleParams));
                    }}>
                        <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                            <code style={{ fontSize: '12px', color: 'black' }}>{JSON.stringify(backtestData.battleParams, null, 4)}</code>
                        </pre>
                    </Box>
                </Collapse>
                <Box sx={{ my: 4 }}>
                    <Stack sx={{ flexDirection: 'row', justifyContent: 'space-evenly', alignItems: 'center' }}>
                        <StatCard title={'Net Finanl Profit:'} subText={` $ ${parseFloat(data_stat?.netpnl).toFixed(2)} `} />
                        <StatCard title={'Total Open Trades:'} subText={` ${parseFloat(data_stat?.openTrades).toFixed(0)} `} />
                        <StatCard title={'Open Trades Volume $:'} subText={` $ ${parseFloat(data_stat?.openTradesVol).toFixed(2)} `} />
                        <StatCard title={'UnRealized Profit:'} subText={` $ ${parseFloat(data_stat?.unRealizedPnl).toFixed(2)} `} />
                        <StatCard title={'Total Closed Trades:'} subText={` ${parseFloat(data_stat?.closedTrades).toFixed(0)} `} />
                        <StatCard title={'Closed Trades Volume $:'} subText={` $ ${parseFloat(data_stat?.closedTradesVol).toFixed(2)} `} />
                        <StatCard title={'Net Realized Profit:'} subText={` $ ${parseFloat(data_stat?.realizedPnl).toFixed(2)} `} />
                        <StatCard title={'Percent Profitable:'} subText={` ${(parseFloat(data_stat?.percentProfitable) * 100).toFixed(0)} % `} />
                        <StatCard title={'Profit Factor:'} desc={'gross profit / gross loss'} subText={` ${(parseFloat(data_stat?.grossProfit) / parseFloat(data_stat?.grossLoss) * (-1)).toFixed(2)} `} />
                        <StatCard title={'Avg Trade:'} desc={'net profit / closed trades'} subText={` $ ${(parseFloat(data_stat?.realizedPnl) / parseFloat(data_stat?.closedTrades)).toFixed(2)} `} />
                        <StatCard title={'Avg Trade Duration:'} subText={` ${(parseFloat(data_stat?.tradedDurationAvg)).toFixed(2) + 'min'} `} />
                    </Stack>

                    <SummaryTable stats={backtestData?.stats} />
                    &nbsp;

                </Box>
            </Container>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars, } }
}

const SummaryTable = props => {

    const [loading, setloading] = useState(false)
    const [data, setData] = useState(false);
    useEffect(() => {
        props.stats && setData(props.stats);
    }, [props.stats]);

    const handleRowDoubleClick = v => {
        console.log('handleRowDoubleClick', v)
    };
    const handleRowClick = async v => {
        console.log('right', true, v.row)(event);
    };

    const refreshData = async () => {
        try {
            // await fetchMarketStats(true);
        } catch (e) { }
    }

    const columns = [
        { field: 'symbol', headerName: 'Symbol', width: 120, },
        { field: 'openTrades', headerName: 'open t', width: 50,  },
        { field: 'unRealizedPnl', headerName: 'unR. Pnl', width: 90,  type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: 2
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            }},
        { field: 'unRealizedPnlRatio', headerName: 'unR%', width: 80, type: 'number',
            valueFormatter: ({ value }) => {
                // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
                return value ? parseFloat(value).toFixed(2) + '%' : value;
            }
        },
        { field: 'closedTrades', headerName: 'closed Trades', type: 'number',  },
        { field: 'percentProfitable', headerName: 'Success %', width: 80, type: 'number',
            valueFormatter: ({ value }) => {
                // let Ratio = parseFloat(value.quoteVolumeTaker) / parseFloat(value.quoteVolume) * 100
                return value ? (parseFloat(value) * 100).toFixed(0) + '%' : value;
            }},
        { field: 'closedTradesVol', headerName: 'closed tVol',  type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: 2
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            },
        },
        { field: 'pnlnet', headerName: 'pnl. -unrlz', type: 'number',
            valueGetter: (params) => params.row?.realizedPnl + params.row?.unRealizedPnl,
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: 2
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            },
        },
        { field: 'realizedPnl', headerName: 'rlz. Pnl', type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: 2
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            },
        },
        { field: 'realizedPnlB4Comm', headerName: 'rlz. Pnl wComm', width: 120, type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: 2
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            },
        },
        { field: 'maxrealizedPnl', headerName: 'pnl. max', type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: 2
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            },
        },
        { field: 'additionalOrders', headerName: 'add. Orders', type: 'number', width: 80, },
        { field: 'additionalMaxOrder', headerName: 'Max Add', type: 'number', width: 80, },
        { field: 'tradedDuration', headerName: 'Duration', type: 'number', width: 80, },
        
        {
            field: 'action', headerName: 'action', minWidth: 150,
            renderCell: (params) => {
                // console.log('paramx', params);
                return (
                    <>
                    <Stack sx={{flexDirection: 'row',  overflowX: 'auto'}}>
                    <Typography
                        onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + params.row.symbol + 'PERP', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                        sx={{ fontSize: 11, p: 0, px: 1}}
                    >
                        tvw
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => window.open('https://www.binance.com/en/futures/' + params.row.symbol + '', "_blank")}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                        sx={{ fontSize: 11, p: 0, px: 1}}
                    >
                        bi
                    </Typography>
                        &nbsp;
                        &nbsp;
                    <Typography
                        onClick={() => window.open('/backtest/' + params.row.symi + '', "_blank")}
                        title={'reset nodes'}
                        size={'xs'}
                        style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                        sx={{ fontSize: 11, p: 0, px: 1, width: '50px'}}
                    >
                        details
                    </Typography> 
                    </Stack>
                    </>
                );
            }
        },

    ];

    return (
        <>


            <Box
                sx={{
                    // height: 300,
                    width: '100%',

                    '& .super-app-theme--cell': {
                        backgroundColor: 'rgba(224, 183, 60, 0.55)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.negative': {
                        backgroundColor: 'rgba(157, 255, 118, 0.49)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.positive': {
                        backgroundColor: '#d47483',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                }}
            >
                {data && <StripedDataGrid
                    rows={data}
                    columns={columns}
                    rowHeight={25}
                    loading={loading}
                    disableColumnMenu={true}
                    slots={{ toolbar: GridToolbar }}
                    slotProps={{
                        toolbar: {
                            printOptions: { disableToolbarButton: true },
                            showQuickFilter: true,
                            csvOptions: {
                                fileName: 'futuresDaily',
                                delimiter: ';',
                                utf8WithBom: true,
                            }
                        }
                    }}
                    initialState={{
                        sorting: {
                            sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                        },
                    }}

                    getRowClassName={(params) =>
                        params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                    }

                    onRowDoubleClick={(row, event) => {
                        handleRowDoubleClick(row.row);
                    }}
                    // onRowClick={handleRowClick}
                    sx={{
                        m: 2,
                        boxShadow: 2,
                    }} />
                }
            </Box>

        </>
    )
}



const StatCard = ({ title, subText, desc }) => {
    let ViewComp = desc ? Tooltip : Box
    return (
        <Card sx={{ p: 1, minWidth: '100px' }}>
            <ViewComp title={desc} variant="solid">
                <Typography sx={{ fontSize: '10px', color: '#00000080' }}>
                    {title}
                </Typography>
                <div>
                    <Box>
                        <Typography title={desc} sx={{ fontSize: '18px', color: '#000000' }}>
                            {subText}
                        </Typography>
                    </Box>
                </div>
            </ViewComp>
        </Card>
    )
};