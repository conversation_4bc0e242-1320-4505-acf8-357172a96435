/* eslint-disable react/display-name */
/* eslint-disable react-hooks/exhaustive-deps */
/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';

import moment from 'moment'
import { signIn, signOut, useSession } from 'next-auth/react'
import { createChart, CrosshairMode } from 'lightweight-charts';
import { appvars } from '../../lib/constants'
import React, {
    useState, useEffect, useContext,
    useRef,
    forwardRef,
    useImperativeHandle,
} from "react";
import { useRouter } from 'next/router'

import clsx from 'clsx';
import Container from '@mui/material/Container';
import { Box, Button, Typography, Stack, Card } from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import Slide from '@mui/material/Slide';
import Dialog from '@mui/material/Dialog';
import Divider from '@mui/material/Divider';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import FunctionsIcon from '@mui/icons-material/Functions';
import AddIcon from '@mui/icons-material/Add';

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';
const ODD_OPACITY = 0.2;


const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

export default function Home(props) {
    const { data: session } = useSession();

    const chartRefMain = useRef(null);
    function fnFetch() {
        return new Promise(async (resolve) => {
            try {
                var uri = '/api/pub/data/backtest/pairresults/' + props.pair
                const data = await fetcher(uri)
                resolve(data)
            } catch (e) {
                console.log('fetch err', e)
            }
        });
    }

    const [backtestData, setbacktestData] = useState(false);
    const [data_ohlc, setdata_ohlc] = useState([]);
    const [data_stats, setdata_stats] = useState([]);
    const [data_transactions, setdata_transactions] = useState([]);
    const [data_trades, setdata_trades] = useState([]);
    const [data_stat, setdata_stat] = useState([]);
    useEffect(() => {
        const getX = async () => {
            let ixx = await fnFetch();
            setbacktestData(ixx);
            let arr_ohlc = [];
            let arr_stat = [];
            let arr_transactions = [];
            let arr_trades = Array.isArray(ixx.backtest) && [...ixx.backtest].slice(-1)[0].trades;
            let arr_stats = Array.isArray(ixx.backtest) && [...ixx.backtest].slice(-1)[0].stats;

            setdata_stat(arr_stats);
            setdata_trades(arr_trades);

            Array.isArray(ixx.backtest) && ixx.backtest.map(c => {
                let cndl = c.candle;
                arr_ohlc.push({
                    ...cndl,
                    time: cndl.openTime / 1000,
                    candleIndex: c.candleIndex,
                    open: Number(cndl.open),
                    close: Number(cndl.close),
                    low: Number(cndl.low),
                    high: Number(cndl.high),
                    quoteVolume: Number(cndl.quoteVolume),
                    volumeIsBlue: Number(cndl.quoteVolumeTaker) / Number(cndl.quoteVolume) > 0.5,
                });
                let dtP = {
                    candleIndex: c.candleIndex,
                    time: c.candle.openTime / 1000,
                }

                let stats = c.stats;
                arr_stat.push({
                    ...dtP,
                    realizedPnl: stats.realizedPnl,
                    unRealizedPnl: stats.unRealizedPnl,
                    pnl: stats.realizedPnl + stats.unRealizedPnl,
                })

                let transactions = c.transactions;
                if (transactions) {
                    arr_transactions.push({
                        ...dtP,
                        transactions: transactions.tickTransactions
                    })
                };

            });
            setdata_stats(arr_stat);
            setdata_ohlc(arr_ohlc);
            setdata_transactions(arr_transactions);

            console.log('pair', props.pair, arr_ohlc.slice(-1));
            console.log('pairresults', ixx);
            // console.log('arr_ohlc', arr_ohlc)
        };
        getX();
    }, []);

    const gotoBars = bars => {
        console.log('parent baras', bars);
        chartRefMain && chartRefMain.current && chartRefMain.current.showBars(bars);
    }
    return (
        <>
            <Container maxWidth="xl">
                <Box sx={{ my: 4 }}>
                    <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', overflowX: 'auto' }}>
                        <Typography variant="h4" component="h1" sx={{ mb: 2, color: 'black', mr: 6 }}>
                            Backtest results - {props.pair}
                        </Typography>
                        <Typography
                            onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + props.pair.split('_')[0] + 'PERP', "_blank")}
                            size={'xs'}
                            style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                            sx={{ fontSize: 11, p: 0, px: 1 }}
                        >
                            tradingview chart
                        </Typography>
                        &nbsp;
                        &nbsp;
                        <Typography
                            onClick={() => window.open('https://www.binance.com/en/futures/' + props.pair.split('_')[0] + '', "_blank")}
                            size={'xs'}
                            style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                            sx={{ fontSize: 11, p: 0, px: 1 }}
                        >
                            binance chart
                        </Typography>
                    </Stack>
                    &nbsp;
                    <CandleStickChart
                        ref={chartRefMain}
                        backtestData={backtestData.backtest}
                        stats={data_stats}
                        OHLC_Data={data_ohlc}
                        transactions={data_transactions}
                    />
                </Box>

                <Summary
                    backtestData={backtestData.backtest}
                    stats={data_stats}
                    trades={data_trades}
                    data_stat={data_stat}
                    OHLC_Data={data_ohlc}
                    transactions={data_transactions}
                    gotobars={gotoBars}
                />


            </Container>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars, } }
}


// const CandleStickChart =  ({OHLC_Data, transactions, backtestData, stats }) => {
const CandleStickChart = forwardRef((props, ref) => {
    const { OHLC_Data, transactions, backtestData, stats } = props;
    const chartRef = useRef(null);
    const chartRefX = useRef(null);
    const chartToolsRef = useRef(null);
    const [crosshair, setcrosshair] = useState(false);
    const [open, setOpen] = React.useState(false);
    const [modalxData, setxmodalData] = useState(false);

    const fitChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale && timeScale.fitContent();
    };

    const resetChart = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale.resetTimeScale();
    };

    const chartEop = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        timeScale && timeScale.scrollToPosition(-1, false);
    }
    const chartBop = () => {
        var timeScale = chartRefX.current && chartRefX.current.timeScale();
        console.log('timeScale', timeScale)
        timeScale && timeScale.scrollToPosition(-1 * OHLC_Data.length, false);
    }

    const handleClose = () => {
        setxmodalData(false);
        setOpen(false);
    };

    useImperativeHandle(ref, () => ({
        async showBars(ix) {
            console.log('bars', ix)
            let bop = ix.split(' ')[0];
            let eop = ix.split(' ')[1];
            var timeScale = chartRefX.current && chartRefX.current.timeScale();
            timeScale.resetTimeScale();
            console.log('timeScale', timeScale, bop, eop);
            // timeScale && timeScale.setVisibleLogicalRange(parseInt(bop), parseInt(eop) )
            timeScale && timeScale.scrollToPosition(-1 * OHLC_Data.length + parseInt(bop), false);
        },
    }));

    useEffect(() => {
        const chart = createChart(chartRef.current, {

            width: 1400,
            height: 500,
            timeScale: {
                rightOffset: 12,
                barSpacing: 4,
                fixLeftEdge: true,
                fixRightEdge: true,
                lockVisibleTimeRangeOnResize: true,
                rightBarStaysOnScroll: true,
                borderVisible: true,
                borderColor: '#000',
                visible: true,
                timeVisible: true,
                secondsVisible: false,
                tickMarkFormatter: (time, tickMarkType, locale) => {
                    //console.log(time, tickMarkType, locale);
                    var txt = moment(time * 1000).format('MM-DD h:mm A');
                    return String(txt);
                },
            },
            watermark: {
                color: 'rgba(11, 94, 29, 0.4)',
                visible: true,
                text: OHLC_Data[0]?.symi,
                fontSize: 12,
                horzAlign: 'left',
                vertAlign: 'top',
            },
            layout: {
                // backgroundColor: '#fff',
                // textColor: '#696969',
                fontSize: 11,
                textColor: 'black',
                background: { type: 'solid', color: 'white' },
            },
            grid: {
                vertLines: {
                    color: "rgba(42, 46, 57, 0.1)"
                },
                horzLines: {
                    color: "rgba(42, 46, 57, 0.1)"
                }
            },
            rightPriceScale: {
                borderVisible: false,
            },
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
            crosshair: {
                mode: CrosshairMode.Normal,
                horzLine: {
                    visible: true,
                    labelVisible: true,
                },
                vertLine: {
                    visible: true,
                    labelVisible: true,
                },
            },


        });
        const candleSeries = chart.addCandlestickSeries({
            upColor: '#6495ED',
            downColor: '#FF6347',
            borderVisible: false,
            // wickVisible: false,
            borderColor: '#000000',
            wickColor: '#000000',
            borderUpColor: '#4682B4',
            borderDownColor: '#A52A2A',
            wickUpColor: '#4682B4',
            wickDownColor: '#A52A2A',

            priceFormat: {
                type: 'price',
                precision: 3,
                minMove: 0.005,
            },
            // wickUpColor: '#26a69a', wickDownColor: '#ef5350',
        });

        var markers = []
        Array.isArray(transactions) && transactions.length !== 0 && transactions.map((ms, mi) => {
            const { transactionData, type } = ms;
            let mark = false;
            ms.transactions.sort((a, b) => (a.openTime > b.openTime ? 1 : -1)).map(mt => {
                if (mt.typ == 'firstOrder') {
                    var mark = {
                        time: (mt.openTime) / 1000,
                        position: mt.direction == 'long' ? 'belowBar' : 'aboveBar',
                        color: 'black', //mt.direction == 'long' ? 'blue' : 'red',
                        shape: 'circle',
                        shape: mt.direction == 'long' ? 'arrowUp' : 'arrowDown',
                        text: 'F' + (mt.direction == 'long' ? 'L' : 'S') + '_' + mt.tradeData[0].tradeNo + '/' + mt.tradeData[0].tradeSubNo + ', ' + Number(mt.candleIndex).toString(),
                        candleIndex: ms.candleIndex
                    }
                } else if (mt.typ == 'additionalOrder') {
                    var mark = {
                        time: (mt.openTime) / 1000,
                        position: mt.direction == 'long' ? 'belowBar' : 'aboveBar',
                        color: 'gray',
                        // color: mt.direction == 'long' ? 'blue' : 'red',
                        // shape: 'circle',
                        shape: mt.direction == 'long' ? 'arrowUp' : 'arrowDown',
                        text: 'A' + '_' + mt.tradeData[0].tradeNo + '/' + mt.tradeData[0].tradeSubNo + ', ' + Number(mt.candleIndex).toString(),
                        candleIndex: ms.candleIndex
                    }
                } else if (mt.typ == 'closed-checkTP') {
                    mark = {
                        time: (mt.openTime) / 1000,
                        position: mt.direction == 'long' ? 'aboveBar' : 'belowBar',
                        color: 'blue',
                        shape: 'square',
                        text: '' + 'TP' + '_' + mt.tradeData[0].tradeNo,
                        candleIndex: ms.candleIndex,
                    };
                } else if (mt.typ == 'closed-checkSL') {
                    mark = {
                        time: (mt.openTime) / 1000,
                        position: mt.direction == 'long' ? 'aboveBar' : 'belowBar',
                        color: 'red',
                        shape: 'circle',
                        text: '' + 'SL' + '_' + mt.tradeData[0].tradeNo,
                        candleIndex: ms.candleIndex,
                    };
                } else if (mt.typ == 'doTrade conditions failed') {
                    mark = {
                        time: (mt.openTime) / 1000,
                        position: mt.conditions.ref.directionStratgy == 'long' ? 'aboveBar' : 'belowBar',
                        color: '#ffcc00',
                        shape: 'circle',
                        text: '' + 'x',
                        candleIndex: mt.candleIndex,
                    };
                };
                mark && markers.push(mark);
            });
            //lastMarkIndex = msIndex - lastMarkIndex > 5 ? msIndex : lastMarkIndex
        })
        markers.length !== 0 && candleSeries.setMarkers(markers);
        candleSeries.setData(OHLC_Data);
        const volumeSeries = chart.addHistogramSeries({
            color: "#000",
            lineWidth: 4,
            priceFormat: {
                type: 'volume',
                precision: 3,
            },
            priceScaleId: '',
            overlay: true,
        });

        let volumeData = OHLC_Data.map(d => {
            return ({
                time: d.time, value: d.quoteVolume, color: d.volumeIsBlue ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
            })
        });

        let pnlData = stats.map(d => {
            return ({
                time: d.time, value: d.pnl,
                color: d.pnl >= 0 ? 'rgba(0, 150, 136, 0.8)' : 'rgba(255,82,82, 0.8)'
            })
        })
        // volumeSeries.setData(volumeData)
        volumeSeries.priceScale().applyOptions({
            scaleMargins: {
                top: 0.8, // highest point of the series will be 70% away from the top
                bottom: 0, // lowest point will be at the very bottom.
            },
        });

        const lineSeries = chart.addLineSeries({
            color: '#2962FF',
            lineWidth: 4,
            priceFormat: {
                type: 'volume',
                precision: 3,
            },
            priceScaleId: '',
            overlay: true,
        });

        lineSeries.setData(pnlData);


        var timeScale = chart.timeScale();
        timeScale.setVisibleLogicalRange({
            from: OHLC_Data.length - 100,
            to: OHLC_Data.length,
        });

        chart.subscribeCrosshairMove((param) => {
            if (param.time) {
                var time = parseFloat(param.time) * 1000;
                const dateStr = param.time;
                var ix = Array.isArray(OHLC_Data) && OHLC_Data.findIndex(d => d.time == param.time);
                var txt = moment(param.time * 1000).format('MM-DD h:mm A');
                // firstRow.innerHTML = '' + txt + ' - last:' + JSON.stringify(param.seriesData.get(candleSeries)) + ' ' + JSON.stringify(ix);
                setcrosshair('' + txt + ' - last:' + JSON.stringify(param.seriesData.get(candleSeries)) + ' ' + JSON.stringify(ix))
                // toolTip.style.display = 'block';
                // const data = param.seriesData.get(candleSeries);
                // const price = data.value !== undefined ? data.value : data.close;
                // toolTip.innerHTML = `<div style="font-size: 24px; margin: 4px 0px; color: ${'black'}">
                //     ${price}
                //     </div><div style="color: ${'black'}, font-size: 11px;">
                //     ${txt}
                //     </div>`;

                // const coordinate = candleSeries.priceToCoordinate(price);
                // let shiftedCoordinate = param.point.x - 50;
                // if (coordinate === null) {
                //     return;
                // }
                // shiftedCoordinate = Math.max(
                //     0,
                //     Math.min(legend.clientWidth - toolTipWidth, shiftedCoordinate)
                // );
                // const coordinateY =
                //     coordinate - toolTipHeight - toolTipMargin > 0
                //         ? coordinate - toolTipHeight - toolTipMargin
                //         : Math.max(
                //             0,
                //             Math.min(
                //                 legend.clientHeight - toolTipHeight - toolTipMargin,
                //                 coordinate + toolTipMargin
                //             )
                //         );
                // toolTip.style.left = shiftedCoordinate + 'px';
                // toolTip.style.top = coordinateY + 'px';

            } else {
                // firstRow.innerText = ' ';
                setcrosshair(false)
            }
        });
        chart.subscribeClick((param) => {
            var time = parseFloat(param.time) * 1000;
            var candles = OHLC_Data
            var ix = Array.isArray(candles) && candles.find(d => d.time == param.time)
            var modalData = Array.isArray(backtestData) && [...backtestData].find(d => d.candleIndex == ix.candleIndex)
            if (!param.point) {
                return;
            };
            setxmodalData(modalData)
            setOpen(true);
            console.log(time, ix.candleIndex, modalData,);
            return;
        });

        chartRefX.current = chart;

        return () => {
            chart.remove();
        };
    }, [OHLC_Data]);

    return (

        <>
            <Button title='Reset Chart View' onClick={resetChart}>Reset </Button>
            <Button title='Fit Chart' onClick={fitChart}>Fit </Button>
            <Button title='BOP Chart View' onClick={chartBop}> BOP</Button>
            <Button title='EOP Chart View' onClick={chartEop}> EOP</Button>
            <div>
                <Typography sx={{ fontSize: 11, ml: '10px', borderBottomWidth: '1px', paddingVertical: '2px' }}>
                    {crosshair}&nbsp;
                </Typography>
            </div>
            <div ref={chartRef} id='chart'></div>
            <div ref={chartToolsRef} style={{ flex: 1, flexDirection: 'row' }} id="chartSummary"></div>
            <div id="chartIndicators"></div>

            <Dialog
                fullScreen
                open={open}
                onClose={handleClose}
                TransitionComponent={Transition}
                sx={{ mx: 30, my: 10 }}
            >

                <Toolbar sx={{ borderBottomWidth: 1 }}>
                    <IconButton
                        edge="start"
                        color="inherit"
                        onClick={handleClose}
                        aria-label="close"
                    >
                        <CloseIcon />
                    </IconButton>
                    <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                        {modalxData?.candle?.symbol} Candle Data
                    </Typography>
                    <Button autoFocus color="inherit" onClick={handleClose}>
                        close
                    </Button>
                </Toolbar>
                <Box sx={{ p: 2 }}>

                    <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(modalxData));

                    }}>
                        <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                            <code style={{ fontSize: '10px' }}>{JSON.stringify(modalxData, null, 4)}</code>
                        </pre>
                    </Box>

                </Box>
            </Dialog>

        </>
    );
});
const Summary = ({ OHLC_Data, transactions, backtestData, stats, trades, data_stat, gotobars }) => {
    const [value, setValue] = React.useState(0);
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    return (
        <>

            <Box sx={{ width: '100%' }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                        <Tab label="Overview" />
                        <Tab label="Performance Summary" />
                        <Tab label="List of Trades" />
                    </Tabs>
                </Box>
                <CustomTabPanel value={value} index={0}>
                    <SummaryOverview {...{ OHLC_Data, transactions, backtestData, stats, data_stat }} />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                    <PerformanceOverview  {...{ OHLC_Data, transactions, backtestData, stats, data_stat }} />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={2}>
                    <ListOfTrades {...{ trades, gotobars }} />
                </CustomTabPanel>
            </Box>

        </>
    )
};
function CustomTabPanel(props) {
    const { children, value, index, ...other } = props;
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
};
const StatCard = ({ title, subText, desc }) => {
    let ViewComp = desc ? Tooltip : Box
    return (
        <Card sx={{ p: 1, minWidth: '100px' }}>
            <ViewComp title={desc} variant="solid">
                <Typography sx={{ fontSize: '10px', color: '#00000080' }}>
                    {title}
                </Typography>
                <div>
                    <Box>
                        <Typography title={desc} sx={{ fontSize: '18px', color: '#000000' }}>
                            {subText}
                        </Typography>
                    </Box>
                </div>
            </ViewComp>
        </Card>
    )
};
const SummaryOverview = props => {
    const { data_stat, trades } = props;
    return (
        <> 
            <Stack sx={{ flexDirection: 'row', justifyContent: 'space-evenly', alignItems: 'center' }}>
                <StatCard title={'Net Profit:'} subText={` $ ${parseFloat(data_stat?.realizedPnl).toFixed(2)} `} />
                <StatCard title={'Total Closed Trades:'} subText={` ${parseFloat(data_stat?.closedTrades).toFixed(0)} `} />
                <StatCard title={'Percent Profitable:'} subText={` ${(parseFloat(data_stat?.percentProfitable) * 100).toFixed(0)} % `} />
                <StatCard title={'Profit Factor:'} desc={'gross profit / gross loss'} subText={` ${(parseFloat(data_stat?.grossProfit) / parseFloat(data_stat?.grossLoss) * (-1)).toFixed(2)} `} />
                <StatCard title={'Max Drawdown:'} subText={` $ ${parseFloat(data_stat?.maxDrawDown).toFixed(2)} `} />
                <StatCard title={'Avg Trade:'} desc={'net profit / closed trades'} subText={` $ ${(parseFloat(data_stat?.realizedPnl) / parseFloat(data_stat?.closedTrades)).toFixed(2)} `} />
                <StatCard title={'Avg Trade Duration:'} subText={` ${(parseFloat(data_stat?.tradedDuration) / parseFloat(data_stat?.closedTrades)).toFixed(2) + 'min'} `} />
            </Stack>
        </>
    )
};
const PerformanceOverview = props => {
    const { data_stat, trades } = props;
    console.log('data_stat', data_stat)
    return (
        <>
            <Box sx={{ p: 2 }}>
                <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(data_stat));
                }}>
                    <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                        <code style={{ fontSize: '10px', color: 'black' }}>{JSON.stringify(data_stat, null, 4)}</code>
                    </pre>
                </Box>
            </Box>
{/*             
            PerformanceOverview Overview....
            net profit...
            gross profit.. (sum +)
            gross loss.. (sum -)
            max run-up;
            max drawdown;
            Max Contracts Held
            Open PL
            Commission Paid
            Total Closed Trades
            Total Open Trades
            Number Winning Trades
            Number Losing Trades
            Percent Profitable (win / total trade)
            Avg Trade (net profit / total trades.)
            Avg Winning Trade (gross profit / total trades)
            Avg Losing Trade
            Avg # Bars in Trades
            Avg # Bars in Winning Trades
            Avg # Bars in Losing Trades
            Buy & Hold Return */
}
        </>
    )
};
const ListOfTrades = props => {
    const { trades } = props;
    const [loading, setloading] = useState(false) 
    const gotoBars = (ref) => {
        console.log('trades', trades);
        console.log('row', ref);
        // console.log('gotoBars', ref.candles);
        props.gotobars(ref.candles)
    }

    const [data, setData] = useState(false);
    const columns = [
        { field: 'id', headerName: 'id', width: 40, },
        { field: 'pair', headerName: 'pair', width: 60, },
        { field: 'strategy', headerName: 'strategy', width: 120,
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 12}}>{params.value}</span>;
            }  },
        { field: 'tradeNo', headerName: 'No', width: 60,  },
        { field: 'closed', headerName: 'Closed', width: 70, 
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 12}}>{params.value}</span>;
            }  },
        { field: 'dir', headerName: 'Dir', width: 60, 
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 12}}>{params.value}</span>;
            }  },
        { field: 'entryAmount', headerName: 'e Amnt',  type: 'number', width: 80,  },
        { field: 'entryPrice', headerName: 'e Price',  type: 'number',  },
        { field: 'closePrice', headerName: 'c Price',  type: 'number',  },
        { field: 'pnl', headerName: 'pnl',  type: 'number',  },
        { field: 'comm', headerName: 'comm',  type: 'number', width: 60,  },
        { field: 'closeNote', headerName: 'c note',  
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 12, color: params.value == 'stopLoss' ? 'red' : 'blue'}}>{params.value}</span>;
            } },
        { field: 'drawDown', headerName: 'drawdown',  type: 'number', width: 90,  },
        { field: 'runUp', headerName: 'run up',  type: 'number', width: 60,  },
        { field: 'candles', headerName: 't candles', width: 60,  
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 10}}>{params.value}</span>;
            } ,  },
        { field: 'bop', headerName: 't bop', width: 80, 
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 10}}>{moment(params.value).format('MM-DD h:mm A')}</span>;
            } },
        { field: 'eop', headerName: 't eop', width: 80,  
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 10}}>{moment(params.value).format('MM-DD h:mm A')}</span>;
            } },
        { field: 'duration', headerName: 'dur', width: 40,  type: 'number',  
            renderCell: (params) => {
                return <span title={params.value} style={{fontSize: 10}}>{params.value}</span>;
            } },
        
            {
                field: 'action', headerName: 'action', minWidth: 150,
                renderCell: (params) => {
                    // console.log('paramx', params);
                    return (
                        <>
                            <Stack sx={{ flexDirection: 'row', overflowX: 'auto' }}>
                                <Typography
                                    onClick={() => gotoBars(params.row)}
                                    title={'view on chart'}
                                    size={'xs'}
                                    style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 0, px: 0, cursor: 'pointer' }}
                                    sx={{ fontSize: 11, p: 0, px: 1, width: '50px' }}
                                >
                                    view
                                </Typography>
                            </Stack>
                        </>
                    );
                }
            },
    
    ];
    useEffect(() => {
        // tableFromJson();
        let stgData = Array.isArray(props.trades) && props.trades.map((d, i) => {
            return ({
                id: i,
                pair: d.pair,
                strategy: d.orderID,
                tradeNo: d.tradeNo + '.' + d.tradeSubNo,
                closed: d.tradeClosed,
                dir: d.direction,
                entryAmount: parseFloat(parseFloat(d.entryAmount).toFixed(2)),
                entryPrice: d.entryPrice,
                closePrice: d.closePrice,
                pnl: parseFloat(parseFloat(d.unRealizedPnl || d.realizedPnl).toFixed(2)),
                comm: parseFloat(parseFloat(d.commission).toFixed(2)),
                closeNote: d.closeNote,
                drawDown: parseFloat(parseFloat(d.drawDown).toFixed(2)),
                runUp: parseFloat(parseFloat(d.runUp).toFixed(2)),
                candles: d.entrycandleIndex + (d.closecandleIndex ? ' ' + d.closecandleIndex : ''),
                bop: d.entryTimeEn ,
                eop: d.closeTimeEn,
                duration: d.tradeDuration,
                // entryNote: d.entryNote, 
            })
        });
        setData(stgData);

    }, [props.trades]);
      
    return (
        <>
            {/* <Typography>
                Trades
            </Typography>
            <div id="showData" align="center"></div> */}

            <Box
                sx={{
                    // height: 300,
                    width: '100%',

                    '& .super-app-theme--cell': {
                        backgroundColor: 'rgba(224, 183, 60, 0.55)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.negative': {
                        backgroundColor: 'rgba(157, 255, 118, 0.49)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.positive': {
                        backgroundColor: '#d47483',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                }}
            >
                {data && <StripedDataGrid
                    rows={data}
                    columns={columns}
                    rowHeight={25}
                    loading={loading}
                    disableColumnMenu={true}
                    slots={{ toolbar: GridToolbar }}
                    slotProps={{
                        toolbar: {
                            printOptions: { disableToolbarButton: true },
                            showQuickFilter: true,
                            csvOptions: {
                                fileName: 'futuresDaily',
                                delimiter: ';',
                                utf8WithBom: true,
                            }
                        }
                    }}
                    initialState={{
                        sorting: {
                            sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                        },
                    }}

                    getRowClassName={(params) =>
                        params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                    }

                    // onRowDoubleClick={(row, event) => {
                    //     handleRowDoubleClick(row.row);
                    // }}
                    // onRowClick={handleRowClick}
                    sx={{
                        m: 2,
                        boxShadow: 2,
                    }} />
                }
            </Box>
        </>
    )
};
const ListOfTrades_generic = props => {
    const { trades } = props;
    console.log('trades', trades);
    let tableFromJson = () => {
        // the json data.
        let stgData = trades.map((d, i) => {
            return ({
                strategy: d.orderID,
                tradeNo: d.tradeNo + '.' + d.tradeSubNo,
                closed: d.tradeClosed,
                dir: d.direction,
                entryAmount: parseFloat(d.entryAmount).toFixed(2),
                entryPrice: d.entryPrice,
                closePrice: d.closePrice,
                pnl: parseFloat(d.unRealizedPnl || d.realizedPnl).toFixed(2),
                comm: parseFloat(d.commission).toFixed(2),
                closeNote: d.closeNote,
                drawDown: parseFloat(d.drawDown).toFixed(2),
                runUp: parseFloat(d.runUp).toFixed(2),
                candles: d.entrycandleIndex + (d.closecandleIndex ? ' ' + d.closecandleIndex : ''),
                bop: moment(d.entryTimeEn).format('MM-DD h:mm A') ,
                eop: moment(d.closeTimeEn).format('MM-DD h:mm A'),
                duration: d.tradeDuration,
                // entryNote: d.entryNote, 
            })
        })
        const myBooks = stgData
        // Extract value from table header. 
        // ('Book ID', 'Book Name', 'Category' and 'Price')
        let col = [];
        for (let i = 0; i < myBooks.length; i++) {
            for (let key in myBooks[i]) {
                if (col.indexOf(key) === -1) {
                    col.push(key);
                }
            }
        }

        // Create table.
        const table = document.createElement("table");
        table.style = "border: 1px solid black;";
        // Create table header row using the extracted headers above.
        let tr = table.insertRow(-1);                   // table row.

        let th = document.createElement("th");      // table header.
        th.innerHTML = '#';
        th.style = "border: 1px solid black; border-collapse: collapse; background-color: #96D4D4;";
        tr.appendChild(th);

        for (let i = 0; i < col.length; i++) {
            let th = document.createElement("th");      // table header.
            th.style = "border: 1px solid black; border-collapse: collapse; background-color: #96D4D4;";
            th.innerHTML = col[i];
            tr.appendChild(th);
        }

        // add json data to the table as rows.
        for (let i = 0; i < myBooks.length; i++) {

            tr = table.insertRow(-1);
            tr.insertCell(-1).innerHTML = `<th>${i + 1}</th>`;

            for (let j = 0; j < col.length; j++) {
                let tabCell = tr.insertCell(-1);
                tabCell.innerHTML = myBooks[i][col[j]] ? myBooks[i][col[j]] : '';
            }
        }

        // Now, add the newly created table with json data, to a container.
        const divShowData = document.getElementById('showData');
        divShowData.innerHTML = "";
        divShowData.appendChild(table);
    }
    useEffect(() => {
        tableFromJson();
    }, []);
      
    return (
        <>
            <Typography>
                Trades
            </Typography>
            <div id="showData" align="center"></div>
        </>
    )
};