/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';

import React, { useState, useEffect, useContext } from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Checkbox, Button, Typography, Stack, CircularProgress } from '@mui/material';
import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

import CloseIcon from '@mui/icons-material/Close';
import Chip from '@mui/material/Chip';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';
import Dialog from '@mui/material/Dialog';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import LinearProgress from '@mui/material/LinearProgress';
import Link from 'next/link'

export default function Home(props) {
    const { ...rest } = props;
    const router = useRouter();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const { slug } = router.query
    const [header, setHeader] = useState(false)
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
            // router.push('/auth/SignInSide');
        },
    })
    useEffect(() => {
        colorMode.setCurrPage('/main/market');
        drawerCollapsed && colorMode.collapseDrawer(true);
    }, []);

    // useEffect(() => {
    // }, [props.updateMe]);

    if (status === "loading") {
        return "Loading..."
    }
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};

    const tokenDetay = Array.isArray(slug) && slug[0] ? Array.isArray(slug) && slug[0] : false;


    return (
        <>
            <Head>
                <title>Gauss Algo</title>
            </Head>
            <AppLayout session={session} {...props}
                pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
                pgBread={<span>x</span>}
            >
                <Stack className="mt-24">
                    {/* {JSON.stringify(Array.isArray(slug) && slug[0])} */}
                    {tokenDetay && (
                        <>
                            <span>token detay</span>
                        </>
                    )}
                    {!tokenDetay && (
                        <ListPairs token={token} updateMe={props.updateMe} />
                    )}
                </Stack>
            </AppLayout>
        </>
    )
}

export async function getServerSideProps() {
    return { props: { appvars } }
}

const bull = (
    <Box
        component="span"
        sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
        •
    </Box>
);

import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
// import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import { BiKnife, BiWindowClose } from "react-icons/bi";
import Drawer from '@mui/material/Drawer';
import moment from "moment";
import { convertUTCDateToLocalDate } from "../../lib/fnx/fnx.fe";

import clsx from 'clsx';
import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';
const ODD_OPACITY = 0.2;
const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));

const ListPairs = props => {
    const { token } = props;
    const btnRef = React.useRef()
    const [data, setData] = useState([]);
    const [dataTime, setdataTime] = useState(false);
    const [loading, setloading] = useState(false)
    const fetchMarketStats = async forceUpdate => {
        setloading(true)
        let res;
        forceUpdate && setData([]);
        forceUpdate && console.log('forceUpdate', forceUpdate);
        try {
            let uri = '/api/pub/data/market_futuresdaily/';
            uri += forceUpdate ? '?force=1' : '';
            res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'X-Host': 'Subanet.com',
                },
            })
            const datax = await res.json()
            // console.log('a1... datax.data.length', datax.data.length)
            Array.isArray(datax.data) && datax.data.map((d, ix) => {
                d.id = ix + 1
            });
            setData(datax.data)
            // console.log('data', datax.data)
            setdataTime(new Date(Date.now()).toLocaleTimeString())
            setloading(false)
            return datax.data
        } catch (e) {
            console.log('fetch error', e)
            alert('Hata', e)
            setloading(false)
            return false;
        }
    }

    const [aiModalOpen, setAiModalOpen] = useState(false);
    const [aiPrompts, setAiPrompts] = useState([]);
    const [selectedPrompt, setSelectedPrompt] = useState(null);
    const [loadingPrompts, setLoadingPrompts] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editedPrompt, setEditedPrompt] = useState('');
    const [promptTitle, setPromptTitle] = useState('');


    const [aiDataSchemas, setAiDataSchemas] = useState([]);
    const [selectedDataSchema, setSelectedDataSchema] = useState(null);
    const [aiData, setAiData] = useState('');

    const [sliceLastNRows, setSliceLastNRows] = useState(20)
    const [includeIndicators, setIncludeIndicators] = useState(false);
    
    const [aiModels, setAiModels] = useState([]);
    const [selectedModel, setSelectedModel] = useState(null);
    const [localLLMLoading, setLocalLLMLoading] = useState(false);
    const [LLMLoading, setLLMLoading] = useState(false);

    const [fPrompt, setFPrompt] = useState('');


    const [drawerData, setdrawerData] = useState(false)
    const [drawerDataURL, setdrawerDataURL] = useState(false)
    const [drawerDataT, setdrawerDataT] = useState(false)
    const [state, setState] = React.useState({
        top: false,
        left: false,
        bottom: false,
        right: false,
    });
    const toggleDrawer = (anchor, open, pairData) => (event) => {
        if (event?.type === 'keydown' && (event?.key === 'Tab' || event?.key === 'Shift')) {
            return;
        }
        if (open) {
            setdrawerDataURL(`https://www.tradingview.com/chart?symbol=BINANCE%3A${pairData.symbol}PERP`);
            setdrawerData(pairData);
        } else {
            setdrawerDataT(false)
            setdrawerData(false)
        }
        setState({ ...state, [anchor]: open });
    };

    const fetchAIModels = async () => {
        try {
            const response = await fetch('http://localhost:3012/api/pub/ai/localllmlist');
            const data = await response.json();
            if (data.models && Array.isArray(data.models)) {
                setAiModels(data.models);
                // Set the first model as default if none is selected
                if (!selectedModel && data.models.length > 0) {
                    setSelectedModel(data.models[0]);
                }
            }
        } catch (e) {
            console.log('Error fetching AI models:', e);
            alert('Error fetching AI models');
        }
    }; 

    useEffect(() => {
        fetchMarketStats();
    }, []);

    // useEffect(() => {
    //     fetchMarketStats(true);
    // }, [props.updateMe]);

    const handleRowClick = async v => {
        toggleDrawer('right', true, v.row)(event);

    };
    const handleRowDoubleClick = v => {
        console.log('handleRowDoubleClick', v)
    }

    const AIData = async () => {
        let data3 = await fetchMarketStats(true);
        if (data3) {
            if (selectedDataSchema) {
                let fields = selectedDataSchema?.sema;
                let dataStg = [];
                Array.isArray(data3) && data3.map(d => {
                    let row = {};
                    fields.map(f => {
                        row[f] = d[f];
                    });
                    dataStg.push(row);
                });
                // console.log('dataStg', data3, dataStg)
                setAiData(dataStg);
            } else {
                // console.log('nodata3', data3 )
                setAiData(data3.klines);
            }
        }
    }

    const formatAIData = (rawData, schema) => {
        if (schema) {
            let fields = schema?.sema;
            let dataStg = [];
            Array.isArray(rawData) && rawData.map(d => {
                let row = {};
                fields.map(f => {
                    row[f] = d[f];
                });
                dataStg.push(row);
            });
            return dataStg;
        } else {
            return rawData;
        }
    }
    
    const refreshData = async () => {
        try {
            await fetchMarketStats(true);
            // console.log('data', data)
        } catch (e) { }

    }

    const columns = [
        { field: 'symbol', headerName: 'Symbol', width: 120, },
        {
            field: 'lastPrice', headerName: 'lastPrice', width: 100, type: 'number',
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: fractionDigit
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            }
        },
        {
            field: 'priceChangePercent', headerName: 'change%', type: 'number', width: 100,
            cellClassName: (params) => {
                if (params.value == null) {
                    return '';
                }
                return clsx('super-app', {
                    negative: params.value < 0,
                    //   positive: params.value > 0,
                });
            },
        },
        {
            field: 'delta', headerName: 'Delta', width: 70, type: 'number',
            valueGetter: (params) => {
                let value = params.row;
                let Ratio = (parseFloat(value.lastPrice) - parseFloat(value.lowPrice)) / (parseFloat(value.highPrice) - parseFloat(value.lowPrice)) * 100
                return Ratio;
            },
            renderCell: ({ value }) => {
                // console.log(p)
                return (
                    <ProgressBar value={(parseFloat(value) / 100)} />

                )
            }
        },
        {
            field: 'openPrice', headerName: 'Open', type: 'number', width: 90,
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: fractionDigit
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            }
        },
        {
            field: 'highPrice', headerName: 'High', type: 'number', width: 90,
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: fractionDigit
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            }
        },
        {
            field: 'lowPrice', headerName: 'Low', type: 'number', width: 90,
            valueFormatter: ({ value }) => {
                let fractionDigit = 7; // value ? valueStg.toString().split(".")[1].length : false;
                let fractionOption = { minimumFractionDigits: 2 }
                fractionOption = {
                    ...fractionOption,
                    maximumFractionDigits: fractionDigit
                };
                return Intl.NumberFormat("en-US", fractionOption).format(value);
            }
        },
        {
            field: 'quoteVolume', headerName: 'VolUSD', type: 'number', width: 140,
            // valueFormatter: ({ value }) => {
            //     return Intl.NumberFormat("en-US", { maximumFractionDigits: 2, minimumFractionDigits: 2 }).format(value);
            // }
            renderCell: (params) => {
                let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
                return <span title={params.value}>{val}</span>;
            }
        },
        { field: 'count', headerName: 'Trades', type: 'number', width: 100 },
        {
            field: 'openTimeHRF', headerName: 'OpenTime', type: 'number', width: 120,
            valueFormatter: ({ value }) => {
                return moment((new Date(value))).format("DD MM, hh:mm:ss");; //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
            },
        },
        {
            field: 'closeTimeHRF', headerName: 'LastUpd', width: 120,
            valueFormatter: ({ value }) => {
                return moment((new Date(value))).format("DD MM, hh:mm:ss");; //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
            },
            // renderCell: (params) => {
            //     let val = moment(convertUTCDateToLocalDate(new Date(params.value))).fromNow(); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()

            //     return <span title={params.value}>{val}</span>;
            // }

        },
        { field: 'batchid', headerName: 'Batch', width: 80 },
    ];

    const AIModal = async ({ modal }) => {
        try {
            // Fetch prompts from the API
            setLLMLoading(false)
            setLoadingPrompts(true);
            setAiModalOpen(true); // Open modal immediately to show loading state
            setSelectedPrompt(null);
            
            // Fetch AI models
            await fetchAIModels();
            const response = await fetch('http://localhost:3012/api/pub/ai/prompts?type=market');
            const data = await response.json();
            
            if (data.prompts) {
                setAiPrompts(data.prompts);
            }

            const response2 = await fetch('http://localhost:3012/api/pub/ai/dataschemas?type=market');
            const data2 = await response2.json();
            if (data2.dataschemes && Array.isArray(data2.dataschemes)) {
                let arrx = data2.dataschemes.filter(d => d.category == 'market');
                // console.log('schemas', arrx, arrx[0])
                setAiDataSchemas(arrx);
                arrx > 0 && setSelectedDataSchema(arrx[0]);
            }

            await fetchMarketStats(true);
            setLoadingPrompts(false);
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
            setLoadingPrompts(false);
            setAiModalOpen(false); // Close modal on error
        }
    };

    const handleEditPrompt = () => {
        setIsEditing(true);
        setEditedPrompt(selectedPrompt.prompt);
        setPromptTitle(selectedPrompt.title);
    };

    const handleSavePrompt = async () => {
        try {
            // Show popup for title input
            const newTitle = prompt('Enter prompt title:', promptTitle);
            if (newTitle === null) return; // User cancelled
            
            const promptData = {
                type: 'market',
                promptID: selectedPrompt.promptID,
                title: newTitle,
                prompt: editedPrompt,
                isUpdate: selectedPrompt.title === newTitle // If same title, update existing, otherwise create new
            };
            
            const response = await fetch('/api/pub/ai/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(promptData),
            });
            
            if (response.ok) {
                // Refresh prompts
                const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
                const data = await response.json();
                
                if (data.prompts) {
                    setAiPrompts(data.prompts);
                }
                
                setIsEditing(false);
                alert('Prompt saved successfully!');
            } else {
                alert('Failed to save prompt');
            }
        } catch (e) {
            console.log('Error saving prompt:', e);
            alert('Error saving prompt');
        }
    };

    const handleDeletePrompt = async () => {
        if (!window.confirm('Are you sure you want to delete this prompt?')) {
            return;
        }
        
        try {
            const response = await fetch('/api/pub/ai/update', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ promptID: selectedPrompt.promptID }),
            });
            
            if (response.ok) {
                // Refresh prompts
                const response = await fetch('http://localhost:3012/api/pub/ai/prompts');
                const data = await response.json();
                
                if (data.prompts) {
                    setAiPrompts(data.prompts);
                }
                
                setSelectedPrompt(null);
                alert('Prompt deleted successfully!');
            } else {
                alert('Failed to delete prompt');
            }
        } catch (e) {
            console.log('Error deleting prompt:', e);
            alert('Error deleting prompt');
        }
    };

    const handleCancelEdit = () => {
        setIsEditing(false);
        setEditedPrompt(selectedPrompt.prompt);
    };

    const buildPrompt = () => {
        if (selectedPrompt) {
            // let data3 = await fetchMarketStats(true);
            let aiDataStg = formatAIData(data, selectedDataSchema);
            let prompt = selectedPrompt.prompt;
            let promptData = prompt
                .replace('<<referans veri >>', JSON.stringify(aiDataStg))
                .replace('<<zaman>>', "" + new Date(new Date(Date.now()) - (new Date().getTimezoneOffset() * 60000)).toISOString());
            return promptData;
        }
    }

    const getLocalLLMResponse = async () => {
        try {
            setLocalLLMLoading(true);
            const promptData = buildPrompt();
            const response = await fetch('/api/pub/ai/localllmquery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: promptData, model: selectedModel }),
            });
            const data = await response.json();
            setLocalLLMLoading(false);
            if (data.response) {
                alert(data.response?.aiResponse);
            } else {
                alert('No response');
            }
        } catch (e) {
            console.log('Error getting response:', e);
            setLocalLLMLoading(false);
            alert('Error getting response');
        }
    }

    const getGeminiResponse = async () => {
        try {
            setLLMLoading(true);
            const promptData = buildPrompt();
            const response = await fetch('/api/pub/ai/llmquery', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ prompt: promptData, model: selectedModel }),
            });
            const data = await response.json();
            setLLMLoading(false);
            if (data.response) {
                alert(data.response?.aiResponse);
            } else {
                alert('No response');
            }
        } catch (e) {
            console.log('Error getting response:', e);
            setLLMLoading(false);
            alert('Error getting response');
        }
    }

    const CopyPrompt2Clipboard = () => {
        navigator.clipboard.writeText(fPrompt);
        alert('Prompt copied to clipboard');
    }

    const copyData = () => {
        navigator.clipboard.writeText(JSON.stringify(formatAIData(data, selectedDataSchema)));
        alert('Prompt datavalues copied to clipboard');
    }
    
    const saveData2Sandbox = async () => { 
        let uri = '/api/pub/ai/sandbox_savedata'
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + props.token,
                    'X-Host': 'Subanet.com',
                },
                body: JSON.stringify({ dataName: 'marketData', dataValue: data }),
            })
            if (!res.ok) {
                var message = `An error has occured: ${res.status} - ${res.statusText}`;
                alert(message);
                return
            }

            const datax = await res.json();

            if (!datax.error) {
                alert('data saved!')
            } else {
                console.log('err desc', datax);
                alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
            }
        }
        catch (e) {
            console.log('e', e)
            alert('Error Code: 981', e)
        }
    }

    useEffect(() => { 
        const asyncx = async () => {
            // await AIData();
            let pstr = buildPrompt();
            setFPrompt(pstr);
        }
        asyncx();
    }, [data, selectedPrompt, selectedDataSchema ]); //aiData, 

    return (
        <>
            <Box
                sx={{
                    // height: 300,
                    width: '100%',

                    '& .super-app-theme--cell': {
                        backgroundColor: 'rgba(224, 183, 60, 0.55)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.negative': {
                        backgroundColor: 'rgba(157, 255, 118, 0.49)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.positive': {
                        backgroundColor: '#d47483',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                }}
            >
                <Box sx={{ m: 2 }} >

                    <Button size="small"
                        variant="outlined"
                        onClick={refreshData}>
                            Refresh Table
                    </Button> &nbsp;{dataTime}
                    &nbsp;

                    <Button size="small" sx={{ ml: 2 }}
                        variant="outlined" onClick={() => AIModal({ modal: true })}>
                        AI Prompt
                    </Button>

                                <Chip key={'219151bctradingxx'}
                                  size='small'
                                  sx={{ borderRadius: 2, color: '#000', m: '2px', ml: '15px' }}
                                  // style={{ border: '1px solid #ccc', marginRight: 5 }}
                                  // sx={{ borderRadius: 2, backgroundColor: '#FF9800', color: '#000' }}
                                  label={'Save Data 2 Sandbox'}
                                  onClick={saveData2Sandbox}
                                />

                </Box>
                {data && <StripedDataGrid
                    rows={data}
                    columns={columns}
                    rowHeight={25}
                    loading={loading}
                    disableColumnMenu={true}
                    slots={{ toolbar: GridToolbar }}
                    slotProps={{
                        toolbar: {
                            showQuickFilter: true,
                            printOptions: { disableToolbarButton: true },
                            csvOptions: {
                                fileName: 'futuresDaily',
                                delimiter: ';',
                                utf8WithBom: true,
                            }
                        }
                    }}
                    initialState={{
                        sorting: {
                            sortModel: [{ field: 'quoteVolume', sort: 'desc' }],
                        },
                    }}

                    getRowClassName={(params) =>
                        params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
                    }

                    onRowDoubleClick={(row, event) => {
                        handleRowDoubleClick(row.row);
                    }}
                    onRowClick={handleRowClick}
                    sx={{
                        m: 2,
                        boxShadow: 2,
                    }} />
                }
            </Box>
            <Drawer
                anchor={'right'}
                open={state['right']}
                onClose={toggleDrawer('right', false)}
            >

                <Box sx={{
                    minHeight: 300,
                    minWidth: 300,
                    // marginLeft: 300,
                    // backgroundColor: '#ffccaa', 
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                    <Card sx={{ minWidth: 255, m: 2 }}>
                        <CardContent>
                            <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
                                Symbol
                            </Typography>
                            <Typography variant="h4" component="div">
                                {drawerData.symbol}&nbsp;{bull}{bull}&nbsp;<Chip variant="filled" color={parseFloat(drawerData.priceChangePercent) < 0 ? "error" : 'primary'} label={parseFloat(drawerData.priceChangePercent).toFixed(3) + '%'} />
                            </Typography>
                            <Typography sx={{ mb: 1.5 }} color="text.secondary">
                                adjective
                            </Typography>
                            <Typography variant="body2">
                                well meaning and kindly.
                                <br />
                                {'"a benevolent smile"'}
                            </Typography>
                        </CardContent>
                        <CardActions>


                            <Typography
                                onClick={() => window.open('https://www.tradingview.com/chart?symbol=BINANCE%3A' + drawerData.symbol + 'PERP', "_blank")}
                                size={'xs'}
                                style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 2, px: 0, cursor: 'pointer' }}
                                sx={{ fontSize: 9, p: 0, px: 1 }}
                            >
                                Tradingview
                            </Typography>
                            &nbsp;
                            &nbsp;
                            <Typography
                                onClick={() => window.open('https://www.binance.com/en/futures/' + drawerData.symbol + '', "_blank")}
                                size={'xs'}
                                style={{ borderWidth: 1, backgroundColor: '#ccc', m: 1, p: 2, px: 0, cursor: 'pointer' }}
                                sx={{ fontSize: 9, p: 0, px: 1 }}
                            >
                                Binance
                            </Typography>

                            <Link href={`/main/market/${drawerData.symbol}`}>
                                <Button size="small"
                                    variant="outlined"
                                    onClick={toggleDrawer('right', false)}>Details</Button>
                            </Link>
                            &nbsp;&nbsp;
                            <Button size="small"
                                variant="outlined"
                                onClick={refreshData}>Refresh Table</Button>
                        </CardActions>
                    </Card>

                    <Box sx={{ maxWidth: 300, overflow: 'auto', alignItems: 'center', justifyContent: 'center' }}>
                        <PairLinks drawerData={drawerData} />
                    </Box>

                </Box>
            </Drawer>

            <Dialog
                open={aiModalOpen}
                onClose={() => setAiModalOpen(false)}
                sx={{ margin: 'auto' }}
                PaperProps={{
                    sx: {
                        width: 'unset',
                        maxWidth: '1200px',
                        minWidth: '1200px',
                        margin: '16px'
                    }
                }}
            >
                <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                            <Typography variant="h6">AI Prompt</Typography>
                            <Typography variant="caption" color={'GrayText'} sx={{ ml: 2 }}>
                                {drawerData?.symbol}
                            </Typography>
                            <Button onClick={() => window.open(`https://www.tradingview.com/chart?symbol=BINANCE%3A${drawerData?.symbol}PERP`, "_blank")} sx={{ ml: 2 }}>tradingview</Button>
                            <Button onClick={() => window.open('https://chat.qwen.ai/', "_blank")} sx={{ ml: 2 }}>Qwen</Button>
                            <Button onClick={() => window.open('https://aistudio.google.com/prompts/new_chat', "_blank")} sx={{ ml: 2 }}>Google AI Studio</Button>
                            <Button onClick={() => window.open('https://chatgpt.com/', "_blank")} sx={{ ml: 2 }}>Chat GPT</Button>


                        </Box>
                        <IconButton onClick={() => setAiModalOpen(false)}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                    {loadingPrompts ? (
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            minHeight: '200px',
                            gap: 2
                        }}>
                            <Box sx={{
                                position: 'relative',
                                display: 'inline-flex'
                            }}>
                                <CircularProgress size={60} thickness={4} />
                                <Box
                                    sx={{
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        position: 'absolute',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <Typography
                                        variant="caption"
                                        component="div"
                                        color="text.secondary"
                                    >
                                        AI
                                    </Typography>
                                </Box>
                            </Box>
                            <Typography variant="h6" color="text.secondary">
                                Loading AI Prompts
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                Please wait while we fetch the latest data...
                            </Typography>
                            <LinearProgress
                                sx={{
                                    width: '100%',
                                    borderRadius: 10
                                }}
                            />
                        </Box>
                    ) : (
                        <>
                            <Autocomplete
                                options={aiPrompts}
                                getOptionLabel={(option) => option.title}
                                onChange={(event, newValue) => {
                                    setSelectedPrompt(newValue);
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        label="Select a prompt"
                                        variant="outlined"
                                        fullWidth
                                    />
                                )}
                                sx={{ width: '100%', marginBottom: '20px' }}
                            />
                            {selectedPrompt && (
                                <>

                                    <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>

                                        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'center' }}>
                                            <Autocomplete
                                                options={aiDataSchemas}
                                                getOptionLabel={(option) => option.title}
                                                value={selectedDataSchema}
                                                onChange={async (event, newValue) => {
                                                    // console.log('event', event);
                                                    // console.log('new value', newValue);
                                                    setSelectedDataSchema(newValue);
                                                    // await fetchMarketStats(true);
                                                    // Refresh AI data when schema changes
                                                    // await AIData();
                                                }}
                                                isOptionEqualToValue={(option, value) => option.semaID === value.semaID}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        label="Select a data schema"
                                                        variant="outlined"
                                                        fullWidth
                                                    />
                                                )}
                                                sx={{ width: '25%', mr: 1 }}
                                            />
                                            <Autocomplete
                                                options={aiModels}
                                                getOptionLabel={(option) => option.name}
                                                value={selectedModel}
                                                onChange={(event, newValue) => {
                                                    setSelectedModel(newValue);
                                                }}
                                                isOptionEqualToValue={(option, value) => option.model === value.model}
                                                renderInput={(params) => (
                                                    <TextField
                                                        {...params}
                                                        label="Select a model"
                                                        variant="outlined"
                                                        fullWidth
                                                    />
                                                )}
                                                sx={{ width: 200 }}
                                            />
                                            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end', ml: 1, gap: 1 }}>

                                                <Button
                                                    variant="outlined"
                                                    size="small"
                                                    onClick={getLocalLLMResponse}
                                                    disabled={localLLMLoading}
                                                    sx={{ mr: 1, minWidth: '120px' }}
                                                >
                                                    {localLLMLoading ? (
                                                        <CircularProgress size={20} />
                                                    ) : (
                                                        'Local LLM'
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="outlined"
                                                    size="small"
                                                    onClick={getGeminiResponse}
                                                    disabled={LLMLoading}
                                                    sx={{ mr: 1, minWidth: '120px' }}
                                                >
                                                    {LLMLoading ? (
                                                        <CircularProgress size={20} />
                                                    ) : (
                                                        'Cloud AI'
                                                    )}

                                                </Button>
                                                <Button
                                                    variant="outlined"
                                                    size="small"
                                                    onClick={CopyPrompt2Clipboard}
                                                    sx={{ mr: 1 }}
                                                >
                                                    Copy Clipboard
                                                </Button>
                                                <Button size="small"
                                                    variant="outlined"
                                                    sx={{ mr: 1 }}
                                                    onClick={copyData}>
                                                    Copy Data CB
                                                </Button>
                                                <Button size="small"
                                                    variant="outlined"
                                                    sx={{ mr: 1 }}
                                                    onClick={refreshData}>
                                                    Refresh AI Data
                                                </Button>
                                            </Box>
                                        </Box>
                                        <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end' }}>
                                            {isEditing ? (
                                                <>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={handleSavePrompt}
                                                        sx={{ mr: 1 }}
                                                    >
                                                        Save
                                                    </Button>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={handleCancelEdit}
                                                    >
                                                        Cancel
                                                    </Button>
                                                </>
                                            ) : (
                                                <>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        onClick={handleEditPrompt}
                                                        sx={{ mr: 1 }}
                                                    >
                                                        Edit
                                                    </Button>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        color="error"
                                                        onClick={handleDeletePrompt}
                                                    >
                                                        Delete
                                                    </Button>
                                                </>
                                            )}
                                        </Box>
                                    </Box>
                                    <Box sx={{ mt: 2, p: 2, border: '1px solid #ccc', borderRadius: '4px', backgroundColor: '#f9f9f9', position: 'relative' }}>
                                        {isEditing ? (
                                            <TextField
                                                fullWidth
                                                multiline
                                                rows={10}
                                                value={editedPrompt}
                                                onChange={(e) => setEditedPrompt(e.target.value)}
                                                variant="outlined"
                                            />
                                        ) : (
                                            <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                                                {fPrompt}
                                            </Typography>
                                        )}
                                    </Box>
                                </>
                            )}
                        </>
                    )}
                </Box>
            </Dialog>

        </>
    )
}

function nFormatter(num, digits) {
    const lookup = [
        { value: 1, symbol: "" },
        { value: 1e3, symbol: "k" },
        { value: 1e6, symbol: "M" },
        { value: 1e9, symbol: "B" },
        { value: 1e12, symbol: "T" },
        { value: 1e15, symbol: "P" },
        { value: 1e18, symbol: "E" }
    ];
    const regexp = /\.0+$|(?<=\.[0-9]*[1-9])0+$/;
    const item = lookup.findLast(item => num >= item.value);
    return item ? (num / item.value).toFixed(digits).replace(regexp, "").concat(" ", item.symbol) : "0";
    // return item ? Intl.NumberFormat("en-US", { minimumFractionDigits: 3 }).format(num / item.value).toString().replace(regexp, "").concat(item.symbol) : "0";

}

const PairLinks = props => {
    const { drawerData } = props;
    //apikey: Key:***********************************
    //https://www.worldcoinindex.com/apiservice
    //https://www.worldcoinindex.com/apiservice/json?key={key}
    //https://www.worldcoinindex.com/apiservice/ticker?key=***********************************&label=ethbtc-ltcbtc&fiat=btc

    //https://coinmarketcap.com/api/documentation/v1/#section/Quick-Start-Guide
    //https://sandbox-api.coinmarketcap.com/v1/cryptocurrency/listings/latest
    //pro-api.coinmarketcap.com
    //'X-CMC_PRO_API_KEY': 'b54bcf4d-1bca-4e8e-9a24-22ff2c3d462c',
    //a29a0225-772a-4ec5-8634-a725724808f0
    return (
        <>
            <Card sx={{ mt: 4, border: 1, overflow: 'auto', alignSelf: 'center', ml: 2, flex: 1, borderColor: '#ccc', }}>
                <CardContent>
                    {JSON.stringify(drawerData)}
                </CardContent>
            </Card>
        </>
    )
}



const Center = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
});

const Element = styled('div')(({ theme }) => ({
    border: `1px solid ${(theme.vars || theme).palette.divider}`,
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
    height: 26,
    borderRadius: 2,
}));

const Value = styled('div')({
    position: 'absolute',
    lineHeight: '24px',
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
});

const Bar = styled('div')({
    height: '100%',
    '&.low': {
        backgroundColor: '#f44336',
    },
    '&.medium': {
        backgroundColor: '#efbb5aa3',
    },
    '&.high': {
        backgroundColor: '#088208a3',
    },
});


const ProgressBar = React.memo(function ProgressBar(props) {
    const { value } = props;
    const valueInPercent = value * 100;

    return (
        <Element>
            <Value>{`${valueInPercent.toFixed(0)}`}</Value>
            <Bar
                className={clsx({
                    low: valueInPercent < 30,
                    medium: valueInPercent >= 30 && valueInPercent <= 70,
                    high: valueInPercent > 70,
                })}
                style={{ maxWidth: `${valueInPercent}%` }}
            />
        </Element>
    );
});
