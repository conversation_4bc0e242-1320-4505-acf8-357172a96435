/* eslint-disable react/display-name */
/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// import { useSession } from 'next-auth/react';
import React, {
  useState, useEffect, useContext, Suspense,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useRouter } from 'next/router'
import Head from 'next/head'
import { signIn, signOut, useSession } from 'next-auth/react'
import { appvars } from '../../lib/constants'
import { Box, Button, Container, LinearProgress, Stack, Typography } from '@mui/material';
import { red } from '@mui/material/colors';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import IconButton from '@mui/material/IconButton';

import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import ArrowRight from '@mui/icons-material/ArrowRight';
import Collapse from '@mui/material/Collapse';
import Modal from '@mui/material/Modal';
import LoadingButton from '@mui/lab/LoadingButton';

import MoreVertIcon from '@mui/icons-material/MoreVert';
import ArrowForwardRoundedIcon from '@mui/icons-material/ArrowForwardRounded';
import AddchartIcon from '@mui/icons-material/Addchart';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import AddToPhotosIcon from '@mui/icons-material/AddToPhotos';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import DisabledByDefaultIcon from '@mui/icons-material/DisabledByDefault';
import NotificationImportantRounded from '@mui/icons-material/NotificationImportantRounded';
import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import CloseIcon from '@mui/icons-material/Close';

import Intervals from './user.action.battle.intervals'
import Pairs from './user.action.battle.pairs';
import Indicators from './user.action.battle.indicators';
import Trading from './user.action.battle.trading';


import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, 
  gridClasses, GridToolbar, GridToolbarContainer, GridToolbarFilterButton, GridToolbarQuickFilter } from '@mui/x-data-grid';
const ODD_OPACITY = 0.2;
// const StripedDataGrid = DataGrid;
const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[200],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));


import _ from 'lodash';

import AppLayout from '../../lib/layouts/layout.user'
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

import Slide from '@mui/material/Slide';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function Home(props) {
  const { ...rest } = props;
  const refModalFiles = useRef();
  const refModalCheckDex = useRef();
  const router = useRouter();
  const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
  const { slug } = router.query
  const [header, setHeader] = useState(false);
  const [backtestLoader, setbacktestLoader] = useState(false);
  const [battleLoader, setbattleLoader] = useState(false);
  const [battleLoader2, setbattleLoader2] = useState(false);
  const [battleListLoader, setbattleListLoader] = useState(false);
  const [activeBattleData, setactiveBattleData] = useState(false);
  const { status, data: session } = useSession({
    required: true,
    onUnauthenticated() {
      signIn();
    },
  })

  const { user } = session ? session : {};
  const { token, refreshToken } = user ? user : {};

  const [open, setOpen] = React.useState(false);
  const [battleParams, setbattleParamsFN] = React.useState({});

  const [sbattleParams, ssetbattleParamsFN] = useStickyState(BattleDefaultParameters, 'battleParameters');

  const saveBattleParams = async () => {
    let data2Post = battleParams;
    let uri = '/api/pub/data/savebattleparameters'
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify(data2Post),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        return
      }

      const datax = await res.json();

      if (!datax.error) {
        // console.log('resp', JSON.stringify(datax));
        alert('params saved!')
      } else {
        console.log('err desc', datax);
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
    }

  }
  const listSavedBattleParams = async () => {

    let uri = '/api/pub/data/listsavebattleparameters'
    try {
      const res = await fetch(uri, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        return
      }

      const datax = await res.json();

      if (!datax.error) {
        refModalFiles && refModalFiles.current && refModalFiles.current.showModal(datax);
      } else {
        console.log('err desc', datax);
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
    }


  }
  const showLocalcache = () => {
    let varx = window.localStorage.getItem('battleParameters');
    console.log('showLocalcache', varx)
  }
  useEffect(() => {
    const getList = async () => {
      colorMode.setCurrPage('/action/battle');
      // showLocalcache();
      drawerCollapsed && colorMode.collapseDrawer(true);
      let list = await battleList();
    }
    getList();
  }, []);

  useEffect(() => {
    if (!sbattleParams) {
      // console.log('no Params here!')
    } else {
      setbattleParamsFN(sbattleParams);
    }
  }, [sbattleParams])

  const clearCache = () => {
    window.localStorage.setItem('battleParameters', null);
    setbattleParamsFN({})
  }
  const backtestStart = async (battleParams, NG = false) => {
    let data2Post;
    if (NG) {
      data2Post = {
        "type": "battle",
        "dtCreated": new Date(Date.now()).toISOString(),
        "parameters": {
          ...battleParams,
          "socketUse": true,
          "socketRefreshFrequency": 10000,
          "timeoutSource": 1500,
          "limit": battleParams.candleCounts,

        }
      };
    } else {
      data2Post = {
        "type": "battle",
        "dtCreated": new Date(Date.now()).toISOString(),
        ...battleParams
      } || {
        "type": "battle",
        "dtCreated": new Date(Date.now()).toISOString(),
        "parameters": {
          "pairs": ["BTCUSDT", "ETHUSDT"], //ok
          "intervals": ["1m", "5m"], //ok
          "battleInterval": "1m", //ok
          "socketUse": true, //ok
          "socketRefreshFrequency": 10000, //ok
          // "dataRefreshIntervals": {
          //   "1m": ['15,30,45 * * * * *'],
          //   "5m": ['20 */3 * * * *', '40 */2 * * * *'],
          // },
          "limit": 500, //ok
          "timeoutSource": 1500,
          "indicatorsWParams": [ //ok
            {
              id: 1,
              indicator: "ema",
              refName: "ema9",
              params: { length: 9, source: 'close', timeFrame: '1m', },
            },
            {
              id: 17,
              indicator: "ema",
              refName: "ema21",
              params: { length: 21, source: 'close', timeFrame: '1m', },
            },
            {
              id: 2,
              indicator: "sma",
              refName: "sma9",
              params: { length: 9, source: 'close', timeFrame: '1m', },
            },
            {
              id: 3,
              indicator: "ema",
              refName: "ema7_tf2",
              params: { length: 7, source: 'close', timeFrame: '5m', },
            },
            {
              id: 4,
              indicator: "rsi",
              refName: "rsi9",
              params: {
                length: 9, source: 'close', timeFrame: '1m',
                upperBand: 70, middleBand: 50, lowerBand: 10,
                rsiSMAFastPeriod: 8, rsiSMASlowPeriod: 14
              },
            },
            {
              id: 5,
              indicator: "cci",
              refName: "cci20",
              params: {
                length: 20, source: 'close', timeFrame: '1m',
                levelHigh: 100, levelLow: -100, SMAfastPeriod: 8
              },
            },
            {
              id: 6,
              indicator: "atr",
              refName: "atr14",
              params: { length: 14, source: 'close', timeFrame: '1m', },
            },
            {
              id: 7,
              indicator: "roc",
              refName: "roc9",
              params: { length: 9, source: 'close', timeFrame: '1m', },
            },
            {
              id: 8,
              indicator: "adx",
              refName: "adx14",
              params: { length: 14, source: 'close', timeFrame: '1m', },
            },
            {
              id: 9,
              indicator: "obv",
              refName: "obv19",
              params: { length: 19, source: 'close', timeFrame: '1m', },
            },
            {
              id: 10,
              indicator: "awesomeOscillator",
              refName: "ao14",
              params: { length: 14, source: 'close', timeFrame: '1m', fastPeriod: 5, slowPeriod: 34 },
            },
            {
              id: 11,
              indicator: "psar",
              refName: "psar14",
              params: { length: 14, source: 'close', timeFrame: '1m', step: 0.02, max: 0.2 },
            },
            {
              id: 12,
              indicator: "macd",
              refName: "macd14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                fastPeriod: 5, slowPeriod: 8, signalPeriod: 3,
                SimpleMAOscillator: false, SimpleMASignal: false
              },
            },
            {
              id: 13,
              indicator: "ichimoku",
              refName: "ichi14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                conversionPeriod: 9, basePeriod: 26,
                spanPeriod: 52, displacement: 26
              },
            },
            {
              id: 14,
              indicator: "srsi",
              refName: "srsi14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                stochasticPeriod: 14, kPeriod: 3, dPeriod: 3
              },
            },
            {
              id: 15,
              indicator: "candlepatterns",
              refName: "cp14",
              params: {
                length: 14, source: 'close', timeFrame: '1m'
              },
            },
            {
              id: 16,
              indicator: "kairi",
              refName: "kairi14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                movType: 'EMA', levelUpper: 10, levelLower: -10
              },
            },

          ],
          "tradingSetup": {
            "platforms": ["tradeSimulator"], // tradeSimulator | binanceTrader
            "entry": {
              "enterPosition": true,
              "direction": "long",
              "pozBudget": 70,
              "maxBudget": 370,
              "addNews": true,
              "additionalPozRatio": "0.05",
              "useAvgAsRefPrice": false,
              "waitForBarClose": true,
              "candleInterval": 3,
              "useMartingaleVar": false,

            },
            "exit": {
              "takeProfit": "0.70",
              "stopLoss": "50",
              "useTrailingStop": false,
              "waitForBarClose": false,
              "useStopLossRatio": true,
              "stopLossRatio": "2"
            },
            "wallet": {
              "wMaxBudget": "400",
              "leverage": "7",
              "maxPair": "41",
              "pacalTPModeActive": false,
              "pacalTPTypeNetPNL": false,
              "pacalTPAmount": "4",
              "pacalSLAmount": "30"
            },
            "strategies": [
              1,
              2
            ],
            "takeprofit": 0,
            "stoploss": 0,
          }
        },
        "props": {}
      };
    }

    setbacktestLoader(true);
    let uri = '/api/pub/data/backtestStart'
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify(data2Post),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        setbacktestLoader(false)
        return
      }

      const datax = await res.json()
      setbacktestLoader(false)
      // await backtestList();
      console.log('open modal for progress... yanı new tab!')

      if (!datax.error) {
        console.log('backtestStart', datax)
        setbacktestLoader(false)
      } else {
        console.log('err desc', datax);
        setbacktestLoader(false)
        alert('backtest action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      setbacktestLoader(false)
      alert('Error Code: 981', e)
    }

  }
  const battleStart = async (battleParams, NG = false, dexCheck = true) => {
    let data2Post;
    if (NG) {
      data2Post = {
        "type": "battle",
        "dtCreated": new Date(Date.now()).toISOString(),
        "parameters": {
          ...battleParams,
          "socketUse": true,
          "socketRefreshFrequency": 10000,
          "timeoutSource": 1500,
          "limit": battleParams.candleCounts,

        }
      };
    } else {
      data2Post = {
        "type": "battle",
        "dtCreated": new Date(Date.now()).toISOString(),
        ...battleParams
      } || {
        "type": "battle",
        "dtCreated": new Date(Date.now()).toISOString(),
        "parameters": {
          "pairs": ["BTCUSDT", "ETHUSDT"], //ok
          "intervals": ["1m", "5m"], //ok
          "battleInterval": "1m", //ok
          "socketUse": true, //ok
          "socketRefreshFrequency": 10000, //ok
          // "dataRefreshIntervals": {
          //   "1m": ['15,30,45 * * * * *'],
          //   "5m": ['20 */3 * * * *', '40 */2 * * * *'],
          // },
          "limit": 500, //ok
          "timeoutSource": 1500,
          "indicatorsWParams": [ //ok
            {
              id: 1,
              indicator: "ema",
              refName: "ema9",
              params: { length: 9, source: 'close', timeFrame: '1m', },
            },
            {
              id: 17,
              indicator: "ema",
              refName: "ema21",
              params: { length: 21, source: 'close', timeFrame: '1m', },
            },
            {
              id: 2,
              indicator: "sma",
              refName: "sma9",
              params: { length: 9, source: 'close', timeFrame: '1m', },
            },
            {
              id: 3,
              indicator: "ema",
              refName: "ema7_tf2",
              params: { length: 7, source: 'close', timeFrame: '5m', },
            },
            {
              id: 4,
              indicator: "rsi",
              refName: "rsi9",
              params: {
                length: 9, source: 'close', timeFrame: '1m',
                upperBand: 70, middleBand: 50, lowerBand: 10,
                rsiSMAFastPeriod: 8, rsiSMASlowPeriod: 14
              },
            },
            {
              id: 5,
              indicator: "cci",
              refName: "cci20",
              params: {
                length: 20, source: 'close', timeFrame: '1m',
                levelHigh: 100, levelLow: -100, SMAfastPeriod: 8
              },
            },
            {
              id: 6,
              indicator: "atr",
              refName: "atr14",
              params: { length: 14, source: 'close', timeFrame: '1m', },
            },
            {
              id: 7,
              indicator: "roc",
              refName: "roc9",
              params: { length: 9, source: 'close', timeFrame: '1m', },
            },
            {
              id: 8,
              indicator: "adx",
              refName: "adx14",
              params: { length: 14, source: 'close', timeFrame: '1m', },
            },
            {
              id: 9,
              indicator: "obv",
              refName: "obv19",
              params: { length: 19, source: 'close', timeFrame: '1m', },
            },
            {
              id: 10,
              indicator: "awesomeOscillator",
              refName: "ao14",
              params: { length: 14, source: 'close', timeFrame: '1m', fastPeriod: 5, slowPeriod: 34 },
            },
            {
              id: 11,
              indicator: "psar",
              refName: "psar14",
              params: { length: 14, source: 'close', timeFrame: '1m', step: 0.02, max: 0.2 },
            },
            {
              id: 12,
              indicator: "macd",
              refName: "macd14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                fastPeriod: 5, slowPeriod: 8, signalPeriod: 3,
                SimpleMAOscillator: false, SimpleMASignal: false
              },
            },
            {
              id: 13,
              indicator: "ichimoku",
              refName: "ichi14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                conversionPeriod: 9, basePeriod: 26,
                spanPeriod: 52, displacement: 26
              },
            },
            {
              id: 14,
              indicator: "srsi",
              refName: "srsi14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                stochasticPeriod: 14, kPeriod: 3, dPeriod: 3
              },
            },
            {
              id: 15,
              indicator: "candlepatterns",
              refName: "cp14",
              params: {
                length: 14, source: 'close', timeFrame: '1m'
              },
            },
            {
              id: 16,
              indicator: "kairi",
              refName: "kairi14",
              params: {
                length: 14, source: 'close', timeFrame: '1m',
                movType: 'EMA', levelUpper: 10, levelLower: -10
              },
            },

          ],
          "tradingSetup": {
            "platforms": ["tradeSimulator"], // tradeSimulator | binanceTrader
            "entry": {
              "enterPosition": true,
              "direction": "long",
              "pozBudget": 70,
              "maxBudget": 370,
              "addNews": true,
              "additionalPozRatio": "0.05",
              "useAvgAsRefPrice": false,
              "waitForBarClose": true,
              "candleInterval": 3
            },
            "exit": {
              "takeProfit": "0.70",
              "stopLoss": "50",
              "useTrailingStop": false,
              "waitForBarClose": false,
              "useStopLossRatio": true,
              "stopLossRatio": "2"
            },
            "wallet": {
              "wMaxBudget": "400",
              "leverage": "7",
              "maxPair": "41",
              "pacalTPModeActive": false,
              "pacalTPTypeNetPNL": false,
              "pacalTPAmount": "4",
              "pacalSLAmount": "30"
            },
            "strategies": [
              1,
              2
            ],
            "takeprofit": 0,
            "stoploss": 0,
          }
        },
        "props": {}
      };
    }

    if (dexCheck) {
      let battleType = data2Post?.parameters.battleType;
      if (battleType?.dex == 'exchange') {
        refModalCheckDex && refModalCheckDex.current && refModalCheckDex.current.showModal(true, data2Post, battleType);
        return;
      }
    }
    setbattleLoader(true);
    let uri = '/api/pub/data/battleStart'
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify(data2Post),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        setbattleLoader(false)
        return
      }

      const datax = await res.json()
      setbattleLoader(false)
      await battleList();

      if (!datax.error) {
        console.log('battleStart', datax)
        setbattleLoader(false);
        router.push('/action/watch')
      } else {
        console.log('err desc', datax);
        setbattleLoader(false)
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
      setbattleLoader(false)
    }

  };

  const [dexCheckModalopen, setdexCheckModalopen] = React.useState(false);

  const handleCloseDexCheckModal = () => {
    setdexCheckModalopen(false);
  };
  // const checkDexModal = async ({mode, data2Post, battleType}) => {
  // };
  const battleStop = async ({ purge = false }) => {

    setbattleLoader2(true);
    let uri = '/api/pub/data/battleStop'
    let pData = { battleData: activeBattleData };
    pData.purge = purge;
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify(pData),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        setbattleLoader2(false)
        return
      }

      const datax = await res.json()
      setbattleLoader2(false);
      await battleList();

      if (!datax.error) {
        setbattleLoader2(false)
      } else {
        console.log('err desc', datax);
        setbattleLoader2(false)
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
      setbattleLoader2(false)
    }
  }
  const battleList = async () => {
    setbattleListLoader(true);
    let uri = '/api/pub/data/battleList'
    try {
      const res = await fetch(uri, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        setbattleListLoader(false)
        return
      }

      const datax = await res.json();
      setactiveBattleData(datax);
      setbattleListLoader(false)

      if (!datax.error) {
        setbattleListLoader(false)
      } else {
        console.log('err desc', datax);
        setbattleListLoader(false)
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
      setbattleListLoader(false)
    }

  }
  const setbattleParams = val => {
    let currV = JSON.parse(JSON.stringify(battleParams));
    let nVal = {
      ...currV,
    };
    let { fname, fvar } = val;
    nVal[fname] = fvar;
    console.log('setbattleParams', battleParams, nVal)
    setbattleParamsFN(nVal);
    ssetbattleParamsFN(nVal);
  }
  const hardReset = async (w) => {
    setbattleLoader2(true);
    let uri = '/api/pub/data/battlehardreset' + '?1=1'
    uri += w ? '&f=1' : '';
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify({ pData: true }),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        setbattleLoader2(false)
        return
      }

      const datax = await res.json()
      setbattleLoader2(false);

      if (!datax.error) {
        setbattleLoader2(false)
      } else {
        console.log('err desc', datax);
        setbattleLoader2(false)
        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
      setbattleLoader2(false)
    }
  }
  //   const [socket, setsocket] = React.useState(global.socket);
  //   useEffect(() => {
  //     console.log('Backtest Socket1', socket?.id);

  //     socket && socket.on('serverBCM', msg => {
  //       console.log('#M2 socketIO(server side)', msg) //DONT USE EXCEP TICKER HELLO
  //       socket.emit('clientBCM', ' (tickerhello) ticker msg channel ready')
  //     });

  //     return () => {
  //         // socket = null;
  //         setsocket(null);
  //     }
  // }, [socket])

  return (
    <>
      <Head>
        <title>Gauss Algo - Battle</title>
      </Head>
      <AppLayout session={session} {...props}
        pgTitle={header ? header : "" + ((Array.isArray(slug) && slug[0]) ? ('User / ' + slug[0].toString().substring(0, 15) + '...') : 'User Management')}
        pgBread={<span>x</span>}
      >
        <Stack className="mt-24">
          {/* <Input /> */}
          <Box sx={{
            m: 2,
          }}>

            <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="h5" component="div">
                  {bull}Battle&nbsp;{bull}&nbsp;
                  {/* <Chip variant="filled" color={'primary'} label={'%'} /> */}
                </Typography>
              </Box>
              <Stack sx={{ flexDirection: 'row' }}>
                <Box sx={{ borderWidth: 0, p: 0, pb: 0, borderRightWidth: 0.5, }}>
                  <Typography sx={{ fontSize: 11, px: 1, fontStyle: 'italic' }}>Cache: </Typography>
                  <Stack sx={{ flex: 1, flexDirection: 'row', alignItems: 'center', }}>
                    <LoadingButton title="Clear" onClick={clearCache} sx={{ p: 0, m: 0 }}>Clear</LoadingButton>
                    <Button onClick={showLocalcache} sx={{ p: 0, m: 0 }}>View</Button>
                  </Stack>
                </Box>

                <Box sx={{ borderWidth: 0, p: 0, pb: 0, borderRightWidth: 0.5, }}>
                  <Typography sx={{ fontSize: 11, px: 1, fontStyle: 'italic' }}>Backtest: </Typography>
                  <Stack sx={{ flex: 1, flexDirection: 'row', alignItems: 'center', mx: 1, }}>
                    <Backtest backtestStart={backtestStart} battleParams={battleParams} />
                  </Stack>
                </Box>

                <Box sx={{ borderWidth: 0, p: 0, pb: 0, }}>
                  <Typography sx={{ fontSize: 11, px: 1, fontStyle: 'italic', minWidth: '200px' }}>Battle: </Typography>
                  <Stack sx={{ borderRightWidth: 0.5, flex: 1, flexDirection: 'row', alignItems: 'center', }}>
                    {(
                      Array.isArray(activeBattleData) &&
                      activeBattleData.length === 0)
                      || (
                        !activeBattleData)
                      ||
                      (
                        typeof activeBattleData === 'object' && activeBattleData[0] == undefined) && (
                        <>
                          <LoadingButton title="Start" onClick={() => battleStart(battleParams, true)} sx={{ p: 0, m: 0 }}>Start</LoadingButton>
                          <LoadingButton loading={battleLoader2} disabled={battleLoader2} sx={{ p: 0, m: 0, ml: 1 }} onClick={() => battleStop({ purge: true })}>
                            Purge DB
                          </LoadingButton>
                        </>

                      )}
                    {activeBattleData && typeof activeBattleData === 'object' && activeBattleData[0] && activeBattleData[0]['battleID'] !== undefined && (
                      <>
                        {/* {JSON.stringify(activeBattleData[0])} */}
                        <LoadingButton loading={battleLoader2} disabled={battleLoader2} sx={{ p: 0, m: 0 }} onClick={() => battleStop({})}>
                          Stop
                        </LoadingButton>
                        <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} loading={battleLoader2} disabled={battleLoader2} onClick={() => battleStop({ purge: true })}>
                          Stop w Purge
                        </LoadingButton>
                        <br />
                      </>
                    )}
                    <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => hardReset()}>
                      Reset
                    </LoadingButton>
                    <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => hardReset('f')}>
                      FF
                    </LoadingButton>
                    {/* <Button onClick={showLocalcache} sx={{ p: 0, m: 0 }}>&nbsp;</Button> */}
                  </Stack>
                </Box>
              </Stack>
            </Stack>

            <hr />
            <Box
              sx={{
                bgcolor: open ? 'rgba(255, 255, 255, 0.2)' : null,
                pb: open ? 0 : 0,
              }}
            >
              <ListItemButton
                alignItems="flex-start"
                onClick={() => setOpen(!open)}
                sx={{
                  px: 3,
                  pt: 2.5,
                  pb: open ? 0 : 0,
                  bgcolor: open ? '#BFCFE7' : '#F8EDFF',
                  '&:hover, &:focus': { '& svg': { opacity: open ? 1 : 0 } },
                }}
              >
                <ListItemText
                  primary="SET BATTLE PARAMETERS"
                  primaryTypographyProps={{
                    fontSize: 15,
                    fontWeight: 'medium',
                    lineHeight: '20px',
                    mb: '2px',
                  }}
                  secondary="Panel için gerekli parametre düzenlemeleri"
                  secondaryTypographyProps={{
                    noWrap: true,
                    fontSize: 12,
                    lineHeight: '16px',
                    color: open ? 'rgba(0,0,0,0)' : 'rgba(0,0,0,0.5)',
                  }}
                  sx={{ my: 0 }}
                />
                <KeyboardArrowDown
                  sx={{
                    mr: -1,
                    opacity: 0,
                    transform: open ? 'rotate(-180deg)' : 'rotate(0)',
                    transition: '0.2s',
                  }}
                />
              </ListItemButton>

              <ShowSavedFiles ref={refModalFiles} />

              {battleLoader && <LinearProgress />}
              <Collapse in={!open}>
                <BattleParams {...props}
                  token={token}
                  user={user}
                  battleParams={battleParams}
                  setbattleParams={setbattleParams}
                  listSavedBattleParams={listSavedBattleParams}
                  saveBattleParams={saveBattleParams} />
              </Collapse>

            </Box>

            <br />
            {/* <BattleParams {...props} /> */}
            <Box>
              {activeBattleData && typeof activeBattleData === 'object' && activeBattleData[0] && activeBattleData[0]['battleID'] !== undefined && (
                <>
                  {/* {JSON.stringify(activeBattleData[0])} */}
                  <LoadingButton loading={battleLoader2} disabled={battleLoader2} variant="outlined" onClick={() => battleStop({})}>
                    Stop Battle
                  </LoadingButton>
                  <LoadingButton sx={{ marginLeft: 5 }} loading={battleLoader2} disabled={battleLoader2} variant="outlined" onClick={() => battleStop({ purge: true })}>
                    Stop Battle w Purge
                  </LoadingButton>
                  <br />
                </>
              )}
              {/* <LoadingButton loading={battleLoader2} disabled={battleLoader2} variant="outlined" onClick={() => battleStop({})}>
                Stop Battle  
              </LoadingButton> */}
            </Box>
            <br />
             {/* Modal for dex tunes  */}
            <CheckDexModal 
              ref={refModalCheckDex}
              token={token}
              battleParams={battleParams} 
              battleStart={battleStart} />
          </Box>
        </Stack>
      </AppLayout>
    </>
  )
}

export async function getServerSideProps() {
  return { props: { appvars } }
}


const bull = (
  <Box
    component="span"
    sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
  >
    •
  </Box>
);

const BattleParams = props => {
  const { activeBattleData, user } = props;
  const { token, refreshToken } = user ? user : {};
  const [value, setValue] = React.useState('1');

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <>
      <Box sx={{ width: '100%', typography: 'body1' }}>
        <TabContext value={value}>
          <Stack sx={{ borderBottom: 1, borderColor: 'divider', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <TabList onChange={handleChange} aria-label="lab API tabs example">
              <Tab label="Base" value="1" />
              <Tab label="Indicators" value="2" />
              <Tab label="Rulesets" value="3" />
              <Tab label="Trading" value="4" />
              <Tab label="Summary" value="5" />
            </TabList>
            <Stack sx={{ mx: 2, flexDirection: 'row', alignItems: 'center' }}>

              <Typography sx={{ fontSize: '11px' }}>Parameters:&nbsp;&nbsp; </Typography>
              <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => props.listSavedBattleParams()}>
                Load
              </LoadingButton>
              &nbsp;&nbsp;
              <LoadingButton sx={{ p: 0, m: 0, ml: 1, }} onClick={() => props.saveBattleParams()}>
                Save
              </LoadingButton>

            </Stack>
          </Stack>
          <TabPanel value="1"><BattleParamsPairs {...props} user={user} token={token} /></TabPanel>
          <TabPanel value="2"><Indicators {...props} user={user} token={token} /></TabPanel>
          <TabPanel value="3">
            <Suspense fallback={<div>loading...</div>}>
              <Strategies {...props} user={user} token={token} />
            </Suspense>
          </TabPanel>
          <TabPanel value="4">
            <Suspense fallback={<div>loading...</div>}>
              <Trading {...props} user={user} token={token} />
            </Suspense>
          </TabPanel>
          <TabPanel value="5">
            <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
              navigator.clipboard.writeText(JSON.stringify(props.battleParams));

            }}>
              <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                <code style={{ fontSize: '12px' }}>{JSON.stringify(props.battleParams, null, 4)}</code>
              </pre>
            </Box>
          </TabPanel>
        </TabContext>
      </Box>
    </>
  )
}

const BattleParamsPairs = props => {
  const setParameters = vals => {
    props.setbattleParams && props.setbattleParams(vals);
  }
  return (
    <>
      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
          <Typography variant="button" display="block" gutterBottom>
            Intervals
          </Typography>
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
          <Intervals {...props} multi={true} callbackFN={setParameters} />
        </Box>
      </Stack>

      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
          <Typography variant="button" display="block" gutterBottom>
            Battle Interval
          </Typography>
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
          <Intervals {...props} candleCounts={true} callbackFN={setParameters} />
        </Box>
      </Stack>

      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 150, width: 150 }}>
          <Typography variant="button" display="block" gutterBottom>
            Pairs
          </Typography>
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>
          <Pairs {...props} callbackFN={setParameters} />

        </Box>
      </Stack>

    </>
  )
}

function useStickyState(defaultValue, key) {
  const [value, setValue] = React.useState(defaultValue);
  // console.log('useStickyState', defaultValue, key)
  React.useEffect(() => {
    const stickyValue = window.localStorage.getItem(key);
    if (stickyValue && stickyValue !== 'null') {
      // console.log('stickyValue2', JSON.parse(stickyValue))
      setValue(JSON.parse(stickyValue));
    } else {
      const fData = async (vx = {}) => {
        window.localStorage.setItem(key, JSON.stringify(vx));
        setValue(JSON.parse(JSON.stringify(vx)));
      }
      fData(defaultValue);
    }
  }, [key]);

  React.useEffect(() => {
    window.localStorage.setItem(key, JSON.stringify(value));
  }, [key, value]);

  return [value, setValue];
}

const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));
const Strategies = props => {
  const refShowRuleSet = useRef();
  const [data, setdata] = React.useState([]);
  const [selected, setSelected] = useState([]);
  const [selectedIndicators, setselectedIndicators] = useState([]);
  const [indicators, setindicators] = React.useState([]);

  function fnFetchIx() {
    return new Promise(async (resolve) => {
      try {
        var uri = '/api/pub/data/indicatorsandparams'
        // console.log(uri)
        const data = await fetcher(uri)
        resolve(data.data)
      } catch (e) {
        console.log('fetch err indicatorsandparams', e)
      }
    });
  }
  function BattleParamConverter(vx) {
    let resp = []
    vx.map(v => {
      let params = {};
      for (let p in v.params) {
        params[p] = v.params[p].default
      }

      let a = {
        id: v.id,
        indicator: v.indicator,
        refName: params.refName,
        battleParams: params
      }
      resp.push(a)
    })
    return resp
  }


  function fnFetch() {
    return new Promise(async (resolve) => {
      try {
        var uri = '/api/pub/data/rulesetsgroup'
        // console.log(uri)
        const data = await fetcher(uri)
        let data2Proc = Array.isArray(data?.data?.data) ? data?.data?.data : [];
        data2Proc.map(a => a.ruleSet = a.ruleSet && typeof a.ruleSet !== 'object' ? JSON.parse(a.ruleSet) : a.ruleSet);
        data2Proc.map(d => d.indicators = getIndicatorsInRuleSet(d));
        resolve(data2Proc)
      } catch (e) {
        console.log('fetch err rulesetsgroup', e)
      }
    });
  }


  useEffect(() => {
    const getX = async () => {
      let ixx = await fnFetch();
      // console.log('rulesets', ixx);
      Array.isArray(ixx) && ixx.sort((a, b) => (a.rulesetName.toLowerCase()) > (b.rulesetName.toLowerCase()) && 1 || -1);
      setdata(ixx);
    };
    const getIX = async () => {
      let ixx = await fnFetchIx();
      setindicators(ixx);
    };
    getX();
    getIX();
    // console.log('props strate', JSON.stringify(props));
  }, []);

  // useEffect(() => {
  //   console.log('selected', selected)
  // }, [selected]);



  useEffect(() => {
    if (props.battleParams) {
      const InitBoxes = async () => {
        let arrX = props.battleParams.indicatorsWParams
        let ax = Array.isArray(arrX) && [...new Set(arrX.map(a => a.indicator))];
        // console.log('1battleParams setselectedIndicators', ax);
        setselectedIndicators(ax);
      }
      InitBoxes();
    }
  }, [])


  useEffect(() => {
    if (props.battleParams) {
      let parami = props.battleParams.rulesets
      const InitBoxes = async () => {
        let arrX = props.battleParams.indicatorsWParams
        let ax = Array.isArray(arrX) && [...new Set(arrX.map(a => a.indicator))];
        // console.log('2battleParams setselectedIndicators', ax);
        setSelected(parami);
        setselectedIndicators(ax);
      }
      parami && !_.isEqual(parami, selected) && InitBoxes();
    }
  }, [props.battleParams])


  useEffect(() => {
    // console.log('eff selectedIndicators data', data, selectedIndicators);
    if (Array.isArray(selectedIndicators) && selectedIndicators.length !== 0 && data.length !== 0) {

      let arrBase = JSON.parse(JSON.stringify(data));
      arrBase.map(a => a.avail = a.indicators.map(ai => selectedIndicators.includes(ai)).every(arr => arr === true));
      !_.isEqual(data, arrBase) && setdata(arrBase);
      // !_.isEqual(data, arrBase) && console.log('set', arrBase);

      let arrBaseS = JSON.parse(JSON.stringify(selected));
      arrBaseS.map(a => a.avail = a.indicators.map(ai => selectedIndicators.includes(ai)).every(arr => arr === true));
      !_.isEqual(selected, arrBaseS) && setSelected(arrBaseS);

    }

  }, [selectedIndicators, data])

  const getIndicatorsInRuleSets = (rulesetJson, setRuleSet = true) => {
    let arr = rulesetJson
    setRuleSet && Array.isArray(arr) && arr.map(a => a.ruleSet = JSON.parse(a.ruleSet));
    let ind = [];
    Array.isArray(arr) && arr.map(a => a.ruleSet.map(xx => xx.criteria.map(cc => ind.push(cc))))
    let inx = [...new Set(ind.map(r => r.indicator))];
    return inx;
  };

  const getIndicatorsInRuleSet = (rulesetJson, withRefs = false) => {
    let arr = rulesetJson
    let ind = [];
    arr.ruleSet.map(xx => xx.criteria.map(cc => ind.push(cc)));
    let inx = [...new Set(ind.map(r => !withRefs ? r.indicator : {
      indicator: r.indicator, indicatorRef: r.indicatorRef
    }))];
    return inx;
  }

  const AddRuleSet = inx => {
    let stg = [...selected];
    let uTaskIndex = stg.findIndex(t => t.rsgID == inx.rsgID);
    if (uTaskIndex < 0) {
      inx.id = Date.now() + Math.floor(Math.random() * 10);
      // inx.ruleSet = inx.ruleSet ? JSON.parse(JSON.s) : [];
      stg = [...stg, inx];
      setSelected(stg);

      let aIX = inx; // setPostData(inx);
      aIX.ruleSet = aIX.ruleSet && typeof aIX.ruleSet !== 'object' ? JSON.parse(aIX.ruleSet) : aIX.ruleSet

      let currBIX = Array.isArray(props.battleParams.rulesets) ? [...props.battleParams.rulesets] : [];
      currBIX.push(aIX);
      props.setbattleParams && props.setbattleParams({ fname: 'rulesets', fvar: currBIX });

    }
  }

  const RemoveRuleSet = inx => {
    let stg = [...selected];
    let uTaskIndex = stg.findIndex(t => t.id == inx.id);
    stg.splice(uTaskIndex, 1);
    setSelected([...stg]);
    props.setbattleParams && props.setbattleParams({ fname: 'rulesets', fvar: stg });
  }

  const viewRuleset = inx => {
    inx.ruleSet = inx.ruleSet ? JSON.parse(JSON.stringify(inx.ruleSet)) : [];
    refShowRuleSet.current && refShowRuleSet.current.showRuleset(inx)
  }

  const isRulesetSelected = rux => {
    // console.log('selected rules', selected, rux);
    let tmp = selected.map(s => s.rsgID);
    let resp = tmp.includes(rux.rsgID);
    return resp;
  }

  const addIndicators = s => {
    if (props.battleParams) {
      let currIX = props.battleParams?.indicatorsWParams;
      let reqInx = getIndicatorsInRuleSet(s, true);
      let d2Push = [];

      if (Array.isArray(reqInx)) {
        reqInx.map(r => {
          let ixData = indicators.find(ix => ix.indicator == r.indicator);
          let params = {};
          for (let p in ixData.params) {
            params[p] = ixData.params[p].default
          };

          let battleIXData = {
            id: (+new Date * Math.random()).toString(36).substring(0, 6),
            indicator: r.indicator,
            refName: r.indicatorRef,
            battleParams: params
          }
          d2Push.push(battleIXData)
        });
      };
      // console.log('currIX', currIX);
      // console.log('d2Push', d2Push);
      if (Array.isArray(currIX)) {
        currIX = [...currIX, ...d2Push];
      } else {
        currIX = [...d2Push];
      }
      props.setbattleParams && props.setbattleParams({ fname: 'indicatorsWParams', fvar: currIX });
      console.log('currIX2B', currIX);

    } else {
      alert('battleParams x?')
    }

    return true;
  }

  return (
    <>
      <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', mb: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', minHeight: 35, minWidth: 600, maxHeight: 600, overflow: 'auto' }}>
          <Card sx={{ minWidth: 600 }}>
            <CardHeader
              sx={{ background: '#ffddaa' }}
              avatar={
                <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                  <NotificationImportantRounded />
                </Avatar>
              }
              action={
                <IconButton aria-label="settings">
                  <MoreVertIcon />
                </IconButton>
              }
              title="Rulesets"
              subheader="Select rulesets"
            />
            <Divider />
          </Card>
            <Divider />
            <SelectRulesetView data={data} selected={selected} 
            AddRuleSet={AddRuleSet}
            viewRuleset={viewRuleset}
            isRulesetSelected={isRulesetSelected} />
        </Box>

        <Box sx={{ borderBottom: 1, borderLeftWidth: 1, pl: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, minWidth: '400px', overflow: 'auto' }}>
          <Typography variant="button" bgcolor={red[100]} sx={{ px: 2 }} display="block" gutterBottom>
            Selected Rulesets {selected && Array.isArray(selected) && selected.length !== 0 && selected.length}
          </Typography>
          <Divider />

          <List
            sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}
            component="nav"
            aria-labelledby="nested-list-subheader"
            dense={true}
          >
            {Array.isArray(selected) && selected.length !== 0 && selected.map((s, ix) => {
              return (

                <ListItemButton key={ix.toString()} sx={{ bgcolor: s.avail ? 'background.paper' : 'red' }}>
                 
                 {!s.avail && (
                    <>
                      &nbsp;&nbsp;
                      <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                        <AddchartIcon sx={{ width: 22 }} onClick={() => addIndicators(s)} />
                      </IconButton>
                      &nbsp;&nbsp;
                    </>
                  )}<IconButton aria-label="settings" sx={{ padding: '4px' }}>
                    <RemoveRedEyeIcon sx={{ width: 16 }} onClick={() => viewRuleset(s)} />
                  </IconButton>
                  <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                    <DeleteForeverIcon sx={{ width: 22 }} onClick={() => RemoveRuleSet(s)} />
                  </IconButton>
                   <ListItemText onClick={() => viewRuleset(s)} primary={s.rulesetName} secondary={s.avail ? '' : 'indicators are not matching'} />

                </ListItemButton>
              )
            })}
          </List>
        </Box>

        <Box sx={{ borderBottom: 1, borderLeftWidth: 1, pl: 1, borderColor: 'divider', minHeight: 35, marginLeft: 2, width: '100%' }}>

          <ShowStrategyRules ref={refShowRuleSet} />
          <Typography
            size={'xs'}
            style={{ borderWidth: 1, backgroundColor: '#ffeeee', m: 1, p: 0, px: 0, cursor: 'pointer' }}
            sx={{ fontSize: 11, p: 0, px: 1 }}
          >
            selectedIndicators:&nbsp;
            {JSON.stringify(selectedIndicators)}
          </Typography>
        </Box>
      </Stack>
    </>
  )
}

const SelectRulesetView = forwardRef((props, ref) => {
  const [rulesetData, setrulesetData] = useState(false)

  const [loading, setloading] = useState(false)
  const [data, setData] = useState(false);
  useEffect(() => {
    if(props.data) {
      let arr = props.data;
      Array.isArray(arr) && arr.map((a, i) => a.id = (i + 1))
      setData(arr);
    }
  }, [props.data]);

  const columns = [
    {
      field: 'rulesetName', headerName: 'Ruleset', width: 380, 
      renderCell: (params) => {
        // console.log('params', params)
        // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
        return (
          <Box sx={{py: 1, px: 1, flexDirection: 'column',
            backgroundColor: props.isRulesetSelected(params.row) ? '#ffcc00' : undefined,
            flexGrow: 1,

          }}>
            {params.row?.avail && !props.isRulesetSelected(params.row) && <ArrowForwardRoundedIcon fontSize="10" sx={{ color: '#000', width: '20px' }} />}
            {params.row?.avail && props.isRulesetSelected(params.row) && <ArrowRight fontSize="10" sx={{ color: '#000', width: '20px' }} />}
            {!params.row?.avail && (
              <span title={'missing indicators'}><DisabledByDefaultIcon sx={{ color: '#000', width: '20px' }} /></span>
            )}
            <span title={JSON.stringify(params.row)} style={{ fontSize: '15px' }}>{(params.row?.rulesetName) }</span>
            <br />
            <span title={params.value} style={{ fontSize: 12 }}>{(params.row?.direction) }</span>
            <span title={params.value} style={{ fontSize: 12 }}>{' ' + JSON.stringify(params.row?.indicators) }</span>
            {/* <span>{JSON.stringify(props.isRulesetSelected(params.row))}</span> */}

            {/* <span title={params.value} style={{ fontSize: '10px', color: '#999' }}>&nbsp;{(params.row?.dtupdated) }</span> */}

          </Box>
        );
      }
    },

    {
      field: 'direction', headerName: 'Type', width: 80, 
      renderCell: (params) => {
        // console.log('params', params)
        // let val = nFormatter(params.value, 3); //.toISOString()).local().format("YYYY-MM-DD HH:mm:ss").toString()
        return (
          <Box sx={{py: 1, px: 1, flexDirection: 'column',
            backgroundColor: props.isRulesetSelected(params.row) ? '#ffcc00' : undefined,
            flexGrow: 1,

          }}> 
            <span title={params.value} style={{ fontSize: 12 }}>{(params.row?.direction) }</span>
            
          </Box>
        );
      }
    },
    {
      field: 'action', headerName: 'action', minWidth: 120,
      renderCell: (params) => {
        return (
          <>
            <Stack sx={{ flexDirection: 'row', overflowX: 'auto' }}>
              <Typography
                onClick={() => props.AddRuleSet(params.row)}
                size={'xs'}
                style={{ borderWidth: 1, borderColor: '#333', borderRadius: '3px', backgroundColor: '#ccc', m: 1, p: '2px', cursor: 'pointer' }}
                sx={{ fontSize: '11px', p: 0, px: 1 }}
              >
                add
              </Typography>
              &nbsp;
              &nbsp;
              <Typography
                onClick={() => props.viewRuleset(params.row)}
                size={'xs'}
                style={{ borderWidth: 1, borderColor: '#333', borderRadius: '3px', backgroundColor: '#ccc', m: 1, p: '2px', cursor: 'pointer' }}
                sx={{ fontSize: 11, p: 0, px: 1 }}
              >
                view
              </Typography>
              &nbsp;
              &nbsp;
            </Stack>
          </>
        );
      }
    },

  ];

  const handleRowDoubleClick = v => {
    console.log('handleRowDoubleClick', v);
    props.viewRuleset(v)
  };
  const handleRowClick = async v => {
    console.log('right', true, v.row)(event);
  };


  useImperativeHandle(ref, () => ({
    async showRuleset(ix) {
      console.log('asda', ix)
    },
  }));
  return (
    <>

      <Box
        sx={{
          // height: 300,
          width: '100%',

          '& .super-app-theme--cell': {
            backgroundColor: 'rgba(224, 183, 60, 0.55)',
            color: '#1a3e72',
            fontWeight: '600',
          },
          '& .super-app.negative': {
            backgroundColor: 'rgba(157, 255, 118, 0.49)',
            color: '#1a3e72',
            fontWeight: '600',
          },
          '& .super-app.positive': {
            backgroundColor: '#d47483',
            color: '#1a3e72',
            fontWeight: '600',
          },
        }}
      >
        {data && <StripedDataGrid
          autoHeight
          rows={data}
          columns={columns}
          rowHeight={55}
          // getRowHeight={() => 'auto'}
          // getEstimatedRowHeight={() => 120}
          loading={loading}
          disableColumnMenu={true}
          // slots={{ toolbar: GridToolbar }}
          slots={{ toolbar: CustomToolbar, }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              csvOptions: { disableToolbarButton: true },
              printOptions: { disableToolbarButton: true },
            }
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'rulesetName', sort: 'asc' }],
            },
            pagination: {
              paginationModel: { page: 0, pageSize: 5 },
            },
          }}
          pageSizeOptions={[5, 20, 50, 100]}
          getRowClassName={(params) =>
            params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
          }

          onRowDoubleClick={(row, event) => {
            handleRowDoubleClick(row.row);
          }}
          // onRowClick={handleRowClick}
          sx={{
            m: 1,
            boxShadow: 2, 
          }} />
        }
      </Box>

    </>
  )
});

function CustomToolbar() {
  return (
    <GridToolbarContainer>
      <GridToolbarFilterButton />
      <Box sx={{ flexGrow: 1 }} />
      <GridToolbarQuickFilter />
    </GridToolbarContainer>
  );
}

const ShowStrategyRules = forwardRef((props, ref) => {
  const [rulesetData, setrulesetData] = useState(false)
  useImperativeHandle(ref, () => ({
    async showRuleset(ix) {
      ix.ruleSet = ix.ruleSet && typeof ix.ruleSet !== 'object' ? JSON.parse(ix.ruleSet) : ix.ruleSet
      setrulesetData({ ...ix })
    },
  }));
  return (
    <>
      <Typography onClick={() => setrulesetData(false)} variant="button" bgcolor={red[100]} sx={{ px: 2 }} display="block" gutterBottom>
        Ruleset Info {rulesetData ? ' / X' : ''}
      </Typography>

      <Typography
        size={'xs'}
        style={{ borderWidth: 1, backgroundColor: '#ffeeee', m: 1, p: 0, px: 0, cursor: 'pointer' }}
        sx={{ fontSize: 11, p: 0, px: 1 }}
      >
        {rulesetData ? '' + rulesetData?.rulesetName : ''}
      </Typography>

      <Divider />

      <Box sx={{ minWidth: '400px', overflow: 'auto', maxHeight: '450px', m: 0, wordBreak: 'break-all' }} onClick={() => {
        navigator.clipboard.writeText(JSON.stringify(rulesetData));
      }}>
        {rulesetData && <pre sx={{ maxWidth: '400px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
          <code className='text-xs' style={{ fontSize: '11px' }}>{JSON.stringify(rulesetData, null, 4)}</code>
        </pre>}
      </Box>

    </>
  )
});


const BattleDefaultParameters = {
  "pairs": [
    "BTCUSDT",
    "ETHUSDT",
    "SOLUSDT",
    "AVAXUSDT",
    "MATICUSDT",
    "ADAUSDT",
    "FILUSDT"
  ],
  "candleCounts": 300,
  "intervals": [
    "1m"
  ],
  "battleInterval": [
    "1m"
  ],
  "indicatorsWParams": [
    {
      "id": 1714940724602,
      "indicator": "rsi",
      "refName": "rsiRef50",
      "battleParams": {
        "refName": "rsiRef50",
        "length": 50,
        "source": "close",
        "timeFrame": "1m",
        "upperBand": 70,
        "middleBand": 50,
        "lowerBand": 10,
        "rsiSMAFastPeriod": 8,
        "rsiSMASlowPeriod": 14
      }
    },
    {
      "id": 1714940753917,
      "indicator": "ema",
      "refName": "emaRef200",
      "battleParams": {
        "refName": "emaRef200",
        "length": 200,
        "source": "close",
        "timeFrame": "1m"
      }
    }
  ],
  "rulesets": [
    {
      "_id": "667e9214627d7faec89e1c50",
      "ruleSet": [
        {
          "id": "RuleSet1719570790252",
          "indicators": [
            "ema",
            "rsi"
          ],
          "criteria": [
            {
              "id": "Rule1719570802096",
              "name": "ema200",
              "indicator": "ema",
              "indicatorRef": "emaRef200",
              "item": "emaPosition",
              "rule": "==`below`"
            },
            {
              "id": "Rule1719570902932",
              "name": "rsi50",
              "indicator": "rsi",
              "indicatorRef": "rsiRef50",
              "item": "indicatorValue",
              "rule": ">50"
            }
          ],
          "name": "ema200rsi50long",
          "key": "ema200rsi50long_key",
          "cond": 1
        }
      ],
      "rulesetName": "ema200rsi50",
      "direction": "long",
      "cond": 1,
      "rsgID": "7ug4gn",
      "is_deleted": 0,
      "dtupdated": "2024-06-28T10:36:04.820Z",
      "indicators": [
        "ema",
        "rsi"
      ],
      "id": 1721115667491
    }
  ],
  "trading": {
    "entry": {
      "enterPositon": true,
      "actOnBarClose": true,
      "direction": "long",
      "positionBudget": 100,
      "positionMaxBudget": 400,
      "addAdditionPosition": true,
      "additionPositionPercentage": 1,
      "additionPosRefPriceIsAvg": true,
      "additionPositionCandleInterval": 3,
      "useMartingaleVar": false,
      "useMartingaleLevels": [
        { level: 1, multiplier: 2 },
        { level: 2, multiplier: 4 }
      ],
    },
    "exit": {
      "takeProfitRatio": 2,
      "stopLoss": 50,
      "stopLossUsePercentage": true,
      "stopLossPercentage": 4,
      "useTrailingStop": true,
      "actOnBarClose": true
    },
    "wallet": {
      "usePacalMode": true,
      "walletBudget": 400,
      "walletLeverage": 20,
      "pacalTakeProfitUSD": 100,
      "pacalStopLossUSD": 400,
      "pacalMaxPairs": 20
    }
  }
}

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};


const ShowSavedFiles = forwardRef((props, ref) => {
  const [open, setOpen] = React.useState(false);
  const [filelist, setfilelist] = React.useState([]);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  useImperativeHandle(ref, () => ({
    async showModal(files) {
      if (files && Array.isArray(files.data)) {
        setfilelist(files.data)
        handleOpen();
      } else {
        alert('files list error')
      }
    },
  }));
  const fetchFile = fl => {
    return new Promise(async (resolve, reject) => {
      let uri = '/api/pub/data/loadbattleparameters?file=' + fl
      try {
        const res = await fetch(uri, {
          method: 'GET',
          headers: {
            'X-Host': 'Subanet.com',
          },
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          reject(false);
        }
        const datax = await res.json();
        if (!datax.error) {
          // console.log('datax', datax.filedata);
          let value = datax.filedata.data;
          resolve(value)

        } else {
          console.log('err desc', datax);
          alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
          reject(false);
        }
      }
      catch (e) {
        console.log('e', e)
        alert('Error Code: 981', e)
        reject(false);
      }

    });
  }
  const viewFile = async (file) => {
    console.log('loadFile', file);
    let value = await fetchFile(file);
    console.log(value);
  };
  const loadFile = async (file) => {
    console.log('loadFile', file);
    let value = await fetchFile(file);
    window.localStorage.setItem('battleParameters', JSON.stringify(value));
    setTimeout(function () {
      window.location.reload(1);
    }, 300);
    // 
  };
  const deleteFile = async (file) => {
    let uri = '/api/pub/data/deletebattleparametersfile?file=' + file
    if (confirm("Delete file! - " + file)) {

      try {
        const res = await fetch(uri, {
          method: 'GET',
          headers: {
            'X-Host': 'Subanet.com',
          },
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          alert(message);
          return
        }
        const datax = await res.json();
        if (!datax.error) {
          // console.log('datax', datax.filedata);
          handleClose();
          return
        } else {
          console.log('err desc', datax);
          alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
          return
        }
      }
      catch (e) {
        console.log('e', e)
        alert('Error Code: 981', e)
        return
      }

    } else {
      console.log('cancelled')
    }

  };
  return (
    <div>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Typography id="modal-modal-title" variant="h6" component="h2" color={'black'}>
            Saved Parameters
          </Typography>

          <Divider />
          <CardContent sx={{ padding: 0 }}>
            {Array.isArray(filelist) && filelist.length !== 0 && (
              <List
                sx={{ width: '100%', bgcolor: 'background.paper' }}
                component="nav"
                aria-labelledby="nested-list-subheader"
                dense={true}
              >
                {Array.isArray(filelist) && filelist.map((i, ix) => {
                  // let indx = getIndicatorsInRuleSet(i);
                  // console.log('isRulesetSelected', isRulesetSelected(i));
                  return (
                    <ListItemButton key={ix.toString()} sx={{ backgroundColor: null, p: 0, }}>
                      <ListItemIcon style={{ minWidth: '10px' }}>
                        <ArrowForwardRoundedIcon fontSize="10" />
                      </ListItemIcon>
                      <ListItemText sx={{ color: 'black', textWrap: 'nowrap', wordBreak: 'keep-all' }} onClick={() => viewFile(i)} primary={i} />
                      <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                        <DeleteForeverIcon sx={{ width: 16 }} onClick={() => deleteFile(i)} />
                      </IconButton>
                      &nbsp;
                      {true && ( //i?.avail
                        <IconButton aria-label="settings" sx={{ padding: '4px' }}>
                          <FileDownloadIcon sx={{ width: 18 }} onClick={() => loadFile(i)} />
                        </IconButton>
                      )}
                      <Divider />
                    </ListItemButton>
                  )
                })}
              </List>
            )}
            {(Array.isArray(filelist) && filelist.length == 0) && (
              <Typography onClick={() => handleClose()} sx={{ color: 'black', my: 2, p: 1, backgroundColor: 'yellow' }}>No File</Typography>
            )}
          </CardContent>
          <Divider />

          <IconButton aria-label="settings" sx={{ padding: '4px', fontSize: '12px' }}>
            <CloseIcon sx={{ width: 18 }} onClick={() => handleClose()} ></CloseIcon>
            <Typography sx={{ mx: 2, fontSize: 12 }} onClick={() => handleClose()}>Close</Typography>
          </IconButton>
        </Box>
      </Modal>
    </div>
  );

});

const keyHead = 'dex:';
const CheckDexModal = forwardRef((props, ref) => {
  const key = 'dexvar';
  const { token, battleParams } = props;
  const [open, setOpen] = React.useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);
  const handleStart = () => {
    handleClose();
    props.battleStart(props.battleParams, true, false);

  }
  const [mounted, setmounted] = useState(false);
  const [dexvalues, setdexvalues] = useState({});
  const [positions, setpositions] = useState([]);
  const [loader, setLoader] = useState(false);
  const [accountData, setaccountdata] = useState(false);

  const [testLoader, settestLoader] = useState(false);
  const [accountLoader, setaccountLoader] = useState(false);
  
  const [irregularPairs, setirregularPairs] = useState([]);


  let keyx = keyHead + 'foo'; 
  const calcPozData = refD => {
      let resp = {};
      const {openOrders, positions, fPRisk} = refD;
      // console.log('refD', refD)
      let pozs = positions;
      let oo = openOrders;
      let pr = positions;
      Array.isArray(pozs) && pozs.map(p => {
          let prx = [...pr].find(pz => pz.symbol == p.symbol);
          // console.log('prx', prx);
          p.markPrice = prx.markPrice;
          p.liquidationPrice =  prx.liquidationPrice;
          p.direction =  prx.direction;

          let oos = Array.isArray(oo) && oo.filter(o => o.symbol == p.symbol);
          p.oos = oos;
           
      })
      return pozs;
  }

  const refreshAccountData = () => {
    console.log('refresh data!')
    fetchAccountData(props.battleParams?.battleType.config, true);
  };

  const fetchAccountData = async (dval = dexvalues, force = false) => {
    setaccountLoader(true);
    const dtBop = Date.now();
    let uri = '/api/pub/data/dexpositions';
    const currValue = window.localStorage.getItem(keyx);
    if (!force && currValue) {
      let jsonCurrValue = JSON.parse(currValue)
      if (Date.now() - jsonCurrValue?.updatetime < (60000 * 10)) {
        console.log('fetch not needed', jsonCurrValue);
        setaccountdata(jsonCurrValue);
        let pozData = calcPozData(jsonCurrValue);
        setpositions(pozData);
        setaccountLoader(false);
        return currValue
      } else {
        console.log('fetch new data', jsonCurrValue);
        // fetchNeeded = true;
        window.localStorage.setItem(keyx, null);
      }
    } else {
      // console.log('currValue', currValue, force);
    }
    try {
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token,
          'X-Host': 'Subanet.com',
        },
        body: JSON.stringify(dval),
      })
      if (!res.ok) {
        var message = `An error has occured: ${res.status} - ${res.statusText}`;
        alert(message);
        setaccountLoader(false)
        return
      }
      const datax = await res.json();
      setaccountLoader(false)
      if (!datax.error) {
        let resp = {
          ...datax.data,
        };
        if (datax.data) {
          // let assets = Array.isArray(datax.data.assets) && datax.data.assets.filter(a => a.updateTime !== 0);
          // let positions = Array.isArray(datax.data.positions) && datax.data.positions.filter(a => parseFloat(a.initialMargin) !== 0);
          // resp.assets = assets;
          // resp.positions = positions;
          resp.loadTime = Date.now() - dtBop;
          resp.updatetime = Date.now();
          resp.updatetimeISO = new Date(Date.now()).toISOString();
        } 
        window.localStorage.setItem(keyx, JSON.stringify(resp));
        setaccountdata(resp);

        let pozData = calcPozData(resp);
        // console.log('resp:', resp, pozData, props.battleParams?.pairs)
        setpositions(pozData);

        setaccountLoader(false);
        return resp;
      } else {
        console.log('err desc', datax);
        setaccountLoader(false)
        alert('test failed! \n'); //JSON.stringify(values, null, 2)
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
      setaccountLoader(false)
      return false;
    };
  };

  useImperativeHandle(ref, () => ({
    async showModal(mode, data2Post, battleType) {
      mode && handleOpen();
      !mode && handleClose();
      battleType && setdexvalues(battleType?.config)
      console.log('data2Postdata2Post', data2Post, battleType);
      // if (files && Array.isArray(files.data)) {
      //   setfilelist(files.data)
      //   handleOpen();
      // } else {
      //   alert('files list error')
      // }
    },
  }));

  const dexInfo = async () => {
    try {
      var uri = '/api/pub/data/dexinfo';
      settestLoader(true);
      const data = await fetcher(uri)
      let keyx = key + ':' + dexvalues.dexCode + ':info'
      console.log('dexinfo data saved', keyx, data);
      window.localStorage.setItem(keyx, JSON.stringify(data));
      settestLoader(false);
    } catch (e) {
      console.log('fetch err', e)
      settestLoader(false);
    }
  }

  useEffect(() => {
    if (props.battleParams && props.battleParams?.battleType?.config) {
        setdexvalues(props.battleParams.battleType.config);
        !mounted && fetchAccountData(props.battleParams.battleType.config);
        !mounted && setmounted(true);
        // !mounted && fetchBalanceData(props.battleParams.battleType.config);
    };
}, [props.battleParams]);

useEffect(() => {
  if (positions && Array.isArray(positions) && positions.length !==0) {
    let pozx = positions.map(p => p.symbol);
    let pozy = props.battleParams?.pairs;
      // console.log('pairs', props.battleParams?.pairs)
      // console.log('positions', pozx );
      const missings = pozx.filter(value => !pozy.includes(value));
      setirregularPairs(missings);
      // console.log('res', missings );
  };
}, [positions])


useEffect(() => {
  if (open) {
    refreshAccountData();
  };
}, [open])


let sumUnrealizedPNL = 0;
let sumNotional = 0;
  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted

      fullWidth={true}
      maxWidth={'lg'}

      onClose={handleClose}
      aria-describedby="alert-dialog-slide-description"
    >
      <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', pr: 2 }}>
        <DialogTitle>{"Battle Will Start On dEXchange?"}</DialogTitle>
        <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography sx={{
            cursor: 'pointer', width: '120px',
            borderWidth: '1px', p: '2px', fontSize: '11px', my: 2,
            px: 1, borderRadius: '5px', backgroundColor: '#cecece', textAlign: 'center',
          }} onClick={dexInfo}>
            updat{testLoader ? 'ing' : 'e'} precisions
          </Typography>
          &nbsp;&nbsp;
          <Typography
            sx={{
              cursor: 'pointer', width: '140px',
              borderWidth: '1px', p: '2px', fontSize: '11px', my: 2, textAlign: 'center',
              px: 1, borderRadius: '5px', backgroundColor: '#cecece'
            }}
            onClick={refreshAccountData}
          >
            refresh{accountLoader ? 'ing' : ''} position data
          </Typography>
        </Stack>
      </Stack>

      <DialogContent>
        <DialogContentText id="alert-dialog-slide-description">
          {accountLoader ? 'Checking open positions and orders...' : 
            ((Array.isArray(positions) && positions.length !== 0) || (Array.isArray(positions) && positions.length !== 0)) ? 
              'There are open positions / orders. Gauss will add them to battle.'
            : 'dEX seems clear. Click start battle!'}.
            
            { Array.isArray(irregularPairs) && 
              irregularPairs.length !== 0 && 
              <Typography sx={{backgroundColor: 'yellow'}}>
              check pairs vs active dex positions!!! {JSON.stringify(irregularPairs)}
              </Typography> 
            }

          <Stack sx={{ display: 'block', borderWidth: 0.5, m: 1, p: 1, backgroundColor: accountLoader ? '#ffcc00' : undefined }}>
            <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'space-between' }}>
              <Box sx={{ borderWidth: 0.5, p: '2px', flex: 1, m: '2px' }}>
                {Array.isArray(positions) && positions.length !== 0 && (
                  <Box sx={{ py: '4px' }}>
                    <Typography component={'h1'}>Positions {Array.isArray(positions) && positions.length}</Typography>
                    <Stack sx={{ flexDirection: 'row' }}>
                      <Cell text={'symbol'} a={'center'} t={'header'} />
                      <Cell text={'dir'} w={'60px'} a={'center'} t={'header'} />
                      <Cell text={'positionAmt'} t={'header'} a={'center'} />
                      <Cell text={'entryPrice'} t={'header'} a={'center'} pre={''} w={'90px'} />
                      <Cell text={'markPrice'} t={'header'} a={'center'} pre={''} w={'90px'} />
                      <Cell text={'unRlizedProfit'} t={'header'} a={'center'} pre={''} w={'120px'} />
                      <Cell text={'notional'} t={'header'} a={'center'} pre={''} />
                    </Stack>
                  </Box>
                )}
                {Array.isArray(positions) && positions.map((p, i) => {
                  //DONE: add uPNLRatio...
                  let ratio = parseFloat(p.unRealizedProfit) / (parseFloat(p.entryPrice) * parseFloat(p.positionAmt)) * (parseFloat(p.positionAmt)< 0 ? -1 : 1);
                  ratio = (ratio * 100).toFixed(2);
                  sumUnrealizedPNL += parseFloat(p.unRealizedProfit);
                  sumNotional += Math.abs(parseFloat(p.notional));
                  return (
                    <Box key={i.toString()} sx={{ py: '4px' }}>
                      <Stack sx={{ flexDirection: 'row', }}>
                        <Cell text={p.symbol} />
                        <Cell text={p.direction} w={'60px'} />
                        <Cell text={p.positionAmt} a={'right'} />
                        <Cell text={parseFloat(p.entryPrice).toFixed(5)} a={'right'} pre={'$ '} w={'90px'} />
                        <Cell text={parseFloat(p.markPrice).toFixed(5)} a={'right'} pre={'$ '} w={'90px'} />
                        <Cell text={parseFloat(p.unRealizedProfit).toFixed(2) + ' / %' + ratio} a={'right'} pre={'$ '} w={'120px'} />
                        <Cell text={parseFloat(p.notional).toFixed(2)} a={'right'} pre={'$ '} />
                      </Stack>
                    </Box>
                  )
                })}

                <Typography sx={[{
                  mx: '2px', my: '2px',
                  px: '2px',
                  borderWidth: '1px',
                  fontSize: '12px',
                  width: props.w || '160px',
                  textAlign: props.a || 'left',
                  backgroundColor: props.t == 'header' ? '#cecece' : undefined,
                }, props.sx]}>
                  {props.pre || 'unrealizedPNL $ '}{sumUnrealizedPNL.toFixed(2)}
                </Typography>
                <Typography sx={[{
                  mx: '2px', my: '2px',
                  px: '2px',
                  borderWidth: '1px',
                  fontSize: '12px',
                  width: props.w || '160px',
                  textAlign: props.a || 'left',
                  backgroundColor: props.t == 'header' ? '#cecece' : undefined,
                }, props.sx]}>
                  {props.pre || 'notional $ '}{sumNotional.toFixed(2)}
                </Typography>

              </Box>

              <Box sx={{ borderWidth: 0.5, p: '2px', flex: 1, m: '2px' }}>
                {Array.isArray(accountData?.openOrders) && accountData?.openOrders.length !== 0 && (
                  <Box sx={{ py: '4px' }}>
                    <Typography component={'h1'}>Open Orders {Array.isArray(accountData?.openOrders) && accountData?.openOrders.length}</Typography>
                    <Stack sx={{ flexDirection: 'row' }}>
                      <Cell text={'symbol'} a={'center'} t={'header'} />
                      <Cell text={'dir'} w={'60px'} a={'center'} t={'header'} />
                      <Cell text={'origQty'} t={'header'} a={'center'} />
                      <Cell text={'price'} t={'header'} a={'center'} pre={''} />
                      <Cell text={'notional'} t={'header'} a={'center'} pre={''} />
                      <Cell text={'reduce/close'} t={'header'} a={'center'} pre={''} />
                    </Stack>
                  </Box>
                )}
                {Array.isArray(accountData?.openOrders) && accountData?.openOrders.map((p, i) => {
                  return (
                    <Box key={i.toString()} sx={{ py: '4px',  }}>
                      <Stack sx={{ flexDirection: 'row', }}>
                        <Cell text={p.symbol} sx={{backgroundColor: p.reduceOnly ? undefined : p.closePosition ? undefined : '#ffcc00'}} />
                        <Cell text={p.side} w={'60px'} />
                        <Cell text={p.origQty} a={'right'} />
                        <Cell text={parseFloat(p.price).toFixed(5)} a={'right'} pre={'$ '} />
                        <Cell text={(parseFloat(p.price) * parseFloat(p.origQty)).toFixed(2)} a={'right'} pre={'$ '} />
                        <Cell text={(p.reduceOnly ? ' Rdc ' : '') + '' + (p.closePosition ? ' Cls ' : '') + '' } a={'center'} pre={''} />
                      </Stack>
                    </Box>
                  )
                })}
              </Box>
            </Stack>
            <Box sx={{ height: '5px' }}>
              {accountLoader && <LinearProgress />}
            </Box>
            {/* checking positions.... list positions and open orders */}
            {/* {JSON.stringify(positions, ' ', 4)} */}
          </Stack>
          <Typography sx={{fontSize: '10px', color: '#ccc', px: '8px'}}>updated on: {accountData?.updatetimeISO}</Typography>
        </DialogContentText>

      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleStart}>Start Battle</Button>
      </DialogActions>
    </Dialog>
  )
});

let numX = "0";
const Backtest = forwardRef((props, ref) => {
  const [open, setOpen] = React.useState(false);
  const [taskRunned, settaskRunned] = React.useState(false);
  const [backteststatus, setbackteststatus] = React.useState(0);
  const [backteststatusDesc, setbackteststatusDesc] = React.useState('');
  const handleOpen = () => {
    // console.log('Backtest handleOpen', socket?.id);
    if (socket?.id) {
      setbackteststatus(0);
      setbackteststatusDesc('Click start to simulate');
      setOpen(true);
    } else {
      alert('refresh/change page')
    }
  };
  const handleClose = () => {
    setOpen(false);
    setbackteststatus(0);
    setbackteststatusDesc('');
    settaskRunned(false);
    numX = "0";
  };
  const [socket, setsocket] = React.useState(global.socket);
  useImperativeHandle(ref, () => ({
    async showModal(files) {
      alert('files list error')
    },
  }));

  const viewResults = async () => {
    // router.push('/action/backtestresults');
    window.open('/backtest', "_blank");
  }

  const backtestStart = async () => {
    if (socket && socket?.id) {
      props.backtestStart && props.backtestStart(props.battleParams, true);
    } else {
      console.log('hata: - soccket not connected')
    }
    // console.log(value);
  };

  const runBacktestScript = async () => {
    var uri = '/api/pub/data/runbacktest'
    // try {
    //   
    //   // console.log(uri)
    //   const data = await fetcher(uri);
    //   if (data) {
    //     console.log('runned!', data)
    //   }
    //   return false
    // } catch (e) {
    //   console.log('fetch err runBacktestScript', e)
    // }

    try {
      if (!taskRunned) {
        settaskRunned(true);
        numX = "1";
        setbackteststatusDesc('running backtest calculations...');
        const res = await fetch(uri, {
          method: 'GET',
          headers: {
            'X-Host': 'Subanet.com',
          },
        })
        if (!res.ok) {
          var message = `An error has occured: ${res.status} - ${res.statusText}`;
          console.log('error in backtest calculations');
          setbackteststatusDesc('error in backtest calculations...');
          alert(message);
          // reject(false);
        }
        const datax = await res.json();
        if (!datax.error) {
          let value = datax;
          setbackteststatus('Backtest calculations completed!');
          setbackteststatusDesc('completed calculations...');
          value?.openReports && window.open('/backtest', "_blank");
          return value
        } else {
          console.log('err desc', datax);
          setbackteststatusDesc('error in backtest calculations...');
          alert('backtest action failed! \n'); //JSON.stringify(values, null, 2)
          return false
        }
      }
    }
    catch (e) {
      console.log('e', e)
      alert('Error Code: 981', e)
    }

  };


  useEffect(() => {
    socket && socket.on('serverBCM', msg => {
      if (msg && typeof msg == 'object') {
        if (msg.mType == 'info') {
          console.log(new Date(Date.now()), '#MBacktest', msg) //DONT USE EXCEP TICKER HELLO
          setbackteststatusDesc(msg.mDesc);
        } else if (msg.mType == 'error') {
          console.error(new Date(Date.now()), '#MBacktest-error', msg)
          setbackteststatusDesc(msg.mDesc);

        } else if (msg.mType == 'act') {
          if (msg.action == 'setActionStatus') {
            console.log(new Date(Date.now()), '#setActionStatus', msg.actionValue) //DONT USE EXCEP TICKER HELLO
            setbackteststatus(msg.actionValue);
            setbackteststatusDesc(msg.actionDesc);
            if (msg.actionValue == 2) {
              console.log('data fetch ok!, run calculations....');
              runBacktestScript();
            }
          } else {
            console.log(new Date(Date.now()), msg )
          }
        }
      }
      socket.emit('clientBCM', ' (tickerhello) ticker msg channel ready')
    });

    return () => {
      // socket = null;
      // setsocket(null);
    }
  }, [global.socket])


  return (
    <>
      <LoadingButton title="backtestStart" onClick={() => handleOpen()} sx={{ p: 0, m: 0, borderWidth: 1 }}>Start Test</LoadingButton>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={[style, { flexDirection: 'row' }]}>
          <Typography id="modal-modal-title" variant="h6" component="h2" color={'black'}>
            Battle Test

            <Typography
              onClick={() => viewResults()}
              sx={{ color: 'black', my: 2, p: 1, cursor: 'pointer' }}>
              <Typography sx={{ fontSize: '10px' }}>
                ViewResults
              </Typography>
            </Typography>

          </Typography>

          <Divider />
          <CardContent sx={{ padding: 0 }}>
            <Typography
              onClick={() => backtestStart()}
              sx={{ color: 'black', my: 2, p: 1, cursor: 'pointer' }}>
              {backteststatus == 0 ? 'Start' : backteststatus == 1 ? 'Work in progress' : 'more...'}
              <Typography sx={{ fontSize: '10px' }}>
                {JSON.stringify(backteststatusDesc)}
              </Typography>
            </Typography>
          </CardContent>
          <Divider />

          <IconButton aria-label="settings" sx={{ padding: '4px', fontSize: '12px' }}>
            <CloseIcon sx={{ width: 18 }} onClick={() => handleClose()} ></CloseIcon>
            <Typography sx={{ mx: 2, fontSize: 12 }} onClick={() => handleClose()}>Close</Typography>
          </IconButton>
        </Box>
      </Modal>
    </>
  )
});


const Cell = props => {
  return (
    <Typography sx={[{
      mx: '2px',
      px: '2px',
      borderWidth: '1px',
      fontSize: '12px',
      width: props.w || '80px',
      textAlign: props.a || 'left',
      backgroundColor: props.t == 'header' ? '#cecece' : undefined,
    }, props.sx]}>
      {props.pre || ''}{props.text}
    </Typography>
  )
}