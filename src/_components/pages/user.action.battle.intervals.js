import React, { useState, useEffect, useRef } from 'react';
import Chip from '@mui/material/Chip';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import Box from '@mui/material/Box';
import _ from 'lodash';

const CardParamsTimes = props => {
  const tagsData = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "12h", "1d"];
  const [selectedTags, setselectedTags] = useState([]);
  const [candles, setCandles] = useState(100)

  const handleChange = (tag, checked) => {
    if (props.multi) {
      // console.log('selectedTags', selectedTags)
      const nextSelectedTags = checked ? [...selectedTags, tag] : selectedTags.filter(t => t !== tag);
      const newA = [...new Set(nextSelectedTags)];
      setselectedTags(newA);
      props?.callbackFN && props?.callbackFN({ fname: 'intervals', fvar: newA });
    } else {
      const nextSelectedTags = checked ? [tag] : selectedTags.filter(t => t !== tag);
      setselectedTags(nextSelectedTags);
      props?.callbackFN && props?.callbackFN({ fname: 'battleInterval', fvar: nextSelectedTags });
    
    }
  }
  const handleCandle = (vals) => {
    let nVal = vals?.target?.value ? parseFloat(vals?.target.value) : vals
    setCandles(nVal)
    props.candleCounts && props?.callbackFN && props.callbackFN({ fname: 'candleCounts', fvar: nVal });
  }

  useEffect(() => {
    if (props.battleParams && (props.battleParams.intervals || props.battleParams.battleInterval || props.battleParams.candleCounts)) {
      let parami = props.multi ? props.battleParams.intervals : props.battleParams.battleInterval;
      let bars = props.battleParams && props.battleParams.candleCounts
      bars && candles !== bars && setCandles(bars);
      parami && !_.isEqual(parami, selectedTags) && setselectedTags(parami)
    }
  }, [props.battleParams])

  return (
    <>
      <Box
        sx={{
          mr: 4,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
        }}>

        {tagsData.map(tag => (
          <Chip
            key={tag}
            style={{ border: '1px solid #ccc', marginRight: 5 }}
            checked={Array.isArray(selectedTags) && selectedTags.indexOf(tag) > -1}
            onClick={() => handleChange(tag, !(Array.isArray(selectedTags) && selectedTags.indexOf(tag) > -1))}
            label={tag}
            color={Array.isArray(selectedTags) && selectedTags.indexOf(tag) > -1 ? 'primary' : undefined}
            variant={'filled'}
            sx={{borderRadius: 2}}
            // variant={selectedTags.indexOf(tag) > -1 ? 'filled' : 'outlined'}
          />
        ))}

        {props.candleCounts && (
          <Tooltip key={'candleQtyX'} placement="top"
          sx={{ padding: 0 }}
          title={'Number of Candles for Indicator calculations'}>
          <FormControl>
          <CssTextField id="candleQty"
            size="small"
            value={candles}
            onChange={handleCandle} 
            style={{ width: '80px', marginLeft: 5, paddingTop: 0, paddingLeft: 5, paddingBottom: 0, margin: 0, paddingTop: 0, height: 6 }}
          />
          </FormControl>

        </Tooltip>
        )}

      </Box>

    </>
  );
}

export default CardParamsTimes;

import { alpha, styled } from '@mui/material/styles';
import { GlobalStyles, createTheme, ThemeProvider, FormControl } from '@mui/material';

const theme = createTheme({
  components: {
    MuiInputBase: {
      defaultProps: {
        disableInjectingGlobalStyles: true,
      },
    },
  },
});

const CssTextField = styled(TextField)({
  '& label.Mui-focused': {
    color: '#A0AAB4',
  },
  '& .MuiInputBase-input': {
    borderRadius: 8,
    position: 'relative',
    // backgroundColor: theme.palette.mode === 'light' ? '#F3F6F9' : '#1A2027',
    // border: '1px solid',
    // borderColor: theme.palette.mode === 'light' ? '#E0E3E7' : '#2D3843',
    fontSize: 14,
    // width: 'auto',
    // marginLeft: '5px',
    padding: '6px 6px',
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    '&:focus': {
      // boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 0.2rem`,
      // borderColor: theme.palette.primary.main,
    },
  },
});
