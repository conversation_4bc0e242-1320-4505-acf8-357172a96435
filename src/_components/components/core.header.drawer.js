/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, { useState, useEffect, useContext } from "react";
import { useRouter } from 'next/router'
import { Typography, Badge, Box, AppBar, Stack, IconButton, Toolbar, Divider } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

import CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange';
import { useTheme, styled, createTheme, ThemeProvider } from '@mui/material/styles';
import { Sidebar, Menu, MenuItem, SubMenu, menuClasses, MenuItemStyles } from 'react-pro-sidebar';
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import { SidebarHeader } from './core.header.drawer.title';
//import icons from react icons
const themes = {
    light: {
        sidebar: {
            backgroundColor: '#ffffff',
            color: '#607489',
        },
        menu: {
            menuContent: '#fbfcfd',
            icon: '#0098e5',
            hover: {
                backgroundColor: '#c5e4ff',
                color: '#44596e',
            },
            disabled: {
                color: '#9fb6cf',
            },
        },
    },
    dark: {
        sidebar: {
            backgroundColor: '#0b2948',
            color: '#8ba1b7',
        },
        menu: {
            menuContent: '#082440',
            icon: '#59d0ff',
            hover: {
                backgroundColor: '#00458b',
                color: '#b6c8d9',
            },
            disabled: {
                color: '#3e5e7e',
            },
        },
    },
};

// hex to rgba converter
const hexToRgba = (hex, alpha) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);

    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};
import CottageOutlinedIcon from '@mui/icons-material/CottageOutlined';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SportsMartialArtsIcon from '@mui/icons-material/SportsMartialArts';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import SettingsInputComponentIcon from '@mui/icons-material/SettingsInputComponent';
import HistoryToggleOffIcon from '@mui/icons-material/HistoryToggleOff';
import BlurOnIcon from '@mui/icons-material/BlurOn';
import PlaylistAddCheckIcon from '@mui/icons-material/PlaylistAddCheck';
import CalculateIcon from '@mui/icons-material/Calculate';
import FunctionsIcon from '@mui/icons-material/Functions';
import VapingRoomsIcon from '@mui/icons-material/VapingRooms';
import DisplaySettingsIcon from '@mui/icons-material/DisplaySettings';
import PointOfSaleIcon from '@mui/icons-material/PointOfSale';  
import SavingsIcon from '@mui/icons-material/Savings';
import HistoryIcon from '@mui/icons-material/History';
import TimelineIcon from '@mui/icons-material/Timeline';

export default function DarkModeButton(props) {
    const router = useRouter();
    const themeMain = useTheme();
    const { mode, colorMode, currPage, isTablet, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const [collapsed, setCollapsed] = React.useState(false);
    const [toggled, setToggled] = React.useState(drawerToggled);
    const [broken, setBroken] = React.useState(drawerBroken);
    const [rtl, setRtl] = React.useState(false);
    const [hasImage, setHasImage] = React.useState(false);
    const [theme, setTheme] = React.useState(mode);

    const menuItemStyles = {
        root: {
            fontSize: "13px",
            fontWeight: 400
        },
        icon: {
            color: themes[theme].menu.icon,
            [`&.${menuClasses.disabled}`]: {
                color: themes[theme].menu.disabled.color
            }
        },
        SubMenuExpandIcon: {
            color: "#b6b7b9"
        },
        subMenuContent: ({ level }) => ({
            backgroundColor:
                level === 0
                    ? hexToRgba(
                        themes[theme].menu.menuContent,
                        hasImage && !collapsed ? 0.4 : 1
                    )
                    : "transparent"
        }),
        button: {
            [`&.${menuClasses.disabled}`]: {
                color: themes[theme].menu.disabled.color
            },
            "&:hover": {
                backgroundColor: hexToRgba(
                    themes[theme].menu.hover.backgroundColor,
                    hasImage ? 0.8 : 1
                ),
                color: themes[theme].menu.hover.color
            }
        },
        label: ({ open }) => ({
            fontWeight: open ? 600 : undefined
        })
    }


    useEffect(() => {
        setTheme(mode);
    }, [mode])

    useEffect(() => {
        setCollapsed(isTablet);
    }, [isTablet])

    useEffect(() => {
        setCollapsed(drawerCollapsed)
    }, [drawerCollapsed])

    useEffect(() => {
        (collapsed !== drawerCollapsed) && colorMode.collapseDrawer(collapsed)
    }, [collapsed])

    useEffect(() => {
        setToggled(drawerToggled);
    }, [drawerToggled])

    useEffect(() => {
        (toggled !== drawerToggled) && colorMode.toggleDrawer(toggled);
    }, [toggled])

    useEffect(() => {
        (broken !== drawerBroken) && colorMode.brokeDrawer(broken)
    }, [broken])

    const navto = ({page}) => {
        router.push(page);
    }

    return (
        <Stack  sx={{
            display: 'flex',
            height: '100vh',
            bgcolor: 'background.default',
            position: 'sticky', top: 0, 
            zIndex: "9999"
        }}>
            
            <Box sx={{ display: 'flex', overflowY: 'auto',  }} flex={1}>
                <Sidebar
                    collapsed={collapsed}
                    toggled={toggled}
                    onBackdropClick={() => setToggled(false)}
                    onBreakPoint={setBroken}
                    image="https://user-images.githubusercontent.com/25878302/144499035-2911184c-76d3-4611-86e7-bc4e8ff84ff5.jpg"
                    rtl={rtl}
                    breakPoint="md"
                    // customBreakPoint="md"
                    backgroundColor={hexToRgba(themes[theme].sidebar.backgroundColor, hasImage ? 0.9 : 1)}
                    rootStyles={{
                        color: themes[theme].sidebar.color,
                    }}
                >
                    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                    <SidebarHeader rtl={rtl} style={{ marginBottom: '16px', marginTop: '4px' }} collapsed={collapsed} />

                        <div style={{ flex: 1, marginBottom: '32px' }}>

                        <div style={{ padding: '0 24px', marginBottom: '8px', marginTop: '32px' }}>
                                <Typography
                                    variant="body2"
                                    fontWeight={600}
                                    style={{ opacity: collapsed ? 0 : 0.7, letterSpacing: '0.5px' }}
                                >
                                    Main
                                </Typography>
                            </div>

                            <Menu menuItemStyles={menuItemStyles}>
                                <MenuItem 
                                    icon={<CottageOutlinedIcon />}
                                    onClick={() => navto({page: '/'})}
                                    style={{
                                        backgroundColor: currPage === '/' ? themes[theme].menu.hover.backgroundColor : null,
                                    }}
                                >
                                    Home
                                </MenuItem>
                                <MenuItem icon={<TrendingUpIcon />}
                                    onClick={() => navto({page: '/main/market'})}
                                    style={{
                                        backgroundColor: currPage === '/main/market' ? themes[theme].menu.hover.backgroundColor : null,
                                    }} >
                                    Market
                                </MenuItem>
                                
                                <Divider />
                            </Menu>

                            <div style={{ padding: '0 24px', marginBottom: '2px', marginTop: '12px' }}>
                                <Typography
                                    variant="body2"
                                    fontWeight={600}
                                    style={{ opacity: collapsed ? 0 : 0.7, letterSpacing: '0.5px' }}
                                >
                                    Battle
                                </Typography>
                            </div>

                            <Menu menuItemStyles={menuItemStyles}>
                                <MenuItem 
                                    icon={<SportsMartialArtsIcon />} 
                                    onClick={() => navto({page: '/action/battle'})}
                                    style={{
                                        backgroundColor: currPage === '/action/battle' ? themes[theme].menu.hover.backgroundColor : null,
                                    }}
                                >
                                    Parameters
                                </MenuItem>

                                <MenuItem 
                                    icon={<MonitorHeartIcon />} 
                                    onClick={() => navto({page: '/action/watch'})}
                                    style={{
                                        backgroundColor: currPage === '/action/watch' ? themes[theme].menu.hover.backgroundColor : null,
                                    }}
                                >
                                    Watch
                                </MenuItem>
                                <MenuItem 
                                    icon={<CurrencyExchangeIcon />} 
                                    onClick={() => navto({page: '/action/trades'})}
                                    style={{
                                        backgroundColor: currPage === '/action/trades' ? themes[theme].menu.hover.backgroundColor : null,
                                    }}
                                >
                                    Trades
                                </MenuItem>
                                <Divider />
                                {/* <Divider />
                                <MenuItem 
                                    icon={<HistoryIcon />} 
                                    onClick={() => navto({page: '/backtest'})}
                                    target="_blank"
                                    component={'a'}
                                    style={{
                                        backgroundColor: currPage === '/backtest' ? themes[theme].menu.hover.backgroundColor : null,
                                    }}
                                >
                                    Backtest Results
                                </MenuItem>
                                <Divider /> */}

                            </Menu> 

                            <div style={{ padding: '0 24px', marginBottom: '2px', marginTop: '12px' }}>
                                <Typography
                                    variant="body2"
                                    fontWeight={600}
                                    style={{ opacity: collapsed ? 0 : 0.7, letterSpacing: '0.5px' }}
                                >
                                    Advanced
                                </Typography>
                            </div>

                            <Menu menuItemStyles={menuItemStyles}>
                                <MenuItem icon={<SettingsInputComponentIcon />}
                                    onClick={() => navto({page: '/advanced/nodes'})}
                                    style={{
                                        backgroundColor: currPage === '/advanced/nodes' ? themes[theme].menu.hover.backgroundColor : null,
                                    }} 
                                    // suffix={<Badge variant="success">New</Badge>}
                                    >
                                    Nodes
                                </MenuItem>
                                <MenuItem icon={<PlaylistAddCheckIcon />}
                                    onClick={() => navto({page: '/advanced/nodetasks'})}
                                    style={{
                                        backgroundColor: currPage === '/advanced/nodetasks' ? themes[theme].menu.hover.backgroundColor : null,
                                    }} 
                                    // suffix={<Badge variant="success">New</Badge>}
                                    >
                                    Node Tasks
                                </MenuItem>

                                <MenuItem icon={<CalculateIcon />}
                                    onClick={() => navto({page: '/advanced/battlerulesets'})}
                                    style={{
                                        backgroundColor: currPage === '/advanced/battlerulesets' ? themes[theme].menu.hover.backgroundColor : null,
                                    }} 
                                    // suffix={<Badge variant="success">New</Badge>}
                                    >
                                    Battle RuleSets
                                </MenuItem>

                                <MenuItem disabled icon={<BlurOnIcon />}>Node Web Sockets</MenuItem>
                                {/* <MenuItem disabled icon={<FaRegHeart />}>
                                    Settings
                                </MenuItem> */}
                                <Divider/>
                            </Menu>


                            <div style={{ padding: '0 24px', marginBottom: '2px', marginTop: '12px' }}>
                                <Typography
                                    variant="body2"
                                    fontWeight={600}
                                    style={{ opacity: collapsed ? 0 : 0.7, letterSpacing: '0.5px' }}
                                >
                                    Sandbox
                                </Typography>
                            </div>

                            <Menu menuItemStyles={menuItemStyles}>
                                <MenuItem icon={<SettingsInputComponentIcon />}
                                    onClick={() => navto({page: '/ai/market-anomali'})}
                                    style={{
                                        backgroundColor: currPage === '/ai/market-anomali' ? themes[theme].menu.hover.backgroundColor : null,
                                    }} 
                                    suffix={<Badge variant="success">New</Badge>}>
                                    AI-Market Anomaly
                                </MenuItem> 
                                {/* <Divider /> */}
                            </Menu>

                        </div>
                        {/* <SidebarFooter collapsed={collapsed} /> */}
                    </div>
                </Sidebar>
                <div style={{ display: 'fixed', top: 10, left: '-65px', right: 0, width: 0, height: 40, zIndex: 9900 }}>
                    <BtnToggle
                        broken={broken}
                        collapsed={collapsed}
                        toggled={toggled}
                        fnx={colorMode.collapseDrawer}
                        fnxT={colorMode.toggleDrawerCollapse}
                    />
                </div>
            </Box>
        </Stack>

    )
};



const BtnToggle = props => {
    const collapseDrawer = () => {
        props.fnx(false);
    };
    const toggleDrawer = () => {
        props.fnxT();
    };
    return (
        <Toolbar
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: props.collapsed ? 'center' : 'flex-end',
              px: [1],
            }}
          >
            <IconButton onClick={toggleDrawer}>
            {props.collapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
          </Toolbar>
    )
}