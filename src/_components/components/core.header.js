/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, { useState, useEffect, useContext } from "react";
import { Box, Badge, Stack } from '@mui/material';
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import AppBar from './core.header.appbar';

function DefaultComponent(props) {
    const { mode, colorMode, isMobile, isTablet } = useContext(ColorModeContext);
    return (
        <>
            <AppBar {...props} />
            <Stack
                sx={{
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'background.default',
                    color: 'text.primary',
                    p: 0,
                }}>
                {/* <span>Mobile - {JSON.stringify(isMobile)}</span>
                <span>isTablet - {JSON.stringify(isTablet)}</span> */}
            </Stack>

        </>
    )
}
export default DefaultComponent