
import React, { useState, useEffect, useContext } from "react";
import { useTheme, styled, createTheme, ThemeProvider } from '@mui/material/styles';

import AppBar from "@mui/material/AppBar"
import Box from "@mui/material/Box"
import Toolbar from "@mui/material/Toolbar"
import Container from "@mui/material/Container"

import AuthBtn from './core.header.appbar.auth';
import { SidebarHeader } from './core.header.drawer.title';
import { DrawerButton } from './core.header.appbar.menuButton';

function ResponsiveAppBar() {
  const theme = useTheme();
  return (
    <AppBar position="sticky" sx={{
      backgroundColor: theme.palette.mode === 'dark' ? theme.background.paperDark : theme.background.paper,
    }}>
      <Container maxWidth="false">
        <Toolbar disableGutters>
          <DrawerButton />
          <Box sx={{ flexGrow: 1, display: { xs: "flex", md: "none" }, mr: 1 }} >
            <SidebarHeader />
          </Box>

          <Box sx={{ flexGrow: 0 }}>
            {/* <DarkModeButton /> */}
            <AuthBtn />
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  )
}
export default ResponsiveAppBar
