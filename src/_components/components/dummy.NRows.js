/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, { useState, useEffect, useContext } from "react";
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";
import { Stack, Box } from "@mui/material";
export default function RownN(props) {
    const [data, setdata] = React.useState([]);

    useEffect(() => {
        let rowN = props.n ? props.n : 10;
        let Arr = []
        for (let i = 0; i < rowN; i++) {
            Arr.push(i);
        }
        setdata(Arr)
    }, [props.n])
    return (
        <>
            <Stack
                direction="column">
                {Array.isArray(data) && data.map((d, i) => {
                    return (
                        <Box key={i.toString()}>
                            <span>{d}</span>
                        </Box>
                    )
                })}
            </Stack>
        </>
    )
};

