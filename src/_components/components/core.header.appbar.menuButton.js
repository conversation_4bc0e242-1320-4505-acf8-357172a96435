
import React, { useState, useEffect, useContext } from "react";
import { signIn, signOut, useSession } from 'next-auth/react'
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import MenuIcon from "@mui/icons-material/Menu"
import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

export const DrawerButton = (props) => {
    const { data: session } = useSession();
    const { mode, colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
    const handleOpenNavMenu = event => {
        !drawerBroken && colorMode.toggleDrawerCollapse();
        drawerBroken && colorMode.toggleDrawerToggle();
    }
    return (
        <Box sx={{ flexGrow: 1 }}>
        <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleOpenNavMenu}
            color="inherit"
        >
            <MenuIcon />
        </IconButton>
    </Box>
    )
}
export default DrawerButton
