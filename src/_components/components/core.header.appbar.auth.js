import React, { useState, useEffect, useRef } from "react";
import { signIn, signOut, useSession } from 'next-auth/react'

import { useTheme } from '@mui/material/styles';

import {
    Avatar,
    Box,
    Card,
    CardContent,
    CardActions,
    Chip,
    ClickAwayListener,
    Divider,
    Grid,
    InputAdornment,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    OutlinedInput,
    Paper,
    Popper,
    Stack,
    Switch,
    Typography,
    Button,
    Popover,
} from '@mui/material';
import Fade from '@mui/material/Fade';
import SettingsInputSvideoIcon from '@mui/icons-material/SettingsInputSvideo';
import DarkModeButton from './button.darkMode';

function DefaultComponent(props) {
    const { data: session } = useSession();
    return (
        <>
            {session ? <LoggedIn session={session} /> : <span>LogIn</span>}
        </>
    )
}
export default DefaultComponent
// material-ui
const LoggedIn = props => {
    const theme = useTheme();
    const anchorRef = useRef(null);
    const {session} = props;
    const [open, setOpen] = useState(false);
    const [anchorEl, setAnchorEl] = React.useState(null);
    const [placement, setPlacement] = React.useState();

    const handleClose = (event) => {
        if (anchorRef.current && anchorRef.current.contains(event.target)) {
            return;
        }
        setOpen(false);
    };
 
    const handleClick = (newPlacement) => (event) => {
        setAnchorEl(event.currentTarget);
        setOpen((prev) => placement !== newPlacement || !prev);
        setPlacement(newPlacement);
      };

    const prevOpen = useRef(open);
    useEffect(() => {
        if (prevOpen.current === true && open === false) {
            anchorRef.current.focus();
        }

        prevOpen.current = open;
    }, [open]);

    return (
        <>
            <Chip
                sx={{
                    height: '48px',
                    alignItems: 'center',
                    borderRadius: '27px',
                    transition: 'all .2s ease-in-out',
                    borderColor: theme.palette.primary.light,
                    backgroundColor: theme.palette.primary.light,
                    '&[aria-controls="menu-list-grow"], &:hover': {
                        borderColor: theme.palette.primary.main,
                        background: `${theme.palette.primary.main}!important`,
                        color: theme.palette.primary.light,
                        '& svg': {
                            stroke: theme.palette.primary.light
                        }
                    },
                    '& .MuiChip-label': {
                        lineHeight: 0
                    }
                }}
                icon={
                    <Avatar
                        // src={<AccountBoxOutlinedIcon />}
                        sx={{
                            ...theme.typography.mediumAvatar,
                            margin: '8px 0 8px 8px !important',
                            cursor: 'pointer'
                        }}
                        ref={anchorRef}
                        aria-controls={open ? 'menu-list-grow' : undefined}
                        aria-haspopup="true"
                        color="inherit"
                    />
                }
                label={<SettingsInputSvideoIcon stroke={1.5} color={theme.palette.primary.main} />}
                variant="outlined"
                ref={anchorRef}
                aria-controls={open ? 'menu-list-grow' : undefined}
                aria-haspopup="true"
                // onClick={handleToggle}
                onClick={handleClick('bottom-end')}
                color="primary"
                aria-describedby={'id'}
            />
            <ClickAwayListener onClickAway={handleClose}>
                <Popper
                    // Note: The following zIndex style is specifically for documentation purposes and may not be necessary in your application.
                    sx={{ zIndex: 1200 }}
                    open={open}
                    anchorEl={anchorEl}
                    placement={placement}
                    transition
                >
                    {({ TransitionProps }) => (
                        <Fade {...TransitionProps} timeout={350}>
                            <Paper>
                                <Box sx={{ minWidth: 275 }}>
                                    <Card variant="outlined"><CardConx {...props} /></Card>
                                </Box>
                                {/* <Typography sx={{ p: 2 }}>The content of the Popper.</Typography> */}
                            </Paper>
                        </Fade>
                    )}
                </Popper>
            </ClickAwayListener>
        </>
    )
}

const bull = (
    <Box
      component="span"
      sx={{ display: 'inline-block', mx: '2px', transform: 'scale(0.8)' }}
    >
      •
    </Box>
  );

  const CardConx = props => {
    return (
        <>
            <CardContent>
                <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
                    Hello Again,
                </Typography>
                <Typography variant="h5" component="div">
                    {props.session?.user?.name}.
                    {/* be{bull}nev{bull}o{bull}lent */}
                </Typography>
                <Typography sx={{ mb: 1.5 }} color="text.secondary">
                    {/* adjective */}
                    {props.session?.user?.email}
                </Typography>
                <Divider />
                <Box sx={{
                    display: 'flex',
                    direction: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }}>
                    <Typography variant="body2">
                        Dark Mode
                    </Typography>
                    <DarkModeButton />
                </Box>

            </CardContent>

      <Divider />
      <CardActions>
        <Button size="small" onClick={() => signOut()}>Signout</Button>
      </CardActions>
        </>
    )
  }
