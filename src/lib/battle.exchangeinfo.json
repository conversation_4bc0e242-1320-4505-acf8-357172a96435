{"BTCUSDT": {"status": "TRADING", "minPrice": "556.80", "maxPrice": "4529764", "tickSize": "0.10", "stepSize": "0.001", "minQty": "0.001", "maxQty": "1000", "minNotional": "100", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHUSDT": {"status": "TRADING", "minPrice": "39.86", "maxPrice": "306177", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "20", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BCHUSDT": {"status": "TRADING", "minPrice": "13.93", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "20", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XRPUSDT": {"status": "TRADING", "minPrice": "0.0143", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "EOSUSDT": {"status": "TRADING", "minPrice": "0.111", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LTCUSDT": {"status": "TRADING", "minPrice": "3.61", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "20", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TRXUSDT": {"status": "TRADING", "minPrice": "0.00132", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETCUSDT": {"status": "TRADING", "minPrice": "1.051", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "20", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LINKUSDT": {"status": "TRADING", "minPrice": "0.464", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.01", "minQty": "0.01", "maxQty": "200000", "minNotional": "20", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XLMUSDT": {"status": "TRADING", "minPrice": "0.00648", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ADAUSDT": {"status": "TRADING", "minPrice": "0.01740", "maxPrice": "20000", "tickSize": "0.00010", "stepSize": "1", "minQty": "1", "maxQty": "2000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XMRUSDT": {"status": "TRADING", "minPrice": "4.36", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DASHUSDT": {"status": "TRADING", "minPrice": "3.82", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZECUSDT": {"status": "TRADING", "minPrice": "2.85", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XTZUSDT": {"status": "TRADING", "minPrice": "0.064", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BNBUSDT": {"status": "TRADING", "minPrice": "6.600", "maxPrice": "100000", "tickSize": "0.010", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ATOMUSDT": {"status": "TRADING", "minPrice": "0.251", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ONTUSDT": {"status": "TRADING", "minPrice": "0.0241", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IOTAUSDT": {"status": "TRADING", "minPrice": "0.0205", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BATUSDT": {"status": "TRADING", "minPrice": "0.0134", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "VETUSDT": {"status": "TRADING", "minPrice": "0.002080", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NEOUSDT": {"status": "TRADING", "minPrice": "1.093", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "QTUMUSDT": {"status": "TRADING", "minPrice": "0.246", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "500000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IOSTUSDT": {"status": "TRADING", "minPrice": "0.000587", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "80000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "THETAUSDT": {"status": "TRADING", "minPrice": "0.1070", "maxPrice": "1000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ALGOUSDT": {"status": "TRADING", "minPrice": "0.0141", "maxPrice": "1000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZILUSDT": {"status": "TRADING", "minPrice": "0.00219", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "KNCUSDT": {"status": "TRADING", "minPrice": "0.03200", "maxPrice": "181", "tickSize": "0.00010", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZRXUSDT": {"status": "TRADING", "minPrice": "0.0179", "maxPrice": "1000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "COMPUSDT": {"status": "TRADING", "minPrice": "8", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OMGUSDT": {"status": "TRADING", "minPrice": "0.1060", "maxPrice": "1000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DOGEUSDT": {"status": "TRADING", "minPrice": "0.002440", "maxPrice": "30", "tickSize": "0.000010", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SXPUSDT": {"status": "TRADING", "minPrice": "0.0454", "maxPrice": "1000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "KAVAUSDT": {"status": "TRADING", "minPrice": "0.0593", "maxPrice": "653", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BANDUSDT": {"status": "TRADING", "minPrice": "0.1647", "maxPrice": "783", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "2000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RLCUSDT": {"status": "TRADING", "minPrice": "0.1029", "maxPrice": "432", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WAVESUSDT": {"status": "SETTLING", "minPrice": "0.3420", "maxPrice": "2249", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MKRUSDT": {"status": "TRADING", "minPrice": "50", "maxPrice": "351788", "tickSize": "0.10", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SNXUSDT": {"status": "TRADING", "minPrice": "0.164", "maxPrice": "1144", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DOTUSDT": {"status": "TRADING", "minPrice": "0.373", "maxPrice": "2398", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DEFIUSDT": {"status": "TRADING", "minPrice": "33.8", "maxPrice": "230680", "tickSize": "0.1", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "YFIUSDT": {"status": "TRADING", "minPrice": "667", "maxPrice": "3836373", "tickSize": "1", "stepSize": "0.001", "minQty": "0.001", "maxQty": "500", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BALUSDT": {"status": "TRADING", "minPrice": "0.630", "maxPrice": "5000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CRVUSDT": {"status": "TRADING", "minPrice": "0.031", "maxPrice": "1000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TRBUSDT": {"status": "TRADING", "minPrice": "1.230", "maxPrice": "5138", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RUNEUSDT": {"status": "TRADING", "minPrice": "0.1720", "maxPrice": "839", "tickSize": "0.0010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SUSHIUSDT": {"status": "TRADING", "minPrice": "0.1430", "maxPrice": "1301", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "EGLDUSDT": {"status": "TRADING", "minPrice": "1.770", "maxPrice": "14295", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SOLUSDT": {"status": "TRADING", "minPrice": "0.4200", "maxPrice": "6857", "tickSize": "0.0010", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ICXUSDT": {"status": "TRADING", "minPrice": "0.0234", "maxPrice": "400", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STORJUSDT": {"status": "TRADING", "minPrice": "0.0190", "maxPrice": "121", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BLZUSDT": {"status": "TRADING", "minPrice": "0.00360", "maxPrice": "22", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "UNIUSDT": {"status": "TRADING", "minPrice": "0.3730", "maxPrice": "2684", "tickSize": "0.0010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AVAXUSDT": {"status": "TRADING", "minPrice": "0.3500", "maxPrice": "2403", "tickSize": "0.0010", "stepSize": "1", "minQty": "1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FTMUSDT": {"status": "TRADING", "minPrice": "0.007700", "maxPrice": "41", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ENJUSDT": {"status": "TRADING", "minPrice": "0.02250", "maxPrice": "153", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FLMUSDT": {"status": "TRADING", "minPrice": "0.0096", "maxPrice": "1000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RENUSDT": {"status": "TRADING", "minPrice": "0.00880", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "KSMUSDT": {"status": "TRADING", "minPrice": "4.060", "maxPrice": "28450", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NEARUSDT": {"status": "TRADING", "minPrice": "0.0480", "maxPrice": "1000", "tickSize": "0.0010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AAVEUSDT": {"status": "TRADING", "minPrice": "4.340", "maxPrice": "38702", "tickSize": "0.010", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FILUSDT": {"status": "TRADING", "minPrice": "1.381", "maxPrice": "10000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RSRUSDT": {"status": "TRADING", "minPrice": "0.000778", "maxPrice": "10", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LRCUSDT": {"status": "TRADING", "minPrice": "0.00520", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OCEANUSDT": {"status": "SETTLING", "minPrice": "0.00010", "maxPrice": "100", "tickSize": "0.00010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CVCUSDT": {"status": "SETTLING", "minPrice": "0.00517", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BELUSDT": {"status": "TRADING", "minPrice": "0.03610", "maxPrice": "204", "tickSize": "0.00010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CTKUSDT": {"status": "SETTLING", "minPrice": "0.02600", "maxPrice": "180", "tickSize": "0.00010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AXSUSDT": {"status": "TRADING", "minPrice": "0.08000", "maxPrice": "6989", "tickSize": "0.00100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ALPHAUSDT": {"status": "TRADING", "minPrice": "0.01760", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZENUSDT": {"status": "TRADING", "minPrice": "1.437", "maxPrice": "10000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SKLUSDT": {"status": "TRADING", "minPrice": "0.00544", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GRTUSDT": {"status": "TRADING", "minPrice": "0.01398", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1INCHUSDT": {"status": "TRADING", "minPrice": "0.0613", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CHZUSDT": {"status": "TRADING", "minPrice": "0.00447", "maxPrice": "100", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SANDUSDT": {"status": "TRADING", "minPrice": "0.00500", "maxPrice": "20000", "tickSize": "0.00010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ANKRUSDT": {"status": "TRADING", "minPrice": "0.001510", "maxPrice": "2000", "tickSize": "0.000010", "stepSize": "1", "minQty": "1", "maxQty": "60000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LITUSDT": {"status": "TRADING", "minPrice": "0.077", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "UNFIUSDT": {"status": "TRADING", "minPrice": "0.222", "maxPrice": "10000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "REEFUSDT": {"status": "TRADING", "minPrice": "0.000516", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "200000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RVNUSDT": {"status": "TRADING", "minPrice": "0.00154", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SFPUSDT": {"status": "TRADING", "minPrice": "0.0223", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XEMUSDT": {"status": "TRADING", "minPrice": "0.0035", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BTCSTUSDT": {"status": "PENDING_TRADING", "minPrice": "0.668", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "COTIUSDT": {"status": "TRADING", "minPrice": "0.00328", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CHRUSDT": {"status": "TRADING", "minPrice": "0.0031", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MANAUSDT": {"status": "TRADING", "minPrice": "0.0136", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ALICEUSDT": {"status": "TRADING", "minPrice": "0.120", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "50000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "HBARUSDT": {"status": "TRADING", "minPrice": "0.00279", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ONEUSDT": {"status": "TRADING", "minPrice": "0.00124", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LINAUSDT": {"status": "TRADING", "minPrice": "0.00087", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "100000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STMXUSDT": {"status": "TRADING", "minPrice": "0.00048", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DENTUSDT": {"status": "TRADING", "minPrice": "0.000070", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "500000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CELRUSDT": {"status": "TRADING", "minPrice": "0.00050", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "HOTUSDT": {"status": "TRADING", "minPrice": "0.000129", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MTLUSDT": {"status": "TRADING", "minPrice": "0.0390", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OGNUSDT": {"status": "TRADING", "minPrice": "0.0137", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NKNUSDT": {"status": "TRADING", "minPrice": "0.00602", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SCUSDT": {"status": "SETTLING", "minPrice": "0.000368", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DGBUSDT": {"status": "SETTLING", "minPrice": "0.00131", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000SHIBUSDT": {"status": "TRADING", "minPrice": "0.000157", "maxPrice": "2000", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "800000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BAKEUSDT": {"status": "TRADING", "minPrice": "0.0001", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GTCUSDT": {"status": "TRADING", "minPrice": "0.200", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BTCDOMUSDT": {"status": "TRADING", "minPrice": "10", "maxPrice": "100000", "tickSize": "0.1", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IOTXUSDT": {"status": "TRADING", "minPrice": "0.00050", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RAYUSDT": {"status": "SETTLING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "500000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "C98USDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "2500000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MASKUSDT": {"status": "TRADING", "minPrice": "0.0040", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ATAUSDT": {"status": "TRADING", "minPrice": "0.0040", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DYDXUSDT": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000XECUSDT": {"status": "TRADING", "minPrice": "0.00050", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GALAUSDT": {"status": "TRADING", "minPrice": "0.00050", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CELOUSDT": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ARUSDT": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "KLAYUSDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ARPAUSDT": {"status": "TRADING", "minPrice": "0.00050", "maxPrice": "20000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CTSIUSDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LPTUSDT": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ENSUSDT": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PEOPLEUSDT": {"status": "TRADING", "minPrice": "0.00010", "maxPrice": "2000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ROSEUSDT": {"status": "TRADING", "minPrice": "0.00010", "maxPrice": "10000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DUSKUSDT": {"status": "TRADING", "minPrice": "0.00010", "maxPrice": "10000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FLOWUSDT": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IMXUSDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "API3USDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "10000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GMTUSDT": {"status": "TRADING", "minPrice": "0.00010", "maxPrice": "10000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "APEUSDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WOOUSDT": {"status": "TRADING", "minPrice": "0.00010", "maxPrice": "10000", "tickSize": "0.00001", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FTTUSDT": {"status": "SETTLING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "JASMYUSDT": {"status": "TRADING", "minPrice": "0.000010", "maxPrice": "100", "tickSize": "0.000001", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DARUSDT": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OPUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "INJUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STGUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SPELLUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "100", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "500000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000LUNCUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LUNA2USDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LDOUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CVXUSDT": {"status": "SETTLING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "1", "minQty": "1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ICPUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "1", "minQty": "1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "APTUSDT": {"status": "TRADING", "minPrice": "0.00100", "maxPrice": "20000", "tickSize": "0.00100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "QNTUSDT": {"status": "TRADING", "minPrice": "0.010000", "maxPrice": "2000", "tickSize": "0.010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FETUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FXSUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "HOOKUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MAGICUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "HIGHUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MINAUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ASTRUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AGIXUSDT": {"status": "SETTLING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PHBUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GMXUSDT": {"status": "TRADING", "minPrice": "0.010000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CFXUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STXUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BNXUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ACHUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SSVUSDT": {"status": "TRADING", "minPrice": "0.010000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CKBUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "100", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PERPUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TRUUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LQTYUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "USDCUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IDUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ARBUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "JOEUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TLMUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "100", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AMBUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "100", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LEVERUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "100", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "100000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RDNTUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "HFTUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XVSUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHBTC": {"status": "TRADING", "minPrice": "0.000010", "maxPrice": "200", "tickSize": "0.000010", "stepSize": "0.01", "minQty": "0.01", "maxQty": "10000", "minNotional": "0.001", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BLURUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "EDUUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IDEXUSDT": {"status": "SETTLING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SUIUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000PEPEUSDT": {"status": "TRADING", "minPrice": "0.0000001", "maxPrice": "200", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "800000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000FLOKIUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "UMAUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RADUSDT": {"status": "SETTLING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "KEYUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "80000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "COMBOUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NMRUSDT": {"status": "TRADING", "minPrice": "0.010000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MAVUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MDTUSDT": {"status": "SETTLING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XVGUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "500000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WLDUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1250000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PENDLEUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "3000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ARKMUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AGLDUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "YGGUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "3000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DODOXUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BNTUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OXTUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "100000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SEIUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CYBERUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "HIFIUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ARKUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GLMRUSDT": {"status": "SETTLING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BICOUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STRAXUSDT": {"status": "SETTLING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LOOMUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BIGTIMEUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BONDUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ORBSUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STPTUSDT": {"status": "SETTLING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WAXPUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BSVUSDT": {"status": "TRADING", "minPrice": "0.01000", "maxPrice": "20000", "tickSize": "0.01000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RIFUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "POLYXUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GASUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "POWRUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SLPUSDT": {"status": "SETTLING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TIAUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SNTUSDT": {"status": "SETTLING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CAKEUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MEMEUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TWTUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "200", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TOKENUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ORDIUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STEEMUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "200", "tickSize": "0.000010", "stepSize": "1", "minQty": "1", "maxQty": "2000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BADGERUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "200", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ILVUSDT": {"status": "TRADING", "minPrice": "0.01000", "maxPrice": "2000", "tickSize": "0.01000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NTRNUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "200", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "KASUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "8000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BEAMXUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000BONKUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "40000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PYTHUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "400000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SUPERUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "250000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "USTCUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ONGUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHWUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "50000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "JTOUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "200", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000SATSUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "1000000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AUCTIONUSDT": {"status": "TRADING", "minPrice": "0.010000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000RATSUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ACEUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MOVRUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "5000", "tickSize": "0.001000", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NFPUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "1000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BTCUSDC": {"status": "TRADING", "minPrice": "0.1", "maxPrice": "500000", "tickSize": "0.1", "stepSize": "0.001", "minQty": "0.001", "maxQty": "800", "minNotional": "100", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHUSDC": {"status": "TRADING", "minPrice": "0.01", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "8000", "minNotional": "20", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BNBUSDC": {"status": "TRADING", "minPrice": "0.010", "maxPrice": "100000", "tickSize": "0.010", "stepSize": "0.01", "minQty": "0.01", "maxQty": "80000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SOLUSDC": {"status": "TRADING", "minPrice": "0.1000", "maxPrice": "100000", "tickSize": "0.0010", "stepSize": "0.01", "minQty": "0.01", "maxQty": "800000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XRPUSDC": {"status": "TRADING", "minPrice": "0.0001", "maxPrice": "100000", "tickSize": "0.0001", "stepSize": "0.1", "minQty": "0.1", "maxQty": "8000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AIUSDT": {"status": "TRADING", "minPrice": "0.000010", "maxPrice": "200", "tickSize": "0.000010", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "XAIUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DOGEUSDC": {"status": "TRADING", "minPrice": "0.000010", "maxPrice": "200", "tickSize": "0.000010", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WIFUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "1000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MANTAUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "1000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ONDOUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "5000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LSKUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ALTUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "JUPUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZETAUSDT": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "200", "tickSize": "0.000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RONINUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "200", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DYMUSDT": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SUIUSDC": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "20000", "tickSize": "0.000010", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OMUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LINKUSDC": {"status": "TRADING", "minPrice": "0.001", "maxPrice": "2000", "tickSize": "0.001", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PIXELUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "STRKUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MAVIAUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ORDIUSDC": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GLMUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "PORTALUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TONUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AXLUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MYROUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "100000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000PEPEUSDC": {"status": "TRADING", "minPrice": "0.0000001", "maxPrice": "200", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "800000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "METISUSDT": {"status": "TRADING", "minPrice": "0.0100", "maxPrice": "2000", "tickSize": "0.0100", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AEVOUSDT": {"status": "TRADING", "minPrice": "0.0100000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WLDUSDC": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "VANRYUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BOMEUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "200000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHFIUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AVAXUSDC": {"status": "TRADING", "minPrice": "0.001000", "maxPrice": "2000", "tickSize": "0.001000", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000SHIBUSDC": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "2000", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "800000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BTCUSDT_240927": {"status": "TRADING", "minPrice": "576.3", "maxPrice": "1000000", "tickSize": "0.1", "stepSize": "0.001", "minQty": "0.001", "maxQty": "500", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHUSDT_240927": {"status": "TRADING", "minPrice": "41.10", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ENAUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "WIFUSDC": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "1000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BCHUSDC": {"status": "TRADING", "minPrice": "0.01", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TNSRUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SAGAUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LTCUSDC": {"status": "TRADING", "minPrice": "0.01", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NEARUSDC": {"status": "TRADING", "minPrice": "0.0010", "maxPrice": "2000", "tickSize": "0.0010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TAOUSDT": {"status": "TRADING", "minPrice": "0.01", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "300", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "OMNIUSDT": {"status": "TRADING", "minPrice": "0.0100", "maxPrice": "100000", "tickSize": "0.0010", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ARBUSDC": {"status": "TRADING", "minPrice": "0.000100", "maxPrice": "20000", "tickSize": "0.000100", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NEOUSDC": {"status": "TRADING", "minPrice": "0.001", "maxPrice": "100000", "tickSize": "0.001", "stepSize": "0.01", "minQty": "0.01", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FILUSDC": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "100000", "tickSize": "0.0010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TIAUSDC": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BOMEUSDC": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "200000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "REZUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ENAUSDC": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHFIUSDC": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "2000", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1000BONKUSDC": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BBUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NOTUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "30000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "TURBOUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "IOUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZKUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MEWUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "LISTAUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ZROUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BTCUSDT_241227": {"status": "TRADING", "minPrice": "576.3", "maxPrice": "1000000", "tickSize": "0.1", "stepSize": "0.001", "minQty": "0.001", "maxQty": "500", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ETHUSDT_241227": {"status": "TRADING", "minPrice": "41.10", "maxPrice": "100000", "tickSize": "0.01", "stepSize": "0.001", "minQty": "0.001", "maxQty": "10000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CRVUSDC": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "3000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RENDERUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BANANAUSDT": {"status": "TRADING", "minPrice": "0.0010000", "maxPrice": "2000", "tickSize": "0.0010000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RAREUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "GUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SYNUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "1000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SYSUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "VOXELUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BRETTUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "ALPACAUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "POPCATUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "2000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "SUNUSDT": {"status": "TRADING", "minPrice": "0.0000010", "maxPrice": "200", "tickSize": "0.0000010", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "VIDTUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "50000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NULSUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "2000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "DOGSUSDT": {"status": "TRADING", "minPrice": "0.0000001", "maxPrice": "200", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "500000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "MBOXUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "CHESSUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "FLUXUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "2000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "BSWUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "QUICKUSDT": {"status": "TRADING", "minPrice": "0.0000100", "maxPrice": "200", "tickSize": "0.0000100", "stepSize": "1", "minQty": "1", "maxQty": "30000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NEIROETHUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "RPLUSDT": {"status": "TRADING", "minPrice": "0.0100000", "maxPrice": "2000", "tickSize": "0.0100000", "stepSize": "0.1", "minQty": "0.1", "maxQty": "100000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "AERGOUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "10000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "POLUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "3000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "UXLINKUSDT": {"status": "TRADING", "minPrice": "0.0001000", "maxPrice": "200", "tickSize": "0.0001000", "stepSize": "1", "minQty": "1", "maxQty": "3000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "1MBABYDOGEUSDT": {"status": "TRADING", "minPrice": "0.0000001", "maxPrice": "200", "tickSize": "0.0000001", "stepSize": "1", "minQty": "1", "maxQty": "600000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}, "NEIROUSDT": {"status": "TRADING", "minPrice": "0.00000010", "maxPrice": "200", "tickSize": "0.00000010", "stepSize": "1", "minQty": "1", "maxQty": "3000000000", "minNotional": "5", "orderTypes": ["LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET", "TRAILING_STOP_MARKET"]}}