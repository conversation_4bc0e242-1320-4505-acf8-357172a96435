
const listValues = {
    barValues: ['open', 'close'],
    timeIntervals: ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "12h", "1d"],
}


const rulesetparams = [
    {
        type: 'moving_averages',
        indicator: 'ema',
        title: 'Exponential Mov. Avg',
        titleShort: 'EMA',
        params: {
            length: {
                default: 9,
                type: 'numeric',
            }, 
            source: {
                default: 'close',
                type: 'list',
                values: listValues.barValues,
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'indicatorValue',
                result: 'numeric',
            },
            {
                name: 'emaTrend',
                result: 'list',
                options: 'Flat,Down,Up'
            },
            {
                name: 'emaCrossUp',
                result: 'bool',
            },
            {
                name: 'emaCrossDown',
                result: 'bool',
            },
            {
                name: 'emaPosition',
                result: 'list',
                options: 'below,above'
            },
            {
                name: 'emaDistanceR',
                result: 'numeric',
            },

        ]
    },
    {
        type: 'moving_averages',
        indicator: 'sma',
        title: 'Standart Mov. Avg',
        titleShort: 'SMA',
        params: {
            length: {
                default: 9,
                type: 'numeric',
            }, 
            source: {
                default: 'close',
                type: 'list',
                values: listValues.barValues,
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'indicatorValue',
                result: 'numeric',
            },
            {
                name: 'smaTrend',
                result: 'list',
                options: 'Irregular,down,Up'
            },
            {
                name: 'smaCrossUp',
                result: 'bool',
            },
            {
                name: 'smaCrossDown',
                result: 'bool',
            },
            {
                name: 'smaPosition',
                result: 'list',
                options: 'below,above'
            },
            {
                name: 'smaDistanceR',
                result: 'numeric',
            },

        ]
    },
    {
        type: 'moving_averages',
        indicator: 'wma',
        title: 'Weighted Mov. Avg',
        titleShort: 'WMA',
        params: {
            length: {
                default: 9,
                type: 'numeric',
            }, 
            source: {
                default: 'close',
                type: 'list',
                values: listValues.barValues,
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'indicatorValue',
                result: 'numeric',
            },
            {
                name: 'wmaTrend',
                result: 'list',
                options: 'Irregular,Down,Up'
            },
            {
                name: 'wmaCrossUp',
                result: 'bool',
            },
            {
                name: 'wmaCrossDown',
                result: 'bool',
            },
            {
                name: 'wmaPosition',
                result: 'list',
                options: 'below,above'
            },
            {
                name: 'wmaDistanceR',
                result: 'numeric',
            },

        ]
    },
    {   
        type: 'oscillators',
        indicator: 'rsi',
        title: 'RSI',
        titleShort: 'RSI',
        params: {
            length: {
                default: 9,
                type: 'numeric',
            }, 
            source: {
                default: 'close',
                type: 'list',
                values: listValues.barValues,
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
            upperBand: {
                default: 70,
                type: 'numeric',
            }, 
            middleBand: {
                default: 50,
                type: 'numeric',
            }, 
            lowerBand: {
                default: 10,
                type: 'numeric',
            }, 
            rsiSMAFastPeriod: {
                default: 8,
                type: 'numeric',
            }, 
            rsiSMASlowPeriod: {
                default: 14,
                type: 'numeric',
            }, 
        },
        resultKeys: [
            {
                name: 'indicatorValue',
                result: 'numeric',
            },
            {
                name: 'rsiSMAFast',
                result: 'numeric',
            },
            {
                name: 'rsiSMASlow',
                result: 'numeric',
            },
            {
                name: 'rsiCrossUpFast',
                result: 'bool',
            },
        ]
    },
    {
        type: 'misc',
        indicator: 'ichimoku',
        title: 'Ichimoku',
        titleShort: 'ICHIMOKU',
        params: {
            length: {
                default: 9,
                type: 'numeric',
            }, 
            source: {
                default: 'close',
                type: 'list',
                values: listValues.barValues,
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
            conversionPeriod: {
                default: 9,
                type: 'numeric',
            }, 
            basePeriod: {
                default: 26,
                type: 'numeric',
            }, 
            spanPeriod: {
                default: 52,
                type: 'numeric',
            }, 
            displacement: {
                default: 26,
                type: 'numeric',
            } 
        },
        resultKeys: [
            {
                name: 'conversion',
                result: 'numeric',
            }, 
            {
                name: 'base',
                result: 'numeric',
            }, 
            {
                name: 'spanA',
                result: 'numeric',
            }, 
            {
                name: 'spanB',
                result: 'numeric',
            }, 
        ]
    },
    {
        type: 'directionalmovement',
        indicator: 'atr',
        title: 'Average True Range',
        titleShort: 'ATR',
        params: {
            length: {
                default: 14,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'atr',
                result: 'numeric',
            },
            {
                name: 'atrVsClosePerc',
                result: 'numeric',
            },

        ]
    },
    {
        type: 'directionalmovement',
        indicator: 'adx',
        title: 'Avg Directional Index',
        titleShort: 'ADX',
        params: {
            length: {
                default: 14,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'adx',
                result: 'numeric',
            },
            {
                name: 'mdi',
                result: 'numeric',
            },
            {
                name: 'pdi',
                result: 'numeric',
            },
            {
                name: 'pdiPos',
                result: 'list',
                options: 'above,below'
            },

        ]
    },
    {
        type: 'volume',
        indicator: 'obv',
        title: 'onBalance Volume',
        titleShort: 'onBalanceVolume',
        params: {
            length: {
                default: 1,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'obv',
                result: 'numeric',
            }, 
        ]
    },
    {
        type: 'volatility',
        indicator: 'chandelierexit',
        title: 'chandelier exit',
        titleShort: 'chandelierExit',
        params: {
            length: {
                default: 14,
                type: 'numeric',
            }, 
            multiplier: {
                default: 3,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'signal',
                result: 'list',
                options: 'buy,sell'
            },
            {
                name: 'signalChanged',
                result: 'bool',
            },
            {
                name: 'signalAge',
                result: 'numeric',
            }, 
        ]
    },

    {
        type: 'volatility',
        indicator: 'bb',
        title: 'Bolinger Bands',
        titleShort: 'bolingerBands',
        params: {
            length: {
                default: 20,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'lower',
                result: 'numeric',
            },
            {
                name: 'middle',
                result: 'numeric',
            },
            {
                name: 'upper',
                result: 'numeric',
            }, 
        ]
    },
    // {
    //     type: 'volatility',
    //     indicator: 'chandelierexitta',
    //     title: 'chandelier exit TA',
    //     titleShort: 'chandelierExitTA',
    //     params: {
    //         length: {
    //             default: 22,
    //             type: 'numeric',
    //         }, 
    //         multiplier: {
    //             default: 3,
    //             type: 'numeric',
    //         }, 
    //         timeFrame: {
    //             default: '1m',
    //             type: 'list',
    //             values: listValues.timeIntervals,
    //         },
    //     },
    //     resultKeys: [
    //         {
    //             name: 'ce',
    //             result: 'json',
    //         },
    //         {
    //             name: 'short',
    //             result: 'numeric',
    //         },
    //         {
    //             name: 'long',
    //             result: 'numeric',
    //         }, 
    //         {
    //             name: 'signal',
    //             result: 'list',
    //             options: 'buy,sell'
    //         },
    //         {
    //             name: 'signalAge',
    //             result: 'numeric',
    //         },
    //     ]
    // },
    {
        type: 'oscillators',
        indicator: 'awesomeOscillator',
        title: 'awesome Oscillator',
        titleShort: 'awesomeOscillator',
        params: {
            length: {
                default: 34,
                type: 'numeric',
            }, 
            fastPeriod: {
                default: 5,
                type: 'numeric',
            }, 
            slowPeriod: {
                default: 34,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'ao',
                result: 'numeric',
            },

        ]
    },
    {
        type: 'oscillators',
        indicator: 'cci',
        title: 'Commodity Channel Index',
        titleShort: 'CCI',
        params: {
            length: {
                default: 20,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'cci',
                result: 'numeric',
            },
            {
                name: 'SMA',
                result: 'numeric',
            },
            {
                name: 'crossUpLevels',
                result: 'bool',
            },
            {
                name: 'crossUpSMA',
                result: 'bool',
            },
            {
                name: 'SMAtrendIsUp',
                result: 'bool',
            },

        ]
    },
    {
        type: 'momentum',
        indicator: 'roc',
        title: 'Rate of Change',
        titleShort: 'roc',
        params: {
            length: {
                default: 12,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'roc',
                result: 'numeric',
            },

        ]
    },
    {
        type: 'momentum',
        indicator: 'psar',
        title: 'Parabolic Sar',
        titleShort: 'psar',
        params: {
            length: {
                default: 56,
                type: 'numeric',
            }, 
            step: {
                default: 0.02,
                type: 'numeric',
            }, 
            max: {
                default: 0.2,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'psar',
                result: 'numeric',
            },
            {
                name: 'psarPosition',
                result: 'list',
                options: 'above,below'
            },
            {
                name: 'age',
                result: 'numeric', // {age, pozs}
            },
            {
                name: 'pozs',
                result: 'string', 
            },

        ]
    },
    {
        type: 'moving_averages',
        indicator: 'macd',
        title: 'MACD',
        titleShort: 'macd',
        params: {
            length: {
                default: 56,
                type: 'numeric',
            }, 
            fastPeriod: {
                default: 5,
                type: 'numeric',
            }, 
            slowPeriod: {
                default: 8,
                type: 'numeric',
            }, 
            signalPeriod: {
                default: 3,
                type: 'numeric',
            }, 
            SimpleMAOscillator: {
                default: false,
                type: 'bool',
            }, 
            SimpleMASignal: {
                default: false,
                type: 'bool',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'MACD',
                result: 'numeric',
            },
            {
                name: 'histogram',
                result: 'numeric', // {age, pozs}
            },
            {
                name: 'signal',
                result: 'numeric', // {age, pozs}
            },
            {
                name: 'MACDPosAge',
                result: 'numeric', // {age, pozs}
            },
            {
                name: 'MACDPos',
                result: 'list',
                options: 'above,below'
            },
            {
                name: 'MaCDPosCross',
                result: 'bool',
            },

        ]
    },
    {
        type: 'momentum',
        indicator: 'srsi',
        title: 'StochasticRSI',
        titleShort: 'StochasticRSI',
        params: {
            length: {
                default: 14,
                type: 'numeric',
            }, 
            rsiPeriod: {
                default: 14,
                type: 'numeric',
            }, 
            stochasticPeriod: {
                default: 14,
                type: 'numeric',
            }, 
            kPeriod: {
                default: 3,
                type: 'numeric',
            }, 
            dPeriod: {
                default: 3,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [ 
            {
                name: 'stochRSI',
                result: 'numeric',
            },
            {
                name: 'k',
                result: 'numeric',
            },
            {
                name: 'd',
                result: 'numeric',
            },
            {
                name: 'kPos',
                result: 'list',
                options: 'above,below'
            },
            {
                name: 'kPosAge',
                result: 'numeric',
            },
            {
                name: 'kdPosCross',
                result: 'bool',
            },

        ]
    },
    {
        type: 'volatility',
        indicator: 'volatilityIndex',
        title: 'volatility Index',
        titleShort: 'volatilityIndex',
        params: {
            length: {
                default: 30,
                type: 'numeric',
            },
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [ 
            {
                name: 'volatility',
                result: 'numeric',
            },
            {
                name: 'comparison',
                result: 'numeric',
            },
        ]
    },

    // {
    //     type: 'momentum',
    //     indicator: 'stochastic',
    //     title: 'Stochastic',
    //     titleShort: 'Stochastic',
    //     params: {
    //         length: {
    //             default: 14,
    //             type: 'numeric',
    //         }, 
    //         signalPeriod: {
    //             default: 14,
    //             type: 'numeric',
    //         }, 
    //         timeFrame: {
    //             default: '1m',
    //             type: 'list',
    //             values: listValues.timeIntervals,
    //         },
    //     },
    //     resultKeys: [
    //         {
    //             name: 'stochastic',
    //             result: 'string',
    //         },
    //         {
    //             name: 'k',
    //             result: 'numeric',
    //         },
    //         {
    //             name: 'd',
    //             result: 'numeric',
    //         },
    //         {
    //             name: 'kVSdPos',
    //             result: 'list',
    //             options: 'above,below'
    //         },

    //     ]
    // },

    {
        type: 'misc',
        indicator: 'candlepatterns',
        title: 'candle patterns',
        titleShort: 'candlePatterns',
        params: {
            length: {
                default: 14,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [

            {
                name: 'deltaPercentile',
                result: 'numeric',
            },
            {
                name: 'deltaValue',
                result: 'numeric',
            },
            {
                name: 'deltaMean',
                result: 'numeric',
            },
            {
                name: 'deltaMedian',
                result: 'numeric',
            },
            {
                name: 'deltaMeanISBiggerdeltaMedian',
                result: 'bool',
            },
            {
                name: 'PCPpercentile',
                result: 'numeric',
            },
            {
                name: 'PCPValue',
                result: 'numeric',
            },
            {
                name: 'PCPmedian',
                result: 'numeric',
            },
            {
                name: 'PCPmean',
                result: 'numeric',
            },
            {
                name: 'PCPmeanISBiggerPCPmedian',
                result: 'bool',
            },
            {
                name: 'QVolumepercentile',
                result: 'numeric',
            },
            {
                name: 'QVolumeValue',
                result: 'numeric',
            },
            {
                name: 'QVolumemedian',
                result: 'numeric',
            },
            {
                name: 'QVolumemean',
                result: 'numeric',
            },
            {
                name: 'lastBar',
                result: 'json',
            },
            {
                name: 'candlepatterns',
                result: 'string',
            },
            {
                name: 'bardeltaPerc1',
                result: 'numeric',
            },
            {
                name: 'bardeltaPerc2',
                result: 'numeric',
            },
            {
                name: 'bardeltaPerc3',
                result: 'numeric',
            },
            {
                name: 'bardeltaPerc5',
                result: 'numeric',
            },
            {
                name: 'bardeltaPerc10',
                result: 'numeric',
            },
            {
                name: 'barHLRangePerc1',
                result: 'numeric',
            },
            {
                name: 'barHLRangePerc2',
                result: 'numeric',
            },
            {
                name: 'barHLRangePerc3',
                result: 'numeric',
            },
            {
                name: 'barHLRangePerc5',
                result: 'numeric',
            },
            {
                name: 'barHLRangePerc10',
                result: 'numeric',
            },
            {
                name: 'barTrend2',
                result: 'string',
            },
            {
                name: 'barTrend3',
                result: 'string',
            },
            {
                name: 'barTrend5',
                result: 'string',
            },
            {
                name: 'barTrend10',
                result: 'string',
            },

            {
                name: 'barKipir1',
                result: 'numeric',
            },
            {
                name: 'barKipir3',
                result: 'numeric',
            },
            {
                name: 'barKipir5',
                result: 'numeric',
            },
            {
                name: 'barKipir10',
                result: 'numeric',
            },

            {
                name: 'barKipir1Avg',
                result: 'numeric',
            },

            {
                name: 'barKipir3Avg',
                result: 'numeric',
            },

            {
                name: 'barKipir5Avg',
                result: 'numeric',
            },
            {
                name: 'barKipir10Avg',
                result: 'numeric',
            },

        ]
    },
    {
        type: 'moving_averages',
        indicator: 'supertrend',
        title: 'Super Trend',
        titleShort: 'superTrend',
        params: {
            length: {
                default: 14,
                type: 'numeric',
            }, 
            multiplier: {
                default: 3,
                type: 'numeric',
            }, 
            timeFrame: {
                default: '1m',
                type: 'list',
                values: listValues.timeIntervals,
            },
        },
        resultKeys: [
            {
                name: 'Direction',
                result: 'numeric',
            },
            {
                name: 'actSignal',
                result: 'bool',
            },
            {
                name: 'position',
                result: 'string',
            },
            {
                name: 'directionTxt',
                result: 'list',
                options: 'above,below'
            },
            {
                name: 'DirectionAge',
                result: 'numeric',
            },

        ]
    },

    // {
    //     type: 'moving_averages',
    //     indicator: 'supertrend_t3',
    //     title: 'Super Trend max',
    //     titleShort: 'superTrend max',
    //     params: {
    //         length: {
    //             default: 14,
    //             type: 'numeric',
    //         }, 
    //         multiplier: {
    //             default: 3,
    //             type: 'numeric',
    //         }, 
    //         timeFrame: {
    //             default: '1m',
    //             type: 'list',
    //             values: listValues.timeIntervals,
    //         },
    //     },
    //     resultKeys: [
    //         {
    //             name: 'superTrend',
    //             result: 'numeric',
    //         },

    //     ]
    // },
]
module.exports = rulesetparams;


/*


ema
sma
wma
rsi
ichimoku
atr
adx
awesomeOscillator
cci
roc
psar
macd,
srsi,
candlepatterns,
superTrend.

bollingerbands
wema,
kst,
williamsr,
adl,
trix,
forceindex,
vwap,
volumeprofile,
fibonacciretracement,
renko,
heikinashi,
mfi,
predictPattern
isTrendingUp,
isTrendingDown,
hasInverseHeadAndShoulder,
hasHeadAndShoulder,
hasDoubleTop,
hasDoubleBottom,
keltnerchannels,

kairi,
ChandelierExit

*/