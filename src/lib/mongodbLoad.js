const MongoClient = require('mongodb').MongoClient; 
let db;
const uri = process.env.MONGODB_URI; // your mongodb connection string
const options = {};
const loadDB = async (redisKey) => {
    if (global.mongoDB) {
        return global.mongoDB;
    }
    if (db) {
        return db;
    }
    try {
        let dbName = 'algoweb';
        const client = await MongoClient.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true });
        db = client.db(dbName);
        global.mongoDB = db;

        console.log(new Date(Date.now()).toISOString(), 'Mongo Client connected!', redisKey)
    } catch (err) {
        console.log('Mongo', err);
    }
    return db;
};

module.exports = loadDB;