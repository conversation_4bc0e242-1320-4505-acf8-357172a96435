const ruleSets = [
    {
        "rsgID" : 1,
        "userCode" : "x",
        "rulesetName" : "tmp1",
        "cond" : 1,
        "direction" : "long",
        "ruleSet" : [ 
            {
                "name" : "Ema X",
                "key" : "emaXRule",
                "indicators": ['ema'],
                "cond" : 1,
                "criteria" : [ 
                    {
                        "name" : "ema9 is below",
                        "indicator": "ema",
                        "indicatorRef": "ema9",
                        "item" : "emaPosition",
                        "rule" : "=='below'",
                        "rown" : 1
                    }, 
                    {
                        "name" : "ema9 distance R is bigg",
                        "indicator": "ema",
                        "indicatorRef": "ema9",
                        "item" : "emaDistanceR",
                        "rule" : ">0.1",
                        "rown" : 2
                    }, 
                    // {
                    //     "name" : "ema9 vs ema21",
                    //     "item" : "indicatorValue",
                    //     "ruleType": "workWithOtherParam",
                    //     "rule" : "- [ema21].indicatorValue > 100",
                    //     "rown" : 3
                    // }
                ]
            }
        ]
    }
]
module.exports = ruleSets;