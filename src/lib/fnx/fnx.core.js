
const fs = require('fs').promises;
const fss = require('fs');
const path = require('path');
const { writeFileSync } = fss;

const logger = (exports.logger = {
  debug: (...arg) => {
    console.log((new Date()).toISOString(), 'DEBUG', ...arg);
  },
  info: (...arg) => {
    console.log((new Date()).toISOString(), 'INFO', ...arg);
  },
  sleep: (ms = 300) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  }//dt, tabl

  // warn: (...arg) => {
  //     console.log(chalk.white.bgRed.bold((new Date()).toISOString(), 'WARN'), ...arg);
  // },
  // success: (...arg) => {
  //     console.log(chalk.white.bgBlue((new Date()).toISOString(), 'SUCCESS'), ...arg);
  // },
}
);

const chunkify = exports.chunkify = (a, n, balanced) => {
  //array splitter...
  if (n < 2)
    return [a];

  var len = a.length,
    out = [],
    i = 0,
    size;

  if (len % n === 0) {
    size = Math.floor(len / n);
    while (i < len) {
      out.push(a.slice(i, i += size));
    }
  }

  else if (balanced) {
    while (i < len) {
      size = Math.ceil((len - i) / n--);
      out.push(a.slice(i, i += size));
    }
  }

  else {

    n--;
    size = Math.floor(len / n);
    if (len % size === 0)
      size--;
    while (i < size * n) {
      out.push(a.slice(i, i += size));
    }
    out.push(a.slice(size * n));

  }

  return out;
}


const main = (exports.main = {
  promiseTimeout: (promise, connTimeOut = 10000) => {
    let timeout = new Promise((resolve, reject) => {
      let id = setTimeout(() => {
        clearTimeout(id);
        reject('promiseTimeout in ' + connTimeOut + 'ms.', promise);
      }, connTimeOut);
    });

    // Returns a race between our timeout and the passed in promise
    return Promise.race([promise, timeout]);
  },
  // generateKey: ({ inclTime = false }) => {
  //   let randStr = (+new Date * Math.random()).toString(36).substring(0, 6);
  //   let resp = inclTime ? Date.now().toString() + '-' + randStr : randStr;
  //   return resp
  // },
  save2file: async (dt, filename, debg = false) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
      try {
        let ffilename = path.join(process.cwd(), "public/assets/" + filename)
        var res = await fs.writeFile(ffilename, JSON.stringify(dt), 'utf8');
        debg && console.log("data is saved.", ffilename, Date.now() - bop);
        resolve(res)
      } catch (err) {
        logger.info('dosya kadedilemedi.', ffilename, err)
        reject(err);
      }
    })
  },
  // warn: (...arg) => {
  //     console.log(chalk.white.bgRed.bold((new Date()).toISOString(), 'WARN'), ...arg);
  // },
  // success: (...arg) => {
  //     console.log(chalk.white.bgBlue((new Date()).toISOString(), 'SUCCESS'), ...arg);
  // },
}
);
