const fnxCore = require('./fnx.core');
const { v4: uuidv4 } = require('uuid');
const q = (exports.q = {
    runQueries: (db) => {
        db.all(`select hero_name, is_xman, was_snapped from hero h
       inner join hero_power hp on h.hero_id = hp.hero_id
       where hero_power = ?`, "Total Nerd", (err, rows) => {
            rows.forEach(row => {
                console.log(row.hero_name + "\t" +row.is_xman + "\t" +row.was_snapped);
            });
        });
    },
    query: (db, sql, params) => {
        // return db.prepare(sql).all(params);
        return new Promise(async (resolve, reject) => {
            try {
                var res = db.prepare(sql).all();
                resolve(res)
            } catch (e) {
                console.log('fbdb query error', e)
                reject(e)
            }
        });
    },      
    listtables: ({db}) => {
        return new Promise(async (resolve, reject) => {
            try {
                let qTxt = `SELECT name
                            FROM sqlite_schema
                            WHERE   type ='table' 
                                    AND
                                    name NOT LIKE 'sqlite_%'
                                    ;`;
                // var query = fndb.query(db, qTxt, null)
                var query = db.prepare(qTxt).all()
                resolve(query);
            } catch (e) {
                console.log('generic time e', e)
                reject(e)
            }
        });
    },
    checktable: {
        core_nodes : ({db}) => {
            return new Promise(async (resolve, reject) => {
                try {
                    let qTxt = `create table IF NOT EXISTS core_nodes (
                        id text primary key not null,
                        process_id int not null,
                        port int not null,
                        task_id int not null,
                        task_name text not null,
                        is_active bool not null DEFAULT(true),
                        is_deleted bool not null DEFAULT(false),
                        dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                    );`;
                    var query = db.exec(qTxt)
                    resolve(query);
                } catch (e) {
                    console.log('core_nodes check failed!', e)
                    reject(e)
                }
            });
        }
    }
});

const cud = (exports.cud = {
    insertOne: ({ db, table, payload, setid = false, shortID = false, debug = false }) => {
        return new Promise(async (resolve, reject) => {
            let sqlq = '';
            try {
                let fields = [];
                let fieldValues = [];
                for (const key in payload) {
                    if (payload.hasOwnProperty(key)) {
                        fields.push(key);
                        fieldValues.push(typeof (payload[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]);
                    }
                }
                if (setid) {
                    id = shortID ? fnx.generateKey({inclTime: true}) : uuidv4();
                    fields.push(setid);
                    fieldValues.push("'" + id + "'");
                }
                sqlq += ' INSERT INTO ' + table + ' (' + fields.toString() + ')';
                sqlq += ' VALUES( ' + fieldValues.toString() + ' );'
                var res = db.prepare(sqlq).run();
                resolve(res)
            } catch (e) {
                console.log('cud insertOne e', e, sqlq)
                reject(e)
            }
        });
    },
    insertManyJSON: async ({
        db, rawData, tableName, time1Field = null, 
        addBatchID = false,
        time2Field = null, debug = false }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let arr = await json2arr(rawData);
                if (addBatchID) {
                    let batchid = Date.now();
                    let sq_pre = `update ${tableName} set is_deleted = true where batchid not in (${batchid})`;
                    db.exec(sq_pre);

                    Array.isArray(arr) && arr.map(a => {
                        a.batchid = batchid;
                        delete a.lastUpdateId;
                        if (time1Field) {
                            a[time1Field + 'HRF'] = new Date(a[time1Field]).toISOString();
                        }
                        if (time2Field) {
                            a[time2Field + 'HRF'] = new Date(a[time2Field]).toISOString();
                        }
                    })
                }
                let sql = await generateSQL({ payload: arr, table: tableName });
                // debug && console.log('sql', sql)
                var res = db.exec(sql);
                resolve(res);
            }
            catch (e) {
                reject(e)
            }
        });
    },
    select: ({ sqlClient, table, condition = null }) => {
        return new Promise((resolve, reject) => {
            const db = sqlClient;
            const queries = [];
            var qSQL = `SELECT rowid as key, * FROM ${table}`;
            var cond = null;
            if (condition) {
                qSQL += ` where 1=1 and ${condition};`
            }
            try {
                let resp = db.prepare(qSQL).all();
                resolve(resp);
            } catch (e) {
                console.log('eee', e)
                reject(e)
            }
        });
    }
});


const json2arr = dt => {
    let arr = [];
    return new Promise(async (resolve, reject) => {
        if (dt) {
            try {
                for (const key in dt) {
                    if (dt.hasOwnProperty(key)) {
                        arr.push(dt[key]);
                        //   console.log(`${key} : ${JSON.stringify(dt[key])}`)
                    }
                }
                resolve(arr)
            }
            catch (e) {
                reject(e)
            }
        } else {
            reject('no value')
        }
    });
}

const getJSONFields = payload => {
    return new Promise(async (resolve, reject) => {
        let resp = {}
        let fields = [];
        let fieldValues = [];
        let tmp = JSON.parse(JSON.stringify(payload))
        try {
            for (const key in tmp) {
                if (tmp.hasOwnProperty(key)) {
                    fields.push(key);
                    fieldValues.push(typeof (tmp[key]) === 'string' ? "'" + payload[key] + "'" : payload[key]);
                }
            }
            resp = { fields, fieldValues };
            resolve(resp);
        }
        catch (e) {
            reject(e);
        }
    });
}

const generateSQL = async ({ payload, table }) => {
    // 
    let ssql = '';
    let arr = payload;
    return new Promise(async (resolve, reject) => {
        for (a of arr) {
            let pl = await getJSONFields(a);
            ssql = ssql + 'INSERT INTO ' + table + ' (' + pl.fields.toString() + ') VALUES (' + pl.fieldValues.toString() + ');\n\n'
        }
        resolve(ssql)
    });
};
