


export const convertUTCDateToLocalDate = (date)=> {
    if (date && date instanceof Date ) {

        var newDate = new Date(date.getTime()+date.getTimezoneOffset()*60*1000);

        var offset = date.getTimezoneOffset() / 60;
        var hours = date.getHours();
    
        newDate.setHours(hours - offset);
    
        return newDate;   
    } else {
        return date
    }
}

export const stats = {
    binCalculator: ({ arr = [], interval = 0.1, numOfBuckets = 1 }) => {
        var bins = [];
        var binCount = 0;
        for (var i = 0; i < numOfBuckets; i += interval) {
            bins.push({
                binNum: binCount,
                minNum: i,
                maxNum: i + interval,
                count: 0
            })
            binCount++;
        }
        //Loop through data and add to bin's count
        for (var i = 0; i < arr.length; i++) {
            var item = arr[i];
            for (var j = 0; j < bins.length; j++) {
                var bin = bins[j];
                if (item > bin.minNum && item <= bin.maxNum) {
                    bin.count++;
                }
            }
        }
        return bins
    }
}