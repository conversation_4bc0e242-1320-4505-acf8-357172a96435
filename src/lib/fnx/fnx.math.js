export const stats = {
    binCalculator: ({ arr = [], interval = 0.1, numOfBuckets = 1 }) => {
        //https://stackoverflow.com/questions/37445495/binning-an-array-in-javascript-for-a-histogram
        var bins = [];
        var binCount = 0;
        for (var i = 0; i < numOfBuckets; i += interval) {
            bins.push({
                binNum: binCount,
                minNum: i,
                maxNum: i + interval,
                count: 0
            })
            binCount++;
        }
        //Loop through data and add to bin's count
        for (var i = 0; i < arr.length; i++) {
            var item = arr[i];
            for (var j = 0; j < bins.length; j++) {
                var bin = bins[j];
                if (item > bin.minNum && item <= bin.maxNum) {
                    bin.count++;
                }
            }
        }
        return bins
    }
}