/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import Icon from '@mui/material/Icon'

// import {
//   MdHome,
//   MdSchema,
//   MdDomain,
//   MdWorkspacesFilled,
//   MdAllInbox,
//   MdAnalytics,
//   MdDataset,
//   MdListAlt,
// } from 'react-icons/md'
 
export const findRoutes = (props = {}) => {
  const {role, isAdmin, s} = props;
  // console.log('role', role);
  // console.log('s', s)
  const menuHier = [
    {
      section: 'Home',
      weight: 10,
      menus: [
        {
          name: 'Main Dashboard',
          layout: '/app',
          path: '/app',
          section: '',
          // icon: <Icon as={MdHome} width='20px' height='20px' color='inherit' />,
        },
      ],
    },
    {
      section: 'Admin',
      weight: 10,
      menus: [
        {
          name: 'Nodes',
          layout: '/app/nodes',
          path: '/app/nodes',
          section: '',
          // icon: <Icon as={MdSchema} width='20px' height='20px' color='inherit' />,
        },
        {
          name: 'Market Data',
          layout: '/app/adminmarketdata',
          path: '/app/adminmarketdata',
          section: '',
          // icon: <Icon as={MdAllInbox} width='20px' height='20px' color='inherit' />,
        },
      ],
    },
    {
      section: 'Market',
      weight: 10,
      menus: [
        {
          name: 'Dashboard',
          layout: '/app/marketdashboard',
          path: '/app/marketdashboard',
          section: '',
          // icon: <Icon as={MdAnalytics} width='20px' height='20px' color='inherit' />,
        },
        {
          name: 'Pairs',
          layout: '/app/marketpairs',
          path: '/app/marketpairs',
          section: '',
          // icon: <Icon as={MdListAlt} width='20px' height='20px' color='inherit' />,
        },
      ],
    },
  ]
  var resp = menuHier

  isAdmin && resp.push( 
    {
      section: 'Admin', weight: 20, menus: [
        {
          w: 5,
          name: 'Manage Users',
          layout: '/app',
          path: '/app/admin/users',
          section: '',
          // icon: <Icon as={MdDomain} width='18px' height='18px' color='inherit' />,
        },
        {
          w: 10,
          name: 'Db Manager',
          layout: '/app',
          path: '/app/admin/dbmanager',
          section: '',
          // icon: <Icon as={MdSchema} width='18px' height='18px' color='inherit' />,
        },
      ]
    });
  return resp
}
// export const findRoutesx = (props) => {
//   const defaultRoute = [
//     {
//       name: 'Main Dashboard',
//       layout: '/app',
//       path: '/app',
//       section: '',
//       icon: <Icon as={MdHome} width='20px' height='20px' color='inherit' />,
//     },
//   ]
//   var resp = defaultRoute

//   switch (props) {
//     case 'appAdmin':
//       resp = [...defaultRoute, {
//         name: 'Admin',
//         layout: '/app',
//         path: '/app/admin',
//         section: '',
//         icon: <Icon as={MdLock} width='20px' height='20px' color='inherit' />,
//       }]
//       break;
//     default:
//       resp = defaultRoute
//   }

//   return resp
// }