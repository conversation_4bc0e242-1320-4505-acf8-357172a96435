const GoogleGenerativeAI = require('@google/generative-ai').GoogleGenerativeAI;
var fnxDb = require('./fnx.db');
var ps = require('ps-node');
var kill = require('tree-kill');
var fs = require('fs')
const fss = require('fs').promises;
const shell = require('shelljs');
const { exec } = require('child_process');
const path = require('path');
const cronstrue = require('cronstrue');
const Binance = require('node-binance-api');
const childProcess = require('child_process');
var spawn = require('child_process').spawn;
// var execfile = require('child_process').execFile
const { v4: uuidv4 } = require('uuid');
const modeDebug = true;
const fnxCore = require('./fnx.core');
const rulesetparams = require('../battle.ruleset.params');

const Redis = require('ioredis');
const redixPrefix = require('../redis.prefix.js');
const { default: sqlTradesClient } = require('../sqltradeslite');

const fnx = require('../../../nodes/_.functions.js')
const fnxIdx = require('../../../nodes/_.functions.indicators.js');
const fnxDex = require('../../../nodes/_.functions.dex.js');
const battleExchangeInfo = require('../battle.exchangeinfo.json');

const cronParser = require('cron-parser');
const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args);
const sleep = exports.sleep = (ms = 300) => { return new Promise(resolve => setTimeout(resolve, ms)); }

const dexSync_init_checkPosTPLs = true;
const commissionRate = 0.0004;
const mongoDbCollections = {
    dbName: 'algoweb',
    dbCollections: {
        users: 'app.users',
        userotps: 'app.users.otp',
        strategies: 'gauss.strategies',
    }
}
const redisKeys = {
    battle_init: 'battle_init',
};
const mongox = exports.mongox = {
    prompts: {
        list: prx => {
            const { redisClient, dbConnMongo, sqlClient, sqlTradesClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const type = query.type || null;
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts');
                        const q = {};
                        q.is_deleted = false;
                        if (query.user) {
                            q.user = `'${'query.user'}'`;
                        };
                        if (type) {
                            q.category =`${type}`;
                        }
                        var qStr = { $match: q };
                        var project = { $project: { _id: 0, prompt: 1, promptID: 1, title: 1, dtCreated: 1 } };
                        var qq = [qStr, project];
                        // console.log('q', qq)
                        var prompts = await coll.aggregate(qq).toArray();
                        // let prompts = [];
                        resolve(prompts)
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: prompts - ', e)
                    reject(e);
                }
            });
        },
        update: prx => {
            const { redisClient, dbConnMongo, sqlClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts');

                        const { promptID, title, prompt, isUpdate, type = null } = body;

                        if (isUpdate) {
                            // Update existing prompt
                            const result = await coll.updateOne(
                                { promptID: promptID },
                                {
                                    $set: {
                                        title: title,
                                        prompt: prompt,
                                        category: type,
                                        dtUpdated: new Date()
                                    }
                                }
                            );
                            resolve(result);
                        } else {
                            // Create new prompt
                            const newPrompt = {
                                promptID: `${Date.now()}`,
                                title: title,
                                prompt: prompt,
                                category: type,
                                dtCreated: new Date(),
                                dtUpdated: new Date(),
                                is_deleted: false
                            };
                            const result = await coll.insertOne(newPrompt);
                            resolve(result);
                        }
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: update prompt - ', e)
                    reject(e);
                }
            });
        },
        delete: prx => {
            const { redisClient, dbConnMongo, sqlClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts');

                        const { promptID } = body;

                        // Mark as deleted instead of actually deleting
                        const result = await coll.updateOne(
                            { promptID: promptID },
                            {
                                $set: {
                                    is_deleted: true,
                                    dtUpdated: new Date()
                                }
                            }
                        );
                        resolve(result);
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: delete prompt - ', e)
                    reject(e);
                }
            });
        }
    },
    dataschemes: {
        list: prx => {
            const { redisClient, dbConnMongo, sqlClient, sqlTradesClient, slug, query, body } = prx;
            return new Promise(async (resolve, reject) => {
                try {
                    if (dbConnMongo) {
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.ai.prompts_dataSchemas');
                        const q = {};
                        q.is_deleted = false;
                        if (query.user) {
                            q.user = `'${'query.user'}'`;
                        };
                        var qStr = { $match: q };
                        var project = { $project: { _id: 0, semaID: 1, title: 1, sema: 1, category: 1, dtCreated: 1 } };
                        var qq = [qStr, project];
                        var prompts = await coll.aggregate(qq).toArray();
                        // let prompts = [];
                        resolve(prompts)
                    } else {
                        reject('no dbConnMongo')
                    }
                } catch (e) {
                    console.log('err fnxAi: dataschemes - list', e)
                    reject(e);
                }
            });
        },
    },
};

const redix = exports.redix = {
    pairklinedata : async prx => {
        const { redisClient, slug, query, body } = prx;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let redisKey = redixPrefix.dataKline + slug[1];
                let redisKeyTicker = redixPrefix.dataKlineTicker + slug[1];
                // console.log('redisKey', redisKey, redixPrefix.dataKline, slug)
                let redisValStg = await redisClient.get(redisKey);
                let redisTickerValStg = await redisClient.get(redisKeyTicker);

                try {
                    redisValStg = JSON.parse(redisValStg);
                }
                catch (e) {}

                try {
                    redisTickerValStg = JSON.parse(redisTickerValStg);
                } catch (e) {};
                if (redisTickerValStg && redisTickerValStg?.k) {
                    let sonKlineTime = redisValStg.slice(-1)[0][0];
                    let tickerTime = redisTickerValStg.k.t;
                    let tickerInterval = redisTickerValStg.k.i;
                    let intervalMs = intervalToMilliseconds(tickerInterval);
                    redisTickerValStg.k.E = redisTickerValStg.E;
                    if (tickerTime - sonKlineTime <= intervalMs) {
                        // Find if there's a kline with the same time as the ticker
                        let existingIndex = redisValStg.findIndex(kline => kline[0] === tickerTime);
                        if (existingIndex !== -1) {
                            redisValStg[existingIndex] = KlineTicker(redisTickerValStg.k, 'array');
                        } else if (tickerTime > sonKlineTime) {
                            // Append ticker data as new kline
                            redisValStg.push(KlineTicker(redisTickerValStg.k, 'array'));
                        }
                    }
                }

                let limit = query.limit || 200 ;
                let resp;
                if (query.json) {
                    resp = redisValStg.map(a => klineArr2Json(a));
                } else {
                    resp = redisValStg;
                }
                resp = resp.slice(-limit);
                let sure = Date.now() - dt
                resolve({ klines: resp, sure });
            }
            catch (e) {
                console.log('redis pairklinedata error', e)
                reject({ data: false });
            }
        });
    }   
}

const ollama = exports.ollama = {
    models : async prx => {
        const { redisClient, slug, query, body } = prx;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                // Ollama API'sinden model listesini çek
                const response = await fetch('http://localhost:11434/api/tags');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const models = data.models || [];
                resolve(models);
    // resolve({ klines: resp, sure });
            }
            catch (e) {
                console.log('ollama models error', e)
                reject({ data: false });
            }
        });
    },
    query : async prx => {
        const { redisClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            var dtBOP = Date.now();
            var model = body.model?.model || 'gemma3:1b"';
            const prompt = body.prompt;
            console.log('modelQueries - ollama: Querying with model:', model, prompt);
            const uri = 'http://127.0.0.1:11434/api/generate';
            if (!prompt) {
                console.error('modelQueries - ollama: Error no prompt', prompt);
                return null;
            }
            try {
                const response = await fetch(uri, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model,
                        prompt,
                        stream: false, // We want a single response
                    }),
                });
                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('modelQueries Ollama query error:', errorData);
                    return null;
                }
                const data = await response.json();
                console.log('modelResponse', Date.now() - dtBOP, data.response);
                const modelResponse = data.response;
                resolve({
                    elapsed: Date.now() - dtBOP,
                    aiResponse: modelResponse,
                });
            } catch (error) {
                console.error('Error fetching metadata from Ollama:', error);
                reject(e.message);
            }
        });
    },
    query_gemini: async prx => {
        const { redisClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            var dtBOP = Date.now();
            const prompt = body.prompt;
            const modelNames = body.model || 'gemini-2.5-flash-lite';

            if (!prompt) {
                console.error('modelQueries - query_gemini: Error no prompt', prompt);
                reject('no prompt');
            }
            try {
                const modelName = modelNames; // Specify the model you want to use
                const genAI = new GoogleGenerativeAI(process.env.GEMINI);
                const genAIModel = genAI.getGenerativeModel({ model: modelName });
                const result = await genAIModel.generateContent(prompt);
                const modelResponse = result.response.text().trim();
                console.log('modelResponse', Date.now() - dtBOP);
                try {
                    //model response ve prompt u bir dosyaya yaz timestamp ile.
                    let fileName = 'gemini_' + Date.now() + '.json';
                    let saveData = {
                        prompt,
                        response: modelResponse,
                        dt: new Date(Date.now()).toISOString(),
                        elapsed: Date.now() - dtBOP,
                    };
                    await fnxCore.main.save2file(saveData, fileName, true);
                    console.log('modelResponse saved to', fileName);
                } catch (e) {
                    console.log('modelResponse save error', e)
                }

                resolve({
                    elapsed: Date.now() - dtBOP,
                    aiResponse: modelResponse,
                });
            } catch (error) {
                console.error('Error fetching metadata from Ollama:', error);
                reject(error.message);
            }
        });
    },
}

const sqlite = exports.sqlite = {   
    list: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try {   
                let qTxt = `SELECT name
                            FROM sqlite_schema
                            WHERE   type ='table' 
                                    AND
                                    name NOT LIKE 'sqlite_%'
                                    ;`;
                var query = sqlClient.prepare(qTxt).all()
                resolve(query);
            } catch (e) {
                console.log('generic time e', e)
            }
        });
    },
    savedata: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            const valuez = JSON.parse(body);
            const dataName = (valuez.dataName) || '';
            const dataValue = typeof valuez.dataValue === 'string' ? valuez.dataValue : JSON.stringify(valuez.dataValue) || '';
            try {
                // Check if sandbox_selectedPair table exists
                let checkTableQuery = `SELECT name FROM sqlite_master WHERE type='table' AND name='sandbox_datax';`;
                let tableExists = sqlClient.prepare(checkTableQuery).get();
                
                // If table doesn't exist, create it
                if (!tableExists) {
                    // console.log('table not exists')
                    let createTableQuery = `
                        CREATE TABLE sandbox_datax (
                            dataName TEXT ,
                            dataValues TEXT ,
                            dtCreated datetime DEFAULT CURRENT_TIMESTAMP,
                            is_deleted boolean DEFAULT false
                        );
                    `;
                    sqlClient.exec(createTableQuery);
                } else {
                    // console.log('table exists')
                }

                //dataName e sahip daha oncesınden bir kayıt varsa onları delete et.
                //gercekten satir olarak sil
                let deleteQuery = `
                        DELETE FROM sandbox_datax
                        WHERE dataName = ?
                        ;
                    `;
                sqlClient.prepare(deleteQuery).run(dataName);
                
                let upsertQuery = `
                        INSERT INTO sandbox_datax (dataName, dataValues, dtCreated, is_deleted)
                        VALUES (?, ?, CURRENT_TIMESTAMP, false)
                        ;
                    `;

                dataName.length > 0 && dataValue.length > 0 && sqlClient.prepare(upsertQuery).run(dataName, dataValue);
                
                resolve({ success: true, message: 'Data saved successfully' });
            } catch (e) {
                console.log('Error saving data:', e);
                reject(e);
            }
        });
    },
};      

const anomali = exports.anomali = {
    runScript: (scriptPath, params, callback, io) => {
        // keep track of whether callback has been invoked to prevent multiple invocations
        var invoked = false;
        var child = childProcess.fork(scriptPath, [params]);

        child.on('message', function (message) {
            console.log('process2 Message from Child process : ' + message);
        });

        child.on('exit', function (code) {
            console.log(`child exiting: code: `, code);
            callback(code);
            // do some cleanup
        })
        child.on('error', function (err) {
            console.log(`child exiting: failed!`, err);
            callback(false)
            // do some cleanup
        })

        child.on('childProcess error', function (err) {
            console.log('error', err)
            if (invoked) return;
            invoked = true;
            callback(err);
        });
        // listen for errors as they may prevent the exit event from firing
        process.on('error', function (err) {
            console.log('process.on error', err)
            if (invoked) return;
            invoked = true;
            callback(err);
        });

        // execute the callback once the process has finished running
        child.on('childProcess exit', function (code) {
            console.log('exit', code)
            if (invoked) return;
            invoked = true;
            var err = code === 0 ? null : new Error('exit code ' + code);
            callback(err);
        });

    },
    prepare_anomaly_data: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try { 
            // console.log('process.cwd()', process.cwd());
            // console.log('body', body);
            anomali.runScript(path.join(process.cwd(), '/nodes/sandbox/ai_anomali.js'), 'nodes', function (err) {
                if (err == 0) { 
                    console.log('finished running backtest');
                    getResp = {
                        error: false,
                        openReports: true,
                        data: {
                            status: 'backtest success!',
                            time: new Date().toISOString(),
                        }
                    };
                    resolve({...getResp, });
                    return
                } else if (err == 1) {
                    console.log('backtest task is in progress');
                    getResp = {
                        error: false,
                        openReports: false,
                        data: {
                            status: 'backtest task is in progress',
                            time: new Date().toISOString(),
                        }
                    };
                    resolve({...getResp, });
                    return
                    // throw err
                } else {
                    console.log('error in run script', err);
                    getResp = {
                        error: true,
                        errCode: err,
                        time: new Date().toISOString(),
                    };
                    resolve({...getResp, });
                    return
                };
            }, io);
            } catch (e) {
                console.log('anomali prepare_anomaly_data error', e)
                reject({ data: false });
            }
        });
    },
    get_anomaly_list: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try { 
                let sql_query = `
                        SELECT * 
                        FROM sandbox_binance_anomaly
                        WHERE 1 = 1
                        -- AND reason_combined NOT IN ('normal')
                        -- ORDER BY anomaly_score DESC;
                `;
                let resp = sqlClient.prepare(sql_query).all();
                resolve(resp);
            } catch (e) {
                console.log('anomali get_anomaly_list error', e)
                reject({ data: false });
            }
        });
    },
    get_anomaly_pairs: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try {
                /**
                 * Kripto para verilerini analiz ederek en iyi 5 scalp trading çiftini belirleyen fonksiyon.
                 * Analiz; volatilite, likidite, momentum ve anormalliklere dayalı bir skorlama sistemi kullanır.
                */
                function analyzePairsForScalping(data) {
                    if (!data || !Array.isArray(data) || data.length === 0) {
                        console.error("Geçersiz veya boş veri seti.");
                        return [];
                    }

                    // 1. Veriyi ön işleme (String'leri sayıya çevirme)
                    const processedData = data.map(p => ({
                        ...p,
                        priceChangePercent: parseFloat(p.priceChangePercent) || 0,
                        quoteVolume: parseFloat(p.quoteVolume) || 0,
                        funding_rate_score: parseFloat(p.funding_rate_score) || 0,
                        range_position_percent: parseFloat(p.range_position_percent) || 0,
                    }));

                    // 2. Normalizasyon için maksimum değerleri bulma
                    const maxValues = processedData.reduce((maxs, p) => ({
                        priceChangePercent: Math.max(maxs.priceChangePercent, p.priceChangePercent),
                        quoteVolume: Math.max(maxs.quoteVolume, p.quoteVolume),
                        funding_rate_score: Math.max(maxs.funding_rate_score, p.funding_rate_score),
                    }), {
                        priceChangePercent: 0,
                        quoteVolume: 0,
                        funding_rate_score: 0,
                    });

                    // 3. Her parite için skor hesaplama
                    const scoredData = processedData.map(pair => {
                        // Kriter Ağırlıkları
                        const weights = {
                            volatility: 0.35,
                            liquidity: 0.25,
                            anomaly: 0.25,
                            momentum: 0.15,
                        };

                        // Kriter Skorlarını Hesapla (0-1 arasında normalize edilmiş)
                        const volatilityScore = maxValues.priceChangePercent > 0 ? pair.priceChangePercent / maxValues.priceChangePercent : 0;
                        const liquidityScore = maxValues.quoteVolume > 0 ? pair.quoteVolume / maxValues.quoteVolume : 0;
                        const momentumScore = pair.range_position_percent / 100;

                        let anomalyScore = 0;
                        const reason = pair.reason_combined.toLowerCase();
                        if (reason.includes("yüksek volatilite")) anomalyScore += 0.4;
                        if (reason.includes("hacim anormalliği")) anomalyScore += 0.4;
                        if (reason.includes("anormal fonlama oranı")) {
                            anomalyScore += 0.5; // Bu özel bir durum olduğu için daha yüksek puan
                        }
                        // funding_rate_score'u da anomaliye dahil et
                        if (maxValues.funding_rate_score > 0) {
                            anomalyScore += (pair.funding_rate_score / maxValues.funding_rate_score) * 0.5;
                        }

                        anomalyScore = Math.min(1.0, anomalyScore); // Skor 1'i geçemez

                        // Nihai Ağırlıklı Skoru Hesapla
                        const finalScore =
                            (volatilityScore * weights.volatility) +
                            (liquidityScore * weights.liquidity) +
                            (anomalyScore * weights.anomaly) +
                            (momentumScore * weights.momentum);

                        return {
                            ...pair,
                            score: finalScore
                        };
                    });

                    // 4. Veriyi skora göre sırala ve en iyi 5'i al
                    const topPairs = scoredData
                        .sort((a, b) => b.score - a.score)
                        .slice(0, 5);

                    // 5. Çıktıyı formatla
                    const generateReasoning = (pair) => {
                        let reasons = [];
                        if ((pair.priceChangePercent / maxValues.priceChangePercent) > 0.5) {
                            reasons.push(`Sıradışı Volatilite (%${pair.priceChangePercent.toFixed(1)})`);
                        }
                        if ((pair.quoteVolume / maxValues.quoteVolume) > 0.25) {
                            reasons.push(`Yüksek Likidite (${(pair.quoteVolume / 1_000_000).toFixed(0)}M USDT)`);
                        }
                        if (pair.range_position_percent > 75) {
                            reasons.push(`Güçlü Momentum (Range Pozisyonu: %${pair.range_position_percent.toFixed(1)})`);
                        }
                        const reasonText = pair.reason_combined.toLowerCase();
                        if (reasonText.includes("hacim anormalliği")) {
                            reasons.push("Belirgin Hacim Anormalliği");
                        }
                        if (reasonText.includes("anormal fonlama oranı")) {
                            reasons.push(`Anormal Fonlama Oranı (Skor: ${pair.funding_rate_score.toFixed(2)}) -> Short Squeeze Potansiyeli`);
                        }
                        return reasons.join('; ');
                    };

                    return topPairs.map((pair, index) => ({
                        Rank: index + 1,
                        Symbol: pair.symbol,
                        ConfidenceScore: (pair.score * 10).toFixed(1) + "/10",
                        Reasoning: generateReasoning(pair),
                        Metrics: {
                            PriceChangePercent: `${pair.priceChangePercent.toFixed(2)}%`,
                            QuoteVolume: `${(pair.quoteVolume / 1_000_000).toFixed(2)}M`,
                            RangePosition: `${pair.range_position_percent.toFixed(2)}%`,
                            Anomaly: pair.reason_combined,
                            FundingRateScore: pair.funding_rate_score.toFixed(2)
                        }
                    }));
                }

                function generateScalpRecommendations(marketData) {
                    // --- Konfigürasyon ve Sabitler ---
                    const CONFIG = {
                        BENCHMARK_SYMBOLS: ["BTCUSDT", "BTCUSDC"], // Piyasa genel durumunu ölçmek için referans alınacak pariteler
                        RECOMMENDATION_COUNT: 5, // Önerilecek maksimum parite sayısı
                        MIN_QUOTE_VOLUME: 50000000, // Analize dahil edilecek minimum 24s hacim (USDT)

                        // Puanlama ağırlıkları (Toplamı 100 olmak zorunda değil, göreceli önem derecesini belirtir)
                        WEIGHTS: {
                            ANOMALY: 45,       // Piyasa genelinden ne kadar saptığı (en önemli)
                            VOLATILITY: 35,    // priceChangePercent ve price_spread_percent
                            LIQUIDITY: 20      // quoteVolume ve işlem sayısı (count)
                        },

                        // Strateji oluşturma parametreleri
                        STRATEGY: {
                            ENTRY_PULLBACK_PERCENT: 0.002, // Fiyattan ne kadar geri çekilmede giriş yapılacağı (%0.2)
                            STOP_LOSS_RANGE_PERCENT: 0.1, // 24s aralığının % kaçının risk mesafesi olarak alınacağı (%10)
                            RR_RATIOS: [1.5, 2.5] // Kâr al hedefleri için Risk/Ödül oranları
                        }
                    };

                    // --- 1. Adım: Piyasa Genel Durumunu Belirle (Benchmark) ---
                    let benchmarkAsset = marketData.find(p => CONFIG.BENCHMARK_SYMBOLS.includes(p.symbol));

                    if (!benchmarkAsset) {
                        console.warn("Benchmark asset (BTC) not found. Anomaly scores may be less accurate.");
                        // Benchmark bulunamazsa piyasa ortalamasını da kullanabiliriz, şimdilik 0 kabul edelim.
                        benchmarkAsset = { priceChangePercent: "0" };
                    }

                    const benchmarkChange = parseFloat(benchmarkAsset.priceChangePercent);

                    // --- 2. Adım: Pariteleri Filtrele ve Puanla ---
                    const scoredPairs = marketData
                        .map(pair => {
                            // Veri tiplerini sayısal formata çevirelim
                            const p = {
                                symbol: pair.symbol,
                                lastPrice: parseFloat(pair.lastPrice),
                                highPrice: parseFloat(pair.highPrice),
                                lowPrice: parseFloat(pair.lowPrice),
                                priceChangePercent: parseFloat(pair.priceChangePercent),
                                price_spread_percent: parseFloat(pair.price_spread_percent),
                                quoteVolume: parseFloat(pair.quoteVolume),
                                count: parseInt(pair.count, 10),
                                range_position_percent: parseFloat(pair.range_position_percent)
                            };

                            // Düşük hacimli pariteleri ele
                            if (p.quoteVolume < CONFIG.MIN_QUOTE_VOLUME || CONFIG.BENCHMARK_SYMBOLS.includes(p.symbol)) {
                                return null;
                            }

                            // Puanlama Mantığı
                            const anomalyScore = Math.abs(p.priceChangePercent - benchmarkChange);
                            const volatilityScore = Math.abs(p.priceChangePercent) + p.price_spread_percent;
                            // Likiditeyi logaritmik olarak alıyoruz ki devasa hacimler skoru aşırı domine etmesin
                            const liquidityScore = Math.log10(p.quoteVolume) + Math.log10(p.count);

                            const totalScore =
                                (anomalyScore * CONFIG.WEIGHTS.ANOMALY) +
                                (volatilityScore * CONFIG.WEIGHTS.VOLATILITY) +
                                (liquidityScore * CONFIG.WEIGHTS.LIQUIDITY);

                            return { ...p, score: totalScore };
                        })
                        .filter(p => p !== null) // Elenen pariteleri temizle
                        .sort((a, b) => b.score - a.score); // En yüksek puandan düşüğe sırala

                    // --- 3. Adım: En İyi Adayları Seç ve Strateji Oluştur ---
                    const topPairs = scoredPairs.slice(0, CONFIG.RECOMMENDATION_COUNT);

                    const recommendedPairs = topPairs.map(pair => {
                        // Strateji oluşturma mantığı
                        const direction = pair.priceChangePercent > 0 ? "Long" : "Short";
                        const dailyRange = pair.highPrice - pair.lowPrice;
                        const riskDistance = dailyRange * CONFIG.STRATEGY.STOP_LOSS_RANGE_PERCENT;

                        let entryLevel, stopLoss, takeProfitLevels;

                        if (direction === "Long") {
                            entryLevel = pair.lastPrice * (1 - CONFIG.STRATEGY.ENTRY_PULLBACK_PERCENT);
                            stopLoss = entryLevel - riskDistance;
                            takeProfitLevels = CONFIG.STRATEGY.RR_RATIOS.map(ratio => ({
                                level: entryLevel + (riskDistance * ratio),
                                riskRewardRatio: `${ratio}:1`
                            }));
                        } else { // Short
                            entryLevel = pair.lastPrice * (1 + CONFIG.STRATEGY.ENTRY_PULLBACK_PERCENT);
                            stopLoss = entryLevel + riskDistance;
                            takeProfitLevels = CONFIG.STRATEGY.RR_RATIOS.map(ratio => ({
                                level: entryLevel - (riskDistance * ratio),
                                riskRewardRatio: `${ratio}:1`
                            }));
                        }

                        return {
                            symbol: pair.symbol,
                            confidenceScore: Math.round((pair.score / scoredPairs[0].score) * 95) + 5, // En yükseği 100 yap, diğerlerini oranla
                            analysis: {
                                marketAnomaly: `Piyasa genelindeki ${benchmarkChange.toFixed(2)}%'lik değişime kıyasla ${pair.priceChangePercent.toFixed(2)}%'lik bir sapma gösteriyor.`,
                                internalAnomaly: `24 saatlik fiyat aralığı (volatilitesi) %${pair.price_spread_percent.toFixed(2)}. Fiyat, bu aralığın %${pair.range_position_percent.toFixed(2)} konumunda.`
                            },
                            strategy: {
                                direction,
                                logic: `Güçlü ${direction === 'Long' ? 'pozitif' : 'negatif'} momentum ve piyasadan ayrışma, trendin devam etme potansiyelini gösteriyor.`,
                                entryLevels: [{ type: "Primary", level: entryLevel.toFixed(5) }],
                                timeframe: {
                                    tradeDuration: "1 ila 15 dakika",
                                    recommendationValidity: "1-2 saat"
                                },
                                riskManagement: {
                                    stopLoss: { level: stopLoss.toFixed(5) },
                                    riskCalculationLogic: "Pozisyon büyüklüğü, (Giriş Fiyatı - Stop-Loss Fiyatı) arasındaki farkın toplam portföyün en fazla %1'i kadar bir riske denk geleceği şekilde ayarlanmalıdır."
                                },
                                takeProfitLevels: takeProfitLevels.map(tp => ({
                                    ...tp,
                                    level: tp.level.toFixed(5)
                                }))
                            }
                        };
                    });

                    // --- 4. Adım: Final JSON Çıktısını Oluştur ---
                    return {
                        analysisTimestamp: new Date().toISOString(),
                        disclaimer: "Bu analiz, sağlanan anlık piyasa verilerine dayanmaktadır ve yüksek riskli, anlık alım-satım (scalp trading) stratejileri için potansiyel fırsatları belirlemeyi amaçlamaktadır. Herhangi bir finansal tavsiye niteliği taşımaz. Kripto para piyasaları aşırı derecede volatildir ve yüksek risk içerir.",
                        marketAnalysis: {
                            overallCondition: "Piyasa genelinde yatay seyir hakim.",
                            observation: `Referans olarak alınan ${benchmarkAsset.symbol}, son 24 saatte %${benchmarkChange.toFixed(3)}'lük bir değişim gösterdi. Bu durum, diğer altcoinlerdeki aşırı hareketlerin piyasa genelinden ayrışan belirgin anomaliler olduğunu göstermektedir.`
                        },
                        selectionCriteria: [
                            { criterion: "Yüksek Volatilite", description: "priceChangePercent ve price_spread_percent metrikleri ile ölçülür." },
                            { criterion: "Yüksek Likidite", description: "quoteVolume ve işlem adedi (count) ile ölçülür." },
                            { criterion: "Piyasa Anomali Skoru", description: "Paritenin fiyat değişiminin, piyasa referansından (BTC) ne kadar saptığını ölçer." }
                        ],
                        recommendedPairs: recommendedPairs
                    };
                }

                function calculateInternalConfidence(pair, direction) {
                    // Zaman dilimleri ve scalp trading için önem derecelerini belirten ağırlıklar
                    const timeframes = {
                        '1m': { weight: 40, candles: 5 },   // En yüksek ağırlık anlık momentumda
                        '15m': { weight: 30, candles: 5 },
                        '1h': { weight: 20, candles: 3 },
                        '4h': { weight: 10, candles: 3 }    // Daha uzun vadeli trend teyidi için
                    };

                    let totalScore = 0;
                    let totalWeight = 0;

                    for (const tf in timeframes) {
                        const klinesKey = `klines_${tf}`;
                        const klines = pair[klinesKey];
                        const config = timeframes[tf];

                        // Yeterli kline verisi yoksa bu zaman dilimini atla
                        if (!klines || klines.length < config.candles) {
                            continue;
                        }

                        const recentKlines = klines.slice(-config.candles);
                        const lastKline = recentKlines[recentKlines.length - 1];
                        const prevKlines = recentKlines.slice(0, -1);

                        let timeframeScore = 0;
                        const maxTimeframeScore = 3; // Her zaman dilimi için maksimum 3 puan

                        // --- Kriter 1: Hacim Artışı (Momentum Teyidi) ---
                        const avgVolume = prevKlines.reduce((sum, k) => sum + parseFloat(k[5]), 0) / prevKlines.length;
                        if (parseFloat(lastKline[5]) > avgVolume * 1.25) { // Son mum hacmi, öncekilerin ortalamasından %25 fazlaysa
                            timeframeScore += 1;
                        }

                        // --- Kriter 2: Trend Yönü ve Mum Kapanışı ---
                        const open = parseFloat(lastKline[1]);
                        const high = parseFloat(lastKline[2]);
                        const low = parseFloat(lastKline[3]);
                        const close = parseFloat(lastKline[4]);

                        const isUpCandle = close > open;
                        const isDownCandle = close < open;

                        if (direction === 'Long' && isUpCandle) {
                            timeframeScore += 1;
                        } else if (direction === 'Short' && isDownCandle) {
                            timeframeScore += 1;
                        }

                        // --- Kriter 3: Kapanış Gücü (Alıcı/Satıcı Baskısı) ---
                        const candleRange = high - low;
                        // Kapanışın mum içindeki konumu (0: en dip, 1: en tepe)
                        const closingPosition = candleRange > 0 ? (close - low) / candleRange : 0.5;

                        if (direction === 'Long' && closingPosition > 0.70) { // Mumun üst %30'unda kapandıysa güçlü alıcı baskısı
                            timeframeScore += 1;
                        } else if (direction === 'Short' && closingPosition < 0.30) { // Mumun alt %30'unda kapandıysa güçlü satıcı baskısı
                            timeframeScore += 1;
                        }

                        // Ağırlıklı skoru hesapla
                        totalScore += (timeframeScore / maxTimeframeScore) * config.weight;
                        totalWeight += config.weight;
                    }

                    if (totalWeight === 0) return 50; // Hiç kline verisi yoksa varsayılan puan

                    // Final skorunu 100'lük sisteme normalize et
                    return Math.round((totalScore / totalWeight) * 100);
                }

                function generateScalpRecommendations_adv(fullMarketData) {
                    const CONFIG = {
                        BENCHMARK_SYMBOLS: ["BTCUSDT", "BTCUSDC"],
                        RECOMMENDATION_COUNT: 5,
                        MIN_QUOTE_VOLUME: 50000000,
                        WEIGHTS: {
                            ANOMALY: 45,
                            VOLATILITY: 35,
                            LIQUIDITY: 20
                        },
                        STRATEGY: {
                            ENTRY_PULLBACK_PERCENT: 0.002,
                            STOP_LOSS_RANGE_PERCENT: 0.1,
                            RR_RATIOS: [1.5, 2.5]
                        }
                    };

                    const benchmarkAsset = fullMarketData.find(p => CONFIG.BENCHMARK_SYMBOLS.includes(p.symbol)) || { priceChangePercent: "0" };
                    const benchmarkChange = parseFloat(benchmarkAsset.priceChangePercent);

                    const scoredPairs = fullMarketData
                        .map(pair => {
                            const p = {
                                symbol: pair.symbol,
                                lastPrice: parseFloat(pair.lastPrice),
                                highPrice: parseFloat(pair.highPrice),
                                lowPrice: parseFloat(pair.lowPrice),
                                priceChangePercent: parseFloat(pair.priceChangePercent),
                                price_spread_percent: parseFloat(pair.price_spread_percent),
                                quoteVolume: parseFloat(pair.quoteVolume),
                                count: parseInt(pair.count, 10),
                                range_position_percent: parseFloat(pair.range_position_percent),
                                // Kline verilerini de saklayalım
                                klines_1m: pair.klines_1m,
                                klines_15m: pair.klines_15m,
                                klines_1h: pair.klines_1h,
                                klines_4h: pair.klines_4h
                            };

                            if (p.quoteVolume < CONFIG.MIN_QUOTE_VOLUME || CONFIG.BENCHMARK_SYMBOLS.includes(p.symbol)) {
                                return null;
                            }

                            // --- Market Confidence Score Hesaplaması ---
                            const anomalyScore = Math.abs(p.priceChangePercent - benchmarkChange);
                            const volatilityScore = Math.abs(p.priceChangePercent) + p.price_spread_percent;
                            const liquidityScore = Math.log10(p.quoteVolume) + Math.log10(p.count);
                            const marketScore = (anomalyScore * CONFIG.WEIGHTS.ANOMALY) +
                                (volatilityScore * CONFIG.WEIGHTS.VOLATILITY) +
                                (liquidityScore * CONFIG.WEIGHTS.LIQUIDITY);

                            // --- Internal Confidence Score Hesaplaması ---
                            const direction = p.priceChangePercent > 0 ? "Long" : "Short";
                            const internalScore = calculateInternalConfidence(p, direction);

                            // Sıralama için birleşik bir skor
                            const combinedScore = (marketScore * 0.6) + (internalScore * 0.4);

                            return { ...p, marketScore, internalScore, combinedScore };
                        })
                        .filter(p => p !== null)
                        .sort((a, b) => b.combinedScore - a.combinedScore);

                    const topMarketScore = scoredPairs.length > 0 ? scoredPairs[0].marketScore : 1;
                    const recommendedPairs = scoredPairs.slice(0, CONFIG.RECOMMENDATION_COUNT).map(pair => {
                        const direction = pair.priceChangePercent > 0 ? "Long" : "Short";
                        const dailyRange = pair.highPrice - pair.lowPrice;
                        const riskDistance = dailyRange * CONFIG.STRATEGY.STOP_LOSS_RANGE_PERCENT;

                        let entryLevel, stopLoss, takeProfitLevels;
                        if (direction === "Long") {
                            entryLevel = pair.lastPrice * (1 - CONFIG.STRATEGY.ENTRY_PULLBACK_PERCENT);
                            stopLoss = entryLevel - riskDistance;
                            takeProfitLevels = CONFIG.STRATEGY.RR_RATIOS.map(ratio => ({
                                level: entryLevel + (riskDistance * ratio),
                                riskRewardRatio: `${ratio}:1`
                            }));
                        } else {
                            entryLevel = pair.lastPrice * (1 + CONFIG.STRATEGY.ENTRY_PULLBACK_PERCENT);
                            stopLoss = entryLevel + riskDistance;
                            takeProfitLevels = CONFIG.STRATEGY.RR_RATIOS.map(ratio => ({
                                level: entryLevel - (riskDistance * ratio),
                                riskRewardRatio: `${ratio}:1`
                            }));
                        }

                        return {
                            symbol: pair.symbol,
                            marketConfidence: Math.min(100, Math.round((pair.marketScore / topMarketScore) * 100)),
                            internalConfidence: pair.internalScore,
                            analysis: {
                                marketAnomaly: `Piyasa genelindeki ${benchmarkChange.toFixed(2)}%'lik değişime kıyasla ${pair.priceChangePercent.toFixed(2)}%'lik bir sapma gösteriyor.`,
                                internalAnomaly: `24 saatlik fiyat aralığı (volatilitesi) %${pair.price_spread_percent.toFixed(2)}. Fiyat, bu aralığın %${pair.range_position_percent.toFixed(2)} konumunda.`
                            },
                            strategy: {
                                direction,
                                logic: `Güçlü ${direction === 'Long' ? 'pozitif' : 'negatif'} momentum ve piyasadan ayrışma, trendin devam etme potansiyelini gösteriyor.`,
                                entryLevels: [{ type: "Primary", level: entryLevel.toFixed(5) }],
                                timeframe: { tradeDuration: "1 ila 15 dakika", recommendationValidity: "1-2 saat" },
                                riskManagement: {
                                    stopLoss: { level: stopLoss.toFixed(5) },
                                    riskCalculationLogic: "Pozisyon büyüklüğü, (Giriş Fiyatı - Stop-Loss Fiyatı) arasındaki farkın toplam portföyün en fazla %1'i kadar bir riske denk geleceği şekilde ayarlanmalıdır."
                                },
                                takeProfitLevels: takeProfitLevels.map(tp => ({ ...tp, level: tp.level.toFixed(5) }))
                            }
                        };
                    });

                    return {
                        analysisTimestamp: new Date().toISOString(),
                        disclaimer: "Bu analiz, sağlanan anlık piyasa verilerine dayanmaktadır ve yüksek riskli, anlık alım-satım (scalp trading) stratejileri için potansiyel fırsatları belirlemeyi amaçlamaktadır. Herhangi bir finansal tavsiye niteliği taşımaz. Kripto para piyasaları aşırı derecede volatildir ve yüksek risk içerir.",
                        marketAnalysis: {
                            overallCondition: "Piyasa genelinde yatay seyir hakim.",
                            observation: `Referans olarak alınan ${benchmarkAsset.symbol}, son 24 saatte %${benchmarkChange.toFixed(3)}'lük bir değişim gösterdi. Bu durum, diğer altcoinlerdeki aşırı hareketlerin piyasa genelinden ayrışan belirgin anomaliler olduğunu göstermektedir.`
                        },
                        selectionCriteria: [
                            { criterion: "Market Confidence", description: "Paritenin piyasa geneline göre volatilitesini, likiditesini ve ayrışmasını ölçer." },
                            { criterion: "Internal Confidence", description: "Paritenin kendi kline verilerine (1m, 15m, 1h, 4h) dayalı anlık trend ve hacim gücünü ölçer." }
                        ],
                        recommendedPairs: recommendedPairs
                    };
                }


                let referenceData = [];

                // sandbox_binance_anomaly tablosundan pair market degerlerini al
                let sql_query = `
                        SELECT * 
                        FROM sandbox_binance_anomaly
                        WHERE 1 = 1;
                `;
                let resp = sqlClient.prepare(sql_query).all();

                // sandbox_binance_fundingrates tablosundan funding rate history degerlerini al ve resp'e ekle
                sql_query = `
                        SELECT * 
                        FROM sandbox_binance_fundingrates
                        WHERE 1 = 1;
                `;
                let resp2 = sqlClient.prepare(sql_query).all();
                resp.map(r => {
                    let funding_history = resp2.find(r2 => r2.symbol == r.symbol)?.data || 0;
                    r.funding_rate_history = funding_history;
                });

                // sandbox_datax tablosundan params degerlerini al ve buradaki intervals degeri ile target intervals dizini olustur
                sql_query = `
                        SELECT * 
                        FROM sandbox_datax
                        WHERE 1 = 1 and dataName = 'params'
                        order by dtCreated desc
                        limit 1;
                `;
                let resp3 = sqlClient.prepare(sql_query).all();
                let target_intervals = resp3[0]?.params?.intervals || ['1d', '1h', '1m'];

                // target_intervals daki interval degerlerine göre sandbox_binance_klines_*** tablosundan verileri al ve resp'e ekle
                target_intervals.map(async interval => {
                    sql_query = `
                            SELECT * 
                            FROM sandbox_binance_klines_${interval}
                            WHERE 1 = 1;
                    `;
                    let resp4 = sqlClient.prepare(sql_query).all();
                    resp.map(r => {
                        let kline_data = resp4.find(r4 => r4.symbol == r.symbol)?.data || 0;
                        r[`kline_data_${interval}`] = kline_data;
                    });
                });

                referenceData = resp;


                // --- KULLANIM ÖRNEĞİ ---
                
                // const top5Pairs = analyzePairsForScalping(referenceData);

                // Sonucu daha okunaklı bir formatta göstermek için console.table kullanın
                // console.log("Risk İştahı Yüksek Scalp Trader için En İyi 5 Parite:");
                // console.table(top5Pairs.map(({ Rank, Symbol, ConfidenceScore, Reasoning }) => ({ Rank, Symbol, ConfidenceScore, Reasoning })));
                let pairx = analyzePairsForScalping(referenceData);
                let method2 = generateScalpRecommendations(referenceData);
                let method2adv = generateScalpRecommendations_adv(referenceData);
                resolve({
                    method1: pairx,
                    method2,
                    method3: method2adv,
                });
            } catch (e) {
                console.log('anomali get_anomaly_pairs error', e)
                reject({ data: false });
            }
        });
    },
    get_klines: prx => {
        const { redisClient, sqlClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try {
                const { intervals, battleInterval = "1m", candleCounts = 24} = JSON.parse(body);
                const { interval, symbol } = query;
                // console.log('get_klines', interval);
                let sql_query;
                if (battleInterval) {
                    if (interval) {
                        if (interval !== 'funding') {
                            sql_query = `
                            SELECT symbol, data 
                            FROM sandbox_binance_klines_${interval.toString()}
                            WHERE 1 = 1;
                        `;
                        } else {
                            sql_query = `
                            SELECT symbol, data 
                            FROM sandbox_binance_fundingrates
                            WHERE 1 = 1;
                        `;
                        }
                    } else {
                        sql_query = `
                            SELECT symbol, data 
                            FROM sandbox_binance_klines_${battleInterval.toString()}
                            WHERE 1 = 1;
                        `;
                    }
                    if (symbol) {
                        sql_query += ` AND symbol = '${symbol}'`;
                    }
                    // console.log('sql_query', sql_query);
                    let resp = sqlClient.prepare(sql_query).all();
                    resolve(resp);
                } else {
                    reject({ data: false });
                }
            } catch (e) {
                console.log('get_klines error', e)
                reject({ data: false });
            }
        });
    }
};

const klineArr2Json = adata => {
    if (Array.isArray(adata)) {
        var time = adata[0];
        var timeTR = new Date(new Date(adata[0]) - (new Date().getTimezoneOffset() * 60000)).toISOString()
        var timeISO = new Date(adata[0]);
        var open = Number(adata[1])
        var high = Number(adata[2])
        var low = Number(adata[3])
        var close = Number(adata[4])
        var volume = Number(adata[5])
        var closeTime = (adata[6])
        var closeTimeISO = new Date(adata[6])
        var assetVolume = Number(adata[7])
        var trades = Number(adata[8])
        var color = close - open >= 0 ? 'blue' : 'red';
        var eventTime = Number(adata[12]) ? new Date(new Date(adata[12]) - (new Date().getTimezoneOffset() * 60000)).toISOString() : null || null;
        var src = Number(adata[11]) == "1" ? 'ticker' : 'kline';
        return {
            src, 
            time, open, high, low, close, volume,
            closeTimeISO, assetVolume, trades, color,
            eventTime, timeTR, timeISO, 
        }
    } else return {}
}

// Function to convert interval string to milliseconds
const intervalToMilliseconds = (interval) => {
    const intervalMap = {
        '1s': 1000,
        '1m': 60000,
        '3m': 180000,
        '5m': 300000,
        '15m': 900000,
        '30m': 1800000,
        '1h': 3600000,
        '2h': 7200000,
        '4h': 14400000,
        '6h': 21600000,
        '8h': 28800000,
        '12h': 43200000,
        '1d': 86400000,
        '3d': 259200000,
        '1w': 604800000,
        '1M': 2592000000
    };
    return intervalMap[interval] || 60000; // default to 1m if not found
};

const KlineTicker = (adata, cbType = '') => {
    //https://github.com/binance/binance-spot-api-docs/blob/master/web-socket-streams.md#klinecandlestick-streams-for-utc
    var time = adata.t;
    var timeTR = new Date(new Date(adata.t) - (new Date().getTimezoneOffset() * 60000)).toISOString()
    var timeISO = new Date(adata.t);
    var open = Number(adata.o)
    var high = Number(adata.h)
    var low = Number(adata.l)
    var close = Number(adata.c)
    var volume = Number(adata.v)
    // var closeTime = new Date(adata.T)
    var closeTimeISO = new Date(adata.T)
    var closeTime = adata.T
    var assetVolume = Number(adata.q)
    var trades = Number(adata.n)

    var takerVolume = Number(adata.V);
    var takerAssetVolume = Number(adata.Q);
    var barClosed = adata.x;
    var eventTime = adata.E;
    var eventTimeTR = eventTime ? new Date(new Date(eventTime) - (new Date().getTimezoneOffset() * 60000)).toISOString() : null
    

    var src = 'Ticker'; 
    var color = close - open >= 0 ? 'blue' : 'red';
    if (cbType == 'json') {
        return {
            src, 
            time, open, high, low, close, volume,
            closeTimeISO, assetVolume, trades, color,
            timeTR, timeISO, eventTime, barClosed
            // src, eventTime,
            // time, timeTR, timeISO,
            // open, high, low, close, volume,
            // closeTimeISO, assetVolume, trades, color
        }
    } else {
        let arr = [time, open.toString(), high.toString(), low.toString(), close.toString(), volume.toString(), closeTime,
            assetVolume.toString(), trades, takerVolume.toString(), takerAssetVolume.toString(), "1", 
            //color, timeTR, timeISO, 
            eventTime];
        return arr;
    }

}
