/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */
import React, { useEffect, useState, useContext } from 'react'
import MuiAppBar from '@mui/material/AppBar';
import { Stack, Toolbar, Box, Typography, Badge } from '@mui/material';

import { ColorModeContext } from "../../lib/context/Provider.themeDarkMode";

import IconButton from '@mui/material/IconButton';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';


import { useTheme, styled, createTheme, ThemeProvider } from '@mui/material/styles';
import * as fnx from '../../lib/fnx/fnx.routes.js';
import Footer from '../../_components/pages/generic.footer'
import PgHeader from '../../_components/components/core.header.js'
import Sidebar from '../../_components/components/core.header.drawer.js'

export default function AdminLayout(props) {
    const { children, session, bread, pgTitle, pgBread, ...rest } = props;
    const user = session ? session.user : null;
    const role = user ? user.role : null
    const isAdmin = user ? user?.roles?.isAdmin : null

    // const theme = useTheme();

    const { mode, colorMode, isMobile, isTablet } = useContext(ColorModeContext);

    const routes = fnx.findRoutes({ role, isAdmin, s: 'index' })

    useEffect(() => {
        window.document.documentElement.dir = 'ltr'
    })
    const [open, setOpen] = React.useState(true);
    const toggleDrawer = () => {
        setOpen(!open);
    };


    const theme = React.useMemo(
        () =>
            createTheme({
                palette: {
                    mode,
                },
            }),
        [mode],
    );

    return (
        <ThemeProvider theme={theme}>
            <Stack
                sx={{
                    display: 'flex',
                    width: '100%',
                    minHeight: "100vh",
                    bgcolor: 'background.default',
                    color: 'text.primary',
                    // borderRadius: 1,
                    p: 0,
                }}
                direction="row"
                spacing={0}
            >
                <Sidebar />

                <Stack
                    sx={{
                        display: 'flex',
                        width: '100%',
                        minHeight: "100vh",
                        bgcolor: 'background.default',
                        color: 'text.primary',
                        // borderRadius: 1,
                        p: 0,
                    }}
                    direction="column"
                    spacing={0}
                >
                    <PgHeader />
                    <Stack
                        sx={{
                            display: 'flex',
                            bgcolor: 'background.default',
                            color: 'text.primary',
                            borderRadius: 1,
                            // borderWidth: 1,
                            p: 0,
                            flex: 1,
                        }}
                    >
                        {children}
                    </Stack>

                    <Stack
                        sx={{
                            display: 'flex',
                            width: '100%',
                            bgcolor: 'background.default',
                            color: 'text.primary',
                            // borderRadius: 1,
                            // borderWidth: 1,
                        }} direction="column" spacing={2}>
                        <Footer />
                    </Stack>

                </Stack>
            </Stack>

        </ThemeProvider>
    )
}

