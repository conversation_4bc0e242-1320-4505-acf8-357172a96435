<% var jsonArray = typeof data != "undefined" ? (JSON.stringify(data)) : [] %>
<!DOCTYPE html>
<meta charset="UTF-8">
<title>Trades</title>
<!-- CSS (load bootstrap from a CDN) -->
<style>
  body { padding-top: 11px; }
  .table td, .table th {
      font-size: 11px; border: '1px solid #ccc'
  }
  table td, table th {
      font-size: 11px; border: 1px solid #ccc; padding: 4px;
  }
</style>
<script>
  let tableFromJson = () => {
    // the json data.
    const myBooks = <%- jsonArray %>

    // Extract value from table header. 
    // ('Book ID', 'Book Name', 'Category' and 'Price')
    let col = [];
    for (let i = 0; i < myBooks.length; i++) {
      for (let key in myBooks[i]) {
        if (col.indexOf(key) === -1) {
          col.push(key);
        }
      }
    }

    // Create table.
    const table = document.createElement("table");

    // Create table header row using the extracted headers above.
    let tr = table.insertRow(-1);                   // table row.

    let th = document.createElement("th");      // table header.
    th.innerHTML = '#';
    tr.appendChild(th);
    
    for (let i = 0; i < col.length; i++) {
      let th = document.createElement("th");      // table header.
      th.innerHTML = col[i];
      tr.appendChild(th);
    }

    // add json data to the table as rows.
    for (let i = 0; i < myBooks.length; i++) {

      tr = table.insertRow(-1);
      tr.insertCell(-1).innerHTML = `<th>${i+1}</th>`;

      for (let j = 0; j < col.length; j++) {
        let tabCell = tr.insertCell(-1);
        let value = myBooks[i][col[j]];
        let fieldName = col[j];
        <!-- console.log('filed', fieldName, value); -->
        if (['unRealizedPnlB4Comm', 'unRealizedPnl', 'entryBudget', 'entryAmount', 'commission', 'realizedPnl', 'realizedPnlB4Comm'].includes(fieldName)) {
          value = parseFloat(value).toFixed(2);
        } else if (['realizedPips', 'realizedPipRatio'].includes(fieldName)) {
          value = parseFloat(value).toFixed(5);
        }
        tabCell.innerHTML = myBooks[i][col[j]] ? value : '';
      }
    }

    // Now, add the newly created table with json data, to a container.
    const divShowData = document.getElementById('showData');
    divShowData.innerHTML = "";
    divShowData.appendChild(table);
  }
</script>


<body onload="tableFromJson()">
  <!-- <input type='button' onclick='tableFromJson()' value='Create Table from JSON data' /> -->
  TradesEJ
  <div id="showData" align="center"></div>
</body>

