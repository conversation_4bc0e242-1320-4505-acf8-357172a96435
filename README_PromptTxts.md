🎯 3. Scalp Stratejisi Oluşturma Mantığı
Scalp, 1-5 dakika içinde gir-çık yapılan bir stratejidir. Bu yüzden aşağıdaki 4 temele dayanır:

1. Fırsat Tespiti
Yüksek volatilite + hacim anomalisi olan çiftleri bul
price_volatility_1m
,
volume_anomaly_1m
2. Likidite ve Spread Kontrolü
İşlem maliyeti düşük, slippage riski az olsun
quoteVolume
,
price_spread_percent
3. Fiyat Konumu ve Trend Analizi
Fiyat dip mi, zirve mi, ortada mı?
range_position_percent
,
priceChangePercent
4. Risk/Reward Hesabı
SL ve TP, volatiliteye göre orantılı olmalı
price_volatility_1m
,
klines
5. Gerçek Zamanlı Kline ile Doğrulama
Son kline’larla hareketin yönünü doğrula
Eğer:
Fiyat yukarı, hacim artıyorsa → long sinyali
Fiyat aşağı, hacim artıyorsa → short sinyali


Strateji Nasıl Oluşturuldu?
1. Aday Seçimi
Yüksek volatilite + hacim anomali
price_volatility_1m
,
volume_anomaly_1m
2. Filtreleme
Spread ve likidite kontrolü
price_spread_percent
,
quoteVolume
3. Trend Analizi
Fiyatın 24s aralığındaki yeri
range_position_percent
4. Giriş/Çıkış
Volatiliteye göre SL/TP
price_volatility_1m
× katsayı
5. Doğrulama
Son kline’larla hareketin yönü
klines
son 3-5 mum

 

-----


🎯 Amaç
Anlık olarak 1-5 dakikalık zaman diliminde işlem yapacak bir scalper için, en yüksek fırsat potansiyeline sahip 5 kripto varlık çiftini tespit etmek ve her biri için risk yönetimiyle uyumlu, veriye dayalı bir strateji sunmak.

🔁 Strateji Nasıl Oluşturuldu? (Mantık Akışı)
Bu strateji, aşağıdaki adımları takip ederek oluşturulur:

Fırsat Tespiti: Yüksek volatilite ve hacim anomalisi olan çiftler tespit edilir.
Aday Seçimi: Belirli niceliksel kriterlere göre adaylar filtrelenecek.
Filtreleme: Dinamik filtrelerle gereksiz çiftler elenir.
Trend Analizi: Fiyatın konumu, momentumu ve likidite dengesi değerlendirilir.
Giriş/Çıkış Hesabı: Volatiliteye göre SL/TP seviyeleri belirlenir.
Doğrulama: Son kline’larla hareketin yönü ve hacim doğrulanır.

🔍 1. Fırsat Tespiti
Aşağıdaki metrikler, son 1 dakikalık mum verisi (klines) ve 24 saatlik ticker verisi kullanılarak hesaplanır:

price_volatility_1m: Son N mumdaki fiyat değişimlerinin mutlak ortalaması (% olarak).
volume_anomaly_1m: Bu çiftin son 1dk hacminin, tüm çiftlerin ortalamasına göre z-skoru.
quoteVolume: 24s’te işlem gören USDT cinsinden hacim.
range_position_percent: (last - low) / (high - low) → Fiyat aralığın neresinde?
price_spread_percent: (high - low) / low → 24s içindeki toplam fiyat aralığı.
funding_rate_score: Uzun/kısa baskısının büyüklüğü.
🔎 Amaç: Volatilite, hacim, likidite, spread ve momentum bir araya geldiğinde fırsat doğar. 

🧩 2. Aday Seçimi (Dinamik Çoklu Koşul Filtresi)
Aşağıdaki dinamik koşullar birleştiğinde çift aday olarak kabul edilir. Sabit değerler yerine, anlık piyasa dinamiklerine göre ayarlanan eşikler kullanılır.
 
# Dinamik Eşikler (Her 1-5 dakikada yeniden hesaplanır)
max_volatility_1m = max(tüm çiftlerin price_volatility_1m)          # Örn: 4.97%
max_volume_anomaly_1m = max(tüm çiftlerin volume_anomaly_1m)       # Örn: 2.538
median_quoteVolume = median(tüm çiftlerin quoteVolume)             # Örn: 120M USDT
topluluk_sayısı = len(tüm_çiftler)

# Dinamik Filtre Kuralları
if (
    # Kural 1: Aşırı volatil + yüksek likidite (devasa fırsat)
    (price_volatility_1m > max_volatility_1m * 0.8 and quoteVolume > median_quoteVolume * 3)
    or
    # Kural 2: Dengeli volatilite + yüksek hacim anomalisi
    (price_volatility_1m > 0.45 and volume_anomaly_1m > 0.25 and price_spread_percent < 25)
    or
    # Kural 3: Aşırı hacim patlaması (anomali > 2.0)
    (volume_anomaly_1m > 2.0 and price_volatility_1m > 0.40)
):
    aday = True
📌 Not: 

0.8, 3, 0.45, 0.25, 25, 2.0, 0.40 gibi değerler sabit değil, piyasa koşullarına göre dinamik eşikler olarak kalibre edilir.
Örneğin, genel piyasa yataydaysa, price_volatility_1m > 0.30 yeterli olabilir. Volatilite artarsa eşik yükselir.

🧹 3. Filtreleme (Risk Yönetimi)
Aday çiftler, aşağıdaki kriterlere göre ek filtrelemeden geçirilir:

price_spread_percent > 30% → Yüksek spread → yüksek slippage riski → dikkatli işlem
funding_rate_score > 2.0 → Aşırı uzun baskısı → düzeltme riski yüksek → short öncelikli
funding_rate_score < -2.0 → Aşırı kısa baskısı → yukarı patlama riski → long öncelikli
range_position_percent < 10 veya > 90 → Fiyat ekstremde → reversal veya devam senaryosu değerlendirilir

📈 4. Trend Analizi (Fiyat Konumu ve Momentum)
Her aday çift için aşağıdaki analiz yapılır:

range_position_percent: 
< 20%
Dip seviyesi → reversal veya son satıcılar
20–80%
Dengeli → hem long hem short geçerli
> 80%
Zirve seviyesi → momentum devamı veya düzeltme

priceChangePercent > 10%: Güçlü momentum → devam ihtimali yüksek
priceChangePercent < -10%: Sert düşüş → reversal veya son alıcılar

🎯 5. Giriş/Çıkış Stratejisi (SL/TP Hesabı)
Her çift için volatiliteye göre orantılı SL ve TP seviyeleri belirlenir.

# Dinamik SL/TP Hesaplama
sl_risk = price_volatility_1m * 0.5   # SL, 1dk volatilitesinin yarısı
tp_target = sl_risk * 2               # Risk/Reward ≥ 1:2

# Örnek: price_volatility_1m = 4.97% → SL = %2.5, TP = %5.0
POZISYON 
"GIRIS ALANI" 
SL 
TP1 
TP2 
TP3

Long
Destek bölgesi (dip testi)
%2.5–3.5
+4–5%
+7–8%
+10%+

Short
Direnç bölgesi (zirve testi)
%2.5–3.5
-4–5%
-7–8%
-10%+

⏱️ Zaman Aralığı: 1-5 dakika (1dk volatilite yüksekse daha kısa) 
✅ 6. Doğrulama (Son Kline ile Gerçek Zamanlı Kontrol)
Her aday çift için son 3-5 mum analiz edilir:
son_kline = klines[-1]
önceki_kline = klines[-2]

# Fiyat hareketi
fiyat_değişimi = (son_kline.close - önceki_kline.close) / önceki_kline.close

# Hacim trendi
hacim_değişimi = (son_kline.volume - önceki_kline.volume) / önceki_kline.volume

# Doğrulama
if fiyat_değişimi > 0 and hacim_değişimi > 0:
    momentum = "yukarı (long sinyal)"
elif fiyat_değişimi < 0 and hacim_değişimi > 0:
    momentum = "aşağı (short sinyal)"
else:
    momentum = "dengeli"

🔍 Amaç: Strateji, sadece metriklerle değil, gerçek zamanlı mum hareketiyle doğrulanır. 

📊 7. Güven Skoru (Confidence Score)
Her aday çift için 0–100 arası bir Güven Skoru hesaplanır:
FAKTOR
AGIRLIK
ACIKLAMA

price_volatility_1m
%30
1dk’da ne kadar hareket var?

volume_anomaly_1m
%25
Son 1dk’da anormal aktivite var mı?

quoteVolume
%20
Likidite yeterli mi?

price_spread_percent
%10
Spread maliyeti kabul edilebilir mi?

range_position_percent
%10
Fiyat momentum bölgesinde mi?

funding_rate_score
%5
Aşırı baskı var mı?


📋 8. Sonuç Formatı (Çıktı Yapısı)
{
  "timestamp": "2025-08-21T20:40:54.535Z",
  "strategy": "scalp",
  "timeframe": "1-5m",
  "top_5_pairs": [
    {
      "symbol": "XNYUSDT",
      "confidence_score": 74.29,
      "direction": 'long',
      "volatility_1m": 4.97,
      "volume_anomaly_1m": 2.538,
      "quoteVolume": 158000000,
      "range_position_percent": 11.29,
      "funding_rate_score": 0.34,
      "entry_long": "0.008700 - 0.008850",
      "entry_short": "0.009100+",
      "stop_loss": "0.008500",
      "take_profit": ["0.009350", "0.009700", "0.010000"],
      "timeframe_minutes": 3,
      "rationale": "Aşırı volatilite + anormal hacim patlaması"
    },
    ...
  ],
  "dynamic_thresholds": {
    "max_volatility_1m": 4.97,
    "max_volume_anomaly_1m": 2.538,
    "median_quoteVolume": 120000000
  }
}

